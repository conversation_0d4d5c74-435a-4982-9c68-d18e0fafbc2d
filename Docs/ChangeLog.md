# 变更日志

## [未发布] - 2025-01-27

### 新增
- **优秀问答库表格功能增强**
  - 新增"暂不处理问答数"列，实时统计每个通话中状态为 `undo` 的问答数量
  - 新增"AI评级"筛选列，支持A-D级筛选
  - 新增"复检结果"筛选列，支持通过/不通过筛选  
  - 新增"复检评级"筛选列，支持A-D级筛选

### 改进
- **用户体验优化**
  - 筛选状态在表头显示，便于用户了解当前筛选条件
  - 表格列布局优化，提升数据可读性

### 技术变更
- 引入 `NexusTableFilterColumn` 组件实现筛选功能
- 新增 `getUndoCount` 函数用于计算暂不处理问答数
- 使用 `ExcellentQandaStatus` 枚举进行状态匹配

### 文件变更
- `apps/telesale-web/src/views/aiQualityInspection/excellentQA/index.vue`
  - 新增 4 个表格列
  - 新增相关组件引入
  - 新增数据计算函数

---

## 版本说明

### 语义化版本规则
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 变更类型
- **新增**: 新功能
- **改进**: 对现有功能的改进
- **修复**: 问题修复
- **移除**: 移除的功能
- **安全**: 安全相关的修复
- **技术变更**: 技术架构或依赖的变更 