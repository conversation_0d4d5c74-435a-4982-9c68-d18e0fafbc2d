# QA详情抽屉暂不处理功能规则文档

## 功能概述

在优秀问答库的QA详情抽屉中，为用户提供"暂不处理"操作，允许用户标记某些问答条目为忽略状态，避免重复处理不需要的内容。

## 设计思路

### 1. 用户场景分析

- **场景1**: 用户查看问答明细时，发现某些问答质量不高或不适合加入知识库
- **场景2**: 用户希望暂时跳过某些问答，专注处理其他重要内容
- **场景3**: 用户需要明确区分已处理、待处理和忽略的问答状态

### 2. 状态管理设计

采用枚举方式管理问答状态，确保状态值的一致性和可维护性：

```typescript
export const ExcellentQandaStatus = {
  MARK: 'mark',      // 已添加 - 已学习到知识库
  UNMARK: 'unmark',  // 待处理 - 等待用户操作
  UNDO: 'undo'       // 暂不处理 - 用户主动忽略
} as const;
```

### 3. 交互设计原则

- **明确性**: 状态标签颜色和文案清晰区分不同状态
- **安全性**: 重要操作需要二次确认，防止误操作
- **简洁性**: 不同状态下显示对应的操作按钮，避免界面冗余

## 实现细节

### 1. 状态显示规则

| 状态 | 标签颜色 | 显示文案 | Element Plus类型 |
|------|----------|----------|------------------|
| mark | 绿色 | 已添加 | success |
| unmark | 蓝色 | 待处理 | info |
| undo | 橙色 | 暂不处理 | warning |

### 2. 操作按钮显示规则

| 状态 | 学习问题按钮 | 暂不处理按钮 | 操作列显示 |
|------|-------------|-------------|-----------|
| mark | 隐藏 | 隐藏 | 条件显示 |
| unmark | 显示 | 显示 | 显示 |
| undo | 隐藏 | 隐藏 | 条件显示 |

**操作列显示逻辑**: 当表格中存在至少一条待处理状态的记录时，显示操作列；否则隐藏操作列。

### 3. API调用规范

#### 接口信息
- **接口名称**: `markExcellentQanda`
- **请求方法**: POST
- **接口路径**: `/admin/excellentQanda/{id}`

#### 暂不处理操作参数
```typescript
{
  items: [{
    ...rowData,           // 原始行数据
    status: 'undo'        // 固定传入'undo'状态
  }]
}
```

### 4. 二次确认弹窗规范

```typescript
await ElMessageBox.confirm(
  '是否忽略该条问答？',     // 确认文案
  '提示',                  // 标题
  {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',       // 警告类型
  }
);
```

### 5. 状态更新流程

1. **用户点击暂不处理** → 显示二次确认弹窗
2. **用户点击取消** → 关闭弹窗，不执行任何操作
3. **用户点击确认** → 调用API接口
4. **API调用成功** → 更新本地状态，显示成功提示，通知父组件刷新
5. **API调用失败** → 显示错误提示，状态不变

## 代码结构

### 1. 核心函数

```typescript
/**
 * 处理暂不处理按钮点击
 * @param row - 行数据
 */
async function handleUndoQuestion(row: any) {
  // 1. 二次确认
  // 2. 调用API
  // 3. 更新状态
  // 4. 错误处理
}
```

### 2. 状态管理函数

```typescript
/**
 * 获取状态标签类型
 */
function getStatusTagType(status: ExcellentQandaStatusType): string

/**
 * 获取状态文本
 */
function getStatusText(status: ExcellentQandaStatusType): string
```

### 3. 计算属性

```typescript
/**
 * 计算是否显示操作列
 */
const hasOperationColumn = computed(() => boolean)
```

## 扩展性考虑

### 1. 状态扩展

如需新增状态，只需：
1. 在 `ExcellentQandaStatus` 枚举中添加新状态
2. 在 `getStatusTagType` 和 `getStatusText` 函数中添加对应逻辑
3. 在操作按钮显示逻辑中添加相应判断

### 2. 操作扩展

如需新增操作按钮：
1. 在操作列模板中添加按钮
2. 在显示逻辑中添加状态判断
3. 实现对应的处理函数

### 3. 批量操作支持

当前设计支持扩展为批量操作：
- API接口已支持 `items` 数组参数
- 可扩展为多选表格，批量传入多个条目

## 测试用例

### 1. 功能测试

- [ ] 点击暂不处理按钮，弹出确认对话框
- [ ] 点击取消，对话框关闭，状态不变
- [ ] 点击确认，调用API，状态更新为暂不处理
- [ ] API调用失败时，显示错误提示

### 2. 界面测试

- [ ] 待处理状态显示蓝色标签和两个操作按钮
- [ ] 已添加状态显示绿色标签，无操作按钮
- [ ] 暂不处理状态显示橙色标签，无操作按钮
- [ ] 所有条目都无操作时，操作列隐藏

### 3. 边界测试

- [ ] 网络异常时的错误处理
- [ ] 快速连续点击的防抖处理
- [ ] 数据为空时的界面显示

## 维护指南

### 1. 状态枚举维护

- 新增状态时必须同时更新类型定义
- 删除状态前确认无业务依赖
- 修改状态值需要考虑数据库兼容性

### 2. 接口维护

- API参数变更需要同步更新类型定义
- 新增字段需要考虑向后兼容性
- 错误码处理需要完善

### 3. 组件维护

- 新增操作按钮需要考虑权限控制
- 状态显示逻辑变更需要更新测试用例
- 性能优化时注意计算属性的依赖关系 