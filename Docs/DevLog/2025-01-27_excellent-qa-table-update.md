# 优秀问答库表格功能更新

## 日期
2025-01-27

## 更新内容

### 新增表格字段
1. **暂不处理问答数** - 统计每个通话中状态为 `undo` 的问答数量
2. **AI评级** - 显示机器人评分，支持筛选（A-D级）
3. **复检结果** - 显示人工打分原因，支持筛选（通过/不通过）
4. **复检评级** - 显示人工评分，支持筛选（A-D级）

### 技术实现

#### 组件使用
- 使用 `nexus-table-filter-column` 组件实现带筛选功能的表格列
- 筛选选项配置：
  - AI评级/复检评级：A级、B级、C级、D级
  - 复检结果：通过、不通过

#### 数据计算
- 暂不处理问答数通过 `getUndoCount` 函数计算
- 使用 `ExcellentQandaStatus.UNDO` 常量进行状态匹配

#### 代码变更
```typescript
// 新增计算函数
function getUndoCount(items: any[]): number {
  if (!items || !Array.isArray(items)) {
    return 0;
  }
  return items.filter(item => item.status === ExcellentQandaStatus.UNDO).length;
}
```

### 文件修改
- `apps/telesale-web/src/views/aiQualityInspection/excellentQA/index.vue`
  - 新增 4 个表格列
  - 引入 `NexusTableFilterColumn` 组件
  - 引入 `ExcellentQandaStatus` 枚举
  - 新增 `getUndoCount` 计算函数

### 功能特性
1. **筛选功能** - 三个新字段均支持下拉筛选
2. **数据统计** - 实时计算暂不处理问答数
3. **用户体验** - 筛选状态在表头显示，便于用户了解当前筛选条件

### 依赖关系
- 依赖 API 接口 `getExcellentQandaList` 返回的数据结构
- 依赖 `ExcellentQandaStatus` 枚举定义
- 依赖 `nexus-table-filter-column` 组件

### 测试要点
1. 验证筛选功能是否正常工作
2. 验证暂不处理问答数计算是否准确
3. 验证表格数据显示是否正确
4. 验证筛选状态重置功能 