# QA详情抽屉暂不处理功能开发日志

**日期**: 2025-01-27  
**功能**: 优秀问答库QA详情抽屉增加暂不处理功能  
**开发者**: AI Assistant  

## 需求概述

在优秀问答库的QA详情抽屉中，增加"暂不处理"操作按钮，允许用户忽略某些问答条目。

## 具体需求

1. 增加"暂不处理"操作按钮
2. 点击按钮调用`markExcellentQanda`接口，传入`status: 'undo'`
3. 增加状态枚举管理
4. 状态变更为暂不处理后，操作按钮全部消失
5. 点击暂不处理时弹出二次确认弹窗

## 实现方案

### 1. API层面修改

**文件**: `apps/telesale-web/src/api/AIQualityInspection/excellentQA.ts`

- 新增状态枚举 `ExcellentQandaStatus`
  - `MARK: 'mark'` - 已添加
  - `UNMARK: 'unmark'` - 待处理  
  - `UNDO: 'undo'` - 暂不处理
- 新增状态类型 `ExcellentQandaStatusType`

### 2. 组件层面修改

**文件**: `apps/telesale-web/src/views/aiQualityInspection/excellentQA/QADetails/components/QADetailsDrawer.vue`

#### 主要变更：

1. **状态显示优化**
   - 使用枚举替代硬编码字符串
   - 新增 `getStatusTagType()` 和 `getStatusText()` 函数
   - 暂不处理状态显示为橙色标签

2. **操作按钮逻辑**
   - 新增"暂不处理"按钮
   - 使用计算属性 `hasOperationColumn` 控制操作列显示
   - 已添加和暂不处理状态下隐藏所有操作按钮

3. **暂不处理功能**
   - 新增 `handleUndoQuestion()` 函数
   - 集成二次确认弹窗
   - 调用API更新状态
   - 本地状态同步更新

4. **数据监听优化**
   - 修改状态映射逻辑，使用枚举值
   - 默认状态为 `UNMARK`

## 技术细节

### 状态枚举设计
```typescript
export const ExcellentQandaStatus = {
  MARK: 'mark',      // 已添加
  UNMARK: 'unmark',  // 待处理
  UNDO: 'undo'       // 暂不处理
} as const;
```

### 二次确认弹窗
```typescript
await ElMessageBox.confirm(
  '是否忽略该条问答？',
  '提示',
  {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }
);
```

### 操作列动态显示
```typescript
const hasOperationColumn = computed(() => {
  return tableData.value.some(item => 
    item.status !== ExcellentQandaStatus.MARK && 
    item.status !== ExcellentQandaStatus.UNDO
  );
});
```

## 测试要点

1. **功能测试**
   - 暂不处理按钮点击正常
   - 二次确认弹窗显示正确
   - 取消操作不改变状态
   - 确认操作正确调用API

2. **状态测试**
   - 已添加状态：绿色标签，无操作按钮
   - 待处理状态：蓝色标签，显示两个操作按钮
   - 暂不处理状态：橙色标签，无操作按钮

3. **界面测试**
   - 操作列在所有条目都无操作时隐藏
   - 按钮布局合理，文案正确
   - 状态标签颜色和文案正确

## 注意事项

1. 使用枚举管理状态，避免硬编码
2. 二次确认提升用户体验
3. 本地状态与服务端状态保持同步
4. 操作列动态显示优化界面布局

## 后续优化建议

1. 考虑添加批量操作功能
2. 状态变更历史记录
3. 撤销暂不处理操作的功能 