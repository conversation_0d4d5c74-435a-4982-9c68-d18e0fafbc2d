# 功能地图

## AI质检模块

### 优秀问答库
- **路径**: `/aiQualityInspection/excellentQA`
- **文件**: `apps/telesale-web/src/views/aiQualityInspection/excellentQA/index.vue`
- **功能**:
  - 优秀问答列表展示
  - 通话ID搜索和时间范围筛选
  - AI评级筛选（A-D级）
  - 复检结果筛选（通过/不通过）
  - 复检评级筛选（A-D级）
  - 暂不处理问答数统计
  - 问答明细查看
  - 录音播放功能

#### 依赖关系
- **API**: `getExcellentQandaList` - 获取优秀问答列表
- **组件**: 
  - `NexusTable` - 表格组件
  - `NexusTableFilterColumn` - 筛选列组件
  - `NexusDatePicker` - 日期选择器
  - `QADetails` - 问答明细组件
  - `DetailDrawer` - 录音播放抽屉
- **枚举**: `ExcellentQandaStatus` - 问答状态枚举

#### 子功能
1. **问答明细** (`QADetails/index.vue`)
   - 问答列表展示
   - 问答状态管理（已添加/待处理/暂不处理）
   - 批量操作功能

2. **录音播放** (`DetailDrawer/index.vue`)
   - 音频播放控制
   - 通话信息展示

### 优秀案例库
- **路径**: `/aiQualityInspection/excellentCases`
- **文件**: `apps/telesale-web/src/views/aiQualityInspection/excellentCases/index.vue`
- **功能**:
  - 优秀案例列表展示
  - AI评级筛选
  - 人工复核功能
  - 通话查看功能

## 组件库

### Nexus组件
- **NexusTable** - 基础表格组件
- **NexusTableFilterColumn** - 带筛选功能的表格列
- **NexusDatePicker** - 日期选择器
- **NexusForm** - 表单组件

### 通用组件
- **DetailDrawer** - 详情抽屉组件
- **ReTable** - 高级表格组件

## 功能间依赖关系

```mermaid
graph TD
    A[优秀问答库] --> B[问答明细]
    A --> C[录音播放]
    A --> D[NexusTable]
    A --> E[NexusTableFilterColumn]
    A --> F[API接口]
    
    B --> G[问答状态管理]
    B --> H[批量操作]
    
    D --> I[表格分页]
    D --> J[数据加载]
    
    E --> K[筛选功能]
    E --> L[下拉选择]
```

## 最近更新
- 2025-01-27: 优秀问答库新增AI评级、复检结果、复检评级筛选功能及暂不处理问答数统计 