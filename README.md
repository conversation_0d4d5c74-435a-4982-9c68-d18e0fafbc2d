# Telesale Platform Monorepo

> 基于现代化技术栈的电销平台 Monorepo 项目

## 📖 技术概述

这是一个采用 Monorepo 架构的电销平台项目，使用 pnpm workspace 进行统一管理，包含多个前端应用和共享包。

## 🏗️ 项目架构

```text
telesale-web_v2/
├── apps/                       # 应用目录
│   ├── telesale-web/          # Web 管理平台
│   ├── telesale-mobile/       # 移动端应用
│   └── telesale-phone/        # 电话系统
├── packages/                   # 共享包目录
│   ├── shared/                # 共享工具库
│   ├── server/                # 服务端共享代码
│   └── docs/                  # 文档系统
├── script/                     # 构建脚本
├── package.json               # 根项目配置
├── pnpm-workspace.yaml        # Workspace 配置
└── turbo.json                 # Turbo 构建配置
```

## 📱 应用模块

### Telesale Web

- **技术栈**: Vue 3 + TypeScript + Element Plus + UnoCSS
- **构建工具**: Vite 2.9.8
- **文档**: [查看详细文档](./apps/telesale-web/README.md)

### Telesale Mobile

- **技术栈**: Vue 3 + Vant + TypeScript
- **构建工具**: Vite
- **文档**: [查看详细文档](./apps/telesale-mobile/README.md)

### Telesale Phone

- **技术栈**: Vue 3 + TypeScript
- **构建工具**: Vite
- **文档**: [查看详细文档](./apps/telesale-phone/README.md)

## 📦 共享包

### @telesale/shared

- **路径**: `packages/shared/`
- **功能**: 通用工具函数、类型定义、常量配置

### @telesale/server

- **路径**: `packages/server/`
- **功能**: 服务端共享代码、API 接口定义

### @guanghe-pub/nexus-docs

- **路径**: `packages/docs/`
- **功能**: 文档系统、组件文档生成

## 🛠️ 技术栈

### 核心技术

- **前端框架**: Vue 3.3.4
- **类型系统**: TypeScript 5.1.6
- **构建工具**: Vite 4.4.4
- **包管理**: pnpm 8.15.4
- **构建系统**: Turbo 2.0.9

### UI 组件库

- **Web端**: Element Plus 2.3.9
- **移动端**: Vant 4.8.0
- **样式方案**: UnoCSS 0.53.5

### 开发工具

- **代码规范**: ESLint + Prettier
- **提交规范**: Commitizen + Commitlint
- **Git钩子**: Husky + lint-staged
- **测试框架**: Jest 29.6.1

## 🚀 快速开始

### 环境要求

- Node.js >= 14.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
# 安装所有依赖
pnpm install
```

### 开发环境

```bash
# 启动 Web 管理平台
pnpm dev:web

# 启动移动端应用
pnpm dev:mobile
```

## 📚 应用文档索引

- [Telesale Web](./apps/telesale-web/README.md) - Web 管理平台
- [Telesale Mobile](./apps/telesale-mobile/README.md) - 移动端应用
- [Telesale Phone](./apps/telesale-phone/README.md) - 电话系统

## 🛠️ 开发文档

- [需求完成生成文档工作流规范](./需求完成生成文档工作流规范.md) - 需求完成后的文档生成标准流程
- [业务组件文档生成规则](./业务组件文档生成规则.md) - 复杂业务组件的文档编写规范
- [飞书文档处理规范](./飞书文档处理规范.md) - 飞书文档的处理和集成规范
- [时间获取规则](./时间获取规则.md) - 项目中时间获取的标准规范和验证方法
