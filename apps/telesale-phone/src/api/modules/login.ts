import Axios from '@/api/axios'

export function login(par: {
  mail: string
  code: string
}): Promise<{
  token: string
}> {
  return Axios.post('/web/token/login', par)
}

export function verifyCode(par: {
  mail: string
}) {
  return Axios.post(`/web/token/sms`, par)
}

export function getUserInfo(): Promise<{
  name: string
  phone: string
  email: string
  username: string
  qmExten: string // 外呼工号
  channel: string // 外呼渠道
  channelId: number
  workerId: number
  avatar: string
  permissions: string[] // 权限列表
  domain: string
  password: string
}> {
  return Axios.get('/web/token/me')
}


// 获取图片
export function getPic(data: string): Promise<string> {
  return Axios.get(`${import.meta.env.VITE_HOST_TELESALE_API}/web/crm/voucher?object=${data}`)
}

// 获取版本号
export function getVersion(): Promise<string> {
  return Axios.get(`http://127.0.0.1:9876/version`)
}

// 上传日志
export function uploadLog(data) {
  return Axios.post(`/admin/logger`, data)
}

// 获取配置项
export function getConfig(workerId: number): Promise<{
  cache: boolean
  hang: boolean
  workerId: string
}> {
  return Axios.get(`/web/${workerId}/config`)
}

// 设置配置项
export function setConfig(par: {
  workerId: number
  cache: boolean
  hang: boolean
}) {
  return Axios.post(`/web/${par.workerId}/config`, par)
}
