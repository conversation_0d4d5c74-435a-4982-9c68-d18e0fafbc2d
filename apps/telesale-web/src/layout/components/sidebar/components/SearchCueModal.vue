<!--
 * @Date         : 2024-10-08 11:24:53
 * @Description  : 线索查询
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { FormInstance } from "element-plus";
import { phoneReg, onionIdReg } from "/@/utils/common/pattern";
import { ClueInfo, searchClueApi } from "/@/api/customer/index";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import timeChange from "/@/utils/handle/timeChange";
import { useDetail } from "/@/utils/handle/customDetails";
import { Folder } from "@element-plus/icons-vue";

interface Props {
  value: boolean;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emits = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const { allAgentObj } = storeToRefs(useUserStore());
const { toDetail } = useDetail();

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emits("update:value", val);
  }
});

const ruleFormRef = ref<FormInstance>();
const loading = ref(false);
const form = ref({
  searchInfo: undefined
});
const dataList = ref<ClueInfo[]>([]);

const handleClose = () => {
  isModel.value = false;
};

const checkForm = (rule: any, value: any, callback: any) => {
  if (!phoneReg.test(value) && !onionIdReg.test(value)) {
    callback(new Error("请输入正确的手机号/洋葱ID"));
  } else {
    callback();
  }
};

const handleSearch = () => {
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      searchClueApi(form.value)
        .then(res => {
          dataList.value = res.data.infos;
        })
        .catch(() => {
          dataList.value = [];
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

const goDetail = (item: ClueInfo) => {
  toDetail(item, item.location === "已成交" ? "done" : "ongoing", "");
  isModel.value = false;
};
</script>

<template>
  <el-dialog
    title="搜索手机号/洋葱ID"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="handleSearch"
    append-to-body
  >
    <div v-loading="loading">
      <el-form :model="form" label-suffix="：" ref="ruleFormRef">
        <el-form-item
          label=""
          prop="searchInfo"
          :rules="[
            {
              required: true,
              message: '请输入手机号/洋葱ID',
              trigger: 'blur'
            },
            { validator: checkForm, trigger: 'blur' }
          ]"
        >
          <el-input
            v-model.trim="form.searchInfo"
            placeholder="请输入手机号/洋葱ID"
            style="width: 100%"
            clearable
          />
        </el-form-item>
      </el-form>
      <div>
        <div v-if="dataList.length > 0" class="mb-20px">
          <div v-for="item in dataList" :key="item.infoUuid">
            <el-card
              class="cursor-pointer hover:bg-#afe4fd rounded-5px mb-20px"
              @click="goDetail(item)"
            >
              <div class="font-bold flex items-center gap-10px">
                <el-icon><Folder :size="20" /></el-icon>
                {{ item.location }}
              </div>
              <el-row :gutter="10" class="mt-10px">
                <el-col :span="6">洋葱ID：{{ item.onionId }}</el-col>
                <el-col :span="7">手机号：{{ item.phone }}</el-col>
                <el-col :span="10">
                  所属坐席：{{ allAgentObj[item.workerId]?.name }}
                </el-col>
              </el-row>
              <el-row :gutter="10" class="mt-10px">
                <el-col :span="12">
                  线索创建时间：{{ timeChange(item.createdAt, 3) }}
                </el-col>
                <el-col :span="12">
                  线索到期时间：{{ timeChange(item.userExpire, 3) }}
                </el-col>
              </el-row>
            </el-card>
          </div>
        </div>

        <el-empty v-else description="暂无查询结果" />
      </div>
    </div>
  </el-dialog>
</template>
