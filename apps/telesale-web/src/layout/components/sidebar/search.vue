<!--
 * @Date         : 2024-10-08 11:26:15
 * @Description  : 搜索功能
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import SearchCueModal from "./components/SearchCueModal.vue";

const isModal = ref(false);

const openModal = () => {
  isModal.value = true;
};
</script>

<template>
  <div
    class="h-50px flex items-center px-20px c-white cursor-pointer"
    @click="openModal"
  >
    <IconifyIconOffline icon="search" class="w-24px text-18px mr-5px" />
    <span class="text-14px w-125px">搜线索</span>
    <SearchCueModal v-model:value="isModal" v-if="isModal" />
  </div>
</template>

<style lang="scss" scoped></style>
