---
category: Hooks
---

# useDraggable

响应式工具，用于使 DOM 元素可拖动。该工具会根据可拖动状态动态绑定和解除鼠标事件，并且可以重置元素的位置。

## 基本用法

```typescript
import { useDraggable } from '/@/hooks/useDaraggable'
import { ref, computed } from 'vue'

// 目标元素引用
const targetRef = ref<HTMLElement | undefined>()
// 拖动触发元素引用
const dragRef = ref<HTMLElement | undefined>()
// 可拖动状态
const draggable =  true
// 溢出状态（可选）
const overflow =  false

// 使用 useDraggable 钩子
const { resetPosition } = useDraggable(targetRef, dragRef, draggable, overflow)
```

## 初始拖动设置

若要在元素首次渲染时就可拖动，可确保 `draggable` 计算属性初始值为 `true`。这样在组件挂载后，拖动功能会立即生效。

```typescript
import { useDraggable } from '/@/hooks/useDaraggable'
import { ref, computed } from 'vue'

const targetRef = ref<HTMLElement | undefined>()
const dragRef = ref<HTMLElement | undefined>()
// 初始可拖动状态为 true
const draggable = computed(() => true)
const overflow = computed(() => false)

const { resetPosition } = useDraggable(targetRef, dragRef, draggable, overflow)
```

## 重置元素位置

`resetPosition` 方法可用于将元素位置重置到初始状态。你可以在需要的时候调用这个方法，例如在某个按钮点击事件中。

```vue
<template>
  <div>
    <!-- 目标元素 -->
    <div ref="targetRef" style="width: 100px; height: 100px; background-color: red;"></div>
    <!-- 拖动触发元素 -->
    <div ref="dragRef">拖动我</div>
    <!-- 重置按钮 -->
    <button @click="resetPosition">重置位置</button>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useDraggable } from '/@/hooks/useDaraggable'

const targetRef = ref<HTMLElement | undefined>()
const dragRef = ref<HTMLElement | undefined>()
const draggable = computed(() => true)
const overflow = computed(() => false)

const { resetPosition } = useDraggable(targetRef, dragRef, draggable, overflow)
</script>
```

## 参数说明

| 参数       | 类型                    | 描述                                                         |
| ---------- | ----------------------- | ------------------------------------------------------------ |
| `targetRef` | `Ref<HTMLElement \| undefined>` | 目标元素的引用，即需要拖动的元素。                           |
| `dragRef`   | `Ref<HTMLElement \| undefined>` | 拖动触发元素的引用，点击该元素可触发拖动操作。               |
| `draggable` | `ComputedRef<boolean>`  | 可拖动状态的计算属性，当值为 `true` 时，元素可拖动；为 `false` 时，不可拖动。 |
| `overflow`  | `ComputedRef<boolean>`  | 可选参数，用于控制元素拖动时是否允许溢出父容器。默认为 `undefined`。 |

## 返回值说明

| 返回值         | 类型       | 描述                                                         |
| -------------- | ---------- | ------------------------------------------------------------ |
| `resetPosition` | `() => void` | 重置元素位置的方法，调用该方法会将元素的偏移量重置为 0，并移除 `transform` 样式。 | 