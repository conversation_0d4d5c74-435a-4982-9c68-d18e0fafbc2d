# useTimeout

`useTimeout` 是一个自定义的 Vue 组合式函数，用于处理定时器相关的操作。因为浏览器的机制，当前标签不活跃的时候定时器和计时器都会被限流，导致计时器不准确。它可以帮助你方便地启动、停止定时器，并跟踪定时器的运行时间。

## 基本用法

```typescript
import { useTimeout } from './useTimeout'
import { ref } from 'vue'

const { start, stop, count } = useTimeout()

const callback = () => {
  console.log('定时器已触发')
}

const delay = 2000 // 2 秒

// 启动定时器
start(callback, delay)

// 停止定时器
// stop()

// 监听定时器运行时间
watch(count, (newCount) => {
  console.log(`定时器已运行 ${newCount} 秒`)
})
```

## 选项配置

`useTimeout` 函数接受一个可选的配置对象 `TimeoutOptions`，该对象包含以下属性：

- `destroyed`：布尔值，默认为 `true`。当组件卸载时，是否自动停止定时器。

```typescript
import { useTimeout } from './useTimeout'
import { ref } from 'vue'

// 配置选项，组件卸载时不自动停止定时器
const options = { destroyed: false }
const { start, stop, count } = useTimeout(options)

// ... 其他代码 ...
```

## 启动定时器

使用 `start` 方法启动定时器，该方法接受两个参数：

- `callback`：定时器触发时执行的回调函数。
- `delay`：定时器的延迟时间，单位为毫秒。

```typescript
import { useTimeout } from './useTimeout'
import { ref } from 'vue'

const { start } = useTimeout()

const callback = () => {
  console.log('定时器已触发')
}

const delay = 3000 // 3 秒

// 启动定时器
start(callback, delay)
```

## 停止定时器

使用 `stop` 方法停止定时器。

```typescript
import { useTimeout } from './useTimeout'
import { ref } from 'vue'

const { start, stop } = useTimeout()

const callback = () => {
  console.log('定时器已触发')
}

const delay = 2000 // 2 秒

// 启动定时器
start(callback, delay)

// 停止定时器
stop()
```

## 跟踪定时器运行时间

使用 `count` 响应式引用跟踪定时器的运行时间，单位为秒。

```typescript
import { useTimeout } from './useTimeout'
import { ref, watch } from 'vue'

const { start, count } = useTimeout()

const callback = () => {
  console.log('定时器已触发')
}

const delay = 4000 // 4 秒

// 启动定时器
start(callback, delay)

// 监听定时器运行时间
watch(count, (newCount) => {
  console.log(`定时器已运行 ${newCount} 秒`)
})
```

以上就是 `useTimeout` 钩子函数的详细使用说明。通过这个钩子，你可以方便地管理定时器的启动、停止和运行时间跟踪。 