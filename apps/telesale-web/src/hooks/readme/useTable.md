# useTable

`useTable` 是一个自定义的 Vue 组合式函数，用于处理表格数据的加载、分页和搜索等功能。它可以帮助你更方便地管理表格数据的状态和操作。

## 基本用法

```typescript
import { useTable } from '/@/hooks/useTable.tsx';

// 假设定义了一个获取表格数据的 API
const fetchTableData = async (params) => {
  // 这里是实际的 API 请求逻辑
  return { data: { list: [], total: 0 } };
};

const options = {
  api: fetchTableData,
  isPages: true,
  immediate: true,
  initParams: {},
  // 可以根据需要添加其他选项
};

const { dataList, loading, searchForm, pageInfo, total, handlerQuery, changePgaeSize, Pagination, onSearch } = useTable(options);
```

## 选项说明

### `Options<T, P>`

| 选项名         | 类型                                       | 默认值   | 说明                                                                                   |
|--------------|------------------------------------------|--------|--------------------------------------------------------------------------------------|
| `isPages`    | `boolean`                                | `true` | 是否启用分页功能                                                                             |
| `immediate`  | `boolean`                                | `true` | 是否在初始化时立即调用 API 获取数据                                                                 |
| `initParams` | `P`                                      | `{}`   | 初始化时传递给 API 的参数                                                                       |
| `api`        | `(params: P) => Promise<ReturnValue<T[] \| ReturnList<T>>>` | -      | 用于获取表格数据的 API 函数，接收参数 `params` 并返回一个 Promise，解析为包含数据的对象                             |
| `beforeRequest` | `(params: UnwrapRef<P>) => UnwrapRef<P>` | -      | 在请求数据之前对参数进行处理的函数，接收当前的搜索参数并返回处理后的参数                                       |
| `requestError` | `(err: any) => void`                    | -      | 请求数据发生错误时的回调函数，接收错误对象 `err`                                               |
| `dataCallback` | `(data: any) => void`                   | -      | 数据获取成功后对数据进行处理的回调函数，接收返回的数据                                           |
| `endCallback` | `() => void`                            | -      | 请求结束后的回调函数，无论请求成功还是失败都会调用                                               |

## 分页操作

### 改变页码
```typescript
changePage(val: number);
```
- **参数**：`val` - 新的页码
- **说明**：调用该方法会更新当前页码并重新获取数据。

### 改变每页显示数量
```typescript
changePgaeSize(val: number);
```
- **参数**：`val` - 新的每页显示数量
- **说明**：调用该方法会将页码重置为 1，并更新每页显示数量，然后重新获取数据。

### 搜索操作
```typescript
onSearch();
```
- **说明**：调用该方法会将页码重置为 1，并重新获取数据，通常用于搜索表单提交时调用。

## 分页组件

```vue
<Pagination props={...} />
```
- **参数**：`props` - `ElPagination` 组件的属性，用于自定义分页组件的样式和行为。
- **说明**：该组件是一个封装好的 `ElPagination` 组件，用于显示分页控件，并绑定了页码和每页显示数量的变化事件。

## 返回值说明

| 返回值         | 类型                                       | 说明                                                                                   |
|--------------|------------------------------------------|--------------------------------------------------------------------------------------|
| `dataList`   | `Ref<T[]>`                               | 存储表格数据的响应式引用                                                                 |
| `loading`    | `Ref<boolean>`                           | 表示表格数据是否正在加载的响应式引用                                                             |
| `searchForm` | `Ref<P>`                                 | 存储搜索表单数据的响应式引用                                                                 |
| `pageInfo`   | `Ref<Partial<PageInfo>>`                 | 存储分页信息的响应式引用，包含 `pageIndex` 和 `pageSize`                                         |
| `total`      | `Ref<number>`                            | 存储表格数据总数的响应式引用                                                                 |
| `handlerQuery` | `() => Promise<void>`                    | 手动触发数据查询的方法                                                                         |
| `changePgaeSize` | `(val: number) => void`                  | 改变每页显示数量的方法                                                                         |
| `Pagination` | `(props: Partial<PaginationProps>) => JSX.Element` | 分页组件，用于显示分页控件                                                                     |
| `onSearch`   | `() => void`                             | 搜索操作的方法，通常用于搜索表单提交时调用                                                         |

通过以上的说明，你可以更方便地使用 `useTable` 钩子来管理表格数据的加载、分页和搜索等功能。