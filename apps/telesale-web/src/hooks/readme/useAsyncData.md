
# useAsyncData

这是一个用于异步获取数据的 React 钩子。它允许你管理异步数据的加载状态，并提供了成功和失败的回调。

## 基本用法

```typescript
import { useAsyncData } from '/@/hooks/useAsyncData';

// 定义一个异步 API 函数
const fetchData = async (params: any) => {
  const response = await fetch(`https://api.example.com/data?${params}`);
  return response.json();
};

// 使用 useAsyncData 钩子
const { send, data, loading } = useAsyncData(fetchData, { id: 1 });

// 手动触发数据获取
const handleClick = () => {
  send();
};
```

## 自动调用

默认情况下，`useAsyncData` 会立即调用 API。如果你想手动调用，可以将 `immediate` 选项设置为 `false`。

```typescript
import { useAsyncData } from '/@/hooks/useAsyncData';

// 定义一个异步 API 函数
const fetchData = async (params: any) => {
  const response = await fetch(`https://api.example.com/data?${params}`);
  return response.json();
};

// 使用 useAsyncData 钩子，设置 immediate 为 false
const { send, data, loading } = useAsyncData(fetchData, { id: 1 }, { immediate: false });

// 手动触发数据获取
const handleClick = () => {
  send();
};
```

## 初始化数据

你可以通过 `initialData` 选项提供初始数据。

```typescript
import { useAsyncData } from '/@/hooks/useAsyncData';

// 定义一个异步 API 函数
const fetchData = async (params: any) => {
  const response = await fetch(`https://api.example.com/data?${params}`);
  return response.json();
};

// 使用 useAsyncData 钩子，提供初始数据
const { send, data, loading } = useAsyncData(fetchData, { id: 1 }, { initialData: { name: 'Initial Data' } });
```

## 成功和失败回调

你可以通过 `onSuccess` 和 `onError` 选项提供成功和失败的回调。

```typescript
import { useAsyncData } from '/@/hooks/useAsyncData';

// 定义一个异步 API 函数
const fetchData = async (params: any) => {
  const response = await fetch(`https://api.example.com/data?${params}`);
  return response.json();
};

// 使用 useAsyncData 钩子，提供成功和失败回调
const { send, data, loading } = useAsyncData(fetchData, { id: 1 }, {
  onSuccess: () => {
    console.log('Data fetched successfully');
  },
  onError: (error) => {
    console.error('Error fetching data:', error);
  }
});
```

以上文档展示了 `useAsyncData` 钩子的基本用法、手动调用、初始化数据以及成功和失败回调的使用。