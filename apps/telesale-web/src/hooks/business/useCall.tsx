/*
 * @Date         : 2024-12-18 18:40:59
 * @Description  : 外呼hooks
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { useUserStore } from "/@/store/modules/user";
import { call, hangup, callState } from "/@/api/customerDetails";
import { useTimeout } from "/@/hooks/useTimeout";
import { secondsToHms } from "/@/utils/time/index";
import { storeToRefs } from "pinia";
import { getAuth } from "/@/utils/auth";
import { useDraggable } from "/@/hooks/useDaraggable";

export const useCall = (phone: Ref<string>) => {
  const drageCallRef = ref();

  const useUser = useUserStore();
  const { setCallDisabled } = useUser;
  const { callDisabled } = storeToRefs(useUser);
  const { resetPosition } = useDraggable(
    drageCallRef,
    drageCallRef,
    callDisabled as ComputedRef<boolean>
  );

  const { count, start, stop } = useTimeout({
    destroyed: false
  });

  const callStatusObj = {
    noCall: "一键拨打",
    calling: "呼叫中",
    called: "通话中",
    hangup: "已挂断"
  };
  const callColorObj = {
    noCall: "primary",
    calling: "warning",
    called: "success",
    hangup: "info"
  };

  const timer = ref();

  const timeNum = computed(() => {
    return secondsToHms(count.value);
  });

  const customCall = getAuth("telesale_admin_custom_call");
  const callStatus = ref("noCall");
  const hangupLoading = ref(false);

  //挂断后三秒复原
  function callReset() {
    stop();
    timer.value = setTimeout(() => {
      count.value = 0;
      setCallDisabled(false);
      callStatus.value = "noCall";
      stop();
    }, 3000);
  }
  //定时查询呼叫状态
  function timerPolling() {
    callStatus.value = "calling";
    callStatusFind();
  }

  //呼叫状态查询
  function callStatusFind() {
    if (callStatus.value === "hangup") {
      callReset();
    } else {
      callState().then(({ data }: { data: any }) => {
        if (data.state === 5) {
          callStatus.value = "called";
        } else if (data.state > 6 || data.state === 1) {
          callStatus.value = "hangup";
        }
      });
      start(() => {
        callStatusFind();
      }, 1000);
    }
  }
  //一键外呼
  function fastCall(familyId?: string) {
    if (callDisabled.value) return ElMessage.warning("正在呼叫中");
    resetPosition();

    if (!phone.value) {
      ElMessage.warning("此线索没有手机号");
      return;
    }
    setCallDisabled(true);

    call({ phone: phone.value, familyId: familyId })
      .then((res: any) => {
        timerPolling();
      })
      .catch((res: any) => {
        setCallDisabled(false);
      });
  }

  //挂断
  function hangupMath() {
    hangupLoading.value = true;
    hangup()
      .then(() => {
        callStatus.value = "hangup";
        hangupLoading.value = false;
        callReset();
      })
      .catch(() => {
        hangupLoading.value = false;
      });
  }

  const CallModal = () => {
    if (callStatus.value === "noCall") return <div></div>;
    return (
      <div
        ref={drageCallRef}
        class="fixed right-0 top-100px z-100000 min-w-130px h-50px hover:cursor-move"
      >
        <el-card ref="drageRef">
          {customCall && phone.value ? (
            <el-button
              type={callColorObj[callStatus.value]}
              onClick={() => fastCall}
              disabled={callDisabled.value}
            >
              {callStatusObj[callStatus.value]}
              {callStatus.value !== "noCall" ? timeNum.value : ""}
            </el-button>
          ) : (
            ""
          )}
          {callStatus.value === "calling" || callStatus.value === "called" ? (
            <el-button
              type="danger"
              onClick={() => hangupMath()}
              disabled={hangupLoading.value}
            >
              挂断
            </el-button>
          ) : (
            ""
          )}
        </el-card>
      </div>
    );
  };

  return {
    fastCall,
    CallModal
  };
};
