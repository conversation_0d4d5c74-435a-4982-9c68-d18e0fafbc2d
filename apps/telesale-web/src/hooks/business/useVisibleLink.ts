/*
 * @Date         : 2024-11-14 17:09:57
 * @Description  : 千元品测试组
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { getLinkSettingApi } from "/@/api/customer/linkSetting";

export const useVisibleLink = () => {
  const hasPermissions = ref<boolean>(false);
  const loading = ref<boolean>(false);
  const getHasPermissions = async () => {
    try {
      loading.value = true;
      const res = await getLinkSettingApi();
      const {
        visibleType,
        invisibleType,
        invisibleStart,
        invisibleEnd,
        inGroups
      } = res.data;

      if (visibleType === 2) {
        if (invisibleType === 1) {
          const current = new Date().getTime() / 1000;

          hasPermissions.value =
            current < invisibleStart || current > invisibleEnd;
        } else {
          hasPermissions.value = !inGroups;
        }
      } else {
        hasPermissions.value = true;
      }
    } finally {
      loading.value = false;
    }
  };
  getHasPermissions();

  return {
    hasPermissions,
    loading
  };
};
