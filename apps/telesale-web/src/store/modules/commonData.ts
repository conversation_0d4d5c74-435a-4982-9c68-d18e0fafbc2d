/*
 * @Date         : 2024-10-30 16:03:28
 * @Description  : 公用数据
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { defineStore } from "pinia";
import { getAddress } from "/@/api/system";

interface CommonData {
  /**
   * @description: 省市区数据
   */
  cityData: {
    name: string;
    code: string;
    children?: CommonData["cityData"];
  }[];
}

export const useCommonDataStore = defineStore({
  id: "commonData",
  state: (): CommonData => ({
    cityData: []
  }),
  actions: {
    async getCityData() {
      if (this.cityData.length > 0) return;
      const res = await getAddress();
      this.cityData = res.data;
    }
  }
});
