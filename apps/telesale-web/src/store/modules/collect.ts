/*
 * @Date         : 2024-10-09 16:18:55
 * @Description  : 收藏夹
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import {
  CollectList,
  getClueCollectApi,
  getCollectApi
} from "/@/api/customer/collect";
import { defineStore } from "pinia";
import { getLabel } from "/@/utils/common";

interface CollectStore {
  collectList: CollectList[];
}

export const useCollectStore = defineStore({
  id: "collect",
  state: (): CollectStore => ({
    collectList: []
  }),
  actions: {
    async getCollectList() {
      try {
        const res = await getCollectApi();
        this.setCollectList(res.data.bookmarkInfos);
      } catch (error) {
        console.error(error);
      }
    },
    setCollectList(list: CollectList[]) {
      this.collectList = list;
    },
    async setCollectClue(infoUuids: string[], list: any[]) {
      try {
        if (!infoUuids.length) return list;
        const { data } = await getClueCollectApi({ infoUuids });

        list.forEach((item: any) => {
          item.collectInfo = {
            infoUuid: item.infoUuid,
            id: getLabel(item.infoUuid, data.collectInfos, "id", "infoUuid")
          };
        });
        return list;
      } catch (error) {
        console.error(error);
        return list;
      }
    },
    updateClueCollect(
      id: number,
      infoUuid: string,
      action: "add" | "delete",
      list: any[]
    ) {
      list.forEach(item => {
        if (item.infoUuid === infoUuid) {
          item.collectInfo.id = action === "add" ? id : undefined;
        }
      });
      return list;
    }
  }
});
