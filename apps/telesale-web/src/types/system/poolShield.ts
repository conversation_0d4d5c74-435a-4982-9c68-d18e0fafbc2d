/*
 * @Date         : 2025-01-19 16:00:00
 * @Description  : 公海池屏蔽规则类型定义
 * @Autor        : system
 * @LastEditors  : system
 */

/**
 * 短期内多次回流客户规则配置
 */
export interface ReflowRuleConfig {
  /** 规则开关状态 */
  enabled: boolean;
  /** X天内 */
  days: number;
  /** X次回流 */
  times: number;
}

/**
 * 流量投流商品规则配置
 */
export interface ProductRuleConfig {
  /** 规则开关状态 */
  enabled: boolean;
  /** 商品ID列表 */
  productIds: string[];
}

/**
 * 公海池屏蔽规则总配置
 */
export interface PoolShieldRules {
  /** 短期内多次回流客户规则 */
  reflowRule: ReflowRuleConfig;
  /** 流量投流商品规则 */
  productRule: ProductRuleConfig;
}

/**
 * 商品ID项
 */
export interface ProductIdItem {
  /** 商品ID */
  id: string;
  /** 唯一标识符，用于列表渲染 */
  key: string;
}
