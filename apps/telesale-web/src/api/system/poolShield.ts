/*
 * @Date         : 2025-01-19 16:00:00
 * @Description  : 公海池屏蔽规则API
 * @Autor        : system
 * @LastEditors  : system
 */

import { http } from "/@/utils/http";
import baseURL from "../url";
import { PoolShieldRules, ReflowRuleConfig, ProductRuleConfig } from "/@/types/system/poolShield";

/**
 * @description: 获取公海池屏蔽规则配置
 * @returns {PoolShieldRules}
 */
export const getPoolShieldRulesApi = () => {
  return http.request<PoolShieldRules>(
    "get",
    `${baseURL.api}/web/pool_shield_rules/get`
  );
};

/**
 * @description: 保存公海池屏蔽规则配置
 * @param {PoolShieldRules} data
 */
export const savePoolShieldRulesApi = (data: PoolShieldRules) => {
  return http.request(
    "post", 
    `${baseURL.api}/web/pool_shield_rules/save`, 
    { data }
  );
};

/**
 * @description: 获取短期内多次回流客户规则配置
 * @returns {ReflowRuleConfig}
 */
export const getReflowRuleApi = () => {
  return http.request<ReflowRuleConfig>(
    "get",
    `${baseURL.api}/web/pool_shield_rules/reflow`
  );
};

/**
 * @description: 保存短期内多次回流客户规则配置
 * @param {ReflowRuleConfig} data
 */
export const saveReflowRuleApi = (data: ReflowRuleConfig) => {
  return http.request(
    "post",
    `${baseURL.api}/web/pool_shield_rules/reflow`,
    { data }
  );
};

/**
 * @description: 获取流量投流商品规则配置
 * @returns {ProductRuleConfig}
 */
export const getProductRuleApi = () => {
  return http.request<ProductRuleConfig>(
    "get",
    `${baseURL.api}/web/pool_shield_rules/product`
  );
};

/**
 * @description: 保存流量投流商品规则配置
 * @param {ProductRuleConfig} data
 */
export const saveProductRuleApi = (data: ProductRuleConfig) => {
  return http.request(
    "post",
    `${baseURL.api}/web/pool_shield_rules/product`,
    { data }
  );
};
