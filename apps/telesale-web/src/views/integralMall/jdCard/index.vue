<!--
 * @Date         : 2025-02-27 11:31:38
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<!--
 * @Date         : 2024-07-17 17:23:22
 * @Description  : 测试环境工具，新增商品
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup name="jdCard">
import { ref } from "vue";
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { columns } from "./data/index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import UploadData from "./components/UploadData.vue";
import {
  jdCardListApi,
  importJdCardApi,
  getJdCardInventoryApi
} from "/@/api/pointsMall/jdCard";
import UploadJdCard from "./dialog/UploadJdCard.vue";

const isModal = ref<boolean>(false);
const infos = ref();
const { dataList, Pagination, onSearch, loading } = useTable({
  api: jdCardListApi,
  dataCallback(res) {
    res.data.list = res.data.jdCardLogs;
  }
});

const getInventory = () => {
  getJdCardInventoryApi().then(res => {
    infos.value = res.data.inventoryInfos;
  });
};

const search = () => {
  onSearch();
  getInventory();
};

getInventory();
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <div class="flex justify-between">
        <div class="flex gap-10px">
          <div class="sum-item" v-for="item in infos" :key="item.name">
            <div class="sum-item-title">{{ item.name }}</div>
            <div>剩余{{ item.noExchange }}, 已兑换{{ item.hasExchange }}</div>
          </div>
        </div>

        <el-button type="primary" class="g-margin-r-10" @click="isModal = true">
          导入京东卡
        </el-button>
      </div>
      <div class="g-table-box">
        <ReTable
          v-if="!deviceDetection()"
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="columns"
        />
        <ReCardList
          v-else
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="columns"
        />
      </div>
      <Pagination />
      <UploadJdCard v-if="isModal" v-model:value="isModal" @onSearch="search" />
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.sum-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--el-color-primary);
  color: #fff;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 10px;
  font-size: 16px;
}
.sum-item-title {
  font-size: 20px;
}
</style>
