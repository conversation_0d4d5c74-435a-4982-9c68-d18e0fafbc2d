/*
 * @Date         : 2025-02-27 11:33:03
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { storeToRefs } from "pinia";
import { TableColumns } from "/@/components/ReTable/types";
import { useUserStore } from "/@/store/modules/user";

const { allAgentObj } = storeToRefs(useUserStore());

export const columns: TableColumns[] = [
  {
    field: "uploadTime",
    desc: "上传时间",
    timeChange: 3
  },
  {
    field: "uploader",
    desc: "上传人",
    customRender: ({ text }) => {
      return allAgentObj.value[text]?.name || "";
    }
  },
  {
    field: "total",
    desc: "总数量"
  },
  {
    field: "jdCard50Count",
    desc: "50元"
  },
  {
    field: "jdCard100Count",
    desc: "100元"
  },
  {
    field: "jdCard200Count",
    desc: "200元"
  }
];
