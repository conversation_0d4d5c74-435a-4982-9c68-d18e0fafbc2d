<!--
 * @Date         : 2024-08-02 10:31:21
 * @Description  : 积分管理弹窗
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import PointOperation from "../components/PointOperation.vue";
import OperationRecord from "../components/OperationRecord.vue";

interface Props {
  value: boolean;
  row: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "success"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const active = ref(1);
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

const getList = () => {
  emit("success");
};
</script>

<template>
  <el-dialog
    :title="'积分管理-' + props.row.onionId"
    v-model="isModel"
    :before-close="handleClose"
  >
    <el-tabs v-model="active">
      <el-tab-pane label="积分操作" :name="1">
        <PointOperation
          :row="props.row"
          @close="handleClose"
          @success="getList"
        />
      </el-tab-pane>
      <el-tab-pane label="操作记录" :name="2" lazy>
        <OperationRecord :userId="row.userId" />
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<style scoped lang="scss">
:deep(.el-form-item .el-input) {
  width: 350px;
}
</style>
