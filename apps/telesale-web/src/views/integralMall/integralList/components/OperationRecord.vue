<!--
 * @Date         : 2024-08-02 12:06:56
 * @Description  : 积分操作记录
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import ReTable from "/@/components/ReTable/index.vue";
import { useTable } from "/@/hooks/useTable";
import { TableColumns } from "/@/components/ReTable/types";
import { ref } from "vue";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import { getPointRecordApi } from "/@/api/pointsMall/pointsManage";
import { getLabel } from "/@/utils/common";
import { pointOperationList, PointOperation } from "../data";

interface Props {
  userId: string;
}
const props = withDefaults(defineProps<Props>(), {});

const { allAgentObj } = storeToRefs(useUserStore());

const listHeader = ref<TableColumns[]>([
  {
    field: "changeType",
    desc: "操作类型",
    customRender: ({ text }) => {
      return getLabel(text, pointOperationList) + "积分";
    }
  },
  {
    field: "point",
    desc: "具体操作内容",
    customRender: ({ text, row }) => {
      return `${getLabel(row.changeType, pointOperationList)}${text}积分${
        row.changeType === PointOperation.transfer
          ? "给" + row.transferOnionId
          : ""
      }`;
    }
  },
  {
    field: "createdAt",
    desc: "操作时间",
    timeChange: 3
  },
  {
    field: "reason",
    desc: "操作原因"
  },
  {
    field: "operatorId",
    desc: "操作人",
    customRender: ({ text }) => {
      return allAgentObj.value[text]?.name || text;
    }
  }
]);

const { dataList, loading } = useTable({
  api: getPointRecordApi,
  initParams: {
    userId: props.userId
  },
  dataCallback: res => {
    res.data = res.data.list;
  },
  isPages: false
});
</script>

<template>
  <div>
    <ReTable
      v-loading="loading"
      :dataList="dataList"
      :listHeader="listHeader"
      class="mb-20px"
    />
  </div>
</template>

<style lang="scss" scoped></style>
