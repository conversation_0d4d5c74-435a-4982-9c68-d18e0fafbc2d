<!--
 * @Date         : 2024-08-02 10:38:07
 * @Description  : 积分操作
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { PointOperation, pointOperationList } from "../data";
import { getLabel } from "/@/utils/common";
import { onionIdReg } from "/@/utils/common/pattern";
import {
  pointOperationApi,
  PointOperationReq
} from "/@/api/pointsMall/pointsManage";

interface Props {
  row: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "close"): void;
  (e: "success"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const loading = ref<Boolean>(false);
const ruleFormRef = ref<FormInstance>();
const form = ref<PointOperationReq>({
  userId: props.row.userId,
  changeType: PointOperation.Add,
  transferOnionId: undefined,
  point: undefined,
  reason: ""
});
const pointLabel = computed<string>(
  () => `${getLabel(form.value.changeType, pointOperationList)}积分数量`
);

const rules = ref<FormRules>({
  changeType: [
    {
      required: true,
      message: "请选择操作类型",
      trigger: "change"
    }
  ],
  transferOnionId: [
    {
      required: true,
      message: "请填写要转移的账号洋葱ID",
      trigger: "change"
    },
    {
      pattern: onionIdReg,
      message: "洋葱ID格式不正确",
      trigger: "change"
    }
  ],
  point: [
    {
      required: true,
      message: `请填写积分数量`,
      trigger: "change",
      type: "number"
    }
  ],
  reason: [
    {
      required: true,
      message: "请填写转移原因",
      trigger: "change"
    }
  ]
});

const handleClose = () => {
  emit("close");
};

const getMaxValue = () => {
  if (form.value.changeType === PointOperation.Add) {
    return 99999999;
  }
  return props.row.totalPoint;
};

const changeTypes = () => {
  form.value.point = undefined;
};

const submitForm = () => {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      if (props.row.onionId === form.value.transferOnionId) {
        return ElMessage.warning("不能转移积分给当前账号");
      }
      loading.value = true;
      pointOperationApi(form.value)
        .then(() => {
          ElMessage.success("操作成功");
          emit("success");
          handleClose();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};
</script>

<template>
  <div>
    <el-form
      :model="form"
      label-suffix="："
      ref="ruleFormRef"
      v-loading="loading"
      :rules="rules"
      label-width="180px"
    >
      <el-form-item label="当前账号的洋葱ID">
        {{ props.row.onionId }}
      </el-form-item>
      <el-form-item label="当前积分数量">
        {{ props.row.totalPoint }}
      </el-form-item>
      <el-form-item label="操作类型" prop="changeType">
        <el-radio-group v-model="form.changeType" @change="changeTypes">
          <el-radio
            v-for="item in pointOperationList"
            :key="item.value"
            :label="item.value"
            :disabled="
              props.row.totalPoint < 1 && item.value !== PointOperation.Add
            "
          >
            {{ item.label }}积分
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="转移给（洋葱ID）"
        prop="transferOnionId"
        v-if="form.changeType === PointOperation.transfer"
      >
        <el-input
          v-model="form.transferOnionId"
          placeholder="请填写要转移的账号洋葱ID"
          clearable
        />
      </el-form-item>
      <el-form-item :label="pointLabel" prop="point">
        <el-input-number
          v-model="form.point"
          :step="1"
          :min="1"
          :max="getMaxValue()"
          :precision="0"
          :placeholder="`请填写${pointLabel}`"
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="操作原因" prop="reason">
        <el-input
          v-model="form.reason"
          type="textarea"
          placeholder="请填写转移原因"
          maxlength="200"
          show-word-limit
          clearable
          :rows="3"
          style="width: 350px"
        />
      </el-form-item>
      <div class="el-dialog__footer">
        <el-button
          type="primary"
          @click="submitForm"
          :disabled="loading"
          :loading="loading"
        >
          确定
        </el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </el-form>
  </div>
</template>

<style scoped lang="scss"></style>
