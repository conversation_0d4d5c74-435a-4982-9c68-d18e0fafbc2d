/*
 * @Date         : 2024-09-14 14:21:19
 * @Description  : 审核需求相关数据
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { ref } from "vue";
import { TableColumns } from "/@/components/ReTable/types";
import { getLabel } from "/@/utils/common";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";

const { allAgentObj } = storeToRefs(useUserStore());

const statusList = [
  { label: "待审核", value: "WAIT" },
  { label: "已通过", value: "DONE" },
  { label: "已拒绝", value: "REFUSE" }
];

export const listHeader = ref<TableColumns[]>([
  {
    field: "createdAt",
    desc: "上传时间",
    timeChange: 3
  },
  {
    field: "phone",
    desc: "手机号"
  },
  {
    field: "imageUrl",
    desc: "截图",
    slot: {
      name: "image"
    }
  },
  {
    field: "status",
    desc: "状态",
    customRender: ({ text }) => {
      return getLabel(text, statusList);
    }
  },
  {
    field: "reason",
    desc: "拒绝原因"
  },
  {
    field: "updatedAt",
    desc: "审核时间",
    timeChange: 3
  },
  {
    field: "operatorId",
    desc: "审核人",
    customRender: ({ text }) => {
      return allAgentObj.value?.[text]?.name;
    }
  }
]);
