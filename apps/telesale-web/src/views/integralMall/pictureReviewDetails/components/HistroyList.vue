<!--
 * @Date         : 2024-09-14 14:13:24
 * @Description  : 历史审核列表
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<!--
 * @Date         : 2024-07-17 17:23:22
 * @Description  : 测试环境工具，新增商品
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup name="UtilsGoods">
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { listHeader } from "../data/index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { getListReview } from "/@/api/integralMall";
import { getPic } from "/@/api/order";

const props = defineProps<{
  userId: string;
}>();

const { dataList, loading } = useTable({
  api: getListReview,
  isPages: false,
  initParams: {
    pageIndex: 1,
    pageSize: 1000,
    userId: props.userId
  },
  dataCallback: async res => {
    const list = res.data.list.sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
    res.data = list;
  }
});

const loadError = (file: string, index: number) => {
  getPic(file).then(({ data }) => {
    dataList.value[index].imageUrl = data;
  });
};
</script>

<template>
  <div v-loading="loading">
    <ReTable
      v-if="!deviceDetection()"
      ref="tableRefs"
      :dataList="dataList"
      :listHeader="listHeader"
    >
      <template #image="{ row, index }">
        <el-image
          lazy
          :src="row.imageUrl"
          style="width: 100px; height: 100px"
          :preview-src-list="[row.imageUrl]"
          :preview-teleported="true"
          @error.once="loadError(row.item?.[0]?.image, index)"
        />
      </template>
    </ReTable>
    <ReCardList
      v-else
      ref="cardRefs"
      :dataList="dataList"
      :listHeader="listHeader"
    />
  </div>
</template>

<style lang="scss" scoped></style>
