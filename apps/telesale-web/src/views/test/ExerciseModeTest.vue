<!--
 * @Date         : 2025-01-27 18:30:00
 * @Description  : 练习模式选择测试页面
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="test-container">
    <div class="test-header">
      <h1>AI练习模式选择测试</h1>
      <p>测试模式选择弹窗的单选功能</p>
    </div>

    <div class="test-content">
      <div class="test-section">
        <h2>当前状态</h2>
        <div class="status-info">
          <p><strong>选中的模式：</strong> {{ currentMode }}</p>
          <p><strong>模式描述：</strong> {{ getModeDescription(currentMode) }}</p>
        </div>
      </div>

      <div class="test-section">
        <h2>操作测试</h2>
        <el-button type="primary" @click="openModeDialog">
          打开模式选择弹窗
        </el-button>
      </div>

      <div class="test-section">
        <h2>测试结果</h2>
        <div class="test-results">
          <div v-for="(result, index) in testResults" :key="index" class="test-result-item">
            <span class="timestamp">{{ result.timestamp }}</span>
            <span class="action">{{ result.action }}</span>
            <span class="mode">{{ result.mode }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 模式选择弹窗 -->
    <ExerciseModeDialog
      v-model:visible="modeDialogVisible"
      @confirm="handleModeConfirm"
      @close="handleModeDialogClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import ExerciseModeDialog from "../aiQualityInspection/myTask/components/ExerciseModeDialog.vue";
import { ExerciseMode } from "../aiQualityInspection/myTask/exercise/store";

// 状态变量
const modeDialogVisible = ref(false);
const currentMode = ref<ExerciseMode>(ExerciseMode.VOICE_TEXT);
const testResults = ref<Array<{
  timestamp: string;
  action: string;
  mode: string;
}>>([]);

/**
 * 获取模式描述
 */
const getModeDescription = (mode: ExerciseMode) => {
  switch (mode) {
    case ExerciseMode.VOICE_TEXT:
      return "语音+文本对练 - 完整的交互体验，支持语音合成和文本显示";
    case ExerciseMode.TEXT_ONLY:
      return "纯文本对练 - 高效的文本交互，取消语音合成，提高对练效率";
    default:
      return "未知模式";
  }
};

/**
 * 添加测试结果
 */
const addTestResult = (action: string, mode: ExerciseMode) => {
  const timestamp = new Date().toLocaleTimeString();
  testResults.value.unshift({
    timestamp,
    action,
    mode: getModeDescription(mode)
  });
  
  // 只保留最近10条记录
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10);
  }
};

/**
 * 打开模式选择弹窗
 */
const openModeDialog = () => {
  modeDialogVisible.value = true;
  addTestResult("打开弹窗", currentMode.value);
};

/**
 * 处理模式选择确认
 */
const handleModeConfirm = (mode: ExerciseMode) => {
  currentMode.value = mode;
  addTestResult("确认选择", mode);
  console.log("选择的模式:", mode);
};

/**
 * 处理模式选择弹窗关闭
 */
const handleModeDialogClose = () => {
  addTestResult("取消选择", currentMode.value);
  console.log("取消模式选择");
};
</script>

<style scoped>
.test-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.test-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.test-header h1 {
  color: #1f2937;
  margin-bottom: 8px;
}

.test-header p {
  color: #6b7280;
  margin: 0;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.test-section {
  background: #f9fafb;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.test-section h2 {
  color: #374151;
  margin: 0 0 16px 0;
  font-size: 18px;
}

.status-info {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
}

.status-info p {
  margin: 8px 0;
  color: #374151;
}

.test-results {
  background: white;
  border-radius: 6px;
  border: 1px solid #d1d5db;
  max-height: 300px;
  overflow-y: auto;
}

.test-result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  gap: 12px;
}

.test-result-item:last-child {
  border-bottom: none;
}

.timestamp {
  color: #6b7280;
  font-size: 12px;
  min-width: 80px;
}

.action {
  color: #1f2937;
  font-weight: 500;
  min-width: 80px;
}

.mode {
  color: #374151;
  flex: 1;
  font-size: 14px;
}
</style>
