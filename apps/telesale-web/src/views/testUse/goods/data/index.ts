/*
 * @Date         : 2024-07-17 17:32:50
 * @Description  : 商品相关数据
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { ref } from "vue";
import { TableColumns } from "/@/components/ReTable/types";
import { getLabel } from "/@/utils/common";

const categoryList = [
  {
    label: "大会员",
    value: "big_vip"
  },
  {
    label: "正价课",
    value: "formal"
  },
  {
    label: "非正价课",
    value: "informal"
  }
];

const isPadList = [
  {
    label: "是",
    value: true
  },
  {
    label: "否",
    value: false
  }
];

export const listHeader = ref<TableColumns[]>([
  {
    field: "goodsID",
    desc: "商品ID",
    isCopy: true
  },
  {
    field: "remark",
    desc: "商品分类名称"
  },
  {
    field: "name",
    desc: "商品名称"
  },
  {
    field: "amount",
    desc: "商品价格"
  },
  {
    field: "category",
    desc: "商品分类",
    filterOptions: {
      columns: categoryList
    },
    customRender: ({ text }) => {
      return getLabel(text, categoryList);
    }
  },
  {
    field: "isPad",
    desc: "是否包含平板",
    filterOptions: {
      columns: isPadList
    },
    customRender: ({ text }) => {
      return getLabel(text, isPadList);
    }
  },
  {
    field: "groups",
    desc: "商品分组",
    customRender: ({ text }) => {
      return text?.join?.("-");
    }
  }
]);
