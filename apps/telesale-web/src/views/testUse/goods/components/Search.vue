<!--
 * @Date         : 2024-07-17 17:29:45
 * @Description  : 查询组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { computed, ref } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { GoodsReq } from "/@/api/testUes/goods";

interface Emits {
  (e: "onSearch"): void;
  (e: "update:form", val: GoodsReq): void;
}

const props = defineProps<{
  form: GoodsReq;
}>();

const emit = defineEmits<Emits>();

function onSearch() {
  emit("onSearch");
}

const form = computed({
  get() {
    return props.form;
  },
  set(val: GoodsReq) {
    emit("update:form", val);
  }
});
</script>
<template>
  <el-form
    ref="formRef"
    :inline="true"
    :model="form"
    class="clearfix"
    @submit.prevent
  >
    <el-form-item prop="remark">
      <el-input
        v-model="form.remark"
        placeholder="请输入商品分类名称"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="商品ID">
      <el-input
        v-model="form.goodsID"
        placeholder="请输入商品ID"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="name">
      <el-input
        v-model="form.name"
        placeholder="请输入商品名称"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
    </el-form-item>
  </el-form>
</template>
