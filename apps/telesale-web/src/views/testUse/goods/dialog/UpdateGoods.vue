<!--
 * @Date         : 2024-07-18 16:37:46
 * @Description  : 新增编辑视频
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import {
  addGood<PERSON><PERSON>,
  getGoodInfoApi,
  updateGood<PERSON>pi
} from "/@/api/testUes/goods";

interface Props {
  value: boolean;
  id?: number;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance | null>();
const form = ref({
  id: undefined,
  goodsId: "",
  remark: ""
});
const rules: FormRules = {
  goodsId: [
    {
      required: true,
      message: "请输入商品商ID",
      trigger: "change"
    }
  ],
  remark: [
    {
      required: true,
      message: "请输入商品分类名称",
      trigger: "change"
    }
  ]
};
function handleClose() {
  isModel.value = false;
}

const getInfo = () => {
  loading.value = true;
  getGoodInfoApi({ id: props.id })
    .then(res => {
      console.log("res", res);
      form.value.id = res.data.id;
      form.value.goodsId = res.data.goodsID;
      form.value.remark = res.data.remark;
    })
    .finally(() => {
      loading.value = false;
    });
};

const submit = async () => {
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      const fn = form.value.id ? updateGoodApi : addGoodApi;
      fn(form.value)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          handleClose();
          emit("onSearch");
        })
        .catch(err => {
          loading.value = false;
        });
    }
  });
};

props.id && getInfo();
</script>

<template>
  <el-dialog
    :title="props.id ? '编辑商品' : '新增商品'"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
  >
    <div v-loading="loading" class="flex justify-center">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="商品ID" prop="goodsId">
          <el-input
            v-model.trim="form.goodsId"
            placeholder="请输入商品商D"
            :disabled="!!props.id"
          />
        </el-form-item>
        <el-form-item label="商品分类名称" prop="remark">
          <el-input
            v-model.trim="form.remark"
            placeholder="请输入商品分类名称"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
