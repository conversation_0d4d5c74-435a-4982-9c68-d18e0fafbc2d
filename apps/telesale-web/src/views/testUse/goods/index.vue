<!--
 * @Date         : 2024-07-17 17:23:22
 * @Description  : 测试环境工具，新增商品
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup name="UtilsGoods">
import { ref } from "vue";
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { listHeader } from "./data";
import { OperationObj } from "/@/components/ReTable/types";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import Search from "./components/Search.vue";
import UpdateGoods from "./dialog/UpdateGoods.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { delGoodApi, getGoodListApi } from "/@/api/testUes/goods";

const rowId = ref<number>();
const isModal = ref<boolean>(false);
const { dataList, Pagination, onSearch, searchForm, loading } = useTable({
  api: getGoodListApi,
  dataCallback(res) {
    res.data.list = res.data.goods;
  }
});

const operation: OperationObj[] = [
  {
    text: "编辑",
    eventFn: row => {
      isModal.value = true;
      rowId.value = row.id;
    }
  },
  {
    text: "删除",
    eventFn: row => {
      ElMessageBox.confirm("确定是否要删除此商品?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        loading.value = true;
        delGoodApi({ id: row.id })
          .then(() => {
            onSearch();
            ElMessage.success("删除成功");
          })
          .finally(() => {
            loading.value = false;
          });
      });
    }
  }
];

const filterHeadData = (data: any) => {
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      searchForm.value[key] = data[key];
    }
  }
  onSearch();
};

const addGoods = () => {
  isModal.value = true;
  rowId.value = undefined;
};
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <div class="flex justify-between">
        <Search ref="formRefs" v-model:form="searchForm" @onSearch="onSearch" />
        <el-button type="primary" @click="addGoods()">新增</el-button>
      </div>
      <div class="g-table-box">
        <ReTable
          v-if="!deviceDetection()"
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
          :width-operation="130"
          @filter-head-data="filterHeadData"
        />
        <ReCardList
          v-else
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
        />
      </div>
      <Pagination />
      <UpdateGoods
        v-if="isModal"
        v-model:value="isModal"
        :id="rowId"
        @onSearch="onSearch"
      />
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
