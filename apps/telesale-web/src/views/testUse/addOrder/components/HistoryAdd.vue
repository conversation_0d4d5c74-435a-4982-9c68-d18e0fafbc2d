<!--
 * @Date         : 2024-07-31 14:29:44
 * @Description  : 历史订单
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { OperationObj, TableColumns } from "/@/components/ReTable/types";
import ReTable from "/@/components/ReTable/index.vue";
import { useStorage } from "@vueuse/core";
import { onActivated, ref } from "vue";
import { createOrderApi } from "/@/api/testUes/addOrder";
import { handelData } from "../data/utils";
import dayjs from "dayjs";
import { cloneDeep } from "lodash";

const dataList = useStorage("test-util-orderList", []);
const loading = ref(false);

const listHeader: TableColumns[] = [
  {
    field: "phone",
    desc: "手机号"
  },
  {
    field: "userId",
    desc: "userId"
  },
  {
    field: "goodId",
    desc: "商品ID"
  },
  {
    field: "orderId",
    desc: "订单号"
  },
  {
    field: "status",
    desc: "订单状态"
  },
  {
    field: "amount",
    desc: "实付金额"
  },
  {
    field: "source",
    desc: "站外订单来源"
  },
  {
    field: "platformOrderNo",
    desc: "第三方平台订单号"
  },
  {
    field: "platform",
    desc: "平台名称"
  },
  {
    field: "worker",
    desc: "终端销售名称"
  },
  {
    field: "paidTime",
    desc: "支付成功时间"
  },
  {
    field: "refundedTime",
    desc: "支付退款时间"
  }
];

const operation: OperationObj[] = [
  {
    text: "一键退款",
    eventFn: row => {
      console.log(row);
      row.refundedTime = dayjs().format("YYYY/MM/DD HH:mm:ss");
      row.status = "退款成功";
      const data = handelData(row);
      loading.value = true;
      createOrderApi(data)
        .then((res: any) => {
          ElMessage.success("退款成功");
          const params = {
            ...cloneDeep(row),
            orderId: res.data._id
          };
          console.log(params);

          dataList.value.unshift(params);
          if (dataList.value.length > 9) {
            dataList.value.length = 10;
          }
        })
        .finally(() => {
          loading.value = false;
        });
    },
    isShow: row => {
      return !row.refundedTime;
    }
  }
];

const getList = () => {
  dataList.value = JSON.parse(localStorage.getItem("test-util-orderList"));
};

defineExpose({
  getList
});
</script>

<template>
  <div class="mb-10px">
    <el-alert
      title="只展示最近十条"
      type="warning"
      effect="light"
      show-icon
      :closable="false"
    />
  </div>
  <div class="g-table-box" v-loading="loading">
    <ReTable
      ref="tableRefs"
      :dataList="dataList"
      :listHeader="listHeader"
      :operation="operation"
    />
  </div>
</template>

<style lang="scss" scoped></style>
