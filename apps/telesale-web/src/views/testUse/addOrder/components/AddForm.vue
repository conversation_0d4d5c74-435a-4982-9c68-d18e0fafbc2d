<!--
 * @Date         : 2024-07-31 14:29:17
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { orderStatusList } from "../data/index";
import dayjs from "dayjs";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import { useStorage } from "@vueuse/core";
import { addOnionUserApi, createOrderApi } from "/@/api/testUes/addOrder";
import { findUser } from "/@/api/customerDetails";
import { cloneDeep } from "lodash-es";
import { getGoodListApi } from "/@/api/testUes/goods";
import { phoneReg } from "/@/utils/common/pattern";
import { handelData } from "../data/utils";

const { userMsg } = storeToRefs(useUserStore());
const orderList = useStorage("test-util-orderList", []);
const formRef = ref<FormInstance>();
const loading = ref<boolean>(false);
const isRandom = ref<boolean>(true);
const restaurants = ref([]);
const goodsList = ref([]);
const form = ref({
  addType: "phone",
  phone: "",
  userId: "",
  goodId: "",
  status: "支付成功",
  amount: 1000,
  source: "123",
  platformOrderNo: "",
  platform: "telesale",
  worker: "mall_telesale_yangshili_" + userMsg.value.id,
  paidTime: dayjs(new Date()).format("YYYY/MM/DD HH:mm:ss"),
  refundedTime: undefined,
  tiyanjibucha: false
});

const addTypeList = [
  {
    label: "手机号",
    value: "phone"
  },
  {
    label: "userId",
    value: "userId"
  }
];

const rules: FormRules = {
  addType: [{ required: true, message: "请选择新增方式", trigger: "blur" }],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    { pattern: phoneReg, message: "请输入正确的手机号", trigger: "blur" }
  ],
  userId: [{ required: true, message: "请输入userId", trigger: "blur" }],
  goodId: [{ required: true, message: "请输入商品ID", trigger: "blur" }],
  status: [{ required: true, message: "请输入订单状态", trigger: "blur" }],
  amount: [{ required: true, message: "请输入实付金额", trigger: "blur" }],
  source: [{ required: true, message: "请输入站外订单来源", trigger: "blur" }],
  platformOrderNo: [
    { required: true, message: "请输入第三方平台订单号", trigger: "blur" }
  ],
  platform: [{ required: true, message: "请输入平台名称", trigger: "blur" }],
  worker: [{ required: true, message: "请输入终端销售名称", trigger: "blur" }],
  paidTime: [{ required: true, message: "请输入支付成功时间", trigger: "blur" }]
};

const randomStr = (length: number) => {
  // 随机生成22位随机字符串
  const str = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
  var result = "";
  for (var i = length; i > 0; --i) {
    result += str[Math.floor(Math.random() * str.length)];
  }
  form.value.platformOrderNo = result;
};

randomStr(22);

const changeRandom = () => {
  if (isRandom.value) {
    randomStr(22);
  } else {
    form.value.platformOrderNo = "";
  }
};

const getGoodsList = () => {
  getGoodListApi({ pageIndex: 1, pgaeSize: 100 }).then(res => {
    console.log("res", res);
    restaurants.value = res.data.goods.map(item => {
      return {
        value: item.goodsID,
        link: item.remark
      };
    });
    goodsList.value = cloneDeep(restaurants.value);
  });
};
getGoodsList();

const querySearch = (queryString: string) => {
  console.log(queryString);

  const data = cloneDeep(restaurants.value);
  goodsList.value = data.filter(item => {
    if (
      item.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0 ||
      item.link.toLowerCase().indexOf(queryString.toLowerCase()) === 0
    ) {
      return true;
    } else {
      return false;
    }
  });
};

const createFilter = (queryString: string) => {
  return restaurant => {
    return (
      restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
    );
  };
};

const createUser = async (phone: string) => {
  try {
    const { data }: { data: any } = await findUser({
      key: "phone",
      value: phone
    });
    if (data.id) {
      form.value.userId = data.id;
      return;
    }
  } catch (err) {
    console.log("err", err.response.data);
    ElMessage.success("正在创建用户...");
    const res = await addOnionUserApi({
      phone: phone,
      type: "signup",
      registEntranceId: "21",
      os: "pc",
      role: "student",
      realIdentity: "student"
    });
    form.value.userId = res.data.id;
    ElMessage.success("创建用户成功");
  }
};

const onSubmit = () => {
  formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      loading.value = true;
      if (form.value.addType === "phone") {
        try {
          await createUser(form.value.phone);
        } catch (error) {
          loading.value = false;
          throw new Error(error);
        }
      }
      const data = handelData(form.value);

      createOrderApi(data)
        .then((res: any) => {
          ElMessage.success("创建订单成功");

          const data = {
            ...cloneDeep(form.value),
            orderId: res.data._id
          };
          orderList.value.unshift(data);

          if (isRandom.value) {
            randomStr(22);
          }
          if (orderList.value.length > 9) {
            orderList.value.length = 10;
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};
</script>

<template>
  <div v-loading="loading">
    <el-form
      :model="form"
      ref="formRef"
      :rules="rules"
      label-width="180px"
      style="width: 100%"
      label-suffix=":"
      inline
    >
      <el-form-item label="新增方式" prop="addType">
        <el-select v-model="form.addType" placeholder="请选择新增方式">
          <el-option
            v-for="item in addTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号" prop="phone" v-if="form.addType === 'phone'">
        <el-input v-model="form.phone" />
      </el-form-item>
      <el-form-item
        label="userId"
        prop="userId"
        v-if="form.addType === 'userId'"
      >
        <el-input v-model="form.userId" />
      </el-form-item>
      <el-form-item label="商品ID" prop="goodId">
        <!-- <el-autocomplete
          v-model="form.goodId"
          :fetch-suggestions="querySearch"
          clearable
          class="w-100%"
        >
          <template #default="{ item }">
            <div class="my-10px">
              <div class="line-height-normal">商品id: {{ item.value }}</div>
              <div class="line-height-normal">分类名称：{{ item.link }}</div>
            </div>
          </template>
        </el-autocomplete> -->
        <el-select
          v-model="form.goodId"
          clearable
          filterable
          value-key="id"
          remote
          :remote-method="querySearch"
          remote-show-suffix
          class="goods-select"
          :teleported="false"
        >
          <el-option
            v-for="item in goodsList"
            :key="item.value"
            :value="item.value"
          >
            <template #default>
              <div class="my-10px">
                <div class="line-height-normal">商品id: {{ item.value }}</div>
                <div class="line-height-normal">分类名称：{{ item.link }}</div>
              </div>
            </template>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select v-model="form.status" placeholder="请选择新增方式">
          <el-option
            v-for="item in orderStatusList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="实付金额" prop="amount">
        <el-input-number v-model="form.amount" style="width: 100%" />
      </el-form-item>
      <el-form-item label="站外订单来源" prop="source">
        <el-input v-model="form.source" />
      </el-form-item>
      <el-form-item label="第三方平台订单号" prop="platformOrderNo">
        <el-input v-model="form.platformOrderNo">
          <template #append>
            <div class="cursor-pointer c-blue" @click="randomStr(22)">
              <el-checkbox
                v-model="isRandom"
                label=""
                :indeterminate="false"
                @change="changeRandom"
              >
                随机生成
              </el-checkbox>
            </div>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="平台名称" prop="platform">
        <el-input v-model="form.platform" />
      </el-form-item>
      <el-form-item label="终端销售名称" prop="worker">
        <el-input v-model="form.worker" />
      </el-form-item>
      <el-form-item label="支付成功时间" prop="paidTime">
        <el-date-picker
          v-model="form.paidTime"
          type="datetime"
          placeholder="请选择支付成功时间"
          value-format="YYYY/MM/DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="支付退款时间" prop="refundedTime">
        <el-date-picker
          v-model="form.refundedTime"
          type="datetime"
          placeholder="请选择支付退款时间"
          value-format="YYYY/MM/DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="是否体验机补差" prop="tiyanjibucha">
        <el-switch v-model="form.tiyanjibucha" />
      </el-form-item>
      <el-form-item label="" style="width: 100%">
        <el-button class="ml-180px" type="primary" @click="onSubmit">
          立即创建
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item) {
  width: 44%;
  .el-input {
    width: 100%;
  }
}
.goods-select {
  width: 100%;
  :deep(.el-select-dropdown__item) {
    height: 60px;
  }
}
</style>
