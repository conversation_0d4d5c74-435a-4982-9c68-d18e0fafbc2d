<!--
 * @Date         : 2024-07-17 17:22:02
 * @Description  : 测试环境工具，创建订单
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup name="UtilsAddOrder">
import { ref } from "vue";
import AddForm from "./components/AddForm.vue";
import HistoryAdd from "./components/HistoryAdd.vue";

const activeName = ref("add");

const historyRefs = ref<InstanceType<typeof HistoryAdd>>();

const tabChange = (e: string) => {
  if (e === "history") {
    historyRefs.value.getList();
  }
};
</script>

<template>
  <div class="g-margin-20">
    <el-card>
      <el-tabs v-model="activeName" @tab-change="tabChange">
        <el-tab-pane label="新增订单" name="add"> <AddForm /></el-tab-pane>
        <el-tab-pane label="历史新增" name="history">
          <HistoryAdd ref="historyRefs" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
