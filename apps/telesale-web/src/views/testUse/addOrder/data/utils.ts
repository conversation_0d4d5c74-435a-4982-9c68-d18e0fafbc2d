/*
 * @Date         : 2025-03-18 12:08:31
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

export const handelData = form => {
  const data = {
    userId: form.userId,
    goodId: form.goodId,
    status: form.status,
    paymentCredentials: {
      id: form.platformOrderNo,
      amount: form.amount,
      source: form.source
    },
    creationWay: {
      bySelf: "",
      productId: ""
    },
    paidTime: form.paidTime,
    refundedTime: form.refundedTime || "",
    updatedBy: "",
    extra: {
      relevance: form.tiyanjibucha ? "tiyanjibucha" : "",
      sellfrom: form.platform,
      ycfrom: form.worker
    }
  };

  return data;
};
