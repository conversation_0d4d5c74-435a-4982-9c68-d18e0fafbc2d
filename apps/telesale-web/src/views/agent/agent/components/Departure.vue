<!--
 * @Date         : 2025-03-31 11:45:50
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { computed, ref, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { setDeparture } from "/@/api/agent";
import { task } from "/@/api/user";
import { useUserStoreHook } from "/@/store/modules/user";
import findAgentMath from "/@/utils/asyn/findAgent";
import findAllAgentMath from "/@/utils/asyn/findAllAgent";
import LeaveSet from "../dialog/LeaveSet.vue";

interface Props {
  value: boolean;
  dataMemory: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "getList"): void;
}

const emits = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emits("update:value", val);
  }
});
const loading = ref(false);
const isModelLeave = ref(false);
const form = ref({
  type: undefined
});

function handleClose() {
  isModel.value = false;
}

const next = () => {
  isModelLeave.value = true;
  // handleClose();
};

function save(type) {
  let params = {
    workerId: props.dataMemory.id,
    all: 0,
    intention: {},
    orgId: undefined
  };

  loading.value = true;
  setDeparture(params)
    .then(({ data }: { data: any }) => {
      timeOut(data);
    })
    .catch(() => {
      loading.value = false;
    });
}

const submit = () => {
  let params = {
    workerId: props.dataMemory.id,
    skipTransfer: true
  };
  loading.value = true;
  setDeparture(params)
    .then(({ data }: { data: any }) => {
      timeOut(data);
    })
    .catch(() => {
      loading.value = false;
    });
};

function find(id) {
  task({ id })
    .then(async (res: any) => {
      if (res.status === 200) {
        clearTimeout(time.value);
        ElMessage.success("坐席已离职，其名下线索已释放");
        loading.value = false;
        useUserStoreHook().setAgentList(await findAgentMath());
        useUserStoreHook().setAllAgentObj(await findAllAgentMath());
        getList();
      } else {
        timeOut(id);
      }
    })
    .catch(() => {
      loading.value = false;
      clearTimeout(time.value);
    });
}

const time = ref();
function timeOut(id) {
  time.value = setTimeout(() => {
    find(id);
  }, 5000);
}

const getList = () => {
  handleClose();
  emits("getList");
};

onUnmounted(() => {
  clearTimeout(time.value);
});
</script>

<template>
  <el-dialog
    :title="'成员离职 - ' + dataMemory.name"
    v-model="isModel"
    :before-close="handleClose"
    append-to-body
    width="700px"
  >
    <div class="w-100% flex justify-center" v-loading="loading">
      <el-radio-group v-model="form.type" class="flex-col items-start!">
        <el-radio :label="1">直接离职，释放离职坐席名下所有线索</el-radio>
        <el-radio :label="2">先转线索，后离职</el-radio>
      </el-radio-group>
    </div>
    <LeaveSet
      v-model:value="isModelLeave"
      :dataMemory="dataMemory"
      @getList="getList"
      v-if="isModelLeave"
    />

    <template #footer>
      <el-button
        v-if="form.type === 1"
        type="primary"
        @click="submit"
        :loading="loading"
      >
        确定
      </el-button>
      <el-button v-if="form.type === 2" type="primary" @click="next">
        下一步
      </el-button>
      <el-button :disabled="loading" @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
<style scoped lang="scss">
.d-no-data {
  text-align: center;
  height: 70px;
  margin-top: 50px;
}

:deep(.el-tabs__content) {
  padding: 5px;
}

:deep(.el-table) {
  .el-table__cell .cell {
    text-align: left;
  }
}

.el-form-item {
  margin-bottom: 0;
}
</style>
