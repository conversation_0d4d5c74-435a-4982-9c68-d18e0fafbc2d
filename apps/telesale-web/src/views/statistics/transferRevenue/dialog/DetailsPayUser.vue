<!--
 * @Date         : 2025-03-06 15:58:46
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { computed } from "vue";
import ReTable from "/@/components/ReTable/index.vue";
import { useTable } from "/@/hooks/useTable";
import { getPayUserApi } from "/@/api/statistics/transferRevenue";
import paramsHandle from "/@/utils/handle/paramsHandle";
import dayjs from "dayjs";

interface Props {
  value: boolean;
  msg: any;
  time: any;
  platformId: number;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const listHeader = [
  { field: "phone", desc: "购课用户手机号" },
  { field: "onionId", desc: "购课用户洋葱ID" },
  { field: "amount", desc: "订单金额" },
  { field: "payTime", desc: "订单成交时间", timeChange: 3 }
];

const { dataList, Pagination, loading } = useTable({
  api: getPayUserApi,
  initParams: {
    userId: props.msg?.userId,
    workerId: props.msg?.workerId,
    startTime: dayjs(props.time[0]).toISOString(),
    endTime: dayjs(props.time[1]).toISOString(),
    platformId: props.platformId
  },
  isPages: false,
  dataCallback(res) {
    res.data = res.data.list;
  }
});
</script>

<template>
  <el-dialog
    :title="'订单详情 - ' + (msg?.phone || msg?.onionId)"
    v-model="isModel"
    :destroy-on-close="true"
  >
    <div v-loading="loading" class="g-pad-b-20">
      <div class="g-table-box">
        <ReTable
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="listHeader"
        />
      </div>
      <Pagination />
    </div>
  </el-dialog>
</template>
