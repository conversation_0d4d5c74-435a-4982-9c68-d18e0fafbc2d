<!--
 * @Date         : 2025-03-04 17:10:31
 * @Description  :

 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { rankPersonRate } from "/@/api/statistics_rank";
import paramsHandle from "/@/utils/handle/paramsHandle";
import RankTable from "/@/components/RankTable/rankV2.vue";
import nameLength from "../utils/nameLength";
import { isEqual } from "lodash-es";

const props = defineProps<{
  orgIds: any[];
  pageName: string;
  tabName: string;
  ruleList: any[];
}>();

const tableHeader = [
  { field: "name", desc: "坐席名称" },
  { field: "amount", desc: "当配营收" },
  { field: "reward", desc: "奖励/元" },
  { field: "groupName", desc: "所属小组" },
  { field: "teamName", desc: "所属团队" }
];

const loading = ref(false);
const tableData = ref([]);
let now = new Date();
let month = now.getMonth() + 1;
const limit = ref(30);

//form查询
const form = reactive({
  type: "month",
  week: now,
  month: now.getFullYear() + "-" + (month > 9 ? month : "0" + month),
  limit: 500
});

function changeVal(val) {
  form.limit = val === "month" ? 500 : 100;
}

function getList() {
  if (loading.value) return;
  loading.value = true;
  let params: any = {
    time: [],
    pageName: props.pageName,
    tabName: props.tabName
  };
  if (form.type === "month") {
    let arr = form.month.split("-");
    params.time = [
      new Date(form.month.replace("-", "/") + "/1").getTime(),
      new Date(Number(arr[0]), Number(arr[1]), 1).getTime() - 1
    ];
  } else {
    let year = form.week.getFullYear();
    let month = form.week.getMonth() + 1;
    let day = form.week.getDate();
    let now = new Date(year + "/" + month + "/" + day).getTime();
    let week = form.week.getDay();
    week === 0 && (week = 7);
    let first = now - (week - 1) * 24 * 60 * 60 * 1000;
    params.time = [first, first + 7 * 24 * 60 * 60 * 1000 - 1];
  }
  form.limit && (params.limit = form.limit);
  rankPersonRate(paramsHandle(params, { newTime: true }))
    .then(({ data }: { data: any }) => {
      let rank = -1, //排名数字
        i = 0, //实际位置
        j = 1, //同一名次并列的人数
        list = [];
      while (rank < limit.value) {
        let item: any = {
          name: nameLength(data.list[i]?.name) || "",
          amount: data.list[i] ? data.list[i].amount : "",
          reward: data.list[i] ? data.list[i].reward : "",
          groupName: data.list[i]?.group?.name || "",
          teamName: data.list[i]?.team?.name || ""
        };
        if (!data.list[i] || !data.list[i - 1]) {
          rank += j;
          j = 1;
        } else {
          if (data.list[i].amount !== data.list[i - 1].amount) {
            rank += j;
            j = 1;
          } else {
            j++;
          }
        }
        item.rank = rank;
        rank < limit.value && list.push(item);
        i++;
      }

      tableData.value = list;
      loading.value = false;
    })
    .catch(err => {
      tableData.value = [];
      loading.value = false;
    });
}

watch(
  () => props.orgIds,
  (n, old) => {
    if (!isEqual(n, old)) {
      getList();
    }
  }
);

watch(
  () => props.ruleList,
  (n, old) => {
    if (!isEqual(n, old)) {
      getList();
    }
  }
);

onMounted(() => {
  getList();
});
</script>

<template>
  <div v-loading="loading">
    <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
      <el-form-item>
        <el-select v-model="form.type" @change="changeVal">
          <el-option label="月份" value="month" />
          <el-option label="周" value="week" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker
          v-model="form.month"
          type="month"
          placeholder="选择月"
          v-if="form.type === 'month'"
          value-format="YYYY-MM"
          :clearable="false"
        />
        <el-date-picker
          v-else
          v-model="form.week"
          type="week"
          format="YYYY [第] ww [周]"
          placeholder="选择周"
          :clearable="false"
        />
      </el-form-item>
      <el-form-item prop="limit">
        <el-select
          v-model="form.limit"
          @keyup.enter="getList"
          placeholder="请选择领取条数"
        >
          <el-option
            v-show="form.type === 'month'"
            label="领取条数大于500条"
            :value="500"
          />
          <el-option
            v-show="form.type === 'week'"
            label="领取条数大于100条"
            :value="100"
          />
          <el-option label="领取条数无限制" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          @click="getList"
        >
          搜索
        </el-button>
      </el-form-item>
    </el-form>
    <!--    name="当配转化率排行榜" -->
    <RankTable
      :tableData="tableData"
      :tableHeader="tableHeader"
      :limit="limit"
      :isRank="true"
      revenueField="amount"
    />
  </div>
</template>
<style scoped lang="scss">
:deep(.el-form--small .el-form-item .el-select .el-input) {
  width: 150px;
}
</style>
