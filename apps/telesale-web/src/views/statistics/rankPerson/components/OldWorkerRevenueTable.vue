<!--
 * @Date         : 2025-03-21 16:27:58
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import RankTable from "/@/components/RankTable/rankV2.vue";
import nameLength from "../utils/nameLength";
import {
  getOldWokerRevenueApi,
  getOldWokerRevenueFileApi
} from "/@/api/statistics/rank";
import { TableColumns } from "/@/components/ReTable/types";
import downloadFile from "/@/utils/handle/downloadFile";
import dayjs from "dayjs";
import { isEqual } from "lodash-es";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";

const props = defineProps<{
  city: number;
  orgList: any[];
}>();

const tableHeader: TableColumns[] = [
  { field: "name", desc: "坐席名称" },
  { field: "amountPerMonth", desc: "本月业绩/元" },
  { field: "orderNum", desc: "订单量" },
  { field: "avgPrice", desc: "均客单价/元" },
  { field: "rangeNum", desc: "排名所在区间" },
  { field: "baseSalary", desc: "底薪" },
  { field: "groupName", desc: "所属小组", minWidth: 400 },
  { field: "teamName", desc: "所属团队" }
];

const loading = ref(false);
const tableData = ref([]);
const rankNum = ref<number>(0);
const { userMsg } = storeToRefs(useUserStore());
const topData = ref();
//form查询
const form = reactive({
  time: dayjs().format("YYYY-MM"),
  areaType: props.city
});

function getList() {
  loading.value = true;
  const params = getParams();
  getOldWokerRevenueApi(params)
    .then(({ data }) => {
      const myRank = data.rankAchievementList?.find((item, index) => {
        return item.workerid === userMsg.value.id;
      });

      if (myRank) {
        topData.value = {
          ...myRank,
          name: myRank ? nameLength(myRank.worker) : "",
          amountPerMonth: myRank?.amount ?? "",
          avgPrice: myRank?.avgPrice ?? "",
          orderNum: myRank?.orderNum ?? "",
          groupName: myRank.group?.name ?? "",
          teamName: myRank.team?.name ?? "",
          rank: myRank.rankIndex - 1
        };
      } else {
        topData.value = undefined;
      }

      rankNum.value = data.rankAchievementList.length;
      let lenLimit = data.rankAchievementList.length || 20;

      let rank = -1, //排名数字
        i = 0, //实际位置
        j = 1, //同一名次并列的人数
        list = [];
      while (rank < lenLimit) {
        let rankAchievementList = data.rankAchievementList[i] || {};
        let item: any = {
          ...rankAchievementList,
          name: rankAchievementList
            ? nameLength(rankAchievementList.worker)
            : "",
          amountPerMonth: rankAchievementList?.amount ?? "",
          avgPrice: rankAchievementList?.avgPrice ?? "",
          orderNum: rankAchievementList?.orderNum ?? "",
          groupName: rankAchievementList.group?.name ?? "",
          teamName: rankAchievementList.team?.name ?? ""
        };
        if (!rankAchievementList || !data.rankAchievementList[i - 1]) {
          rank += j;
          j = 1;
        } else {
          if (
            rankAchievementList.amount !==
            data.rankAchievementList[i - 1].amount
          ) {
            rank += j;
            j = 1;
          } else {
            j++;
          }
        }
        item.rank = rank;
        rank < lenLimit && list.push(item);
        i++;
      }
      tableData.value = list;
      loading.value = false;
    })
    .catch(() => {
      tableData.value = [];
      loading.value = false;
    });
}

const getParams = () => {
  const params = {
    start: 0,
    end: 0,
    areaType: props.city,
    orgIds: props.orgList || []
  };
  if (form.time) {
    params.start = dayjs(form.time).startOf("month").unix();
    params.end = dayjs(form.time).endOf("month").unix();
  }
  return params;
};

const exportFile = () => {
  const params = getParams();
  getOldWokerRevenueFileApi(params).then(res => {
    downloadFile(res);
  });
};

watch(
  () => props.orgList,
  (n, old) => {
    if (!isEqual(n, old)) {
      getList();
    }
  }
);

onMounted(() => {
  getList();
});
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <div class="flex justify-between">
      <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
        <el-form-item prop="time">
          <el-date-picker
            v-model="form.time"
            type="month"
            placeholder="选择月"
            value-format="YYYY-MM"
            :clearable="false"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :icon="useRenderIcon('search')"
            @click="getList"
          >
            搜索
          </el-button>
        </el-form-item>
        <el-form-item>
          <span class="c-red">共计{{ rankNum || 0 }}人参与排行</span>
        </el-form-item>
      </el-form>
      <el-button
        type="primary"
        @click="exportFile"
        v-auth="
          props.city === 1
            ? 'telesale_admin_rank_person_old_revence_export'
            : 'telesale_admin_rank_person_new_revence_export'
        "
      >
        导出
      </el-button>
    </div>
    <RankTable
      :tableData="tableData"
      :tableHeader="tableHeader"
      :isRank="true"
      :limit="20"
      :fixed-top-data="topData"
      revenueField="amountPerMonth"
    />
  </div>
</template>
