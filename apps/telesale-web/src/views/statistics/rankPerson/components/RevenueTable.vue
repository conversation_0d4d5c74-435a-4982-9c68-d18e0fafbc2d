<!--
 * @Date         : 2025-03-04 17:08:22
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useUserStore, useUserStoreHook } from "/@/store/modules/user";
import { rankPersonRevenue } from "/@/api/statistics_rank";
import paramsHandle from "/@/utils/handle/paramsHandle";
import originDate from "/@/utils/handle/originDate";
import customerTypeList from "/@/utils/data/customerTypeList";
import RankTable from "/@/components/RankTable/rankV2.vue";
import nameLength from "../utils/nameLength";
import { isEqual } from "lodash-es";
import { storeToRefs } from "pinia";

const tableHeader = [
  { field: "name", desc: "坐席名称" },
  { field: "amountPerMonth", desc: "本月业绩/元" },
  { field: "orderNum", desc: "订单量" },
  { field: "avgPrice", desc: "均客单价/元" },
  { field: "groupName", desc: "所属小组" },
  { field: "teamName", desc: "所属团队" }
];

const props = defineProps<{
  orgIds: any[];
}>();

const loading = ref(true);
const tableData = ref([]);
const { userMsg } = storeToRefs(useUserStore());
const topData = ref();
const limit = ref(20);
let revenceLimit =
  useUserStoreHook().authorizationMap.indexOf(
    "telesale_admin_rank_person_revence"
  ) > -1;

//form查询
const form = reactive({
  time: originDate(),
  orderType: undefined
});

function getList() {
  loading.value = true;
  rankPersonRevenue(paramsHandle(form, { time: true, minus: ["orderType"] }))
    .then(({ data }: { data: any }) => {
      data.amountPerMonth = data.amountPerMonth.filter((item: any) => {
        const teamIds =
          item.microGroup?.path?.split(",").filter(item => item) || [];
        if (item.microGroup) {
          teamIds.push(item.microGroup.id);
        }

        return teamIds.some((id: any) => {
          return props.orgIds.includes(Number(id));
        });
      });

      const myRank = data.amountPerMonth.find((item, index) => {
        item.rank = index;
        return item.workerid === userMsg.value.id;
      });

      if (myRank) {
        topData.value = {
          name: myRank ? nameLength(myRank.worker) : "",
          amountPerMonth: myRank.amount || "",
          avgPrice: myRank.avgPrice || "",
          orderNum: myRank.orderNum || "",
          groupName: myRank.group?.name || "",
          teamName: myRank.team?.name || "",
          team: myRank.team,
          rank: myRank.rank
        };
      } else {
        topData.value = undefined;
      }

      let realLength = revenceLimit ? data.amountPerMonth.length : 20;
      let lenLimit = realLength < limit.value ? limit.value : realLength;

      let rank = -1, //排名数字
        i = 0, //实际位置
        j = 1, //同一名次并列的人数
        list = [];
      while (rank < lenLimit) {
        let amountPerMonth = data.amountPerMonth[i] || {};
        let item: any = {
          name: amountPerMonth ? nameLength(amountPerMonth.worker) : "",
          amountPerMonth: amountPerMonth.amount || "",
          avgPrice: amountPerMonth.avgPrice || "",
          orderNum: amountPerMonth.orderNum || "",
          groupName: amountPerMonth.group?.name || "",
          teamName: amountPerMonth.team?.name || "",
          team: amountPerMonth.team
        };
        if (!amountPerMonth || !data.amountPerMonth[i - 1]) {
          rank += j;
          j = 1;
        } else {
          if (amountPerMonth.amount !== data.amountPerMonth[i - 1].amount) {
            rank += j;
            j = 1;
          } else {
            j++;
          }
        }
        item.rank = rank;
        rank < lenLimit && list.push(item);
        i++;
      }
      // let index = data.amountPerMonth.findIndex(
      //   item => item.workerid === useUserStoreHook().userMsg.id
      // );
      // if (index > i - 2) {
      //   let amountPerMonth = data.amountPerMonth[index];
      //   let item: any = {
      //     name: amountPerMonth ? nameLength(amountPerMonth.worker) : "",
      //     amountPerMonth: amountPerMonth ? amountPerMonth.amount : ""
      //   };
      //   item.rank = index;
      //   list.push(item);
      // }

      tableData.value = list;
      loading.value = false;
    })
    .catch(() => {
      tableData.value = [];
      loading.value = false;
    });
}

watch(
  () => props.orgIds,
  (n, old) => {
    if (!isEqual(n, old)) {
      getList();
    }
  }
);

onMounted(() => {
  getList();
});
</script>

<template>
  <div v-loading="loading">
    <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
      <el-form-item prop="time">
        <el-date-picker
          v-model="form.time"
          type="datetimerange"
          value-format="x"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59)
          ]"
        />
      </el-form-item>
      <el-form-item prop="orderType">
        <el-select
          v-model="form.orderType"
          placeholder="请选择订单类型"
          clearable
          @keyup.enter="getList"
        >
          <el-option
            v-for="item in customerTypeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          @click="getList"
        >
          搜索
        </el-button>
      </el-form-item>
    </el-form>
    <!--    name="营收排行榜" -->
    <RankTable
      :tableData="tableData"
      :tableHeader="tableHeader"
      :fixedTopData="topData"
      :limit="limit"
      :isRank="true"
      revenueField="amountPerMonth"
    />
  </div>
</template>
