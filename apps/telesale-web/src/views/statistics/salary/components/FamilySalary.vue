<!--
 * @Date         : 2025-01-06 11:36:29
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useAppStoreHook } from "/@/store/modules/app";
import sendDownloadRequest from "/@/utils/handle/download";
import paramsHandle from "/@/utils/handle/paramsHandle";
import durationChange from "/@/utils/handle/durationChange";
import { userGridOptions } from "/@/hooks/useGridOptions";
import PhoneInfniteScrollCardList from "/@/components/PhoneInfniteScrollCardList/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import Search from "./Search.vue";
import { familyColumns } from "../utils/listHeader";
import { getIconRender } from "/@/utils/common/trend";
import { getFamilyCategoryData } from "/@/utils/common/index";
import { Top, Bottom } from "@element-plus/icons-vue";
import {
  exportFamilySalary<PERSON>pi,
  getFamilySalary<PERSON>pi
} from "/@/api/statistics/salart";

let device = useAppStoreHook().device;

//带分页列表数据必备
let loading = ref(true);
let dataList = ref([]);
const sumData = ref([]);

function formHandler() {
  let params = paramsHandle(formRefs.value.form, {
    time: true,
    string: ["workerid"],
    zero: ["department", "orgId"]
  });
  params.workerid && params.orgId && delete params.orgId;
  params.familyCategory = getFamilyCategoryData(params.familyCategory);
  return params;
}

function exportFile() {
  loading.value = true;
  sendDownloadRequest(exportFamilySalaryApi, formHandler(), loading);
}

//form查询
const formRefs = ref();
function getList() {
  loading.value = true;
  getFamilySalaryApi(formHandler())
    .then(({ data }: { data: any }) => {
      data.forEach(item => {
        item.convRate = parseFloat(item?.convRate || 0);
      });
      dataList.value = data;
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      loading.value = false;
    });
}

function toFixedChange(val, num = 2) {
  return val ? val.toFixed(num) : 0;
}

function summaryMethod({ columns, data }) {
  let len = data.length;
  const sums = [].fill(columns.length, undefined);
  sums[0] = "统计";
  let effectiveNum = 0;
  let callFamilyNumber = 0;
  let callConversionAmount = 0;
  let callConversionNumber = 0;
  let callFamilyRate = 0;
  let callFamilyPrice = 0;
  columns.forEach((column, index) => {
    const hasColums = ["workerId", "microGroup"];
    if (!hasColums.includes(column.field)) {
      const values = data.map(item => Number(item[column.field]));
      const total = values.reduce((prev, curr) => {
        const value = Number(curr);
        return !isNaN(value) ? prev + curr : prev;
      }, 0);
      sumData.value[index] = toFixedChange(total / len, 2);
      switch (index) {
        case 3:
          callFamilyNumber = toFixedChange(total, 0);
          sums[index] = `总：${callFamilyNumber}，均：${toFixedChange(
            total / len,
            2
          )}`;
          break;
        case 4:
          sums[index] = `总：${toFixedChange(total, 0)}，均：${toFixedChange(
            total / len,
            2
          )}`;
          break;
        case 5:
          sums[index] = `总：${toFixedChange(total, 0)}，均：${toFixedChange(
            total / len,
            2
          )}`;
          break;
        case 6:
          sums[index] = `总：${toFixedChange(total, 0)}，均：${toFixedChange(
            total / len,
            2
          )}`;
          break;
        case 7:
          sumData.value[index] = toFixedChange(total / len, 4);
          effectiveNum = toFixedChange(total, 0);
          sums[index] = `总：${effectiveNum}，均：${toFixedChange(
            total / len,
            4
          )}`;
          break;
        case 8:
          sumData.value[index] = toFixedChange(
            (effectiveNum / callFamilyNumber) * 100,
            0
          );
          sums[index] = callFamilyNumber
            ? `均：${toFixedChange(
                (effectiveNum / callFamilyNumber) * 100,
                0
              )}%`
            : "0%";

          break;
        case 9:
          sums[index] = `均：${durationChange(total / len)}`;
          break;
        case 10:
          callConversionNumber = toFixedChange(total, 0);
          sums[index] = `总：${callConversionNumber}，均：${toFixedChange(
            total / len,
            2
          )}`;
          break;
        case 11:
          callConversionAmount = toFixedChange(total, 0);
          sums[index] = `总：${callConversionAmount}，均：${toFixedChange(
            total / len,
            2
          )}`;
          break;
        case 12:
          callFamilyRate = callConversionNumber / callFamilyNumber;
          sumData.value[index] = toFixedChange(callFamilyRate * 100, 4);
          sums[index] = `均：${sumData.value[index]}%`;
          break;
        case 13:
          callFamilyPrice =
            callConversionAmount > 0
              ? Math.round(callConversionAmount / callConversionNumber)
              : 0;
          sumData.value[index] = callFamilyPrice;
          sums[index] = `均：${callFamilyPrice}`;
          break;
        case 14:
          sumData.value[index] = toFixedChange(
            callFamilyRate * callFamilyPrice,
            2
          );
          sums[index] = `均：${sumData.value[index]}`;
          break;

        default:
          break;
      }
    }
  });
  return len ? [sums] : [];
}

const { gridOptions } = userGridOptions(dataList, familyColumns, summaryMethod);
onMounted(() => {
  getList();
});
</script>

<template>
  <div v-loading="loading">
    <Search
      ref="formRefs"
      type="family"
      @onSearch="getList"
      @exportFile="exportFile"
    />
    <div class="mb-10px flex items-center">
      <el-icon color="#67C23A" size="14">
        <Bottom />
      </el-icon>
      <span class="c-coolgray">代表低于统计的平均值</span>
      <el-icon color="red" size="14" class="ml-10px">
        <Top />
      </el-icon>
      <span class="c-coolgray">代表高于统计的平均值</span>
    </div>
    <div class="g-table-box">
      <vxe-grid v-bind="gridOptions" v-if="device !== 'mobile'">
        <template #callNumWithFamily="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #callNumWithInfo="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #callNumWithPhone="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #callNum="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #effectiveNumWithFamily="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #effectiveRateWithFamily="{ row, column, columnIndex }">
          {{ row[column["field"]] + "%" }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #effectiveCallLengthWithFamily="{ row, column, columnIndex }">
          {{ durationChange(row[column["field"]]) }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #callConversionNumWithFamily="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #callConversionFeeWithFamily="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #callConversionRateWithFamily="{ row, column, columnIndex }">
          {{
            row[column["field"]]
              ? row[column["field"]].toFixed(4) + "%"
              : "0.0000%"
          }}
          <component
            :is="getIconRender(row, column, sumData[columnIndex], 4)"
          />
        </template>
        <template #callConversionAvgWithFamily="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #callConversionARPUWithFamily="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
      </vxe-grid>
      <template v-else>
        <PhoneInfniteScrollCardList :loading="loading" :list="dataList">
          <template #defalut="scope">
            <ReCardList
              ref="cardRefs"
              :dataList="scope.dataList"
              :listHeader="familyColumns"
              :isCardBox="false"
            />
          </template>
        </PhoneInfniteScrollCardList>
      </template>
    </div>
  </div>
</template>
