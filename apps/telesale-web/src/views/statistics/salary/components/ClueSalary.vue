<!--
 * @Date         : 2025-01-06 11:36:14
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useAppStoreHook } from "/@/store/modules/app";
import { getSalary, salaryExport } from "/@/api/statistics";
import sendDownloadRequest from "/@/utils/handle/download";
import paramsHandle from "/@/utils/handle/paramsHandle";
import durationChange from "/@/utils/handle/durationChange";
import { userGridOptions } from "/@/hooks/useGridOptions";
import PhoneInfniteScrollCardList from "/@/components/PhoneInfniteScrollCardList/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import Search from "./Search.vue";
import { listHeader } from "../utils/listHeader";
import { getIconRender } from "/@/utils/common/trend";
import { Top, Bottom } from "@element-plus/icons-vue";

let device = useAppStoreHook().device;

//带分页列表数据必备
let loading = ref(true);
let dataList = ref([]);
const sumData = ref([]);

function formHandler() {
  let params = paramsHandle(formRefs.value.form, {
    time: true,
    string: ["workerid"],
    zero: ["department", "orgId"]
  });
  params.workerid && params.orgId && delete params.orgId;
  return params;
}

function exportFile() {
  loading.value = true;
  sendDownloadRequest(salaryExport, formHandler(), loading);
}

//form查询
const formRefs = ref();
function getList() {
  loading.value = true;
  getSalary(formHandler())
    .then(({ data }: { data: any }) => {
      data.forEach(item => {
        item.convRate = parseFloat(item?.convRate || 0);
      });
      dataList.value = data;
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      loading.value = false;
    });
}

function toFixedChange(val, num) {
  return val ? val.toFixed(num) : 0;
}

function summaryMethod({ columns, data }) {
  let len = data.length;
  let amount = 0,
    total = 0,
    callNum = 0,
    validNum = 0;
  const sums = [];
  sums.fill(columns.length, undefined);
  sums[0] = "统计";
  columns.forEach((column, index) => {
    if (
      column.property &&
      column.property !== "worker" &&
      column.property !== "microGroup" &&
      column.property !== "group" &&
      column.property !== "team" &&
      column.property !== "stage"
    ) {
      const values = data.map(item => Number(item[column.property]));
      sums[index] = values.reduce((prev, curr) => {
        const value = Number(curr);
        return !isNaN(value) ? prev + curr : prev;
      }, 0);

      switch (index) {
        case 1:
          callNum = sums[index];
          sumData.value[index] = toFixedChange(sums[index] / len, 2);
          sums[index] = `总：${toFixedChange(
            sums[index],
            0
          )}，均：${toFixedChange(sums[index] / len, 2)}`;

          break;
        case 3:
          sumData.value[index] = toFixedChange(sums[index] / len, 2);
          sums[index] = `总：${toFixedChange(
            sums[index],
            0
          )}次，均：${toFixedChange(sums[index] / len, 2)}次`;
          break;
        case 4:
          sumData.value[index] = toFixedChange(sums[index] / len, 2);
          sums[index] = `均：${durationChange(sums[index] / len)}`;
          break;
        case 5:
          sumData.value[index] = toFixedChange(sums[index] / len, 2);
          validNum = sums[index];
          sums[index] = `总：${toFixedChange(
            sums[index],
            0
          )}，均：${toFixedChange(sums[index] / len, 2)}`;
          break;
        case 6:
          sumData.value[index] = toFixedChange((validNum / callNum) * 100, 0);
          sums[index] = callNum
            ? `均：${toFixedChange((validNum / callNum) * 100, 0)}%`
            : "0%";
          break;
        case 7:
          sumData.value[index] = toFixedChange(sums[index] / len, 2);
          amount = sums[index];
          sums[index] = `总：${toFixedChange(
            sums[index],
            1
          )}，均：${toFixedChange(sums[index] / len, 2)}`;
          break;
        case 8:
          total = sums[index];
          sumData.value[index] = toFixedChange(sums[index] / len, 2);
          sumData.value[2] = callNum
            ? toFixedChange((total / callNum) * 100, 4)
            : 0;

          sums[2] = callNum
            ? `均：${toFixedChange((total / callNum) * 100, 4)}%`
            : "0%";
          sums[index] = `总：${toFixedChange(
            sums[index],
            0
          )}，均：${toFixedChange(sums[index] / len, 2)}`;
          break;
        case 9:
          sumData.value[index] = amount > 0 ? Math.round(amount / total) : 0;
          sums[index] = amount > 0 ? Math.round(amount / total) : 0;
          break;
      }
    }
  });
  return len ? [sums] : [];
}

let { gridOptions } = userGridOptions(dataList, listHeader, summaryMethod);
onMounted(() => {
  getList();
});
</script>

<template>
  <div v-loading="loading">
    <Search
      type="clue"
      ref="formRefs"
      @onSearch="getList"
      @exportFile="exportFile"
    />
    <div class="mb-10px flex items-center">
      <el-icon color="#67C23A" size="14">
        <Bottom />
      </el-icon>
      <span class="c-coolgray">代表低于统计的平均值</span>
      <el-icon color="red" size="14" class="ml-10px">
        <Top />
      </el-icon>
      <span class="c-coolgray">代表高于统计的平均值</span>
    </div>
    <div class="g-table-box">
      <vxe-grid v-bind="gridOptions" v-if="device !== 'mobile'">
        <template #totalSlot="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #convRate="{ row, column, columnIndex }">
          {{
            row[column["field"]]
              ? row[column["field"]].toFixed(4) + "%"
              : "0.0000%"
          }}
          <component
            :is="getIconRender(row, column, sumData[columnIndex], 4)"
          />
        </template>
        <template #callNum="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #time="{ row, column, columnIndex }">
          {{ durationChange(row[column["field"]]) }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #valid="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #rate="{ row, column, columnIndex }">
          {{ row[column["field"]] + "%" }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #amount="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #orderNum="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
        <template #avgPrice="{ row, column, columnIndex }">
          {{ row[column["field"]] }}
          <component :is="getIconRender(row, column, sumData[columnIndex])" />
        </template>
      </vxe-grid>
      <template v-else>
        <PhoneInfniteScrollCardList :loading="loading" :list="dataList">
          <template #defalut="scope">
            <ReCardList
              ref="cardRefs"
              :dataList="scope.dataList"
              :listHeader="listHeader"
              :isCardBox="false"
            />
          </template>
        </PhoneInfniteScrollCardList>
      </template>
    </div>
  </div>
</template>
