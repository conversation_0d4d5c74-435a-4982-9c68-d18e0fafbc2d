<!--
 * @Date         : 2025-03-04 17:05:00
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { rankTarget } from "/@/api/statistics_rank";
import paramsHandle from "/@/utils/handle/paramsHandle";
import RankTable from "/@/components/RankTable/index.vue";
import { isEqual } from "lodash-es";

interface Props {
  orgIds: any[];
  level?: number;
}

const props = defineProps<Props>();

const tableHeader = [
  { field: "name", desc: props.level === 2 ? "团队名称" : "小组名称" },
  { field: "rate", desc: "目标完成率" }
];

const loading = ref(true);
const tableData = ref([]);

let now = new Date();
let month = now.getMonth() + 1;
//form查询
const form = reactive({
  month: now.getFullYear() + "-" + (month > 9 ? month : "0" + month)
});

function getList() {
  loading.value = true;
  let params: any = { time: [], level: props.level, orgIds: props.orgIds };
  let arr = form.month.split("-");
  params.time = [
    new Date(arr[0] + "/" + arr[1] + "/1").getTime(),
    new Date(Number(arr[0]), Number(arr[1]), 1).getTime() - 1
  ];

  rankTarget(paramsHandle(params, { newTime: true }))
    .then(({ data }: { data: any }) => {
      let rank = -1,
        j = 1;
      data.forEach((item, i) => {
        item.rate = item.rate ? item.rate.toFixed(2) + "%" : "";
        if (!i || item.rate !== data[i - 1].rate) {
          rank += j;
          j = 1;
        } else {
          j++;
        }
        item.rank = rank;
      });
      tableData.value = data;
      loading.value = false;
    })
    .catch(() => {
      tableData.value = [];
      loading.value = false;
    });
}

watch(
  () => props.orgIds,
  (n, old) => {
    if (!isEqual(n, old)) {
      getList();
    }
  }
);

onMounted(() => {
  getList();
});
</script>

<template>
  <div v-loading="loading">
    <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
      <el-form-item>
        <el-date-picker
          v-model="form.month"
          type="month"
          placeholder="选择月"
          value-format="YYYY-MM"
          :clearable="false"
          @change="getList"
          @submit.prevent
        />
      </el-form-item>
    </el-form>
    <!--    name="目标完成率排行榜" -->
    <RankTable
      :tableData="tableData"
      :tableHeader="tableHeader"
      :isRank="true"
    />
  </div>
</template>
