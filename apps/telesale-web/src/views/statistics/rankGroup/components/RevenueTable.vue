<!--
 * @Date         : 2025-03-04 16:45:12
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { rankRevenue } from "/@/api/statistics_rank";
import {
  disabledDate,
  clearDate,
  onPick
} from "/@/utils/handle/timeSelectLimit";
import paramsHandle from "/@/utils/handle/paramsHandle";
import originDate from "/@/utils/handle/originDate";
import customerTypeList from "/@/utils/data/customerTypeList";
import RankTable from "/@/components/RankTable/index.vue";
import { isEqual } from "lodash-es";

interface Props {
  orgIds: any[];
  level?: number;
}

const props = defineProps<Props>();

const tableHeader = [
  { field: "name", desc: props.level === 2 ? "团队名称" : "小组名称" },
  { field: "amount", desc: "本月业绩/元" }
];

const loading = ref(false);
const tableData = ref([]);

function defaultTime() {
  return [originDate()[0], new Date(new Date().toLocaleDateString()).getTime()];
}

//form查询
const form = reactive({
  time: defaultTime(),
  orderType: undefined
});

function getList() {
  loading.value = true;
  let params = {
    time: [],
    orderType: form.orderType,
    level: props.level,
    orgIds: props.orgIds
  };
  form.time &&
    form.time.length === 2 &&
    (params.time = [form.time[0], form.time[1] + 24 * 3600 * 1000]);

  rankRevenue(paramsHandle(params, { newTime: true, minus: ["orderType"] }))
    .then(({ data }: { data: any }) => {
      let rank = -1,
        j = 1;
      data.groupAmount.forEach((item, i) => {
        if (!i || item.amount !== data.groupAmount[i - 1].amount) {
          rank += j;
          j = 1;
        } else {
          j++;
        }
        item.rank = rank;
      });
      tableData.value = data.groupAmount.filter(item => item.amount > 0);
      loading.value = false;
    })
    .catch(() => {
      tableData.value = [];
      loading.value = false;
    });
}

watch(
  () => props.orgIds,
  (n, old) => {
    if (!isEqual(n, old)) {
      getList();
    }
  }
);

onMounted(() => {
  getList();
});
</script>

<template>
  <div v-loading="loading">
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="clearfix"
      :clearable="false"
    >
      <el-form-item prop="time">
        <el-date-picker
          v-model="form.time"
          type="daterange"
          value-format="x"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :disabled-date="disabledDate"
          @visible-change="clearDate"
          @calendar-change="onPick"
          :clearable="false"
        />
      </el-form-item>
      <el-form-item prop="orderType" v-if="level === 3">
        <el-select
          v-model="form.orderType"
          placeholder="请选择订单类型"
          clearable
          @keyup.enter="getList"
        >
          <el-option
            v-for="item in customerTypeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          @click="getList"
        >
          搜索
        </el-button>
      </el-form-item>
    </el-form>
    <!--    name="营收排行榜" -->
    <RankTable
      :tableData="tableData"
      :tableHeader="tableHeader"
      :isRank="true"
    />
  </div>
</template>
