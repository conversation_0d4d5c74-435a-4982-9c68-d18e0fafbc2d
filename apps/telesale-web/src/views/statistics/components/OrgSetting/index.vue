<!--
 * @Date         : 2025-03-21 16:12:04
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { Setting } from "@element-plus/icons-vue";
import { tabSettingApi } from "/@/api/statistics/rankGroup";
import AddTab from "../TabsAction/dialog/AddTab.vue";

const props = defineProps<{
  orgList: any[];
  pageName: string;
  level?: number;
  tabName: string;
}>();

const emits = defineEmits(["update:orgList"]);

const tabData = {
  id: 0,
  orgList: []
};

const settingInfo = ref();

const loading = ref(false);
const addModal = ref(false);
const rowId = ref();

const getTabs = () => {
  loading.value = true;
  tabSettingApi({ action: "list", pageName: props.pageName })
    .then((res: any) => {
      settingInfo.value = res.data[0] || tabData;
    })
    .finally(() => {
      loading.value = false;
    });
};

const addTab = () => {
  rowId.value = settingInfo.value?.id;
  addModal.value = true;
};

watch(
  () => settingInfo.value?.orgList,
  n => {
    emits("update:orgList", n);
  }
);

getTabs();
</script>

<template>
  <div v-loading="loading">
    <div v-auth="'telesale_admin_person_settingTab'">
      <el-dropdown trigger="click">
        <span class="el-dropdown-link">
          <el-icon class="ml-10px">
            <Setting />
          </el-icon>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="addTab">编辑</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <AddTab
      v-if="addModal"
      v-model:value="addModal"
      :pageName="props.pageName"
      :id="rowId"
      :level="props.level"
      :tabName="props.tabName"
      @onSearch="getTabs"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.is-disabled) {
  cursor: pointer;
}
</style>
