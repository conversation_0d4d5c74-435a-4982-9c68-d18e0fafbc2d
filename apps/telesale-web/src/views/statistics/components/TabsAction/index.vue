<!--
 * @Date         : 2025-03-04 12:09:57
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<script lang="ts" setup>
import { Plus, Setting } from "@element-plus/icons-vue";
import AddTab from "./dialog/AddTab.vue";
import { tabSettingApi } from "/@/api/statistics/rankGroup";
import { getAuth } from "/@/utils/auth";
import { isString } from "lodash";

const props = defineProps<{
  pageName: string;
  level?: number;
  permission: string;
  isRule?: boolean;
}>();

const emits = defineEmits(["onSearch"]);

const tabData = {
  id: 0,
  tabName: "",
  orgList: [],
  ruleList: undefined
};

const tabList = ref([]);

const loading = ref(false);
const addModal = ref(false);
const active = ref();
const rowId = ref();
const hasPermission = getAuth(props.permission);

const addTab = (id?: number) => {
  rowId.value = id;
  addModal.value = true;
};

const getTabs = (isReload?: boolean) => {
  loading.value = true;
  tabSettingApi({ action: "list", pageName: props.pageName })
    .then((res: any) => {
      if (hasPermission) {
        res.data.push(tabData);
      }
      res.data.forEach((item: any) => {
        if (isString(item.ruleList)) {
          try {
            item.ruleList = JSON.parse(item.ruleList);
          } catch (error) {
            item.ruleList = [];
          }
        }
      });

      tabList.value = res.data;
      if (!active.value) {
        active.value = res.data[0]?.id;
      }
      if (isReload) {
        refresh();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const delTab = (id: number) => {
  ElMessageBox.confirm("是否确认删除？", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    loading.value = true;
    tabSettingApi({ action: "delete", id })
      .then(() => {
        active.value = undefined;
        getTabs();
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

const refresh = () => {
  const current = tabList.value.find(item => item.id === active.value);
  emits("onSearch", current.orgList);
};

getTabs();
</script>

<template>
  <div v-loading="loading">
    <el-tabs v-model="active" type="card" tab-position="top">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.id"
        :name="item.id"
        lazy
        :disabled="item.id === 0"
      >
        <template #label>
          <div class="flex items-center">
            <span>{{ item.tabName }}</span>
            <el-dropdown
              v-if="item.id === active && hasPermission && tabList.length > 1"
              trigger="click"
            >
              <span class="el-dropdown-link">
                <el-icon class="ml-10px">
                  <Setting />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="addTab(item.id)">
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item @click="delTab(item.id)">
                    <span class="c-red">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <el-icon
            v-if="item.id === 0 && hasPermission"
            @click="addTab()"
            size="20"
          >
            <Plus />
          </el-icon>
        </template>
        <template v-if="item.id !== 0"><slot :data="item" /></template>
      </el-tab-pane>
    </el-tabs>

    <AddTab
      v-if="addModal"
      v-model:value="addModal"
      :pageName="props.pageName"
      :id="rowId"
      :level="props.level"
      @onSearch="getTabs"
      :isRule="props.isRule"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.is-disabled) {
  cursor: pointer;
}
</style>
