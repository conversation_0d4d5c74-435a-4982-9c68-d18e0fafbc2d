<!--
 * @Date         : 2025-02-26 17:59:28
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { tabSettingApi } from "/@/api/statistics/rankGroup";
import findOrganizationMath from "/@/utils/asyn/findOrganization";
import { Delete } from "@element-plus/icons-vue";
import { cloneDeep } from "lodash-es";

interface Props {
  value: boolean;
  id?: number;
  pageName: string;
  level?: number;
  tabName?: string;
  isRule?: boolean;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {
  isRule: false
});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance | null>();
const orgAllData = ref([]);
const form = ref({
  id: undefined,
  pageName: props.pageName,
  action: "",
  tabName: props.tabName,
  orgList: undefined,
  ruleList: []
});
const rules: FormRules = {
  tabName: [
    {
      required: true,
      message: "请输入Tab页名称",
      trigger: "change"
    }
  ],
  orgList: [
    {
      required: true,
      message: "请选择统计范围",
      trigger: "change"
    }
  ],
  ruleList: [
    {
      required: true,
      message: "请设置奖励区间",
      trigger: "change"
    },
    {
      validator: (rule, value, callback) => {
        if (!validateRules()) {
          callback(new Error("奖励区间设置有误"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ]
};
function handleClose() {
  isModel.value = false;
}

const getInfo = () => {
  loading.value = true;
  tabSettingApi({ action: "detail", id: props.id, pageName: props.pageName })
    .then(res => {
      form.value = res.data as any;
      if (props.tabName) {
        form.value.tabName = props.tabName;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const getOrgData = () => {
  loading.value = true;
  findOrganizationMath()
    .then(res => {
      if (props.level) {
        setDisabel(res);
      }
      orgAllData.value = res;
      if (props.id) {
        getInfo();
      } else {
        loading.value = false;
      }
    })
    .catch(() => {
      loading.value = false;
    });
};

// setDisabel
const setDisabel = data => {
  data.forEach(item => {
    if (item.level - 1 > props.level) {
      item.disabled = true;
    }

    if (item.children?.length) {
      setDisabel(item.children);
    }
  });
};

// 添加奖励规则
const addRule = () => {
  form.value.ruleList.push({
    min: undefined,
    max: undefined,
    amount: 0
  });
};

// 删除奖励规则
const removeRule = (index: number) => {
  form.value.ruleList.splice(index, 1);
};

// 验证奖励规则是否重叠
const validateRules = () => {
  const rules = form.value.ruleList;
  if (!rules || rules.length === 0) return true;

  // 检查每个规则的起始名次是否小于等于结束名次
  for (const rule of rules) {
    if (rule.min > rule.max) {
      return false;
    }
  }

  // 检查规则之间是否重叠
  const sortedRules = [...rules].sort((a, b) => a.min - b.min);
  for (let i = 0; i < sortedRules.length - 1; i++) {
    if (sortedRules[i].max >= sortedRules[i + 1].min) {
      return false;
    }
  }

  return true;
};

// 获取规则错误信息
const getRuleError = (index: number) => {
  const rule = form.value.ruleList[index];
  if (!rule) return "";

  if (rule.min > rule.max) {
    return "起始名次不能大于结束名次";
  }

  // 检查与其他规则是否重叠
  for (let i = 0; i < form.value.ruleList.length; i++) {
    if (i === index) continue;
    const otherRule = form.value.ruleList[i];

    // 检查当前规则是否与其他规则重叠
    if (
      (rule.min <= otherRule.max && rule.max >= otherRule.min) ||
      (otherRule.min <= rule.max && otherRule.max >= rule.min)
    ) {
      return `与第${i + 1}个规则区间重叠`;
    }
  }

  return "";
};

getOrgData();

const submit = async () => {
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      form.value.action = form.value.id ? "update" : "add";
      const data = cloneDeep(form.value);
      data.ruleList.forEach(item => {
        item.min = item.min + "";
        item.max = item.max + "";
        item.amount = item.amount + "";
      });
      tabSettingApi(form.value)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          handleClose();
          emit("onSearch", !!props.id);
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
};
</script>

<template>
  <el-dialog
    :title="props.id || props.tabName ? '编辑Tab页' : '新增Tab页'"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
    append-to-body
    width="800px"
  >
    <div v-loading="loading" class="flex justify-center">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="Tab页名称" prop="tabName">
          <el-input
            v-model.trim="form.tabName"
            placeholder="请输入Tab页名称"
            maxlength="10"
            show-word-limit
            clearable
            :disabled="!!props.tabName"
            style="width: 80%"
          />
        </el-form-item>
        <el-form-item label="选择统计范围" prop="orgList">
          <el-cascader
            v-model="form.orgList"
            :options="orgAllData"
            :props="{
              value: 'id',
              label: 'name',
              checkStrictly: true,
              multiple: true,
              emitPath: false
            }"
            placeholder="请选择统计范围"
            filterable
            clearable
            :show-all-levels="false"
            style="width: 100%"
            class="input-full"
          />
        </el-form-item>
        <el-form-item
          label="奖励设置"
          prop="ruleList"
          required
          v-if="props.isRule"
        >
          <div v-for="(rule, index) in form.ruleList" :key="index" class="mb-2">
            <div class="flex items-center">
              <el-input-number
                v-model="rule.min"
                :min="1"
                :precision="0"
                placeholder="起始名次"
              />
              <span class="mx-4px">名至</span>
              <el-input-number
                v-model="rule.max"
                :min="rule.min || 1"
                :precision="0"
                placeholder="结束名次"
              />
              <span class="mx-4px">名，</span>
              <span class="mx-4px">奖励</span>
              <el-input-number
                v-model="rule.amount"
                :min="0"
                :precision="0"
                placeholder="奖励金额"
              />
              <span class="ml-2">元</span>
              <el-button
                type="danger"
                circle
                @click="removeRule(index)"
                class="ml-2"
                :icon="Delete"
              />
            </div>
            <div v-if="getRuleError(index)" class="text-red-500 text-xs mt-1">
              {{ getRuleError(index) }}
            </div>
          </div>
          <el-button type="primary" link @click="addRule" class="mt-2">
            <span class="i-ep-plus mr-1" />
            添加奖励设置
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
:deep(.el-input) {
  width: 100%;
}
</style>
