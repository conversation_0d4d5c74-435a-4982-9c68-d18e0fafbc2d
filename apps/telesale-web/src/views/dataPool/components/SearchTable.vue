<!--
 * @Date         : 2024-12-02 14:19:38
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { ref } from "vue";
import { findPool } from "/@/api/user";
import HelpHintForm from "/@/components/HelpHintForm/index.vue";
import { useCueDesc } from "/@/hooks/business/useCueDesc";
import { getExperimentGroupStatisticsApi } from "/@/api/system/distribute";

const { classObj, getClass } = useCueDesc();

const activeStage = ref("初中");
const loading = ref(false);
const tableData = ref([
  {
    clueType: "A",
    name: "aClass",
    leadsCount: "",
    remainCount: "",
    time: undefined
  },
  {
    clueType: "B",
    name: "bClass",
    leadsCount: "",
    remainCount: "",
    time: undefined
  },
  {
    clueType: "C",
    name: "cClass",
    leadsCount: "",
    remainCount: "",
    time: undefined
  },
  {
    clueType: "D",
    name: "dClass",
    leadsCount: "",
    remainCount: "",
    time: undefined
  },
  {
    clueType: "E",
    name: "eClass",
    leadsCount: "",
    remainCount: "",
    time: undefined
  }
]);

function findTime(row, index) {
  loading.value = true;
  let data = {
    stage: activeStage.value,
    tag: row.clueType,
    duration: row.time
  };
  getExperimentGroupStatisticsApi(data)
    .then(({ data }: { data: any }) => {
      tableData.value[index].remainCount = data.remainCount;
      tableData.value[index].leadsCount = data.leadsCount;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

function defaultFind() {
  loading.value = true;
  let data = {
    duration: 0,
    tag: "A"
  };
  Promise.all([
    getExperimentGroupStatisticsApi(data),
    getExperimentGroupStatisticsApi({ ...data, tag: "B" }),
    getExperimentGroupStatisticsApi({ ...data, tag: "C" }),
    getExperimentGroupStatisticsApi({ ...data, tag: "D" }),
    getExperimentGroupStatisticsApi({ ...data, tag: "E" })
  ])
    .then((res: any) => {
      res.forEach((item, index) => {
        tableData.value[index].remainCount = item.data.remainCount;
        tableData.value[index].leadsCount = item.data.leadsCount;
      });
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

defaultFind();
getClass(activeStage.value);
</script>

<template>
  <div v-loading="loading">
    <div class="d-cont pad20">
      <div element-loading-text="拼命加载中..." class="tableBox">
        <el-table
          ref="tableBox"
          stripe
          :data="tableData"
          highlight-current-row
          :border="true"
        >
          <el-table-column prop="clueType" label="线索类型">
            <template #default="scope">
              <HelpHintForm
                :content="classObj[scope.row.name]"
                :label="scope.row.clueType + '类'"
                :noColon="true"
              />
            </template>
          </el-table-column>
          <el-table-column prop="leadsCount" label="当日领取量" />
          <el-table-column prop="remainCount" label="剩余线索量" />
          <el-table-column label="操作" min-width="160">
            <template v-slot="scope">
              近
              <el-input-number
                v-model="scope.row.time"
                :controls="false"
                :min="0.1"
                :precision="1"
              />
              <span style="padding-right: 20px">小时</span>
              <el-button
                type="primary"
                link
                @click="findTime(scope.row, scope.$index)"
              >
                查询
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
