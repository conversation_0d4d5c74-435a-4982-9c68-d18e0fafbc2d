<!--
 * @Date         : 2024-12-02 14:20:34
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { ref } from "vue";
import { findPool } from "/@/api/user";
import HelpHintForm from "/@/components/HelpHintForm/index.vue";
import { useCueDesc } from "/@/hooks/business/useCueDesc";

const { classObj, getClass } = useCueDesc();

const activeStage = ref("初中");
const loading = ref(false);
const tableData = ref([
  {
    clueType: "A",
    name: "aClass",
    leadsCount: "",
    rtUser: "",
    time: undefined
  },
  {
    clueType: "B",
    name: "bClass",
    leadsCount: "",
    rtUser: "",
    time: undefined
  },
  {
    clueType: "C",
    name: "cClass",
    leadsCount: "",
    rtUser: "",
    time: undefined
  },
  {
    clueType: "D",
    name: "dClass",
    leadsCount: "",
    rtUser: "",
    time: undefined
  },
  {
    clueType: "E",
    name: "eClass",
    leadsCount: "",
    rtUser: "",
    time: undefined
  }
]);

function changeStage() {
  tableData.value.forEach(item => {
    item.time = undefined;
  });
  getClass(activeStage.value);
  defaultFind();
}
function findTime(row, index) {
  loading.value = true;
  let data = {
    stage: activeStage.value,
    tag: row.clueType,
    duration: row.time
  };
  findPool(data)
    .then(({ data }: { data: any }) => {
      tableData.value[index].rtUser = data.rtUser;
      tableData.value[index].leadsCount = data.leadsCount;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

function defaultFind() {
  loading.value = true;
  let data = {
    stage: activeStage.value,
    duration: 0,
    tag: "A"
  };
  Promise.all([
    findPool(data),
    findPool({ ...data, tag: "B" }),
    findPool({ ...data, tag: "C" }),
    findPool({ ...data, tag: "D" }),
    findPool({ ...data, tag: "E" })
  ])
    .then((res: any) => {
      res.forEach((item, index) => {
        tableData.value[index].rtUser = item.data.rtUser;
        tableData.value[index].leadsCount = item.data.leadsCount;
      });
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

defaultFind();
getClass(activeStage.value);
</script>

<template>
  <div v-loading="loading">
    <el-tabs v-model="activeStage" type="card" @tab-change="changeStage">
      <el-tab-pane label="小学" name="小学" />
      <el-tab-pane label="初中" name="初中" />
      <el-tab-pane label="高中" name="高中" />
    </el-tabs>
    <div class="d-cont pad20">
      <div element-loading-text="拼命加载中..." class="tableBox">
        <el-table
          ref="tableBox"
          stripe
          :data="tableData"
          highlight-current-row
          :border="true"
        >
          <el-table-column prop="clueType" label="线索类型">
            <template #default="scope">
              <HelpHintForm
                :content="classObj[scope.row.name]"
                :label="scope.row.clueType + '类'"
                :noColon="true"
              />
            </template>
          </el-table-column>
          <el-table-column prop="leadsCount" label="当日领取量" />
          <el-table-column prop="rtUser" label="剩余线索量" />
          <el-table-column label="操作" min-width="160">
            <template v-slot="scope">
              近
              <el-input-number
                v-model="scope.row.time"
                :controls="false"
                :min="0.1"
                :precision="1"
              />
              <span style="padding-right: 20px">小时</span>
              <el-button
                type="primary"
                link
                @click="findTime(scope.row, scope.$index)"
              >
                查询
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
