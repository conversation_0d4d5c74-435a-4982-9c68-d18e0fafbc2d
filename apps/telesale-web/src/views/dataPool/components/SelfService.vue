<script setup lang="ts">
import { ref } from "vue";
import HelpHintForm from "/@/components/HelpHintForm/index.vue";
import { getSelfPoolApi } from "/@/api/pool/index";

const activeStage = ref("初中");
const loading = ref(false);
const tableData = ref([
  {
    clueType: "付费",
    name: "pay",
    leadsCount: "",
    remainCount: "",
    time: undefined
  },
  {
    clueType: "新增",
    name: "new",
    leadsCount: "",
    remainCount: "",
    time: undefined
  }
]);

function changeStage() {
  tableData.value.forEach(item => {
    item.time = undefined;
  });
  defaultFind();
}
function findTime(row, index) {
  loading.value = true;
  let data = {
    stage: activeStage.value,
    tag: row.name,
    duration: row.time
  };
  getSelfPoolApi(data)
    .then(({ data }: { data: any }) => {
      tableData.value[index].remainCount = data.remainCount;
      tableData.value[index].leadsCount = data.leadsCount;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

function defaultFind() {
  loading.value = true;
  let data = {
    stage: activeStage.value,
    duration: 0,
    tag: "pay"
  };
  Promise.all([getSelfPoolApi(data), getSelfPoolApi({ ...data, tag: "new" })])
    .then((res: any) => {
      res.forEach((item, index) => {
        tableData.value[index].remainCount = item.data.remainCount;
        tableData.value[index].leadsCount = item.data.leadsCount;
      });
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

defaultFind();
</script>

<template>
  <div v-loading="loading">
    <el-tabs v-model="activeStage" type="card" @tab-change="changeStage">
      <el-tab-pane label="小学" name="小学" />
      <el-tab-pane label="初中" name="初中" />
      <el-tab-pane label="高中" name="高中" />
    </el-tabs>
    <div class="d-cont pad20">
      <div element-loading-text="拼命加载中..." class="tableBox">
        <el-table
          ref="tableBox"
          stripe
          :data="tableData"
          highlight-current-row
          :border="true"
        >
          <el-table-column prop="clueType" label="线索类型" />
          <el-table-column prop="leadsCount" label="当日领取量" />
          <el-table-column prop="remainCount" label="剩余线索量" />
          <el-table-column label="操作" min-width="160">
            <template v-slot="scope">
              近
              <el-input-number
                v-model="scope.row.time"
                :controls="false"
                :min="0.1"
                :precision="1"
              />
              <span style="padding-right: 20px">小时</span>
              <el-button
                type="primary"
                link
                @click="findTime(scope.row, scope.$index)"
              >
                查询
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
