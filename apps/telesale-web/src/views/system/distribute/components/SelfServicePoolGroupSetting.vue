<!--
 * @Date         : 2025-04-01 14:37:16
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import {
  getSelfServicePoolGroupApi,
  saveSelfServicePoolGroupApi
} from "/@/api/system/distribute";
import getHistoryMetric from "/@/utils/asyn/getHistoryMetric";
import { storageLocal } from "/@/utils/storage";
import PoolGroupSettingDrawer from "../drawer/PoolGroupSettingDrawer.vue";

const localKey = "selfServicePool";

const drawerLocal = storageLocal.getItem(localKey) || {};

const loading = ref<boolean>(false);
const drawerData = ref(drawerLocal);
const drawer = ref<boolean>(!!drawerLocal.groupId);
const ruleFormRef = ref<FormInstance>();
const formSet = ref({
  list: []
});

const validateLimit = (rule: any, value: number, callback: any, row) => {
  if (!value && value !== 0) {
    return callback(new Error("不能为空"));
  }
  if (row.paidLimit > row.dailyAllocate) {
    callback(new Error("不得超过每日线索分配量"));
  } else {
    callback();
  }
};

const getList = () => {
  loading.value = true;
  getSelfServicePoolGroupApi({ groupId: 0 })
    .then(({ data }: { data: any }) => {
      formSet.value.list = data.list;
    })
    .finally(() => {
      loading.value = false;
    });
};

const setting = (row: any) => {
  drawerData.value = row;
  // 刷新的时候保存原状态
  storageLocal.setItem(localKey, row);
  drawer.value = true;
};

const submit = () => {
  ruleFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      saveSelfServicePoolGroupApi({ list: formSet.value.list })
        .then(() => {
          getHistoryMetric();
          ElMessage.success("操作成功");
          getList();
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      ElMessage.warning("请检查数据是否填写正确");
    }
  });
};

const reset = () => {
  formSet.value.list.forEach(item => {
    item.dailyAllocate = 0;
    item.paidLimit = 0;
  });
  ruleFormRef.value?.clearValidate();
};

getList();
</script>

<template>
  <div v-loading="loading">
    <div class="tip">
      <el-icon :size="24">
        <IconifyIconOffline icon="information-line" />
      </el-icon>
      坐席每日自助线索池线索分配量将按照以下顺序取值：组、团、全局；若其中某一级的数值为0则会取下一级的数值，所有数值都为0时表示坐席可领的线索量为0。
    </div>
    <el-form ref="ruleFormRef" :model="formSet">
      <el-table
        class="d-table"
        :data="formSet.list"
        :border="true"
        highlight-current-row
      >
        <el-table-column property="name" label="团名称" />
        <el-table-column
          property="dailyAllocate"
          label="每日线索分配量"
          width="300px"
        >
          <template #default="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.dailyAllocate'"
              :rules="{
                required: true,
                message: '不能为空',
                trigger: 'blur'
              }"
            >
              <el-input-number
                v-model="scope.row.dailyAllocate"
                controls-position="right"
                :precision="0"
                :step="1"
                :min="0"
                :max="999999"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          property="paidLimit"
          label="每日付费线索领取上限"
          width="300px"
        >
          <template #default="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.paidLimit'"
              :rules="{
                trigger: 'blur',
                validator: (rule: any, value: number, callback: any) => validateLimit(
                  rule,
                  value,
                  callback,
                  scope.row
                )
              }"
            >
              <el-input-number
                v-model="scope.row.paidLimit"
                controls-position="right"
                :precision="0"
                :step="1"
                :min="0"
                :max="999999"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button link type="primary" @click="setting(scope.row)">
              设置小组分配量
            </el-button>
          </template>
        </el-table-column>
        <template #empty><el-empty description="暂无数据" /></template>
      </el-table>
      <div
        class="text-center"
        style="margin-top: 20px"
        v-if="formSet.list.length > 0"
      >
        <el-button @click="reset">重置</el-button>
        <el-button type="primary" @click="submit">确定</el-button>
      </div>
    </el-form>
    <PoolGroupSettingDrawer
      v-model:drawer="drawer"
      :data="drawerData"
      :localKey="localKey"
      v-if="drawer"
    />
  </div>
</template>
<style scoped lang="scss">
.tip {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  font-weight: bold;
  background-color: #eaf5ff;
  margin-bottom: 20px;
  gap: 6px;
}

.d-table {
  :deep(.el-form-item) {
    margin-bottom: 0;
    .el-form-item__content {
      display: block;
    }
  }
  :deep(td) {
    padding: 0;
    .cell {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
