<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { getDistribute, setDistribute } from "/@/api/system";
import getHistoryMetric from "/@/utils/asyn/getHistoryMetric";
import HelpHintForm from "/@/components/HelpHintForm/index.vue";
import { useCueDesc } from "/@/hooks/business/useCueDesc";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import {
  getExperimentGroupApi,
  getExperimentGroupStatisticsApi,
  saveExperimentGroupApi
} from "/@/api/system/distribute";
import { cloneDeep } from "lodash-es";

const { classObj, getClass } = useCueDesc();
const { jobAllAgentList } = storeToRefs(useUserStore());

const agentData = computed(() => {
  return jobAllAgentList.value.filter(item => item.stage === "初中");
});

let rerferList = [
  {
    key: "aClass",
    bigKey: "A"
  },
  {
    key: "bClass",
    bigKey: "B"
  },
  {
    key: "cClass",
    bigKey: "C"
  },
  {
    key: "dClass",
    bigKey: "D"
  },
  {
    key: "eClass",
    bigKey: "E"
  }
];

function objChange(val) {
  let obj = {};
  rerferList.forEach(item => {
    obj[item.key] = val;
  });
  return obj;
}

const loading = ref(false);
const clueCount = ref({
  leadsCount: undefined,
  remainCount: undefined,
  tagLeads: undefined,
  tagRemain: undefined
});
const form: any = reactive({
  allocateThreshold: undefined,
  enable: false,
  workers: [],
  ...objChange(undefined)
});
const activeStage = ref("初中");

let ruleCheck = (rule, value, callback) => {
  if (value === undefined) {
    return callback(new Error("不能为空"));
  }
  let sum =
    Number(form.aClass || 0) +
    Number(form.bClass || 0) +
    Number(form.cClass || 0) +
    Number(form.dClass || 0) +
    Number(form.eClass || 0);
  if (sum !== 100) {
    return callback(new Error("线索比例设置之和必须等于100"));
  }
  return callback();
};

const formRef = ref<FormInstance>();
const rules = reactive<FormRules>({
  allocateThreshold: [
    {
      required: true,
      message: "请输入线索分配量",
      trigger: "blur"
    }
  ],
  workers: [
    {
      required: true,
      message: "请选择坐席",
      trigger: "blur"
    }
  ],
  ...objChange([{ required: true, validator: ruleCheck, trigger: "blur" }])
});

function mathHandle(val) {
  return Math.round((val * form.allocateThreshold) / 100);
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl || loading.value) return;

  await formEl.validate(valid => {
    if (valid) {
      let sum =
        mathHandle(form.aClass) +
        mathHandle(form.bClass) +
        mathHandle(form.cClass) +
        mathHandle(form.dClass) +
        mathHandle(form.eClass);
      if (form.allocateThreshold > sum) {
        ElMessage.warning("每类线索之和小于每日线索分配量，请重新调整比例");
        return;
      }
      save();
    } else {
      return false;
    }
  });
};

function save() {
  loading.value = true;
  const params = {
    allocateThreshold: form.allocateThreshold,
    enable: form.enable,
    workers: form.workers,
    rates: rerferList.map(item => {
      return {
        tag: item.bigKey,
        rate: form[item.key]
      };
    })
  };
  saveExperimentGroupApi(params)
    .then(() => {
      getHistoryMetric();
      ElMessage.success("已保存");
      getData();
    })
    .catch(() => {
      loading.value = false;
    });
}
function getData() {
  formRef.value.resetFields();
  loading.value = true;
  getClass(activeStage.value);
  getExperimentGroupApi()
    .then(({ data }) => {
      data.rates.forEach(item => {
        let word = item.tag.toLowerCase();
        form[word + "Class"] = item.rate;
      });
      form.allocateThreshold = data.allocateThreshold;
      form.enable = data.enable;
      form.workers = data.workers;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

const getClueCount = () => {
  getExperimentGroupStatisticsApi().then(res => {
    clueCount.value = res.data;
  });
};

onMounted(() => {
  getData();
  getClueCount();
});
</script>

<template>
  <div v-loading="loading">
    <el-form :model="form" ref="formRef" :rules="rules" :label-width="150">
      <el-form-item label="是否开启测试公海池" prop="enable" required>
        <el-switch
          v-model="form.enable"
          :active-value="true"
          :inactive-value="false"
        />
      </el-form-item>
      <template v-if="form.enable">
        <el-form-item label="坐席设置" prop="workers">
          <el-select-v2
            v-model="form.workers"
            filterable
            clearable
            multiple
            value-key="id"
            :options="agentData"
            placeholder="请选择坐席"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="每日线索分配量" prop="allocateThreshold">
          <el-input-number
            v-model="form.allocateThreshold"
            controls-position="right"
            :min="1"
            :step="1"
            step-strictly
          />
        </el-form-item>
        <el-form-item label="当日领取线索量">
          <span class="c-#e6a23c font-bold text-16px">
            {{ clueCount.leadsCount }}
          </span>
        </el-form-item>
        <el-form-item label="剩余线索量">
          <span class="c-#e6a23c font-bold text-16px">
            {{ clueCount.remainCount }}
          </span>
        </el-form-item>
        <div class="d-tit">线索分配比例设置</div>
        <el-form-item
          v-for="item in rerferList"
          :key="item.key"
          :prop="item.key"
        >
          <template #label>
            <HelpHintForm
              :content="classObj[item.key]"
              :label="item.bigKey + '类'"
            />
          </template>
          <el-input-number
            v-model="form[item.key]"
            controls-position="right"
            :step="1"
            :min="0"
            :max="100"
            step-strictly
          />
          （%）
        </el-form-item>
      </template>

      <el-form-item>
        <el-button type="primary" @click="submitForm(formRef)">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<style scoped lang="scss">
.d-tit {
  border-top: 1px solid #eee;
  margin-top: 10px;
  padding: 20px 0;
  font-weight: bold;
}
</style>
