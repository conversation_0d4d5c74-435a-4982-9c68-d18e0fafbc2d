<!--
 * @Date         : 2025-01-10 16:09:24
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts" name="distribute">
import { ref, watch } from "vue";
import { useUserStore } from "/@/store/modules/user";
import { usePermissionStore } from "/@/store/modules/permission";
import { storeToRefs } from "pinia";
import Distribute from "./Distribute.vue";
import GroupSetting from "./GroupSetting.vue";
import SelfServicePoolGlobal from "./SelfServicePoolGlobal.vue";
import SelfServicePoolGroupSetting from "./SelfServicePoolGroupSetting.vue";

const { authorizationMap } = useUserStore();
const usePermission = usePermissionStore();
const { cachePageList } = storeToRefs(usePermission);

watch(
  () => cachePageList.value,
  n => {
    if (!n.includes("distribute")) {
      localStorage.removeItem("selfServicePoolTab");
      localStorage.removeItem("selfServicePool");
    }
  },
  {
    deep: true,
    immediate: true
  }
);

const tabList = [
  {
    label: "全局分配设置",
    name: "1",
    auth: "telesale_admin_system_distribute_selfPool_global_clue"
  },
  {
    label: "按团/组设置线索分配量",
    name: "2",
    auth: "telesale_admin_system_distribute_selfPool_group_clue"
  }
];

const authTab = tabList.filter(item => authorizationMap.includes(item.auth));
const current = ref<string>(
  localStorage.getItem("selfServicePoolTab") || authTab[0]?.name
);

const isAuth = authTab.some(item => item.name === current.value);
if (!isAuth) {
  current.value = authTab[0]?.name || "";
}

const isGroup = authTab.some(item => item.name === "2");
if (!isGroup) {
  localStorage.removeItem("selfServicePoolTab");
  localStorage.removeItem("selfServicePool");
}

const changePage = (val: string) => {
  localStorage.setItem("selfServicePoolTab", val);
};
</script>

<template>
  <el-tabs type="card" v-model="current" @tab-change="changePage">
    <el-tab-pane
      v-for="item in authTab"
      :key="item.name"
      :label="item.label"
      :name="item.name"
    />
    <SelfServicePoolGlobal v-if="current === '1'" />
    <SelfServicePoolGroupSetting v-if="current === '2'" />
  </el-tabs>
</template>
<style scoped lang="scss"></style>
