<!--
 * @Date         : 2025-04-01 14:32:30
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<!--
 * @Date         : 2025-01-10 16:09:24
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref } from "vue";
import PoolSetting from "./PoolSetting.vue";

const tabValue = ref("初中");
const tabList = [
  {
    label: "小学",
    value: "小学"
  },
  {
    label: "初中",
    value: "初中"
  },
  {
    label: "高中",
    value: "高中"
  }
];
</script>

<template>
  <el-tabs v-model="tabValue" type="card">
    <el-tab-pane
      v-for="item in tabList"
      :key="item.value"
      :label="item.label"
      :name="item.value"
      lazy
    >
      <PoolSetting :stage="item.value" />
    </el-tab-pane>
  </el-tabs>
</template>
