<!--
 * @Date         : 2025-01-10 16:12:30
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import {
  saveSelfServicePool,
  getSelfServicePool,
  SelfServicePool
} from "/@/api/system/distribute";
import getHistoryMetric from "/@/utils/asyn/getHistoryMetric";

const props = defineProps<{
  stage: string;
}>();
const loading = ref<boolean>(false);
const formRef = ref<FormInstance | null>();
const form = ref<SelfServicePool>({
  stage: props.stage,
  dailyLimit: undefined,
  dailyPayLimit: undefined
});
const rules: FormRules = {
  dailyLimit: [
    {
      required: true,
      message: "请输入每日线索分配量",
      trigger: "blur"
    }
  ],
  dailyPayLimit: [
    {
      required: true,
      message: "请输入每日付费线索领取上限",
      trigger: "blur"
    },
    {
      validator: (rule, value, callback) => {
        if (value > form.value.dailyLimit) {
          callback(new Error("不得超过每日线索分配量"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ]
};

const setData = async () => {
  await formRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      saveSelfServicePool(form.value)
        .then(() => {
          getHistoryMetric();
          ElMessage.success("操作成功");
          getData();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

let getData = () => {
  loading.value = true;
  getSelfServicePool({ stage: props.stage })
    .then(({ data }) => {
      form.value = data;
    })
    .finally(() => {
      loading.value = false;
    });
};

getData();
</script>

<template>
  <div v-loading="loading">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-suffix="："
      :label-width="useAppStoreHook().device !== 'mobile' ? '200px' : ''"
      @submit.prevent
    >
      <el-form-item label="每日线索分配量" prop="dailyLimit">
        <el-input-number
          v-model="form.dailyLimit"
          :min="0"
          :max="99999999"
          :precision="0"
          placeholder="请输入每日线索分配量"
          clearable
          class="input-max"
        />
      </el-form-item>
      <el-form-item label="每日付费线索领取上限" prop="dailyPayLimit">
        <el-input-number
          v-model="form.dailyPayLimit"
          :min="0"
          :max="99999999"
          :precision="0"
          placeholder="请输入每日付费线索领取上限"
          clearable
          class="input-max"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="setData">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
