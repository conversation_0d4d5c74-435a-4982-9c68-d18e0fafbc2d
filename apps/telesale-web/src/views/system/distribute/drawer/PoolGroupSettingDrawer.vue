<!--
 * @Date         : 2025-04-01 16:54:38
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { useAppStoreHook } from "/@/store/modules/app";
import { ElMessage, FormInstance } from "element-plus";
import { storageLocal } from "/@/utils/storage";
import getHistoryMetric from "/@/utils/asyn/getHistoryMetric";
import {
  getSelfServicePoolGroupApi,
  saveSelfServicePoolGroupApi
} from "/@/api/system/distribute";

const props = withDefaults(
  defineProps<{
    drawer: boolean;
    data: any;
    localKey: string;
  }>(),
  {
    drawer: false
  }
);
const emits = defineEmits<{
  (e: "update:drawer", val: boolean): void;
  (e: "onSearch"): void;
}>();

const isDrawer = computed({
  get() {
    return props.drawer;
  },
  set(val: boolean) {
    emits("update:drawer", val);
  }
});
const { device } = useAppStoreHook();
const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance>();
const formSet = ref({
  list: []
});

const validateLimit = (rule: any, value: number, callback: any, row) => {
  if (!value && value !== 0) {
    return callback(new Error("不能为空"));
  }
  if (row.paidLimit > row.dailyAllocate) {
    callback(new Error("不得超过每日线索分配量"));
  } else {
    callback();
  }
};

const getInfo = () => {
  const { groupId } = props.data;
  if (!groupId) return;
  loading.value = true;
  getSelfServicePoolGroupApi({ groupId })
    .then(({ data }: { data: any }) => {
      formSet.value.list = data.list;
    })
    .finally(() => {
      loading.value = false;
    });
};

const submit = () => {
  ruleFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      saveSelfServicePoolGroupApi({ list: formSet.value.list })
        .then(() => {
          ElMessage.success("操作成功");
          getHistoryMetric();
          emits("onSearch");
        })
        .finally(() => {
          loading.value = false;
          close();
        });
    } else {
      ElMessage.warning("请检查数据是否填写正确");
    }
  });
};

const reset = () => {
  formSet.value.list.forEach(item => {
    item.dailyAllocate = 0;
    item.paidLimit = 0;
  });
  ruleFormRef.value?.clearValidate();
};

const close = () => {
  storageLocal.removeItem(props.localKey);
  emits("update:drawer", false);
};
onMounted(() => {
  getInfo();
});
</script>

<template>
  <el-drawer
    v-model="isDrawer"
    close-on-click-modal
    @close="close"
    :size="device === 'mobile' ? '100%' : 700"
  >
    <template #header>
      <div class="title">
        <span>{{ props.data.name }}</span>
        <IconifyIconOffline icon="arrow-right" />
        <span>小组线索分配设置</span>
      </div>
    </template>

    <el-form ref="ruleFormRef" :model="formSet" v-loading="loading">
      <el-table
        class="d-table"
        :data="formSet.list"
        :border="true"
        highlight-current-row
      >
        <el-table-column property="name" width="250px" label="小组名称" />
        <el-table-column
          property="dailyAllocate"
          label="每日线索分配量"
          width="200px"
        >
          <template #default="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.dailyAllocate'"
              :rules="{
                required: true,
                message: '不能为空',
                trigger: 'blur'
              }"
            >
              <el-input-number
                v-model="scope.row.dailyAllocate"
                controls-position="right"
                :precision="0"
                :step="1"
                :min="0"
                :max="999999"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          property="paidLimit"
          label="每日付费线索领取上限"
          width="200px"
        >
          <template #default="scope">
            <el-form-item
              :prop="'list.' + scope.$index + '.paidLimit'"
              :rules="{
                trigger: 'blur',
                validator: (rule: any, value: number, callback: any) => validateLimit(
                  rule,
                  value,
                  callback,
                  scope.row
                )
              }"
            >
              <el-input-number
                v-model="scope.row.paidLimit"
                controls-position="right"
                :precision="0"
                :step="1"
                :min="0"
                :max="999999"
              />
            </el-form-item>
          </template>
        </el-table-column>
        <template #empty><el-empty description="暂无数据" /></template>
      </el-table>
    </el-form>
    <template #footer>
      <div
        class="text-center"
        style="margin-top: 20px"
        v-if="formSet.list.length > 0"
      >
        <el-button @click="reset" :loading="loading">重置</el-button>
        <el-button type="primary" @click="submit" :loading="loading">
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<style scoped lang="scss">
.title {
  display: flex;
  align-items: center;
}
.d-table {
  width: 650px;
  :deep(.el-form-item) {
    margin-bottom: 0;
    .el-form-item__content {
      display: block;
    }
  }
  :deep(td) {
    padding: 0;
    .cell {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
