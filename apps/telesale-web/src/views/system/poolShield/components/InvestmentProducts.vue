<!--
 * @Date         : 2025-06-19 18:04:54
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<!--
 * @Date         : 2025-06-19 17:15:29
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { ref } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { getProfileApi, setProfileApi } from "/@/api/active";
import { Delete, Plus } from "@element-plus/icons-vue";

const loading = ref(false);
const form = ref({
  state: undefined,
  good_id: [] as string[]
});

const changeState = (): Promise<boolean> => {
  const newState = form.value.state === 1 ? 2 : 1;
  const actionText = newState === 1 ? "开启" : "关闭";

  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(`是否${actionText}？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(async () => {
        await onSubmit();
        ElMessage.success(`已${actionText}规则`);
        resolve(true);
      })
      .catch(() => {
        reject(false);
      });
  });
};

const onSubmit = async () => {
  loading.value = true;
  setProfileApi({
    name: "user_buy_investment_products",
    value: form.value,
    note: "用户购买过流量投流商品"
  })
    .then(() => {
      ElMessage.success("操作成功");
      getData();
    })
    .finally(() => {
      loading.value = false;
    });
};

// 添加商品ID
const addGoodId = () => {
  if (form.value.good_id.length < 20) {
    form.value.good_id.push("");
  }
};

// 删除商品ID
const removeGoodId = (index: number) => {
  if (form.value.good_id.length > 1) {
    form.value.good_id.splice(index, 1);
  }
};

const getData = () => {
  loading.value = true;
  getProfileApi({ name: "user_buy_investment_products" })
    .then(({ data }: { data: any }) => {
      form.value = data.value || { state: undefined, good_id: [""] };
      // 确保至少有一个空的商品ID输入框
      if (!form.value.good_id || form.value.good_id.length === 0) {
        form.value.good_id = [""];
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 初始化数据
getData();
</script>

<template>
  <div v-loading="loading">
    <div class="g-wrapper-no">
      用户购买过流量投流商品
      <el-switch
        v-model="form.state"
        :active-value="1"
        :inactive-value="2"
        :before-change="changeState"
      />
    </div>
    <el-row :gutter="20">
      <el-col :span="4" :offset="0" />
      <el-col :span="16" :offset="0">
        <el-form :model="form">
          <el-form-item
            v-for="(_item, index) in form.good_id"
            :key="index"
            label="商品ID"
            :prop="`good_id.${index}`"
            :rules="[{ required: true, message: '请输入商品ID' }]"
          >
            <div class="input-group">
              <el-input
                v-model="form.good_id[index]"
                placeholder="请输入商品ID"
              />
              <el-form-item>
                <el-button
                  type="primary"
                  :icon="Plus"
                  @click="addGoodId"
                  v-if="
                    form.good_id.length - 1 === index &&
                    form.good_id.length < 20
                  "
                />
              </el-form-item>
              <el-button
                v-if="form.good_id.length > 1"
                type="danger"
                :icon="Delete"
                @click="removeGoodId(index)"
              />
            </div>
          </el-form-item>

          <!-- 添加商品ID按钮 -->

          <div>
            <el-form-item>
              <el-button type="primary" @click="onSubmit">保存</el-button>
            </el-form-item>
          </div>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
.input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.input-group .el-input {
  flex: 1;
}

.ml-2 {
  margin-left: 8px;
}

.text-gray-500 {
  color: #6b7280;
  font-size: 14px;
}
</style>
