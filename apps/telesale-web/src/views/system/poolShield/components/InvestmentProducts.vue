<!--
 * @Date         : 2025-06-19 18:04:54
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<!--
 * @Date         : 2025-06-19 17:15:29
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { ref, computed } from "vue";
import { ElMessageBox, ElMessage, FormInstance, FormRules } from "element-plus";
import { getProfileApi, setProfileApi } from "/@/api/active";
import { Delete, Plus } from "@element-plus/icons-vue";

const loading = ref(false);
const formRef = ref<FormInstance>();

const form = ref({
  state: undefined,
  good_id: [] as string[]
});

// 动态表单验证规则
const rules = computed<FormRules>(() => {
  const isEnabled = form.value.state === 1;
  const dynamicRules: FormRules = {};

  // 为每个商品ID添加验证规则
  form.value.good_id.forEach((_, index) => {
    dynamicRules[`good_id.${index}`] = [
      {
        required: isEnabled,
        message: "请输入商品ID",
        trigger: "blur"
      },
      {
        min: 1,
        message: "商品ID不能为空",
        trigger: "blur"
      }
    ];
  });

  return dynamicRules;
});

const changeState = (): Promise<boolean> => {
  const actionText = form.value.state === 2 ? "开启" : "关闭";

  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(`是否${actionText}？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        resolve(true);
        nextTick(() => {
          onSubmit();
        });
      })
      .catch(() => {
        reject(false);
      });
  });
};

const onSubmit = async () => {
  // 如果开关开启，需要验证表单
  if (form.value.state === 1) {
    if (!formRef.value) return;

    // 检查是否至少有一个商品ID
    if (
      !form.value.good_id.length ||
      form.value.good_id.every(id => !id.trim())
    ) {
      ElMessage.error("开启规则时至少需要一个商品ID");
      return;
    }

    try {
      await formRef.value.validate();
    } catch (error) {
      ElMessage.error("请填写完整的商品ID信息");
      return;
    }
  }

  loading.value = true;
  setProfileApi({
    name: "user_buy_investment_products",
    value: form.value,
    note: "用户购买过流量投流商品"
  })
    .then(() => {
      ElMessage.success("操作成功");
      getData();
    })
    .finally(() => {
      loading.value = false;
    });
};

// 添加商品ID
const addGoodId = () => {
  if (form.value.good_id.length < 20) {
    form.value.good_id.push("");
  }
};

// 删除商品ID
const removeGoodId = (index: number) => {
  if (form.value.good_id.length > 1) {
    form.value.good_id.splice(index, 1);
  }
};

const getData = () => {
  loading.value = true;
  getProfileApi({ name: "user_buy_investment_products" })
    .then(({ data }: { data: any }) => {
      form.value = data.value || { state: undefined, good_id: [""] };
      // 确保至少有一个空的商品ID输入框
      if (!form.value.good_id || form.value.good_id.length === 0) {
        form.value.good_id = [""];
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 初始化数据
getData();
</script>

<template>
  <div v-loading="loading">
    <div class="g-wrapper-no">
      用户购买过流量投流商品
      <el-switch
        v-model="form.state"
        :active-value="1"
        :inactive-value="2"
        :before-change="changeState"
      />
    </div>
    <el-row :gutter="20">
      <el-col :span="4" :offset="0" />
      <el-col :span="16" :offset="0">
        <el-form ref="formRef" :model="form" :rules="rules">
          <el-form-item
            v-for="(_item, index) in form.good_id"
            :key="index"
            label="商品ID"
            :prop="`good_id.${index}`"
          >
            <div class="input-group">
              <el-input
                v-model="form.good_id[index]"
                placeholder="请输入商品ID"
              />
              <el-button
                v-if="
                  form.good_id.length - 1 === index && form.good_id.length < 20
                "
                type="primary"
                :icon="Plus"
                circle
                @click="addGoodId"
              />
              <el-button
                v-if="form.good_id.length > 1 && form.state === 1"
                type="danger"
                :icon="Delete"
                circle
                @click="removeGoodId(index)"
              />
            </div>
          </el-form-item>

          <!-- 添加商品ID按钮 -->

          <div>
            <el-form-item>
              <el-button type="primary" @click="onSubmit" :loading="loading">
                保存
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped>
.input-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.input-group .el-input {
  flex: 1;
}

.ml-2 {
  margin-left: 8px;
}

.text-gray-500 {
  color: #6b7280;
  font-size: 14px;
}
</style>
