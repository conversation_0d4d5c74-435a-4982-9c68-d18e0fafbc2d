<!--
 * @Date         : 2025-06-19 17:15:29
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
const form = ref({
  state: 1,
  day: 0,
  frequency: 0
});

const changeState = () => {
  console.log("1");
};

const onSubmit = () => {
  console.log("for", form.value);
};
</script>

<template>
  <div>
    <div class="g-wrapper-no">
      短期内多次回流的客户
      <el-switch
        v-model="form.state"
        :active-value="1"
        :inactive-value="2"
        @change="changeState"
      />
    </div>
    <el-row :gutter="20">
      <el-col :span="4" :offset="0" />
      <el-col :span="16" :offset="0">
        <el-form :model="form" ref="form" label-width="80px" inline>
          <el-form-item>
            <el-input-number
              v-model="form.day"
              :min="0"
              :max="365"
              :precision="0"
            />
            <span class="mx-4px">天内回流</span>
          </el-form-item>
          <el-form-item>
            <el-input-number
              v-model="form.frequency"
              :min="0"
              :max="99999999"
              :precision="0"
            />
            <span class="ml-4px">次的线索不允许进入公海池</span>
          </el-form-item>
          <div>
            <el-form-item>
              <el-button type="primary" @click="onSubmit">保存</el-button>
            </el-form-item>
          </div>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped></style>
