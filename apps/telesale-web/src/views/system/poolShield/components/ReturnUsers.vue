<!--
 * @Date         : 2025-06-19 17:15:29
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { getProfileApi, setProfileApi } from "/@/api/active";

const loading = ref(false);
const form = ref({
  state: undefined,
  day: undefined,
  frequency: undefined
});

const changeState = (): Promise<boolean> => {
  // 计算即将切换到的状态
  const actionText = form.value.state === 2 ? "开启" : "关闭";

  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(`是否${actionText}？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(async () => {
        resolve(true);
        nextTick(() => {
          onSubmit();
        });
      })
      .catch(() => {
        reject(false);
      });
  });
};

const onSubmit = async () => {
  loading.value = true;
  setProfileApi({
    name: "customers_returning",
    value: form.value,
    note: "短期内多次回流的客户"
  })
    .then(() => {
      ElMessage.success("操作成功");
      getData();
    })
    .finally(() => {
      loading.value = false;
    });
};

const getData = () => {
  loading.value = true;
  getProfileApi({ name: "customers_returning" })
    .then(({ data }: { data: any }) => {
      form.value = data.value;
    })
    .finally(() => {
      loading.value = false;
    });
};
getData();
</script>

<template>
  <div v-loading="loading">
    <div class="g-wrapper-no">
      短期内多次回流的客户
      <el-switch
        v-model="form.state"
        :active-value="1"
        :inactive-value="2"
        :before-change="changeState"
      />
    </div>
    <el-row :gutter="20">
      <el-col :span="4" :offset="0" />
      <el-col :span="16" :offset="0">
        <el-form :model="form" inline>
          <el-form-item prop="day">
            <el-input-number
              v-model="form.day"
              :min="0"
              :max="365"
              :precision="0"
            />
            <span class="mx-4px">天内回流</span>
          </el-form-item>
          <el-form-item prop="frequency">
            <el-input-number
              v-model="form.frequency"
              :min="0"
              :max="99999999"
              :precision="0"
            />
            <span class="ml-4px">次的线索不允许进入公海池</span>
          </el-form-item>
          <div>
            <el-form-item>
              <el-button type="primary" @click="onSubmit">保存</el-button>
            </el-form-item>
          </div>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<style lang="scss" scoped></style>
