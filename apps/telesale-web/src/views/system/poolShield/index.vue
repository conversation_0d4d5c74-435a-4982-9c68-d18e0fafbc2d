<script setup lang="ts" name="qualityCheckDurationSet">
import { ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { getH5SettingApi, updateH5SettingApi } from "/@/api/warm";
import ReturnUsers from "./components/ReturnUsers.vue";

const loading = ref<boolean>(false);
const formRef = ref<FormInstance | null>();
const form = ref({
  qq: "",
  guide: "",
  introduce: ""
});
const rules: FormRules = {
  qq: [
    {
      required: true,
      message: "请输入QQ号",
      trigger: "blur"
    },
    {
      pattern: /^\d{5,11}$/,
      message: "QQ号格式不正确",
      trigger: "blur"
    }
  ],
  guide: [
    {
      required: true,
      message: "请输入评论引导文案",
      trigger: "blur"
    }
  ],
  introduce: [
    {
      required: true,
      message: "请输入暖暖介绍文案",
      trigger: "blur"
    }
  ]
};

const setData = async () => {
  await formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      updateH5SettingApi(form.value)
        .then(() => {
          ElMessage.success("操作成功");
          getData();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

let getData = () => {
  loading.value = true;
  getH5SettingApi()
    .then(({ data }: { data: any }) => {
      form.value = data;
    })
    .finally(() => {
      loading.value = false;
    });
};

getData();
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <ReturnUsers />
    </el-card>
  </div>
</template>
