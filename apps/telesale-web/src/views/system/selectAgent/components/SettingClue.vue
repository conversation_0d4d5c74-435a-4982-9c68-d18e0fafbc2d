<!--
 * @Date         : 2025-03-18 18:06:08
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import ClueData from "./ClueData.vue";

const props = defineProps<{
  pushType: string;
}>();

const stage = ref("初中");

const stageList = [
  {
    value: "小学",
    label: "小学"
  },
  {
    value: "初中",
    label: "初中"
  },
  {
    value: "高中",
    label: "高中"
  }
];
</script>

<template>
  <div>
    <el-tabs
      v-model="stage"
      type="card"
      tab-position="top"
      class="radio-like-tabs"
    >
      <el-tab-pane
        v-for="item in stageList"
        :key="item.value"
        :label="item.label"
        :name="item.value"
        lazy
      >
        <ClueData :stage="stage" :pushType="props.pushType" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="scss" scoped>
.radio-like-tabs {
  // 去除 el-tabs 默认的边框和间距
  .el-tabs__nav-wrap::after {
    background-color: transparent;
  }
  .el-tabs__nav-scroll {
    padding: 0;
  }
  // 调整 el-tab-pane 标签样式
  :deep(.el-tabs__item) {
    // 基础样式，模拟 el-radio-button 外观
    border: 1px solid #dcdfe6;
    color: #606266;
    margin-right: -1px;
    padding: 9px 15px;
    font-size: 14px;
    border-radius: 0;
    cursor: pointer;
    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
    &:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
    // 选中状态样式
    // 鼠标悬停样式
    &:hover {
      color: #409eff;
    }
    &.is-active {
      background-color: #409eff;
      border-color: #409eff;
      color: #fff;
      z-index: 1;
      &:hover {
        color: #fff;
      }
    }
  }
}
</style>
