<!--
 * @Date         : 2025-03-18 18:10:49
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { storeToRefs } from "pinia";
import UploadAgent from "../dialog/UploadAgent.vue";
import { getWokerSelectApi } from "/@/api/system/workerSelect";
import { useUserStore } from "/@/store/modules/user";
import dayjs from "dayjs";

const props = defineProps<{
  stage: string;
  pushType: string;
}>();

const { allAgentObj } = storeToRefs(useUserStore());
const loading = ref(false);
const isModal = ref(false);
const workerInfo = ref();

const search = () => {
  loading.value = true;
  getWokerSelectApi({ stage: props.stage, pushType: props.pushType })
    .then(res => {
      workerInfo.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
};

search();
</script>

<template>
  <el-card>
    <el-button type="primary" class="g-margin-r-10" @click="isModal = true">
      上传坐席名单
    </el-button>
    <div class="text-12px c-blueGray my-10px">
      新上传的名单将于上传后第二天0点开始替换原先的版本生效，上传的名单顺序将作为分配线索的顺序
    </div>
    <div class="mt-20px font-bold" v-if="workerInfo?.pendingWorkers?.length">
      待生效版本，于{{
        workerInfo?.createdAt
          ? dayjs(workerInfo?.createdAt).format("YYYY-MM-DD HH:mm:ss")
          : ""
      }}上传
      <div class="flex gap-10px flex-wrap my-10px">
        <el-tag v-for="item in workerInfo?.pendingWorkers" :key="item">
          {{ allAgentObj[item].name }}
        </el-tag>
      </div>
    </div>
    <div class="mt-40px font-bold">
      线上生效版本（分配顺序为名字展示顺序,由左至右由上至下）
      <div class="flex gap-10px flex-wrap my-10px">
        <el-tag v-for="item in workerInfo?.workers" :key="item">
          {{ allAgentObj[item].name }}
        </el-tag>
      </div>
    </div>

    <UploadAgent
      v-if="isModal"
      v-model:value="isModal"
      :stage="props.stage"
      :pushType="props.pushType"
      @onSearch="search"
    />
  </el-card>
</template>
