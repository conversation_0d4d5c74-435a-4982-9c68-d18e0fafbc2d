<!--
 * @Date         : 2024-10-16 10:32:16
 * @Description  : 企微备注入库上限
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts" name="qualityCheckDurationSet">
import { ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { getProfileApi, setProfileApi } from "/@/api/active";

const loading = ref<boolean>(false);
const formRef = ref<FormInstance | null>();
const form = ref({
  maxImport: undefined
});
const rules: FormRules = {
  maxImport: [
    {
      required: true,
      message: "请输入企微备注入库上限",
      trigger: "blur"
    }
  ]
};

const setData = async () => {
  await formRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      const params = {
        name: "import_by_phone",
        value: form.value,
        note: "单日单坐席备注手机号拉取入库上限配置"
      };
      setProfileApi(params)
        .then(() => {
          ElMessage.success("操作成功");
          getData();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

let getData = () => {
  loading.value = true;
  getProfileApi({ name: "import_by_phone" })
    .then(({ data }) => {
      form.value = data.value as { maxImport: number };
    })
    .finally(() => {
      loading.value = false;
    });
};

getData();
</script>

<template>
  <div v-loading="loading">
    <div class="d-tip-box">
      <IconifyIconOffline icon="information-line" style="font-size: 25px" />
      <span>
        该页面用于设置坐席每天可以通过在企业微信上备注手机号拉取用户入库的数量上限（入库的前提是符合对应的标签且在CRM系统没有坐席归属）
      </span>
    </div>
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-suffix="："
      :label-width="useAppStoreHook().device !== 'mobile' ? '300px' : ''"
      @submit.prevent
    >
      <el-form-item label="企微备注入库上限" prop="maxImport">
        <el-input-number
          v-model="form.maxImport"
          :min="0"
          :max="99999999"
          :precision="0"
          placeholder="请输入企微备注入库上限"
          clearable
          class="input-max"
        />
        <span style="margin-left: 10px">条</span>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="setData">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.d-tip-box {
  background-color: #eaf5ff;
  font-weight: bold;
  padding: 10px 20px;
  margin-bottom: 20px;
  font-size: 15px;
  line-height: 1.7;
}
</style>
