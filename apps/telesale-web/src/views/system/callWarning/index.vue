<!--
 * @Date         : 2024-07-12 10:41:53
 * @Description  : 外呼行为预警提醒
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts" name="qualityCheckDurationSet">
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import {
  getCallWarningApi,
  setCallWarningApi
} from "/@/api/system/callWarning";
import { Plus, Delete } from "@element-plus/icons-vue";
import { cloneDeep } from "lodash-es";
import dayjs from "dayjs";
import findOrganizationMath from "/@/utils/asyn/findOrganization";

const orgData = ref([]);
const loading = ref<boolean>(false);
const formRef = ref<FormInstance | null>();
const form = ref({
  alarmRange: [],
  alarmNode: [],
  notifyAt: [],
  interval: 0
});
const ruleTime = ref({
  activeTime: "",
  updateAt: ""
});
const rules: FormRules = {
  alarmNode: [
    {
      required: true,
      message: "请选择启动提醒的架构",
      trigger: "blur"
    }
  ],
  interval: [
    {
      required: true,
      message: "请输外呼间隔时长",
      type: "number",
      trigger: "blur"
    }
  ]
};

// 毫秒转化秒函数
const msToSeconds = (ms: number) => {
  return Math.floor(ms / 1000);
};

// 获取当天时分秒的时间戳
const getTimeSeconds = (ms: number) => {
  return ms - dayjs().startOf("d").valueOf() - 28800000;
};

// 获取时间整天的时间错
const getDaySeconds = (ms: number) => {
  return ms + dayjs().startOf("d").valueOf() + 28800000;
};

function removeFifthLevel(tree, level) {
  tree?.forEach(child => {
    if (child?.children) {
      if (level < 4) {
        removeFifthLevel(child.children, level + 1);
      } else {
        child.children = null;
      }
    }
  });
  return tree;
}

const getOrgList = async () => {
  orgData.value = await findOrganizationMath();
  orgData.value = removeFifthLevel(cloneDeep(orgData.value), 1);
};

const addAlarmRange = () => {
  form.value.alarmRange.push({
    time: undefined
  });
};
const removeAlarmRange = (index: number) => {
  form.value.alarmRange.splice(index, 1);
};
const addNotifyAt = () => {
  form.value.notifyAt.push({
    time: undefined
  });
};
const removeNotifyAt = (index: number) => {
  form.value.notifyAt.splice(index, 1);
};

const setData = async () => {
  await formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      const params: any = cloneDeep(form.value);
      params.alarmRange = params.alarmRange.map(item => {
        return {
          startAt: msToSeconds(getTimeSeconds(item.time[0])),
          endAt: msToSeconds(getTimeSeconds(item.time[1]))
        };
      });
      params.notifyAt = params.notifyAt.map(item => {
        return msToSeconds(getTimeSeconds(item.time));
      });

      delete params.lastUpdatedAt;

      setCallWarningApi(params)
        .then(() => {
          ElMessage.success("操作成功");
          getData();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

let getData = () => {
  loading.value = true;
  getCallWarningApi()
    .then(res => {
      const data: any = res.data;

      data.alarmRange = data?.alarmRange.map(item => {
        return {
          time: [
            getDaySeconds(item.startAt * 1000),
            getDaySeconds(item.endAt * 1000)
          ]
        };
      });
      data.notifyAt = data?.notifyAt.map(item => {
        return {
          time: getDaySeconds(item * 1000)
        };
      });

      if (data?.notifyAt.length === 0) {
        data.notifyAt = [
          {
            item: undefined
          }
        ];
      }
      if (data?.alarmRange.length === 0) {
        data.alarmRange = [
          {
            item: undefined
          }
        ];
      }

      form.value = data;
      if (data.lastUpdatedAt) {
        ruleTime.value.updateAt = dayjs(data.lastUpdatedAt * 1000).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        const nextDay = dayjs(ruleTime.value.updateAt).startOf("d").add(1, "d");
        ruleTime.value.activeTime = nextDay.format("YYYY-MM-DD HH:mm:ss");
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

getOrgList();
getData();
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <div
        class="bg-#eaf5ff w-100% p-10px mb-20px"
        v-if="dayjs().isBefore(ruleTime.activeTime)"
      >
        当前规则版本于
        {{ ruleTime.updateAt }}
        修改，将于
        {{ ruleTime.activeTime }}
        日生效
      </div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-suffix="："
        :label-width="useAppStoreHook().device !== 'mobile' ? '300px' : ''"
        style="width: 80%"
      >
        <el-form-item label="启动提醒的架构" prop="alarmNode">
          <el-cascader
            v-model="form.alarmNode"
            :options="orgData"
            :props="{
              value: 'id',
              label: 'name',
              checkStrictly: true,
              multiple: true,
              emitPath: false
            }"
            placeholder="请选择小组"
            filterable
            clearable
            :show-all-levels="false"
            style="width: 100%"
            class="input-full"
          />
        </el-form-item>
        <el-form-item label="监听时间段（每天）" required>
          <div>
            <el-form-item
              class="mb-20px!"
              v-for="(item, index) in form.alarmRange"
              :key="index"
              :prop="'alarmRange.' + index + '.time'"
              :rules="[
                {
                  required: true,
                  message: '请选择监听时间断',
                  trgigger: 'blur',
                  type: 'array'
                }
              ]"
            >
              <el-time-picker
                v-model="item.time"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="x"
                class="mr-10px"
              />
              <div class="w-100px">
                <el-button
                  v-if="index === form.alarmRange.length - 1"
                  type="primary"
                  :icon="Plus"
                  circle
                  @click="addAlarmRange"
                />
                <el-button
                  v-if="form.alarmRange.length > 1"
                  type="danger"
                  :icon="Delete"
                  circle
                  @click="removeAlarmRange(index)"
                />
              </div>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="提醒时间（每天）" required>
          <div>
            <el-form-item
              class="mb-20px!"
              v-for="(item, index) in form.notifyAt"
              :key="index"
              :prop="`notifyAt.${index}.time`"
              :rules="[
                {
                  required: true,
                  message: '请选择提醒时间',
                  trgigger: 'blur',
                  type: 'number'
                }
              ]"
            >
              <el-time-picker
                v-model="item.time"
                placeholder="请选择提醒时间"
                value-format="x"
                format="HH:mm"
                class="mr-10px"
              />
              <div class="w-100px">
                <el-button
                  v-if="index === form.notifyAt.length - 1"
                  type="primary"
                  :icon="Plus"
                  circle
                  @click="addNotifyAt"
                />
                <el-button
                  v-if="form.notifyAt.length > 1"
                  type="danger"
                  :icon="Delete"
                  circle
                  @click="removeNotifyAt(index)"
                />
              </div>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="外呼间隔时常超过X分钟标记" prop="interval">
          <el-input-number
            v-model="form.interval"
            placeholder=""
            clearable
            :min="1"
            :max="99999999"
            :precision="0"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="setData"> 确定 </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.d-tip-box {
  background-color: #eaf5ff;
  font-weight: bold;
  padding: 10px 20px;
  margin-bottom: 20px;
  font-size: 15px;
  line-height: 1.7;
}
.el-input-number {
  width: 300px;
  :deep(.el-input) {
    width: 300px;
  }
}
:deep(.el-form-item .input-full) {
  .el-input {
    width: 100%;
  }
}
</style>
