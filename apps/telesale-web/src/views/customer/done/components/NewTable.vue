<!--
 * @Date         : 2024-11-07 17:34:41
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { ref, onActivated, computed, inject } from "vue";
import { storeToRefs } from "pinia";
import { useTable } from "/@/hooks/useTable";
import { useAppStore } from "/@/store/modules/app";
import { listHeader as columnsList, hideFiled } from "../utils/data";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import Search from "../../ongoing/components/NewSearch.vue";
import { cloneDeep, isBoolean, isEqual } from "lodash-es";
import { TableColumns } from "/@/components/ReTable/types";
import { buyList, findLock, getOngingList } from "/@/api/customer";
import SettingTable from "/@/components/SettingTable/index.vue";
import { useCollectStore } from "/@/store/modules/collect";
import Operation from "../../ongoing/components/Operation.vue";
import { getAuth } from "/@/utils/auth";
import DialogTransfer from "/@/components/DialogTransfer/index.vue";
import ViewModal from "/@/views/customer/components/Menu/ViewModal.vue";
import { ViewData } from "/@/api/common/view";
import { formatTime } from "/@/utils/common";
import { getFamilyCategoryData } from "/@/utils/common";
import { tableSelectFn } from "/@/utils/common/table";
import CustomerDrawer from "/@/views/customer/components/CustomerDrawer/index.vue";
import { useCustomerDrawer } from "/@/views/customer/hooks/useCustomerDrawer";

const emits = defineEmits(["addView", "updateLoading"]);
const { device } = storeToRefs(useAppStore());
const { setCollectClue, updateClueCollect } = useCollectStore();
const viewData = inject("viewData") as Ref<ViewData>;
const setViewData = inject("setViewData") as Function;

const searchRef = ref<InstanceType<typeof Search>>();
const listHeader = computed(() => {
  return columnsList.value.filter(item => {
    if (typeof item.isShow === "function") {
      return item.isShow();
    } else {
      return true;
    }
  });
});

const { drawerIdList, isCustomerVisible, setIdList } = useCustomerDrawer();
const columns = ref<TableColumns[]>([]);
const isModelItem = ref<boolean>(false);
const tableRefs = ref<InstanceType<typeof ReTable>>();
const rowIndex = ref<number>();
const dataMemory = ref();
const isModelTransfer = ref<boolean>(false);
const isViewModal = ref<boolean>(false);
const ids = ref();
const settingTableRef = ref<InstanceType<typeof SettingTable>>();
const hideList = ref(hideFiled);
const showList = ref([]);
const pages = ref(0);

const { loading, dataList, onSearch, searchForm, handlerQuery, Pagination } =
  useTable({
    api: buyList,
    immediate: false,
    initParams: {
      callCount: -1,
      openCal: 14
    },
    beforeRequest: data => {
      data.notDial = data.firstDialDuration;
      if (data.orgId && data.workerid) {
        data.orgId = undefined;
      }
      data.familyCategory = getFamilyCategoryData(data.familyCategory);
      return data;
    },
    dataCallback: async res => {
      if (res.data.list.length > 0) {
        setIdList(res.data.list);
        const infoids = res.data.list.map(item => item.infoUuid);
        res.data.list = await getLock(
          infoids,
          res.data.list,
          searchRef.value?.searchForm?.sort
        );
        res.data.list = await setCollectClue(infoids, res.data.list);
      }
    },
    endCallback: () => {
      setCurrent(rowIndex.value);
    }
  });

//表头重置
function resetFilter() {
  tableRefs.value?.clearFilterData();
  tableRefs.value?.clearSort();
  setViewData({
    tableSelect: {},
    tableSort: {},
    search: {}
  });
}

const reset = e => {
  setViewData({
    ...viewData.value,
    tableColumns: e
  });
};

const filterHeadData = (filterData: Record<string, any>) => {
  for (const key in filterData) {
    searchRef.value.searchForm[key] = filterData[key];
  }
  setViewData({
    search: {
      ...(viewData.value.search || {}),
      ...filterData
    },
    tableSelect: {
      ...(viewData.value.tableSelect || {}),
      ...filterData
    }
  });
};

const sortChange = column => {
  if (column.prop) {
    searchRef.value.searchForm.orderBy = column.prop.replace(
      /([A-Z])/g,
      function (match) {
        return "_" + match.toLowerCase();
      }
    );
    searchRef.value.searchForm.sort = column.order
      ? column.order.slice(0, -6)
      : "";
    searchRef.value.searchForm.combSort = [];
    if (
      viewData.value?.tableSort?.prop !== column.prop ||
      viewData.value?.tableSort?.order !== column.order
    ) {
      setViewData({
        search: {
          ...(viewData.value.search || {}),
          combSort: []
        },
        tableSort: {
          prop: column.prop,
          order: column.order
        }
      });
    }
  }
};

function getLock(infoids, list, sort) {
  return findLock({ infoUuids: infoids })
    .then(({ data }: { data: any }) => {
      list.forEach(item => {
        sort === "asc" && (item.lastDial = item.lastDial || ************);
        item.lockStatus = data.some(ele => item.infoUuid === ele.infoUuid);
      });
      return list;
    })
    .catch(() => {
      return list;
    });
}

const updateCollect = (
  id: number,
  infoUuid: string,
  action: "add" | "delete"
) => {
  dataList.value = updateClueCollect(id, infoUuid, action, dataList.value);
};
const formatData = form => {
  const data = cloneDeep(form);
  data.workerid = data.workerid ? data.workerid + "" : undefined;
  data.hasOrder = isBoolean(data.hasOrder) ? data.hasOrder : undefined;
  formatTime(data, "createAtMin", "createAtMax", "createTime");
  formatTime(data, "clueStart", "clueEnd", "clueTime");
  formatTime(data, "lastActiveStart", "lastActiveEnd", "watchTime");
  formatTime(data, "start", "end", "time");
  formatTime(data, "lastDialStart", "lastDialEnd", "lastDialTime");
  formatTime(data, "regTimeStart", "regTimeEnd", "regTime");
  formatTime(data, "lastDealingStart", "lastDealingEnd", "lastDealing");
  formatTime(data, "lastPaidTimeStart", "lastPaidTimeEnd", "lastPaidTime");
  formatTime(data, "authEndAtStart", "authEndAtEnd", "authTime");
  if (!data.callCount && data.callCount !== 0) {
    data.callCount = -1;
  }
  return data;
};

const searchData = () => {
  setViewData({
    search: searchRef.value?.searchForm,
    tableSort: searchRef.value?.searchForm.orderBy
      ? viewData.value.tableSort
      : {}
  });
};

const getList = () => {
  rowIndex.value = undefined;
  if (searchRef.value?.searchForm) {
    const data = formatData(searchRef.value?.searchForm);
    searchForm.value = data;
  }
  onSearch();
};

function transferMore() {
  let cus = tableRefs.value?.handleSelectionChange();
  if (!cus.length) {
    ElMessage.warning("您未选择任何线索");
    return;
  }
  ids.value = cus;
  dataMemory.value = {};
  isModelTransfer.value = true;
}

function openCustomerDrawer(row) {
  dataMemory.value = row;
  isCustomerVisible.value = true;
}

function parantMath({ key, params }) {
  console.log(key);

  switch (key) {
    case "openCustomerDrawer":
      openCustomerDrawer(params);
      break;
  }
}

const saveView = () => {
  isViewModal.value = true;
};

const addView = () => {
  emits("addView");
};

//回显上一次点击详情或者申请的用户
const setCurrent = (row: any) => {
  if (!row) {
    return;
  }
  let index = dataList.value.findIndex(item => item.infoUuid === row.infoUuid);
  if (index < 0) {
    return;
  }
  tableRefs.value?.setCurrent(index);
};

const initTable = () => {
  tableRefs.value?.clearSort();
  searchRef.value.resetForm();

  searchRef.value.searchForm = {
    ...searchRef.value.searchForm,
    ...viewData.value.search
  };

  hideList.value = viewData.value.tableColumns?.hideList || hideFiled;
  showList.value = viewData.value.tableColumns?.showList || [];
  nextTick(() => {
    settingTableRef.value?.initData();
    tableSelectFn(columns.value, viewData.value?.tableSelect);
  });

  if (viewData.value?.tableSort?.prop) {
    tableRefs.value?.sort(viewData.value.tableSort);
  }

  nextTick(() => {
    getList();
  });
};

const clearSort = () => {
  tableRefs.value?.clearSort();
};

onActivated(() => {
  if (device.value !== "mobile") {
    rowIndex.value = tableRefs.value?.getClickRow();
  }
  pages.value++;
  if (pages.value > 1) {
    handlerQuery();
  }
});

watch(
  () => loading.value,
  val => {
    emits("updateLoading", val);
  }
);

defineExpose({
  initTable,
  getList
});
</script>

<template>
  <div>
    <Search
      ref="searchRef"
      v-model:dataMemory="dataMemory"
      v-model:loading="loading"
      @onSearch="searchData"
      @clearSort="clearSort"
      @reset="resetFilter"
      @transferMore="transferMore"
      @saveView="saveView"
      :type="2"
    />
    <ReTable
      v-if="device !== 'mobile'"
      ref="tableRefs"
      :dataList="dataList"
      :listHeader="columns"
      :sort-change="sortChange"
      @filterHeadData="filterHeadData"
      :widthOperation="140"
      :selection="getAuth('telesale_admin_custom_transfer')"
      @parantMath="parantMath"
    >
      <template #appendColumn>
        <el-table-column fixed="right" label="操作" width="180">
          <template #header>
            <span>操作</span>
            <IconifyIconOffline
              icon="setting"
              @click="isModelItem = true"
              style="
                cursor: pointer;
                color: #409eff;
                font-size: 25px;
                position: absolute;
                right: 15px;
              "
            />
          </template>
          <template #default="scope">
            <Operation
              :row="scope.row"
              v-model:loading="loading"
              @getList="getList"
              :type="2"
              @addCollect="updateCollect($event, scope.row.infoUuid, 'add')"
              @removeCollect="
                updateCollect($event, scope.row.infoUuid, 'delete')
              "
            />
          </template>
        </el-table-column>
      </template>
    </ReTable>
    <template v-else>
      <ReCardList
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        @parantMath="parantMath"
        :isAppendColumn="true"
      >
        <template #appendColumn="scope: { row: any }">
          <Operation
            :row="scope.row"
            v-model:loading="loading"
            @getList="getList"
            :type="2"
            @addCollect="updateCollect($event, scope.row.infoUuid, 'add')"
            @removeCollect="updateCollect($event, scope.row.infoUuid, 'delete')"
          />
        </template>
      </ReCardList>
    </template>
    <div class="mt-10px">
      <Pagination />
    </div>
    <DialogTransfer
      v-model:value="isModelTransfer"
      :msg="dataMemory"
      :id="ids"
      :transferType="true"
      type="转线索"
      @getList="getList"
      v-if="isModelTransfer"
    />
    <CustomerDrawer
      v-if="isCustomerVisible"
      v-model:value="isCustomerVisible"
      :infoUuid="dataMemory.infoUuid"
      :idList="drawerIdList"
    />
    <ViewModal
      v-if="isViewModal"
      v-model:value="isViewModal"
      belong="已成交"
      :viewData="viewData"
      @success="addView"
    />
    <SettingTable
      ref="settingTableRef"
      v-model:visible="isModelItem"
      v-model:columns="columns"
      :listHeader="listHeader"
      :hideFiled="hideList"
      :showFiled="showList"
      @success="reset"
    />
  </div>
</template>

<style lang="scss" scoped></style>
