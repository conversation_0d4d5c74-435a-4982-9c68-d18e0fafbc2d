import { ref, watch } from "vue";
import durationChange from "/@/utils/handle/durationChange";
import stageList from "../../../../utils/data/stageList";
import { useUserStore } from "/@/store/modules/user";
import getDefinedItem from "/@/utils/handle/getDefinedItem";
import filterDataChange from "/@/utils/handle/filterDataChange";
import { storeToRefs } from "pinia";
import { TableColumns } from "/@/components/ReTable/types";
import { callStatusList } from "../../ongoing/utils/list";
import timeChange from "/@/utils/handle/timeChange";
import {
  roleList,
  userPayList,
  clueSourceTree
} from "@telesale/shared/src/data/customer";
import sourceObj from "/@/utils/data/sourceObj";
import { getLabel } from "/@/utils/common";
import { goodTypeList, gradeList } from "/@/utils/data/common";
import {
  familyCategoryList,
  familyCategoryListMap,
  studyInfoBindList,
  studyInfoBindListMap
} from "/@/utils/const/customer";
import { getAuth } from "/@/utils/auth";

const { userMsg } = storeToRefs(useUserStore());

const isList = [
  { label: "是", value: true },
  { label: "否", value: false }
];

//企业微信id, 为空代表没关联企微,不为空代表已经关联企微
function wecomChange(row) {
  return row["WeComOpenId"] ? "是" : "否";
}

//首次呼叫间隔为空时
function durationChangeMath(row) {
  return row["firstDialDuration"]
    ? durationChange(row["firstDialDuration"])
    : "暂无呼叫记录";
}

//表格名称转换
function shopName(row) {
  return;
}

const isFamily = getAuth("telesale_admin_customer_family_detail");

const activeList: TableColumns[] = [
  {
    field: "familyId",
    desc: "家庭ID",
    isFamily: true,
    minWidth: 115,
    isCopy: true,
    fixed: true,
    addType: "up",
    isShow: () => getAuth("telesale_admin_customer_family_detail")
  },
  {
    field: "onionid",
    desc: "洋葱ID",
    event: "openCustomerDrawer",
    isCopy: true,
    minWidth: 115,
    fixed: true
  },
  {
    field: "phone",
    desc: "客户手机号",
    minWidth: 130,
    isCopy: true,
    fixed: true
  },
  {
    field: "openCount",
    desc: "打开学情报告次数",
    minWidth: 110,
    sortable: true,
    addType: "up"
  },
  {
    field: "studyInfoBindType",
    desc: "学情被绑定状态",
    minWidth: 110,
    addType: "up",
    filterOptions: {
      columns: studyInfoBindList,
      isMultiple: true
    },
    customRender: ({ text }) => {
      return studyInfoBindListMap[text];
    },
    isShow: () => getAuth("telesale_admin_customer_family_detail")
  },
  {
    field: "familyCategory",
    desc: "所属家庭类型",
    minWidth: 110,
    addType: "up",
    filterOptions: {
      columns: familyCategoryList,
      isMultiple: true
    },
    customRender: ({ text }) => {
      return familyCategoryListMap[text];
    },
    isShow: () => getAuth("telesale_admin_customer_family_detail")
  },
  { field: "orderid", desc: "订单ID", isCopy: true, minWidth: 120 },
  {
    field: "payTime",
    desc: "成交时间",
    isTimeSort: true,
    sortable: true,
    timeChange: 2,
    minWidth: 100
  },
  {
    field: "lastPaidTime",
    desc: "最近一次付费时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 170,
    sortable: true
  },
  {
    field: "goodName",
    desc: "购买课程名称",
    minWidth: 155,
    customRender: ({ row }) => {
      return row.good?.name;
    }
  },
  { field: "amount", desc: "成交额", minWidth: 70 },
  {
    field: "goodType",
    desc: "订单商品类型",
    filterOptions: {
      columns: goodTypeList,
      isMultiple: true
    },
    addType: "up",
    minWidth: 105,
    customRender: ({ text }) => {
      return getLabel(text, goodTypeList);
    }
  },
  {
    field: "lastActiveTime",
    desc: "最近一次看课时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true
  },
  {
    field: "isPadUser",
    desc: "是否购买平板",

    minWidth: 105,
    filterOptions: {
      columns: isList
    },
    customRender: ({ text }) => {
      return getLabel(text, isList);
    }
  },
  {
    field: "newExam",
    desc: "是否新题型",
    minWidth: 105,
    filterOptions: {
      columns: isList
    },
    customRender: ({ text }) => {
      return getLabel(text, isList);
    }
  },
  {
    field: "historyAmount",
    desc: "历史付费金额总和",
    isTimeSort: true,
    headerTip: true,
    minWidth: 115,
    sortable: true
  },
  {
    field: "description",
    desc: "用户信息记录",
    minWidth: 160,
    customRender: ({ text }) => {
      return (
        <el-tooltip
          popper-class="max-w-700px"
          effect="dark"
          content={text}
          placement="top"
        >
          <div class="w-120px line-clamp-3 ">{text}</div>
        </el-tooltip>
      );
    }
  },
  {
    field: "stage",
    desc: "学段",
    minWidth: 75,
    filterOptions: {
      columns: stageList,
      isMultiple: true,
      label: "text"
    }
  },
  {
    field: "grade",
    desc: "年级",
    minWidth: 75,
    filterOptions: {
      columns: gradeList,
      isMultiple: true,
      label: "text"
    }
  },
  {
    field: "workerName",
    desc: "坐席名称",
    minWidth: 85,
    idTransfer: "name",
    isShow: () => userMsg.value.leafNode
  },

  {
    field: "userExpire",
    desc: "到期时间",
    isTimeSort: true,
    timeChange: 2,
    headerTip: true,
    minWidth: 105,
    sortable: true
  },
  {
    field: "workerExten",
    desc: "外呼工号",
    minWidth: 90,
    idTransfer: "exten",
    isShow: () => userMsg.value.leafNode
  },
  {
    field: "firstDialDuration",
    desc: "首次呼叫间隔",
    filters: durationChangeMath,
    headerTip: true,
    minWidth: 145,
    filterOptions: {
      columns: [
        {
          label: "暂无呼叫记录",
          value: true
        }
      ]
    }
  },
  { field: "nickname", desc: "是否关联企微", filters: wecomChange },
  {
    field: "callState",
    desc: "外呼状态",
    minWidth: 100,
    typeChange: callStatusList,
    filterOptions: {
      columns: callStatusList,
      isMultiple: true,
      label: "text"
    }
  },
  {
    field: "callCount",
    desc: "外呼次数",
    isTimeSort: true,
    minWidth: 110,
    sortable: true
  },
  {
    field: "lastDial",
    desc: "最近一次拨打时间",
    isTimeSort: true,
    filters: row => {
      if (!row.lastDial) {
        return "暂无呼叫记录";
      }
      return row.lastDial < 253370764800
        ? timeChange(row.lastDial, 2)
        : "暂无呼叫记录";
    },
    headerTip: true,
    minWidth: 130,
    sortable: true
  },
  {
    field: "lastDealing",
    desc: "最近一次拨通时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true
  },
  {
    field: "source",
    desc: "来源",
    typeChange: sourceObj,
    minWidth: 90,
    filterCascaderOptions: {
      columns: clueSourceTree
    }
  },
  {
    field: "authEndAt",
    desc: "权益到期时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true
  },
  {
    field: "learnLength",
    desc: "过去30天学习时长",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true,
    customRender: ({ text }) => {
      return text ? durationChange(text) : "";
    }
  },
  {
    field: "role",
    desc: "用户身份",
    minWidth: 110,
    filterOptions: {
      columns: roleList
    },
    customRender: ({ text }) => {
      return getLabel(text, roleList);
    }
  },
  {
    field: "payCategory",
    desc: "用户付费分类",
    minWidth: 110,
    filterOptions: {
      columns: userPayList,
      isMultiple: true
    },
    customRender: ({ text }) => {
      return getLabel(text, userPayList);
    }
  },
  {
    field: "createdAt",
    desc: "创建时间",
    isTimeSort: true,
    sortable: true,
    timeChange: 1,
    headerTip: true,
    minWidth: 100
  },
  {
    field: "province",
    desc: "省-市-区/县",
    minWidth: 130,
    customRender: ({ row }) => {
      return `${row["province"]}${row["city"]}${row["district"]}`;
    }
  }
];

const hideFiled = ["workerExten", "firstDialDuration", "nickname"];
const listHeader = ref<any[]>([...activeList]);

export { listHeader, activeList, hideFiled };
