<!--
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-22 16:43:47
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-04-24 11:27:32
 * @FilePath: /telesale-web_v2/apps/telesale-web/src/views/customer/linkSetting/components/LinkGoodsSetting.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Date         : 2025-02-13 18:52:28
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup name="UtilsGoods">
import { ref } from "vue";
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { visibleType } from "../data/index";
import { OperationObj, TableColumns } from "/@/components/ReTable/types";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getVenueLinkGoodsListApi,
  deleteVenueLinkGoodsApi
} from "/@/api/customer/linkSetting";
import { treeToObject } from "/@/utils/common";
import { getLabel } from "@telesale/shared";
import UpdateLinkGoods from "../dialog/UpdateLinkGoods.vue";

const props = defineProps<{
  orgData: any[];
}>();

const rowId = ref<string>();
const isModal = ref<boolean>(false);
const { dataList, onSearch, loading } = useTable({
  api: getVenueLinkGoodsListApi,
  isPages: false,
  dataCallback(res) {
    res.data = res.data.list;
  }
});

const orgMap = computed(() => {
  return treeToObject(props.orgData);
});

const listHeader: TableColumns[] = [
  {
    field: "goodsId",
    desc: "商品ID"
  },
  {
    field: "goodsName",
    desc: "商品名称"
  },
  {
    field: "showType",
    desc: "展示方式",
    customRender: ({ text }) => {
      return text === 1 ? "不展示" : "部分可见";
    }
  },
  {
    field: "groupIds",
    desc: "可见范围",
    showTip: true,
    customRender: ({ text, row }) => {
      return row.showType === 2
        ? text?.map(item => orgMap.value[item]).join("、")
        : "全员屏蔽";
    }
  }
];

const operation: OperationObj[] = [
  {
    text: "编辑",
    eventFn: row => {
      isModal.value = true;
      rowId.value = row.id;
    }
  },
  {
    text: "移除",
    eventFn: row => {
      ElMessageBox.confirm("确定是否要移除此商品?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        loading.value = true;
        deleteVenueLinkGoodsApi({ id: row.id })
          .then(() => {
            onSearch();
            ElMessage.success("移除成功");
          })
          .finally(() => {
            loading.value = false;
          });
      });
    }
  }
];

const addGoods = () => {
  isModal.value = true;
  rowId.value = undefined;
};
</script>

<template>
  <div cla v-loading="loading">
    <div class="flex justify-end mb-10px">
      <el-button type="primary" @click="addGoods">添加商品</el-button>
    </div>
    <div class="g-table-box">
      <ReTable
        v-if="!deviceDetection()"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
        :width-operation="130"
      />
      <ReCardList
        v-else
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
      />
    </div>
    <UpdateLinkGoods
      v-if="isModal"
      v-model:value="isModal"
      :id="rowId"
      :orgData="orgData"
      @onSearch="onSearch"
    />
  </div>
</template>
