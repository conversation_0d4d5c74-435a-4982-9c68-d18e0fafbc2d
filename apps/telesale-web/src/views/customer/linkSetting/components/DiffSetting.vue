<!--
 * @Date         : 2025-02-13 18:52:28
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup name="UtilsGoods">
import { ref } from "vue";
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { visibleType } from "../data/index";
import { OperationObj, TableColumns } from "/@/components/ReTable/types";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import UpdateGoods from "../dialog/UpdateGoods.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getDiffSettingApi,
  updateDiffSettingApi
} from "/@/api/customer/linkSetting";
import { treeToObject } from "/@/utils/common";
import { getLabel } from "@telesale/shared";

const props = defineProps<{
  orgData: any[];
}>();

const rowId = ref<string>();
const isModal = ref<boolean>(false);
const { dataList, onSearch, loading } = useTable({
  api: getDiffSettingApi,
  isPages: false,
  dataCallback(res) {
    res.data = res.data.infos;
  }
});

const orgMap = computed(() => {
  return treeToObject(props.orgData);
});

const listHeader: TableColumns[] = [
  {
    field: "skuGoodId",
    desc: "商品ID"
  },
  {
    field: "skuGoodName",
    desc: "商品名称"
  },
  {
    field: "showQR",
    desc: "是否展示二维码",
    customRender: ({ text }) => {
      return getLabel(text, visibleType);
    }
  },
  {
    field: "visibleGroups",
    desc: "可见范围",
    showTip: true,
    customRender: ({ text, row }) => {
      return row.visibleToPart
        ? text?.map(item => orgMap.value[item]).join("、")
        : "未设置，全员可见";
    }
  }
];

const operation: OperationObj[] = [
  {
    text: "编辑",
    eventFn: row => {
      isModal.value = true;
      rowId.value = row.skuGoodId;
    }
  },
  {
    text: "移除",
    eventFn: row => {
      ElMessageBox.confirm("确定是否要移除此活动?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        loading.value = true;
        updateDiffSettingApi({ skuGoodId: row.skuGoodId, action: "delete" })
          .then(() => {
            onSearch();
            ElMessage.success("移除成功");
          })
          .finally(() => {
            loading.value = false;
          });
      });
    }
  }
];

const addGoods = () => {
  isModal.value = true;
  rowId.value = undefined;
};
</script>

<template>
  <div cla v-loading="loading">
    <div class="flex justify-end mb-10px">
      <el-button type="primary" @click="addGoods">添加补差商品</el-button>
    </div>
    <div class="g-table-box">
      <ReTable
        v-if="!deviceDetection()"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
        :width-operation="130"
      />
      <ReCardList
        v-else
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
      />
    </div>
    <UpdateGoods
      v-if="isModal"
      v-model:value="isModal"
      :id="rowId"
      :orgData="orgData"
      @onSearch="onSearch"
    />
  </div>
</template>

<style lang="scss" scoped></style>
