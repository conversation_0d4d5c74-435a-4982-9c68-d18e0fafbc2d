<script setup lang="ts">
import { ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import {
  getLinkSettingApi,
  settingLinkApi,
  LinkSettingReq
} from "/@/api/customer/linkSetting";
import LinkGoodsSetting from "./LinkGoodsSetting.vue";

const props = defineProps<{
  orgData: any[];
}>();

const loading = ref<boolean>(false);
const formRef = ref<FormInstance | null>();

const form = ref<LinkSettingReq>({
  visibleType: undefined,
  invisibleType: undefined,
  invisibleTime: undefined,
  invisibleGroups: undefined,
  invisibleStart: undefined,
  invisibleEnd: undefined
});
const rules: FormRules = {
  visibleType: [
    {
      required: true,
      message: "请选择可见类型",
      trigger: "blur"
    }
  ],
  invisibleType: [
    {
      required: true,
      message: "请选择屏蔽类型",
      trigger: "blur"
    }
  ],
  invisibleTime: [
    {
      required: true,
      message: "请选择屏蔽时间",
      trigger: "blur"
    }
  ],
  invisibleGroups: [
    {
      required: true,
      message: "请选择屏蔽范围",
      trigger: "blur"
    }
  ]
};

const setData = async () => {
  await formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      if (form.value.invisibleTime?.length) {
        form.value.invisibleStart = form.value.invisibleTime[0] / 1000;
        form.value.invisibleEnd = form.value.invisibleTime[1] / 1000;
      }
      settingLinkApi(form.value)
        .then(() => {
          ElMessage.success("操作成功");
          getData();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

let getData = () => {
  loading.value = true;
  getLinkSettingApi()
    .then(({ data }) => {
      if (data.invisibleStart) {
        data.invisibleTime = [
          data.invisibleStart * 1000,
          data.invisibleEnd * 1000
        ];
      }
      for (const key in data) {
        data[key] = data[key] || undefined;
      }
      form.value = data;
    })
    .finally(() => {
      loading.value = false;
    });
};

getData();
</script>

<template>
  <div v-loading="loading">
    <div class="w-50%">
      <div>功能开启设置：</div>
      <el-form ref="formRef" :model="form" :rules="rules" label-suffix="：">
        <el-form-item prop="visibleType">
          <el-radio-group v-model="form.visibleType">
            <el-radio :label="1">全员可见</el-radio>
            <el-radio :label="2">设置特殊规则</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="form.visibleType === 2">
          <el-form-item prop="invisibleType">
            <el-radio-group v-model="form.invisibleType">
              <el-radio :label="1">全员屏蔽</el-radio>
              <el-radio :label="2">部分屏蔽</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="invisibleTime" v-if="form.invisibleType === 1">
            <el-date-picker
              v-model="form.invisibleTime"
              type="datetimerange"
              value-format="x"
              range-separator="至"
              start-placeholder="屏蔽时间-开始"
              end-placeholder="屏蔽时间-结束"
              :default-time="[
                new Date(2000, 1, 1, 0, 0, 0),
                new Date(2000, 2, 1, 23, 59, 59)
              ]"
            />
          </el-form-item>
          <el-form-item prop="invisibleGroups" v-if="form.invisibleType === 2">
            <el-cascader
              v-model="form.invisibleGroups"
              :options="props.orgData"
              :props="{
                value: 'id',
                label: 'name',
                checkStrictly: true,
                multiple: true,
                emitPath: false
              }"
              placeholder="请选择小组"
              filterable
              clearable
              :show-all-levels="false"
              style="width: 100%"
              class="input-full"
            />
          </el-form-item>
        </template>

        <el-form-item>
          <el-button type="primary" @click="setData">确定</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="mt-20px">
      商品展示设置：
      <LinkGoodsSetting :orgData="props.orgData" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .input-full) {
  .el-input {
    width: 100%;
  }
}
</style>
