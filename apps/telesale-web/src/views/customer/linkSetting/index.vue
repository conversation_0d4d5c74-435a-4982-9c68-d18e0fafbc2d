<!--
 * @Date         : 2025-02-13 18:15:41
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup name="linkSetting">
import { ref } from "vue";
import LinkSetting from "./components/LinkSetting.vue";
import DiffSetting from "./components/DiffSetting.vue";
import findOrganizationMath from "/@/utils/asyn/findOrganization";

const active = ref("link");

const orgAllData = ref([]);

const getOrgData = () => {
  findOrganizationMath().then(res => {
    orgAllData.value = res;
  });
};

getOrgData();
</script>

<template>
  <div class="g-margin-20">
    <el-card>
      <el-tabs v-model="active">
        <el-tab-pane label="会场链接设置" name="link">
          <LinkSetting :orgData="orgAllData" />
        </el-tab-pane>
        <el-tab-pane label="补差价设置" name="diff" lazy>
          <DiffSetting :orgData="orgAllData" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
