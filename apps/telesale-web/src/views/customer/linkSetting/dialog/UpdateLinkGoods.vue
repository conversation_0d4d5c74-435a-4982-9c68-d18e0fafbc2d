<!--
 * @Date         : 2025-02-13 18:55:40
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import {
  getVenueLinkGoodsDetailApi,
  addVenueLinkGoodsApi,
  updateVenueLinkGoodsApi,
  VenueLinkGoods
} from "/@/api/customer/linkSetting";

interface Props {
  value: boolean;
  id?: number;
  orgData: any[];
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance | null>();
const form = ref<VenueLinkGoods>({
  goodsId: undefined,
  showType: 2,
  groupIds: undefined,
  inGroups: undefined
});
const rules: FormRules = {
  goodsId: [
    {
      required: !props.id,
      message: "请输入商品ID",
      trigger: "change"
    }
  ],
  groupIds: [
    {
      required: true,
      message: "请输入商品分类名称",
      trigger: "change"
    }
  ]
};
function handleClose() {
  isModel.value = false;
}

const getInfo = () => {
  loading.value = true;
  getVenueLinkGoodsDetailApi({ id: props.id })
    .then(res => {
      form.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
};

const submit = async () => {
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      const fn = props.id ? updateVenueLinkGoodsApi : addVenueLinkGoodsApi;
      fn(form.value)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          handleClose();
          emit("onSearch");
        })
        .catch(err => {
          loading.value = false;
        });
    }
  });
};

props.id && getInfo();
</script>

<template>
  <el-dialog
    :title="props.id ? '编辑会场链接商品展示规则' : '配置会场链接商品展示规则'"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
  >
    <div v-loading="loading">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        label-width="190px"
        :rules="rules"
      >
        <el-form-item label="商品ID" prop="goodsId">
          <el-input
            v-model.trim="form.goodsId"
            placeholder="请输入商品ID"
            clearable
            :disabled="!!props.id"
          />
        </el-form-item>
        <el-form-item label="展示方式" prop="showType">
          <el-radio-group v-model="form.showType">
            <el-radio :label="1">不展示</el-radio>
            <el-radio :label="2">部分可见</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label-width="0"
          prop="groupIds"
          v-if="form.showType === 2"
        >
          提示：若未开启仅部分成员可见，则该课程链接可以被所不有坐席看见；如开启，则可以根据组织架构选择可见的成员范围（多选）。
          <el-cascader
            v-model="form.groupIds"
            :options="props.orgData"
            :props="{
              value: 'id',
              label: 'name',
              checkStrictly: true,
              multiple: true,
              emitPath: false
            }"
            placeholder="请选择小组"
            filterable
            clearable
            :show-all-levels="false"
            style="width: 100%"
            class="input-full"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-form-item) {
  .el-input {
    width: 100%;
  }
}
</style>
