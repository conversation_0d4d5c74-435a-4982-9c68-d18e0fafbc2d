<!--
 * @Date         : 2025-02-13 18:55:40
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import {
  DiffSettingData,
  getDiffSettingInfoApi,
  updateDiffSettingApi
} from "/@/api/customer/linkSetting";

interface Props {
  value: boolean;
  id?: string;
  orgData: any[];
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance | null>();
const form = ref<DiffSettingData>({
  skuGoodId: undefined,
  showQR: false,
  visibleToPart: undefined,
  visibleGroups: undefined
});
const rules: FormRules = {
  skuGoodId: [
    {
      required: !props.id,
      message: "请输入商品ID",
      trigger: "change"
    }
  ],
  visibleGroups: [
    {
      required: true,
      message: "请输入商品分类名称",
      trigger: "change"
    }
  ]
};
function handleClose() {
  isModel.value = false;
}

const getInfo = () => {
  loading.value = true;
  getDiffSettingInfoApi({ skuGoodId: props.id })
    .then(res => {
      form.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
};

const submit = async () => {
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      form.value.action = props.id ? "update" : "add";
      updateDiffSettingApi(form.value)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          handleClose();
          emit("onSearch");
        })
        .catch(err => {
          loading.value = false;
        });
    }
  });
};

props.id && getInfo();
</script>

<template>
  <el-dialog
    :title="props.id ? '编辑补差商品' : '配置补差商品'"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
  >
    <div v-loading="loading">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        label-width="190px"
        :rules="rules"
      >
        <el-form-item label="商品ID" prop="skuGoodId">
          <el-input
            v-model.trim="form.skuGoodId"
            placeholder="请输入商品ID"
            clearable
            :disabled="!!props.id"
          />
        </el-form-item>
        <el-form-item label="是否展示二维码" prop="showQR">
          <el-switch
            v-model="form.showQR"
            :active-value="true"
            :inactive-value="false"
          />
        </el-form-item>
        <el-form-item label="是否开启仅部分成员可见" prop="visibleToPart">
          <el-switch
            v-model="form.visibleToPart"
            :active-value="true"
            :inactive-value="false"
          />
        </el-form-item>
        <el-form-item
          label-width="0"
          prop="visibleGroups"
          v-if="form.visibleToPart"
        >
          提示：若未开启仅部分成员可见，则该课程链接可以被所不有坐席看见；如开启，则可以根据组织架构选择可见的成员范围（多选）。
          <el-cascader
            v-model="form.visibleGroups"
            :options="props.orgData"
            :props="{
              value: 'id',
              label: 'name',
              checkStrictly: true,
              multiple: true,
              emitPath: false
            }"
            placeholder="请选择小组"
            filterable
            clearable
            :show-all-levels="false"
            style="width: 100%"
            class="input-full"
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-form-item) {
  .el-input {
    width: 100%;
  }
}
</style>
