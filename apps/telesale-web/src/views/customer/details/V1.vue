<script setup lang="ts">
import { ref, onMounted, ComputedRef, computed, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { useUserStore, useUserStoreHook } from "/@/store/modules/user";
import { detail } from "/@/api/customerDetails";
import { useRoute, useRouter } from "vue-router";
import { RouteConfigs } from "/@/layout/types";
import { useMultiTagsStoreHook } from "/@/store/modules/multiTags";
import BaseMsg from "./components/BaseMsg.vue";
import Tabs from "./components/Tabs.vue";
import PushPay from "./dialog/PushPay.vue";
import ExperienceGive from "./dialog/ExperienceGive.vue";
import Release from "./dialog/Release.vue";
import Evaluate from "./dialog/Evaluate.vue";
import SendMsg from "./dialog/SendMsg.vue";
import GiveProfile from "./dialog/GiveProfile.vue";
import Call from "./components/Call.vue";
import DialogTransfer from "/@/components/DialogTransfer/index.vue";
import findSubject from "/@/utils/asyn/findSubject";
import sendPointMath from "/@/utils/handle/sendPoint";
import collectBtn from "./components/collectBtn.vue";
import { storeToRefs } from "pinia";
import {
  getAbLearningApi,
  getDiscoveryPageAuthApi,
  getPhoneWhiteApi,
  getUserCoolApi
} from "/@/api/customer/details";
import SendMessage from "./dialog/SendMessage.vue";
import FamilyModal from "/@/views/customer/components/FamilyModal/index.vue";
import { getQualityUserApi } from "@telesale/server/src/api/customer/details";

const route = useRoute();
const router = useRouter();

const { closePage: closeDetails } = useMultiTagsStoreHook();
const { userMsg } = storeToRefs(useUserStore());

let multiTags: ComputedRef<Array<RouteConfigs>> = computed(() => {
  return useMultiTagsStoreHook()?.multiTags;
});

function closePage() {
  let valueIndex: number = multiTags.value.findIndex((item: any) => {
    if (item.path === "/customer/details/index") {
      return item.query.infoUuid === customMsg.value.infoUuid;
    }
  });
  useMultiTagsStoreHook().handleTags("splice", "", {
    startIndex: valueIndex,
    length: 1
  });
  nextTick(() => {
    if (route.query?.back === "-1") {
      router.back();
    } else {
      router.push({ name: customMsg.value.from });
    }
  });
}

const infoUuid = (route.query?.infoUuid || "") as string;
const from: any = route.query?.from || "";
const type = sessionStorage.getItem(infoUuid as string) || "";

const loading = ref(false);
const customMsg = ref();
const callRefs = ref();
const hasRisk = ref(false);
const coolInfo = ref();
const isModelGive = ref(false);
const isModelRelease = ref(false);
const isModelEvaluate = ref(false);
const isModelSend = ref(false);
const isModelTransfer = ref(false);
const isModelPush = ref(false);
const isModalProfile = ref<boolean>(false);
const isFamilyModal = ref<boolean>(false);
const tasRef = ref<InstanceType<typeof Tabs>>();
const getDiscoveryPageAuth = ref(false);
const studyPageAuth = ref(false);
const specialOldUsers = ref(false);
const messageModal = ref(false);

function comTip() {
  if (!customMsg.value?.userid) {
    ElMessage.warning("该用户暂无洋葱账号");
    return true;
  }
  return false;
}

function openPush() {
  if (comTip()) return;
  isModelPush.value = true;
}

function openEvaluate() {
  if (comTip()) return;
  isModelEvaluate.value = true;
}
function openSendMsg() {
  if (comTip()) return;
  isModelSend.value = true;
}

const giveProfile = () => {
  if (comTip()) return;
  isModalProfile.value = true;
};

const isOwn = ref(true);
function getDetail() {
  if (!infoUuid) {
    sendPointCommon();
    isOwn.value = false;
    return;
  }
  loading.value = true;
  detail({ infoUuid: infoUuid })
    .then(({ data }: { data: any }) => {
      sendPointCommon();
      try {
        const fromData = JSON.parse(from);
        data.from = fromData.from; //来源页面 - 后续返回会跳转到原页面
        data.pushId = Number(fromData.pushId); //推送线索id
        let index = Number(fromData.important); //价值等级
        data.openMethod = fromData.openMethod; //推送线索状态 push未处理 handle已处理
        data.important = [5, 4, 3][index - 1]; //价值等级对应文字说明
        data.pushMsg = fromData.pushMsg || ""; //推荐理由
      } catch (e) {
        data.from = from;
      }
      customMsg.value = data;
      if (type) {
        sessionStorage.setItem(infoUuid as string, "");
        nextTick(() => {
          callRefs.value.fastCall();
        });
      }
      getPhoneWhite();
      getCool();
      getCard();
      getStudyPage();
      getSpecialOldUsers();
      loading.value = false;
    })
    .catch(() => {
      sendPointCommon();
      isOwn.value = false;
      loading.value = false;
    });
}

const sendMessage = () => {
  messageModal.value = true;
};

const getInfo = () => {
  loading.value = true;
  detail({ infoUuid: infoUuid })
    .then(({ data }: { data: any }) => {
      customMsg.value.role = data.role;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
};

const getPhoneWhite = () => {
  if (!customMsg.value?.phone) {
    hasRisk.value = true;
    return;
  }
  loading.value = true;
  getPhoneWhiteApi({ userId: customMsg.value.userid })
    .then(res => {
      hasRisk.value = res.data.hasRisk;
    })
    .finally(() => {
      loading.value = false;
    });
};

const getCool = () => {
  loading.value = true;
  getUserCoolApi({ userId: customMsg.value.userid })
    .then(res => {
      coolInfo.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
};

let applyClue = ref({ ...useUserStoreHook().pointParams.applyClue });
function sendPointCommon() {
  if (applyClue.value?.status !== "1") return;
  applyClue.value.getUserMsgTime = new Date().getTime();
  sendPointMath(
    { category: "site", eventKey: "clickApplyLeads" },
    applyClue.value
  );
}

const back = async () => {
  const res = await closeDetails(route);
  if (res) return;
  closePage();
};

const resetPushRecord = () => {
  tasRef.value?.resetPushRecord();
};

const sendClass = () => {
  if (hasRisk.value)
    return ElMessage.error(
      "当前用户未绑定手机号或者手机号存在风险，不支持赠送体验课"
    );
  isModelGive.value = true;
};

const getCard = () => {
  getDiscoveryPageAuthApi({ userId: customMsg.value.userid }).then(res => {
    getDiscoveryPageAuth.value = res.data.hasAuth;
  });
};

const getStudyPage = async () => {
  const { data } = await getAbLearningApi({ userId: customMsg.value.userid });
  studyPageAuth.value = data.isA;
};

const getSpecialOldUsers = async () => {
  const { data } = await getQualityUserApi({ userId: customMsg.value.userid });
  specialOldUsers.value = data.ok;
};

const openFamilyModal = () => {
  isFamilyModal.value = true;
};

onMounted(async () => {
  console.log("all in time:", new Date().getTime() - Number(route.query?.time));
  //进入详情页面时间戳存储
  if (applyClue.value?.status === "1") {
    applyClue.value.enterDetailsPageTime = new Date().getTime();
    console.log(
      "进入客户详情的时间：",
      new Date(applyClue.value.enterDetailsPageTime).toLocaleString()
    );
    useUserStoreHook().setPointParams("applyClue", {});
  }
  if (!useUserStoreHook().setSubjectList.length) {
    useUserStoreHook().setSubjectList(await findSubject());
  }
  getDetail();
});
</script>

<template>
  <div>
    <div v-if="!isOwn" class="g-margin-20 d-no-own" v-loading="loading">
      此线索已失效
    </div>
    <div v-if="isOwn && !customMsg" class="g-margin-20 d-loading">
      正在加载中...
    </div>
    <div v-if="isOwn && !!customMsg" class="g-margin-20" v-loading="loading">
      <div class="d-box">
        <el-button plain @click="openPush" v-if="customMsg?.userid">
          支付推送
        </el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="sendMessage"
          v-if="getDiscoveryPageAuth"
        >
          留言卡片
        </el-button> -->
        <el-button type="primary" plain @click="openSendMsg">
          邀约短信
        </el-button>
        <Call
          ref="callRefs"
          :customMsg="customMsg || {}"
          v-if="customMsg"
          @closePage="closePage"
          @resetPushRecord="resetPushRecord"
        />
        <el-button plain @click="back">返回</el-button>
      </div>

      <el-alert
        v-if="hasRisk"
        title="❗️当前用户未绑定手机号或者手机号存在风险，不支持赠送体验课！风险校验不一定100%准确，若跟用户核实无风险，可联系团长解除风险标记"
        type="error"
        :closable="false"
      />
      <el-alert
        v-if="coolInfo?.hasCoolDownDuration"
        :title="`❗️该用户历史被设置了跟进冷静期，冷静期到期时间：${coolInfo?.cdExpire}`"
        type="error"
        :closable="false"
      />
      <div class="flex items-start">
        <div class="flex-1 overflow-auto">
          <el-card class="g-card g-margin-b-20">
            <div class="g-card__header d-pad-left flex justify-between">
              <div>
                基础信息
                <el-button
                  type="primary"
                  @click="openFamilyModal"
                  v-auth="'telesale_admin_customer_family_detail'"
                >
                  查看更多家庭信息
                </el-button>
              </div>
              <div class="g-right">
                <collectBtn
                  v-if="customMsg?.workerid === userMsg.id"
                  :infoUuid="infoUuid"
                  class="mr-12px"
                />
                <el-button
                  v-auth="'telesale_admin_giveProfile'"
                  type="primary"
                  plain
                  @click="giveProfile"
                >
                  赠送资料
                </el-button>
                <el-button type="primary" plain @click="sendClass">
                  体验赠送
                </el-button>
                <el-button type="primary" plain @click="isModelRelease = true">
                  释放
                </el-button>
                <el-button type="primary" plain @click="isModelTransfer = true">
                  转线索
                </el-button>
              </div>
            </div>
            <BaseMsg
              v-if="customMsg"
              :customMsg="customMsg || null"
              :pushDiscover="getDiscoveryPageAuth"
              :studyPageAuth="studyPageAuth"
              :specialOldUsers="specialOldUsers"
              @success="getInfo"
            />
          </el-card>
          <Tabs
            ref="tasRef"
            v-if="customMsg"
            :customMsg="customMsg || {}"
            :hasRisk="hasRisk"
          />
          <PushPay
            v-model:value="isModelPush"
            :onionid="customMsg?.onionid || ''"
            :userid="customMsg?.userid || ''"
            :phone="customMsg?.phone || ''"
            :pushDiscover="getDiscoveryPageAuth"
            :stage="customMsg?.stage"
            :grade="customMsg?.grade"
            v-if="isModelPush"
          />
          <ExperienceGive
            v-model:value="isModelGive"
            :userid="customMsg?.userid || ''"
            v-if="isModelGive"
          />
          <Release
            v-model:value="isModelRelease"
            :infoUuid="customMsg?.infoUuid || ''"
            v-if="isModelRelease"
            @closePage="closePage"
          />
          <Evaluate
            v-model:value="isModelEvaluate"
            :userid="customMsg?.userid || ''"
            v-if="isModelEvaluate"
          />
          <GiveProfile
            v-if="isModalProfile"
            v-model:value="isModalProfile"
            :userId="customMsg?.userid || ''"
          />
          <SendMessage
            v-if="messageModal"
            v-model:value="messageModal"
            :userId="customMsg.userid"
          />
          <SendMsg
            v-model:value="isModelSend"
            :customMsg="customMsg || {}"
            v-if="isModelSend"
          />
          <DialogTransfer
            v-model:value="isModelTransfer"
            :msg="customMsg || {}"
            :id="customMsg.infoUuid"
            :transferType="false"
            type="转线索"
            @closePage="closePage"
            @getList="getDetail"
            v-if="isModelTransfer"
          />
          <FamilyModal
            v-if="isFamilyModal"
            v-model:value="isFamilyModal"
            :familyId="customMsg.familyId"
            :userId="customMsg.userid"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.d-loading {
  text-align: center;
  font-size: 30px;
  margin-top: 150px;
}
.d-no-own {
  text-align: center;
  font-size: 30px;
  margin-top: 150px;
  color: #f56c6c;
}
.d-box {
  text-align: center;
  :deep(.el-button) {
    margin-bottom: 10px;
  }
}
.d-pad-left {
  padding-right: 0;
}
</style>
