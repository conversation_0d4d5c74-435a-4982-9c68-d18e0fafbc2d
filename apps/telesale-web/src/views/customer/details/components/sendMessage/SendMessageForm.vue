<!--
 * @Date         : 2024-12-26 15:41:56
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { ref } from "vue";
import { useUserStore } from "/@/store/modules/user";
import { FormInstance, FormRules } from "element-plus";
import {
  getSyncOrderLeaveListApi,
  sendMessageCardApi
} from "/@/api/customer/details";

interface Props {
  userId: string;
}

const emits = defineEmits(["success"]);
const props = withDefaults(defineProps<Props>(), {});
const { userMsg } = storeToRefs(useUserStore());

const loading = ref<boolean>(false);
const formRef = ref<FormInstance>();
const form = ref({
  id: undefined,
  userId: props.userId,
  workerId: userMsg.value.id
});
const messageList = ref([]);

const rules: FormRules = {
  id: [{ required: true, message: "请选择要发送的消息", trigger: "blur" }]
};

const getList = () => {
  loading.value = true;
  getSyncOrderLeaveListApi()
    .then(res => {
      messageList.value = res.data.leaves;
    })
    .finally(() => {
      loading.value = false;
    });
};
getList();

const onSubmit = () => {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      sendMessageCardApi(form.value)
        .then(res => {
          ElMessage.success("操作成功");
          close();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

const close = () => {
  emits("success");
};
</script>

<template>
  <el-form
    :model="form"
    ref="formRef"
    :rules="rules"
    label-width="0"
    :inline="false"
    v-loading="loading"
    class="send-message-form"
  >
    <div class="bg-#ecf5fd p-10px font-bold">
      说明：一天只能推送1次，推送的留言卡片会展示在APP发现页，卡片当天24点后过期
    </div>
    <el-form-item class="mt-4px w-100%">
      <el-radio-group v-model="form.id">
        <el-radio
          v-for="item in messageList"
          :key="item.id"
          :label="item.id"
          class="mt-10px w-100%"
        >
          <div class="text-wrap line-height-16px w-98%">
            【{{ item.id }}】{{ item.leave }}
          </div>
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item class="mt-40px">
      <div class="w-100% flex justify-end">
        <el-button type="primary" @click="onSubmit">发送</el-button>
        <el-button @click="close">取消</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<style lang="scss">
.send-message-form {
  .el-radio__label {
    width: 100%;
  }
}
</style>
