<!--
 * @Date         : 2024-12-26 15:44:52
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { TableColumns } from "/@/components/ReTable/types";
import { useTable } from "/@/hooks/useTable";
import { getSyncOrderLeaveRecordListApi } from "/@/api/customer/details";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";

const props = defineProps<{
  userId: string;
}>();

const { allAgentObj } = storeToRefs(useUserStore());

const listHeader: TableColumns[] = [
  {
    field: "id",
    desc: "推送的留言ID"
  },
  {
    field: "pushAt",
    desc: "推送时间",
    timeChange: 2
  },
  {
    field: "workerId",
    desc: "推送人",
    customRender: ({ text }) => {
      return allAgentObj.value[text]?.name;
    }
  }
];

const { dataList, loading } = useTable({
  api: getSyncOrderLeaveRecordListApi,
  initParams: {
    userId: props.userId
  },
  isPages: false,
  dataCallback(res) {
    res.data = res.data.leaveRecords;
  }
});
</script>

<template>
  <div class="bg-#ecf5fd p-10px font-bold mb-10px">
    说明：展示近3天内的推送留言卡片记录
  </div>
  <ReTable
    class="mb-40px"
    ref="tableRefs"
    v-loading="loading"
    :dataList="dataList"
    :listHeader="listHeader"
  />
</template>

<style lang="scss" scoped></style>
