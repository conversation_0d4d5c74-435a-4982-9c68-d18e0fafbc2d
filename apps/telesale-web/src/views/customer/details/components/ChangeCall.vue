<!--
 * @Date         : 2024-09-12 10:32:58
 * @Description  : 切换外呼渠道
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { storeToRefs } from "pinia";
import { changeChannelToApp } from "/@/utils/common/call";
import { useUserStore } from "/@/store/modules/user";
import getNO from "/@/utils/asyn/getNO";
import channelMath from "/@/utils/asyn/findChannel";
import { changeCallChannelApi } from "/@/api/customer/call";
import getPersonMsgMath from "/@/utils/asyn/getPersonMsg";

const props = defineProps<{
  title: string;
  disabled: boolean;
}>();

const emits = defineEmits(["call"]);

const useUser = useUserStore();
const { channelList, userMsg } = storeToRefs(useUser);

const useChannelList = ref([]);
const currentChannel = ref(
  userMsg.value.channelId ? Number(userMsg.value.channelId) : undefined
);

const loading = ref(false);
const channelLoading = ref(false);

const getChannel = async () => {
  const res = await channelMath();
  useUser.setChannelList(res);
  const myChannelList = await getNO(userMsg.value.id);
  const list = [];
  myChannelList?.forEach(item => {
    const channelInfo = channelList.value.find(
      find => item.channelId === find.value
    );
    if (channelInfo?.value) {
      list.push({
        ...item,
        ...channelInfo
      });
    }
  });
  useChannelList.value = list;
};

const changeChannel = async (event: any, e: any) => {
  if (event.target.tagName === "INPUT") return;

  if (Number(userMsg.value.channelId) !== Number(e.value)) {
    loading.value = true;
    changeCallChannelApi({ channelId: e.value })
      .then(async () => {
        useUser.setUserMsg(await getPersonMsgMath()); // 刷新用户信息
        await changeChannelToApp();
        setTimeout(() => {
          call();
          loading.value = false;
        }, 2000);
      })
      .catch(async () => {
        loading.value = false;
      });
  } else {
    call();
  }
};

const visiableChange = async (e: boolean) => {
  if (e) {
    try {
      channelLoading.value = true;
      useUser.setUserMsg(await getPersonMsgMath()); // 刷新用户信息
      currentChannel.value = userMsg.value.channelId
        ? Number(userMsg.value.channelId)
        : undefined;
      await getChannel();
    } finally {
      channelLoading.value = false;
    }
  }
};

const call = () => {
  emits("call");
};

// onMounted(async () => {
//   await getChannel();
// });
</script>

<template>
  <el-dropdown
    split-button
    type="primary"
    @click="call"
    :disabled="props.disabled"
    class="mx-10px channel-btn"
    trigger="click"
    @visible-change="visiableChange"
    v-loading="loading"
  >
    {{ props.title }}
    <template #dropdown>
      <el-dropdown-menu v-loading="channelLoading">
        <el-radio-group
          v-model="currentChannel"
          class="flex flex-col items-start"
          v-if="useChannelList.length > 0"
        >
          <el-dropdown-item
            class="w-100% p-0!"
            v-for="(item, index) in useChannelList"
            :key="index"
            @click="changeChannel($event, item)"
            :disabled="item.state === 1"
          >
            <el-radio
              :label="item.value"
              class="w-100% h-100%! px-20px py-10px"
              :disabled="item.state === 1"
            >
              {{ item.text }}
            </el-radio>
          </el-dropdown-item>
        </el-radio-group>
        <el-dropdown-item v-else class="w-100% p-0!">
          <div class="w-100% h-100%! px-20px py-10px">暂无外呼渠道</div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<style lang="scss" scoped>
.channel-btn {
  :deep(.el-button-group) .el-button {
    margin: 0;
  }
}
</style>
