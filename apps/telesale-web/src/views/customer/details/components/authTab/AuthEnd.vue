<!--
 * @Date         : 2024-08-29 14:50:18
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { TableColumns } from "/@/components/ReTable/types";
import { SpecialCourse } from "/@/api/customer/authContent";
import { targetChange } from "../../data";
import ReTable from "/@/components/ReTable/index.vue";

interface Props {
  data: SpecialCourse[];
  type: string;
  examPaperData: any[];
  treeData: any[];
}

const props = defineProps<Props>();

const listHeader: TableColumns[] = [
  {
    field: "targetId",
    desc: "名称",
    customRender: ({ row }) => {
      row.targetType = props.type;
      row.targetId = row.id;
      return targetChange(row, props.treeData, props.examPaperData);
    }
  },
  {
    field: "expireTime",
    desc: "总截止时间",
    timeChange: 3
  }
];
</script>

<template>
  <div v-if="props.data?.length > 0">
    <ReTable :dataList="props.data" :listHeader="listHeader" />
  </div>
</template>
