<!--
 * @Date         : 2024-08-29 14:49:58
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="tsx">
import { ref } from "vue";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import { dataType } from "/@/utils/data/dataType";
import durationChange from "/@/utils/handle/durationChange";
import stageAllList from "/@/utils/data/stageAllList";
import { AuthInfo } from "/@/api/customer/authContent";
import { TableColumns } from "/@/components/ReTable/types";
import { targetChange } from "../../data";

let device = useAppStoreHook().device;
interface Props {
  data: AuthInfo[];
  examPaperData: any[];
  treeData: any[];
}

const props = defineProps<Props>();

function durationChangeMath(row) {
  return row["duration"] ? durationChange(row["duration"] / 1000) : "";
}

const computedDays = data => {
  const currentDate = new Date().getTime();
  const state = data.state;
  if (state === "unused") return formatDays(data.duration);
  else if (state === "used") return 0 + "天";
  else if (state === "using") {
    const endDate = new Date(data.endTime).getTime();
    const startDate = new Date(data.startTime).getTime();
    if (currentDate < endDate && currentDate > startDate) {
      return formatDays(endDate - currentDate);
    } else if (currentDate > endDate || currentDate < startDate) {
      return formatDays(data.duration);
    }
  } else return 0 + "天";
};

const formatDays = time => {
  const ss = 1000;
  const mi = ss * 60;
  const hh = mi * 60;
  const dd = hh * 24;
  const day = Math.floor(time / dd);
  const hour = Math.floor((time - day * dd) / hh);
  let str = "";
  if (day > 0) str += day + "天";
  if (hour > 0) str += hour + "小时";
  return str;
};

const listHeader = ref<TableColumns[]>([
  {
    field: "targetId",
    desc: "授权名称",
    minWidth: 100,
    customRender: ({ row }) => {
      return targetChange(row, props.treeData, props.examPaperData);
    }
  },
  {
    field: "sourceType",
    desc: "来源类型",
    typeChange: dataType.sourceTypeObj,
    customRender: ({ text }) => {
      const isFamily = text === "家庭组共享";
      return (
        <span class={isFamily ? "c-#e6a23c" : ""}>
          {isFamily ? text : dataType.sourceTypeObj[text] || text}
        </span>
      );
    }
  },
  { field: "sourceId", desc: "来源ID" },
  { field: "startTime", desc: "生效时间", timeChange: 3, minWidth: 100 },
  { field: "endTime", desc: "截止时间", timeChange: 3, minWidth: 100 },
  { field: "duration", desc: "总时长", filters: durationChangeMath },
  {
    field: "surplus",
    desc: "剩余时长",
    minWidth: 110,
    customRender: ({ row }) => {
      return computedDays(row);
    }
  },
  { field: "state", desc: "状态", typeChange: dataType.stateObj }
]);
</script>

<template>
  <div v-if="props.data?.length > 0">
    <template v-if="device !== 'mobile'">
      <ReTable :dataList="props.data" :listHeader="listHeader" />
    </template>
    <template v-else>
      <ReCardList
        :dataList="props.data"
        :listHeader="listHeader"
        :isCardBox="false"
      />
    </template>
  </div>
</template>
