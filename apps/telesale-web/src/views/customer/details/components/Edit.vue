<!--
 * @Date         : 2024-12-20 15:38:24
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
const props = defineProps<{
  value: any;
  options: {
    label: string;
    value: any;
  }[];
}>();
const emit = defineEmits<{
  (e: "update:value", value: any): void;
  (e: "submit"): void;
  (e: "cancel"): void;
}>();
const isEdit = ref(false);
const cloneValue = ref();
const initValue = computed({
  get: () => {
    return props.value;
  },
  set: val => {
    emit("update:value", val);
  }
});

const editRole = () => {
  cloneValue.value = props.value;
  isEdit.value = true;
};

const submit = () => {
  isEdit.value = false;
  emit("submit");
};

const cancel = () => {
  isEdit.value = false;
  initValue.value = cloneValue.value;
  emit("cancel");
};
</script>

<template>
  <template v-if="isEdit">
    <div class="flex gap-10px items-center">
      <el-select v-model="initValue" class="w-[calc(100%-50px)]">
        <el-option
          v-for="(item, index) in props.options"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <div class="edit-label w-50px">
        <IconifyIconOffline
          icon="check"
          class="mt-5px mr-10px"
          @click.stop="submit"
          style="color: #67c23a"
        />
        <IconifyIconOffline
          icon="close-bold"
          class="mt-5px"
          @click.stop="cancel"
          style="color: red"
        />
      </div>
    </div>
  </template>
  <template v-else>
    <div class="flex gap-10px items-center">
      <slot />
      <IconifyIconOffline icon="edits" class="icon" @click="editRole" />
    </div>
  </template>
</template>

<style lang="scss" scoped></style>
