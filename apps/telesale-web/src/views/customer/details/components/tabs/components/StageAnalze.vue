<!--
 * @Date         : 2025-02-17 16:39:30
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<template>
  <el-card>
    <iframe
      ref="IframeRef"
      :width="deviceDetection() ? '333' : '375'"
      :src="url"
      height="667"
      class="g-margin-b-20"
    />
    <div>
      <!-- <el-button type="primary" @click.stop="copyUrl">复制链接</el-button> -->
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import { watch, ref, onMounted, onUnmounted } from "vue";
import { ElMessage } from "element-plus";
import { deviceDetection } from "/@/utils/deviceDetection";
import { useClipboard } from "@vueuse/core";

const props = defineProps<{
  token?: string;
  userId: string;
}>();
const omvd = Math.random().toString(36).substring(2, 12);
const IframeRef = ref();

const url = ref(
  `${import.meta.env.VITE_BASE_H5_URL}/subjectAnalysis.html?userId=${
    props.userId
  }`
);

const { copy } = useClipboard();

watch(
  () => props.userId,
  value => {
    if (value) {
      url.value = `${
        import.meta.env.VITE_BASE_H5_URL
      }/subjectAnalysis.html?userId=${props.userId}`;
    }
  }
);
const copyUrl = () => {
  const omvd = Math.random().toString(36).substring(2, 12);
  copy(url.value);
  ElMessage.success("复制成功");
};

async function listenPostMessage(event) {
  if (event.origin.indexOf("yangcongxing") !== -1) {
    const { type, blob } = event.data;

    if (type === "copyText" || type === "copyImage") {
      const item = new window.ClipboardItem(blob);
      try {
        await navigator.clipboard.write([item]);
        ElMessage.success("复制成功");
      } catch (error) {
        console.error(error);
        ElMessage.error("复制失败");
      }
    }
  }
}
onMounted(() => {
  window.addEventListener("message", listenPostMessage);
});
onUnmounted(() => {
  window.removeEventListener("message", listenPostMessage);
});
</script>

<style lang="scss" scoped>
.iframe-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400px;
  padding: 20px 0;
  cursor: pointer;
  background: #ffffff;

  .top-route {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 20px 10px;
    color: #000;
  }

  iframe {
    width: 375px;
    height: 667px;
    padding: 0 20px 20px;
    resize: vertical;
    border: none;
    border-right: 0.5px solid #ebeef5;
    border-bottom: 0.5px solid #ebeef5;
  }

  .iframe__copy-url {
    margin-left: auto;
  }
}

.iframe-container-landscape {
  width: 700px;

  iframe {
    width: 667px;
    height: 375px;
    resize: none;
    border: none;
    border-right: 0.5px solid #ebeef5;
    border-bottom: 0.5px solid #ebeef5;
  }
}
</style>
