<!--
 * @Date         : 2024-10-18 14:59:39
 * @Description  : 加购平板
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { getHasIpadApi } from "/@/api/customer/details";
import { IpadGoodInfo } from "@telesale/shared/src/businessHooks/payPush/types";
import {
  isStagesList,
  stagesType
} from "@telesale/shared/src/data/exclusiveLink";
import { getLabel } from "@telesale/shared";
import { IpadReq, padLinkApi } from "/@/api/customer/link";
import { cloneDeep } from "lodash-es";
import { getInstallmentPayType } from "@telesale/shared/src/businessHooks/payPush/installmentPay";

const props = defineProps<{
  userId: string;
}>();

const loading = ref(false);
const dialogVisible = ref(false);
const imgUrl = ref("");
const ruleFormRef = ref<FormInstance | null>();
const padList = ref<IpadGoodInfo[]>([]);
const form = ref<IpadReq>({
  pad: undefined,
  isInstallment: 2,
  installmentPayType: undefined
});
const rules: FormRules = {
  pad: [
    {
      required: true,
      message: "请选择加购商品",
      trigger: "change"
    }
  ],
  isInstallment: [
    {
      required: true,
      message: "请选择分期支付",
      trigger: "change"
    }
  ]
};

const changeInstallment = () => {
  form.value.installmentPayType = ["alipayFq"];
};

const getInfo = () => {
  loading.value = true;
  getHasIpadApi({ userId: props.userId })
    .then(res => {
      padList.value = res.data.ok ? res.data.pads : [];
    })
    .finally(() => {
      loading.value = false;
    });
};

const submit = async () => {
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      const params = cloneDeep(form.value);
      params.installmentPayType = getInstallmentPayType(
        form.value.isInstallment as number,
        form.value.installmentPayType as string[]
      );
      padLinkApi(params)
        .then(({ data }) => {
          dialogVisible.value = true;
          let blob = new Blob([data], { type: "png" });
          imgUrl.value = (window.URL || window.webkitURL).createObjectURL(blob);
          ElMessage.success("操作成功");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

getInfo();
</script>

<template>
  <div v-loading="loading">
    <el-form
      :model="form"
      label-suffix="："
      ref="ruleFormRef"
      label-width="120px"
      :rules="rules"
    >
      <el-form-item label="加购商品" prop="pad">
        <el-select v-model="form.pad" placeholder="请选择加购商品">
          <el-option
            v-for="item in padList"
            :key="item.label"
            :label="item.label"
            :value="item.label"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分期支付" prop="isInstallment">
        <el-radio-group
          v-model="form.isInstallment"
          @change="changeInstallment"
        >
          <el-radio
            v-for="(item, index) in isStagesList"
            :key="index"
            :label="item.value"
          >
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="分期支付方式"
        prop="installmentPayType"
        v-if="form.isInstallment === 1"
      >
        <el-checkbox-group v-model="form.installmentPayType">
          <el-checkbox
            v-for="(item, index) in stagesType"
            :key="index"
            :label="item.value"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="商品售价">
        {{ getLabel(form.pad, padList, "amount", "label") }}
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit">生成二维码</el-button>
      </el-form-item>
    </el-form>
    <div v-if="imgUrl" style="text-align: center; padding-bottom: 10px">
      <el-dialog title="加购平板二维码" v-model="dialogVisible">
        <ErCodeDown :imgUrl="imgUrl" />
      </el-dialog>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
