<!--
 * @Date         : 2025-01-20 10:00:00
 * @Description  : Tabs设置对话框组件
 * @Autor        : AI Assistant
 * @LastEditors: xia<PERSON>hen <EMAIL>
-->

<script setup lang="ts">
import { computed, ref, nextTick } from "vue";
import { Grid, Top, Close } from "@element-plus/icons-vue";
import Sortable from "sortablejs";

interface TabItem {
  name: string;
  label: string;
  component: any;
  props?: any;
  show: boolean;
  fixed?: boolean;
  condition?: () => boolean;
}

interface Props {
  visible: boolean;
  allTabs: TabItem[];
  visibleTabs: TabItem[];
}

interface Emits {
  (e: "update:visible", value: boolean): void;
  (
    e: "confirm",
    data: { visibleTabs: TabItem[]; hiddenTabNames: string[] }
  ): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit("update:visible", value)
});

// 设置对话框相关数据
const selectedTabNames = ref<string[]>([]);
const settingTabs = ref<TabItem[]>([]);

// 初始化设置对话框数据
function initSettingData() {
  selectedTabNames.value = props.visibleTabs.map(tab => tab.name);
  // 使用深拷贝确保不会共享引用
  settingTabs.value = props.visibleTabs.map(tab => ({ ...tab }));
}

// 全选/取消全选
const checkAll = computed(() => {
  return selectedTabNames.value.length === props.allTabs.length;
});

const isIndeterminate = computed(() => {
  return (
    selectedTabNames.value.length > 0 &&
    selectedTabNames.value.length < props.allTabs.length
  );
});

function handleCheckAllChange(val: boolean) {
  selectedTabNames.value = val
    ? props.allTabs.map(tab => tab.name)
    : props.allTabs.filter(tab => tab.fixed).map(tab => tab.name);
  updateSettingTabs();
}

// 更新设置标签页
function updateSettingTabs() {
  settingTabs.value = props.allTabs.filter(tab =>
    selectedTabNames.value.includes(tab.name)
  );
}

// 切换标签页选择状态
function toggleTabSelection(
  checked: boolean | string | number,
  tabName: string
) {
  const isChecked = Boolean(checked);
  if (isChecked) {
    const tab = props.allTabs.find(t => t.name === tabName);
    if (tab) {
      settingTabs.value.push(tab);
    }
  } else {
    settingTabs.value = settingTabs.value.filter(tab => tab.name !== tabName);
  }
}

// 保存设置
function saveSettings() {
  const hiddenTabNames = props.allTabs
    .filter(tab => !selectedTabNames.value.includes(tab.name))
    .map(tab => tab.name);

  emit("confirm", {
    visibleTabs: settingTabs.value.map(tab => ({ ...tab })),
    hiddenTabNames
  });

  dialogVisible.value = false;
}

// 取消设置
function cancelSettings() {
  dialogVisible.value = false;
}

// 将标签页移动到顶部
function top(index: number) {
  if (index > 0) {
    const item = settingTabs.value.splice(index, 1)[0];
    settingTabs.value.unshift(item);
  }
}

// 初始化拖拽排序
function initSortable() {
  nextTick(() => {
    const el = document.querySelector(".setting-tabs-sortable");
    if (el) {
      new Sortable(el, {
        animation: 150,
        handle: ".move",
        ghostClass: "sortable-ghost",
        onEnd: ({ oldIndex, newIndex }) => {
          if (oldIndex !== newIndex) {
            const itemToMove = settingTabs.value.splice(oldIndex, 1)[0];
            settingTabs.value.splice(newIndex, 0, itemToMove);
          }
        }
      });
    }
  });
}

// 监听对话框打开事件
function handleDialogOpen() {
  initSettingData();
  initSortable();
}
</script>

<template>
  <el-dialog
    title="模块设置"
    v-model="dialogVisible"
    width="60%"
    :before-close="cancelSettings"
    @open="handleDialogOpen"
  >
    <div class="mb-20px">
      左侧【可选模块】中被勾选的字段将会显示在列表，可以在右侧【已选模块】区域点击每个字段前面的图标拖动进行排序
    </div>
    <div class="flex">
      <div class="checked-box">
        <div class="text-18px c-black bg-#f8f8f8 px-10px py-10px">可选模块</div>
        <div class="p-10px">
          <el-checkbox
            v-model="checkAll"
            :indeterminate="isIndeterminate"
            @change="handleCheckAllChange"
          >
            全选
          </el-checkbox>
          <el-checkbox-group v-model="selectedTabNames">
            <el-checkbox
              v-for="(tab, index) in props.allTabs"
              :key="index"
              :label="tab.name"
              :disabled="tab.fixed"
              @change="(checked: boolean) => toggleTabSelection(checked, tab.name)"
            >
              {{ tab.label }}
            </el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div class="sort-box">
        <div class="text-18px c-black bg-#f8f8f8 px-10px py-10px">
          已选标签页
        </div>
        <div class="p-10px setting-tabs-sortable">
          <div
            class="flex justify-between items-center mb-10px"
            v-for="(tab, index) in settingTabs"
            :key="tab.name"
          >
            <div class="flex items-center">
              <el-icon :class="['cursor-move', 'move']" size="18">
                <Grid />
              </el-icon>
              <div class="ml-8px">{{ tab.label }}</div>
            </div>
            <div>
              <el-icon
                size="18"
                color="#409efc"
                class="mr-5px cursor-pointer"
                @click="top(index)"
              >
                <Top />
              </el-icon>
              <el-icon
                v-if="!tab.fixed"
                size="18"
                color="red"
                class="cursor-pointer"
                @click="
                  selectedTabNames = selectedTabNames.filter(
                    name => name !== tab.name
                  );
                  updateSettingTabs();
                "
              >
                <Close />
              </el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="cancelSettings">取 消</el-button>
      <el-button type="primary" @click="saveSettings">确 定</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.flex {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.checked-box {
  flex: 1;
  border: 1px solid #ccc;
  margin-right: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.sort-box {
  min-width: 240px;
  border: 1px solid #ccc;
  max-height: 400px;
  overflow-y: auto;
}

.sortable-ghost {
  background-color: #409efc;
  color: #fff;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-move {
  cursor: move;
}

.mb-10px {
  margin-bottom: 10px;
}

.mb-20px {
  margin-bottom: 20px;
}
</style>
