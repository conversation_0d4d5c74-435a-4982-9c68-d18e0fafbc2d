/*
 * @Date         : 2025-01-20 10:00:00
 * @Description  : Tabs配置管理composable
 * @Autor        : AI Assistant
 * @LastEditors: xiaozhen <EMAIL>
 */

import { ref, computed, type ComputedRef } from "vue";
import { ElMessage } from "element-plus";
import { getProfileApi, setProfileApi } from "/@/api/active";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";

export interface TabItem {
  name: string;
  label: string;
  component: any;
  props?: any;
  show: boolean;
  fixed?: boolean;
  condition?: () => boolean;
}

export function useTabsConfig(allTabs: ComputedRef<TabItem[]>) {
  const activeTab = ref<string>("orderRecord");
  const visibleTabs = ref<TabItem[]>([]);
  const hiddenTabNames = ref<string[]>([]);
  const loading = ref(false);
  const { userMsg } = storeToRefs(useUserStore());
  const nameKey = `customerTabs-${userMsg.value.id}`;

  // 过滤出符合条件的标签页
  const filteredTabs = computed(() => {
    return allTabs.value.filter(tab => {
      if (tab.condition) {
        return tab.condition() && tab.show;
      }
      return tab.show;
    });
  });

  // 从后端加载配置
  async function loadTabsConfig() {
    try {
      loading.value = true;
      const response = await getProfileApi({
        name: nameKey
      });

      if (response.data.value.hiddenTabs) {
        const config = response.data.value;
        hiddenTabNames.value = config.hiddenTabs || [];

        // 如果有保存的顺序，按照保存的顺序排列标签页
        if (config.tabOrder && config.tabOrder.length > 0) {
          const orderedTabs: TabItem[] = [];

          // 按照保存的顺序添加标签页
          config.tabOrder.forEach((tabName: string) => {
            const tab = filteredTabs.value.find(t => t.name === tabName);
            if (tab && !hiddenTabNames.value.includes(tabName)) {
              orderedTabs.push({ ...tab });
            }
          });

          // 添加新增的标签页（可能是后来添加的功能）
          filteredTabs.value.forEach(tab => {
            if (
              !config.tabOrder.includes(tab.name) &&
              !hiddenTabNames.value.includes(tab.name)
            ) {
              orderedTabs.push({ ...tab });
            }
          });

          activeTab.value = orderedTabs[0].name;

          visibleTabs.value = orderedTabs;
        } else {
          // 如果没有保存顺序，使用默认过滤
          visibleTabs.value = filteredTabs.value.filter(
            tab => !hiddenTabNames.value.includes(tab.name)
          );
        }
      } else {
        // 没有保存的配置，使用默认配置
        visibleTabs.value = filteredTabs.value;
      }
    } catch (error) {
      console.error("加载标签页配置失败", error);
      // 出错时使用默认配置
      visibleTabs.value = filteredTabs.value;
    } finally {
      loading.value = false;
    }
  }

  // 保存配置到后端
  async function saveTabsConfig() {
    try {
      loading.value = true;
      const config = {
        hiddenTabs: hiddenTabNames.value,
        tabOrder: visibleTabs.value.map(tab => tab.name)
      };

      await setProfileApi({
        note: "客户详情tab配置",
        name: nameKey,
        value: {
          ...config
        }
      });

      ElMessage.success("配置保存成功");
    } finally {
      loading.value = false;
    }
  }

  // 更新配置
  function updateTabsConfig(
    newVisibleTabs: TabItem[],
    newHiddenTabNames: string[]
  ) {
    visibleTabs.value = newVisibleTabs;
    hiddenTabNames.value = newHiddenTabNames;
    saveTabsConfig();
  }

  // 处理新增tab的情况
  function handleNewTabs() {
    const currentTabNames = visibleTabs.value.map(tab => tab.name);
    const newTabs = filteredTabs.value.filter(
      tab =>
        !currentTabNames.includes(tab.name) &&
        !hiddenTabNames.value.includes(tab.name)
    );

    if (newTabs.length > 0) {
      // 将新增的tab添加到最后
      visibleTabs.value = [...visibleTabs.value, ...newTabs];
      saveTabsConfig();
    }
  }

  return {
    visibleTabs,
    hiddenTabNames,
    filteredTabs,
    loading,
    activeTab,
    loadTabsConfig,
    saveTabsConfig,
    updateTabsConfig,
    handleNewTabs
  };
}
