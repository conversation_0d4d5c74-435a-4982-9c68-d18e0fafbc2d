<template>
  <el-card>
    <div v-loading="loading">
      <div class="top-route">
        <div class="mb-10px">
          <el-button
            :icon="ArrowLeft"
            circle
            :disabled="!routeList.length"
            type="primary"
            @click="back"
          />
          <el-button :icon="Refresh" circle type="primary" @click="refresh" />
        </div>
      </div>
      <div />
      <iframe
        ref="IframeRef"
        :width="deviceDetection() ? '333' : '375'"
        :src="url"
        :height="iframeHeight"
        class="g-margin-b-20"
        @load="loadIframe"
      />
      <div class="mb-10px">
        <el-button type="primary" @click="clickCapture" :loading="!loadingPage">
          一键截图当前页面
        </el-button>
        <el-button type="primary" @click="clickQrCode">
          生成小程序二维码
        </el-button>
      </div>
      <el-button type="primary" @click="copyUrl">复制链接</el-button>
      <QRCard v-if="qrCardModal" v-model:value="qrCardModal" :url="qrLink" />
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import { watch, ref, onMounted, onUnmounted, onDeactivated } from "vue";
import { ArrowLeft, Refresh } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { deviceDetection } from "/@/utils/deviceDetection";
import { useClipboard, useThrottleFn } from "@vueuse/core";
import {
  convertImageUrlToBase64,
  copyToClipboard
} from "/@/utils/common/imgUrlTransform";
import QRCard from "../../dialog/QRCard.vue";
import { forwardApi } from "/@/api/common";

const IframeRef = ref();
type Orientation = "portrait" | "horizontal";
const routeList = ref<{ orientation: Orientation }[]>([]);
const forwardList = ref<{ orientation: Orientation }[]>([]);
const currentStatus = ref<Orientation>("portrait");
const url = ref();
const studyReportTab = ref("");
const loading = ref(false);
const loadingPage = ref(false);
const isOldReport = ref(false);
const studyReportDateDaily = ref("");
const studyReportDateWeekly = ref("");
const studyReportTextbook = ref({ stageId: Number, subjectId: Number });
const iframeHeight = ref(667);
const qrCardModal = ref(false);
const qrLink = ref<string>("");
const isMessage = ref(false);

const props = defineProps<{
  token?: string;
  userId: string;
  active: string;
}>();
const omvd = Math.random().toString(36).substring(2, 12);

const { copy } = useClipboard();

watch(
  () => props.token,
  value => {
    if (value) {
      url.value = `${
        import.meta.env.VITE_7to12_HOST
      }/onion-learning/study-report/?token=${value}&channel=crm&fromPageName=crm&userId=${
        props.userId
      }&omvd=${omvd}`;
    }
  }
);

watch(
  () => props.active,
  value => {
    if (value === "1") {
      localStorage.setItem("message-userId", props.userId);
      window.addEventListener("message", listenPostMessage);
      window.addEventListener("message", screenshotMessage);
    } else {
      window.removeEventListener("message", listenPostMessage);
      window.removeEventListener("message", screenshotMessage);
    }
  }
);

const copyUrl = () => {
  const omvd = Math.random().toString(36).substring(2, 12);
  copy(
    `${
      import.meta.env.VITE_7to12_HOST
    }/onion-learning/study-report/?token=${encodeURIComponent(
      props.token
    )}&channel=crm_msg&fromPageName=crm_msg&userId=${
      props.userId
    }&origin=wuhan_msg&omvd=${omvd}`
  );
  ElMessage.success("复制成功");
};
const back = () => {
  if (!routeList.value.length) return;
  const route = routeList.value.pop();
  const last = routeList.value[routeList.value.length - 1];
  currentStatus.value = last?.orientation || "portrait";
  forwardList.value.push(route!);
  window.history.go(-1);
};

const refresh = () => {
  const link = url.value;
  url.value = "";
  routeList.value = [];
  currentStatus.value = "portrait";
  setTimeout(() => {
    url.value = link;
  }, 0);
};

const loadIframe = () => {
  if (url.value) {
    loadingPage.value = true;
  }
};

/** CRM分享截图 */
const clickCapture = () => {
  if (isOldReport.value) {
    ElMessage.warning("旧学情报告不支持截图～");
    return;
  }
  loading.value = true;
  isMessage.value = false;
  setTimeout(() => {
    if (!isMessage.value) {
      ElMessage.warning("该页面暂不支持截图");
      loading.value = false;
    }
  }, 3000);
  // screenShot(IframeRef.value)
  IframeRef.value.contentWindow.postMessage(
    {
      type: "SCREENSHOT_START"
    },
    "*"
  );
};

const envVersion = () => {
  switch (import.meta.env.MODE) {
    case "test":
      return "develop";
    case "stage":
      return "trial";
    case "master":
      return "release";
    default:
      return "develop";
  }
};

const generateQrCode = data => {
  return new Promise((resolve, reject) => {
    let scene = "";
    forwardApi({
      targetReal: "study-parent.7to12",
      targetPath: "/study-parent/wechat/saveShareLoginKey",
      method: "post",
      ...data
    })
      .then(res => {
        if (res?.data) {
          scene = res.data?.key || "";
        }
      })
      .finally(() => {
        forwardApi({
          targetReal: "wechat-base.7to12",
          targetPath: "/wechat-base/mini_program/qrcode",
          method: "post",
          appid: "wxad8a35ca577b3b8d",
          scene: `yc${scene}`,
          envVersion: envVersion()
        })
          .then(({ data }) => {
            if (data.image) {
              convertImageUrlToBase64(data.image)
                .then(base64 => {
                  resolve(base64);
                })
                .catch(err => {
                  reject(err);
                });
            } else {
              reject(new Error(data.errmsg));
            }
          })
          .catch(err => {
            reject(err);
          });
      });
  });
};

/** CRM分享生成二维码 */
const clickQrCode = () => {
  loading.value = true;
  generateQrCode({
    scene: "STUDY_REPORT_SHARE",
    uid: props.userId,
    expireSecond: 3600 * 24,
    data: {
      tab: studyReportTab.value,
      date:
        studyReportTab.value === "daily"
          ? studyReportDateDaily.value
          : studyReportDateWeekly.value,
      fromPageName: "crm_copyReportQRCode"
    }
  })
    .then((res: any) => {
      qrLink.value = res;
      qrCardModal.value = true;
    })
    .catch(err => {
      ElMessage.error("生成小程序二维码失败", err.message || err.msg);
    })
    .finally(() => {
      loading.value = false;
    });
};

const screenshotMessage = useThrottleFn(async event => {
  isMessage.value = true;
  if (localStorage.getItem("message-userId") !== props.userId) {
    return;
  }
  if (event.data?.type === "SHOW_REPORT_NEW") {
    isOldReport.value = false;
  } else if (event.data?.type === "SHOW_REPORT_OLD") {
    isOldReport.value = true;
  }
  if (!event.data?.type?.includes("SCREENSHOT")) {
    return;
  }
  const { height, type, error, blob } = event.data;
  if (height) {
    iframeHeight.value = height;
    await nextTick();
  }

  if (type === "SCREENSHOT_END") {
    await nextTick();
    iframeHeight.value = 667;
    loading.value = false;
    if (error) {
      ElMessage.error(`截图生成失败:${error}`);
    } else {
      ElMessage.success("截图生成成功");
      copyToClipboard(blob)
        .then(() => {
          ElMessage.success("学情报告图片复制成功");
        })
        .catch(err => {
          console.error("学情报告图片复制失败", err);
          ElMessage.error(`学情报告图片复制失败: ${err.message || err.msg}`);
        });
    }
  }
});

const listenPostMessage = useThrottleFn(event => {
  if (localStorage.getItem("message-userId") !== props.userId) {
    return;
  }
  // 验证消息来源，确保安全
  if (
    event.origin.indexOf("yangcongxing") !== -1 ||
    event.origin.indexOf("5173") !== -1
  ) {
    const { type, orientation, tab, date, url: link, textbook } = event.data;

    if (type === "ROUTE_CHANGE") {
      currentStatus.value = orientation;
      routeList.value.push({ orientation });
    } else if (type === "TAB_CHANGE") {
      // CRM分享截图参数
      studyReportTab.value = tab;
    } else if (type === "DATE_DAILY_CHANGE") {
      // CRM分享截图参数
      studyReportDateDaily.value = date;
    } else if (type === "DATE_WEEKLY_CHANGE") {
      // CRM分享截图参数
      studyReportDateWeekly.value = date;
    } else if (type === "URL_CHANGE") {
      // url.value = link;
      console.log("1111111111 URL_CHANGE", link);
    } else if (type === "TEXTBOOK_CHANGE") {
      // CRM分享截图参数
      studyReportTextbook.value = textbook;
      console.log("1111111111 TEXTBOOK_CHANGE", textbook);
    }
  }
});

onMounted(() => {
  localStorage.setItem("message-userId", props.userId);
  window.addEventListener("message", listenPostMessage);
  window.addEventListener("message", screenshotMessage);
});

onActivated(() => {
  localStorage.setItem("message-userId", props.userId);
  window.addEventListener("message", listenPostMessage);
  window.addEventListener("message", screenshotMessage);
});

onUnmounted(() => {
  window.removeEventListener("message", listenPostMessage);
  window.removeEventListener("message", screenshotMessage);
});

onDeactivated(() => {
  window.removeEventListener("message", listenPostMessage);
  window.removeEventListener("message", screenshotMessage);
});
</script>

<style lang="scss" scoped>
.iframe-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400px;
  padding: 20px 0;
  cursor: pointer;
  background: #ffffff;

  .top-route {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 20px 10px;
    color: #000;
  }

  iframe {
    width: 375px;
    height: 667px;
    padding: 0 20px 20px;
    resize: vertical;
    border: none;
    border-right: 0.5px solid #ebeef5;
    border-bottom: 0.5px solid #ebeef5;
  }

  .iframe__copy-url {
    margin-left: auto;
  }
}

.iframe-container-landscape {
  width: 700px;

  iframe {
    width: 667px;
    height: 375px;
    resize: none;
    border: none;
    border-right: 0.5px solid #ebeef5;
    border-bottom: 0.5px solid #ebeef5;
  }
}
</style>
