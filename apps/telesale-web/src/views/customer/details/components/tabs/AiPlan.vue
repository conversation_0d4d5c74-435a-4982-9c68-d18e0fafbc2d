<!--
 * @Date         : 2024-11-21 10:43:30
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<script setup lang="ts">
import AllClass from "../aiClass/AllClass.vue";
import StudyClass from "../aiClass/StudyClass.vue";
import AddClass from "../aiClass/dialog/AddClass.vue";

const props = defineProps<{
  userId: string;
  onionId: string;
}>();

const active = ref("all");
const isModal = ref(false);
const allClassRef = ref();

const add = () => {
  isModal.value = true;
};

const getClass = () => {
  allClassRef.value?.getData();
};
</script>

<template>
  <div class="relative">
    <div class="absolute top-0 right-10px z-10">
      <el-button type="primary" @click="add">新增班级</el-button>
    </div>
    <el-tabs v-model="active">
      <el-tab-pane label="所有班课" name="all">
        <AllClass ref="allClassRef" :userId="props.userId" />
      </el-tab-pane>
      <el-tab-pane label="正在学的班课动态" name="studying" lazy>
        <StudyClass :userId="props.userId" />
      </el-tab-pane>
    </el-tabs>

    <AddClass
      v-if="isModal"
      v-model:value="isModal"
      :userId="props.userId"
      :onionId="props.onionId"
      @onSearch="getClass"
    />
  </div>
</template>
