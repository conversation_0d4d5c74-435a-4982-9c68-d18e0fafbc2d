<!--
 * @Date         : 2024-11-27 18:04:53
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<template>
  <div class="report-v3 flex justify-center">
    <div v-if="!props.userId">
      <el-alert title="未获取到用户id" type="warning" :closable="false" />
    </div>
    <div v-else class="min-w-400px">
      <el-tabs v-model="active">
        <el-tab-pane label="学情报告" name="1">
          <IframeContainer
            :active="active"
            :token="token"
            :userId="props.userId"
          />
        </el-tab-pane>
        <el-tab-pane label="学情分析" name="2" lazy>
          <StudyAnalze :active="active" :token="token" :userId="props.userId" />
        </el-tab-pane>
        <el-tab-pane label="学科分析" name="3" lazy>
          <StageAnalze :userId="props.userId" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import IframeContainer from "./IframeContainer.vue";
import { ref } from "vue";
import { getIframeTokenApi } from "/@/api/customer/details";
import StudyAnalze from "./components/StudyAnalze.vue";
import StageAnalze from "./components/StageAnalze.vue";

const props = defineProps<{
  userId: string;
}>();

const token = ref("");

const active = ref("1");

function init() {
  getIframeTokenApi({
    userId: props.userId,
    source: "dianxiao"
  }).then(res => {
    token.value = res.data.token;
  });
}

props.userId && init();
</script>

<style lang="scss" scoped>
.report-v3 {
  display: flex;
  min-height: 320px;
  text-align: center;
}
</style>
