<!--
 * @Date         : 2025-01-20 10:00:00
 * @Description  : 通用Tabs容器组件
 * @Autor        : AI Assistant
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import { ref, computed, onMounted, watch } from "vue";
import { Setting } from "@element-plus/icons-vue";
import TabsSettingDialog from "./TabsSettingDialog.vue";
import { useTabsConfig, type TabItem } from "../composables/useTabsConfig";

interface Props {
  pageName: string;
  allTabs: TabItem[];
  defaultActiveTab?: string;
  showSetting?: boolean;
}

interface Emits {
  (e: "tab-change", tabName: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  showSetting: true,
  defaultActiveTab: ""
});

const emit = defineEmits<Emits>();

const activeTab = ref(props.defaultActiveTab || "");
const settingVisible = ref(false);
const tabRefs = ref<Record<string, any>>({});

// 使用tabs配置composable
const {
  visibleTabs,
  filteredTabs,
  loading,
  activeTab: activeTabConfig,
  loadTabsConfig,
  updateTabsConfig,
  handleNewTabs
} = useTabsConfig(computed(() => props.allTabs));

// 设置tab组件的ref
function setTabRef(tabName: string, el: any) {
  if (el) {
    tabRefs.value[tabName] = el;
  }
}

// 获取指定tab的ref
function getTabRef(tabName: string) {
  return tabRefs.value[tabName];
}

// 打开设置对话框
function openSettingDialog() {
  settingVisible.value = true;
}

// 处理设置确认
function handleSettingConfirm(data: {
  visibleTabs: TabItem[];
  hiddenTabNames: string[];
}) {
  updateTabsConfig(data.visibleTabs, data.hiddenTabNames);
}

// 处理tab切换
function handleTabChange(tabName: string) {
  emit("tab-change", tabName);
}

watch(
  () => activeTabConfig.value,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      activeTab.value = newVal;
    }
  }
);

// 监听tabs变化，处理新增tab
watch(
  () => props.allTabs.length,
  () => {
    handleNewTabs();
  }
);

defineExpose({
  loadTabsConfig,
  visibleTabs,
  activeTab,
  getTabRef,
  tabRefs
});
</script>

<template>
  <div class="tabs-container" v-loading="loading">
    <div class="tabs-header">
      <el-tabs
        type="border-card"
        v-model="activeTab"
        @tab-change="handleTabChange"
      >
        <el-tab-pane
          v-for="(tab, index) in visibleTabs"
          :key="index"
          :label="tab.label"
          :name="tab.name"
          lazy
        >
          <component
            :is="tab.component"
            v-bind="tab.props"
            :ref="(el: any) => setTabRef(tab.name, el)"
          />
        </el-tab-pane>
      </el-tabs>
      <el-button
        v-if="showSetting"
        class="setting-btn"
        type="primary"
        circle
        @click="openSettingDialog"
      >
        <el-icon><Setting /></el-icon>
      </el-button>
    </div>

    <!-- 设置对话框 -->
    <TabsSettingDialog
      v-if="showSetting"
      v-model:visible="settingVisible"
      :all-tabs="filteredTabs"
      :visible-tabs="visibleTabs"
      @confirm="handleSettingConfirm"
    />
  </div>
</template>

<style lang="scss" scoped>
.tabs-container {
  position: relative;
}

.tabs-header {
  position: relative;
}

.setting-btn {
  position: absolute;
  right: 10px;
  top: -18px;
  z-index: 100;
}
</style>
