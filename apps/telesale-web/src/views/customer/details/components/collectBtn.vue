<!--
 * @Date         : 2024-10-14 18:03:12
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { Star, StarFilled } from "@element-plus/icons-vue";
import { getClueCollectApi } from "/@/api/customer/collect";
import CollectModal from "/@/components/CollectModal/index.vue";

const props = defineProps<{
  infoUuid: string;
}>();

const collectId = ref<number>(undefined);
const loading = ref(false);

const handlerQuery = () => {
  loading.value = true;
  getClueCollectApi({ infoUuids: [props.infoUuid] })
    .then(res => {
      collectId.value = res.data.collectInfos[0]?.id;
    })
    .finally(() => {
      loading.value = false;
    });
};

handlerQuery();

onActivated(() => {
  handlerQuery();
});
</script>

<template>
  <div v-loading="loading" class="inline-block">
    <CollectModal
      :collectInfo="{
        infoUuid: props.infoUuid,
        id: collectId
      }"
      @success="handlerQuery"
      @remove="handlerQuery"
    >
      <el-button type="primary" plain>
        <template v-if="collectId">
          <el-icon :size="22" color="#ffc714">
            <StarFilled />
          </el-icon>
        </template>
        <template v-else>
          <el-icon :size="20" color="#ffc714">
            <Star />
          </el-icon>
        </template>
      </el-button>
    </CollectModal>
  </div>
</template>

<style lang="scss" scoped></style>
