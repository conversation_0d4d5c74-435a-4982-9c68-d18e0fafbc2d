<!--
 * @Date         : 2024-07-18 16:37:46
 * @Description  : 新增编辑视频
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { forwardApi } from "/@/api/common";
import dayjs from "dayjs";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";

interface Props {
  value: boolean;
  userId: string;
  onionId: string;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const { userMsg } = storeToRefs(useUserStore());

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance | null>();
const classList = ref([]);
const classInfo = ref();
const form = ref({
  classId: undefined,
  userIds: undefined,
  operationUser: undefined,
  publisher: undefined,
  weekdays: undefined,
  examTime: undefined,
  customDays: undefined,
  difficulty: undefined
});

const daysList = [
  {
    label: "周一",
    value: 1
  },
  {
    label: "周二",
    value: 2
  },
  {
    label: "周三",
    value: 3
  },
  {
    label: "周四",
    value: 4
  },
  {
    label: "周五",
    value: 5
  },
  {
    label: "周六",
    value: 6
  },
  {
    label: "周日",
    value: 0
  }
];

const disabledDate = (time: Date, startTime: string, endTime: string) => {
  const start = dayjs(startTime);
  const end = dayjs(endTime);
  const now = dayjs(time);
  return now.isBefore(start) || now.isAfter(end);
};

const rules: FormRules = {
  classId: [
    {
      required: true,
      message: "请选择班级名称",
      trigger: "change"
    }
  ],
  publisher: [
    {
      required: true,
      message: "请选择教材版本",
      trigger: "change"
    }
  ],
  examTime: [
    {
      required: true,
      message: "请选择考试日期",
      trigger: "change"
    }
  ],
  customDays: [
    {
      required: true,
      message: "请选择班课学习日期",
      trigger: "change"
    }
  ],
  weekdays: [
    {
      required: true,
      message: "请选择每周学习日",
      trigger: "change"
    }
  ],
  difficulty: [
    {
      required: true,
      message: "请选择内容难度",
      trigger: "change"
    }
  ]
};
function handleClose() {
  isModel.value = false;
}

const getClassList = () => {
  loading.value = true;
  forwardApi({
    targetPath: "/study-plan/admin/classList",
    method: "post",
    target: "studyPlan",
    status: 3,
    pageSize: 0
  })
    .then(res => {
      classList.value = res.data.list?.map(item => {
        return {
          label: item.id + "-" + item.nickname,
          value: item.id
        };
      });
    })
    .finally(() => {
      loading.value = false;
    });
};
const getClassInfo = async (val: number) => {
  if (val) {
    loading.value = true;
    forwardApi({
      targetPath: "/study-plan/admin/joinClassFields",
      method: "get",
      target: "studyPlan",
      id: val
    })
      .then(res => {
        classInfo.value = res.data;
        classInfo.value.publisherLabels =
          classInfo.value.publisherLabels?.map(item => {
            return {
              label: item.name,
              value: item.id
            };
          }) || [];
        form.value.publisher = undefined;
        form.value.weekdays = undefined;
        form.value.examTime = undefined;
        form.value.customDays = undefined;
        form.value.difficulty = undefined;
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

const submit = async () => {
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      const params = {
        ...form.value,
        userIds: [props.userId],
        operationUser: userMsg.value?.name,
        labelIds: []
      };
      if (form.value.publisher) {
        params.labelIds.push(params.publisher);
        delete params.publisher;
      }
      if (params.difficulty) {
        params.labelIds.push(params.difficulty);
        delete params.difficulty;
      }

      loading.value = true;
      forwardApi({
        targetPath: "/study-plan/admin/add/class/users",
        method: "post",
        target: "studyPlan",
        ...params
      })
        .then(res => {
          loading.value = false;
          if (res.data.errors?.length) {
            ElMessage.error(res.data.errors[0].error);
            return;
          }
          ElMessage.success("操作成功");
          handleClose();
          emit("onSearch");
        })
        .catch(err => {
          loading.value = false;
        });
    }
  });
};

getClassList();
</script>

<template>
  <el-dialog
    title="添加班级"
    v-model="isModel"
    :before-close="handleClose"
    width="900px"
    @submit.prevent="submit"
  >
    <div v-loading="loading">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="洋葱ID" prop="onionId">
          {{ props.onionId }}
        </el-form-item>
        <el-form-item label="班级名称" prop="classId">
          <el-select-v2
            v-model="form.classId"
            :options="classList"
            placeholder="请选择班级"
            clearable
            filterable
            @change="getClassInfo"
            style="width: 100%"
          />
        </el-form-item>
        <template v-if="classInfo">
          <el-form-item label-width="0">
            <div class="flex flex-wrap pl-38px">
              <span
                v-for="(item, index) in classInfo.classInfo.labelNames"
                :key="index"
                class="mr-20px mb-10px"
              >
                {{ item.name }}：{{ item.subName }}
              </span>
            </div>
          </el-form-item>
          <el-form-item
            label="教材版本"
            prop="publisher"
            v-if="classInfo.publisherLabels?.length"
          >
            <el-select-v2
              v-model="form.publisher"
              :options="classInfo.publisherLabels"
              placeholder="请选择教材版本"
              clearable
              filterable
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item
            label="考试日期"
            prop="examTime"
            v-if="classInfo.examTime"
          >
            <el-date-picker
              style="width: 100%"
              v-model="form.examTime"
              type="date"
              value-format="YYYY-MM-DD"
              :disabled-date="
                $event =>
                  disabledDate(
                    $event,
                    classInfo.examTime?.startTime,
                    classInfo.examTime?.endTime
                  )
              "
              placeholder="选择考试日期"
            />
          </el-form-item>
          <el-form-item
            label="班课学习日期"
            prop="customDays"
            v-if="classInfo.customDays"
          >
            <el-date-picker
              style="width: 100%"
              v-model="form.customDays"
              type="dates"
              value-format="YYYY-MM-DD"
              :disabled-date="
                $event =>
                  disabledDate(
                    $event,
                    classInfo.customDays?.startTime,
                    classInfo.customDays?.endTime
                  )
              "
              placeholder="选择班课学习日期"
            />
          </el-form-item>
          <el-form-item
            label="每周学习日"
            prop="weekdays"
            v-if="classInfo.weekDays?.length"
          >
            <el-checkbox-group v-model="form.weekdays">
              <el-checkbox
                v-for="item in daysList"
                :key="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item
            label="内容难度"
            prop="difficulty"
            v-if="classInfo.difficultyLabels?.length"
          >
            <el-radio-group v-model="form.difficulty">
              <el-radio
                v-for="item in classInfo.difficultyLabels"
                :key="item.id"
                :label="item.id"
              >
                {{ item.name }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
