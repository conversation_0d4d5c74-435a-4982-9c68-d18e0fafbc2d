/*
 * @Date         : 2024-12-24 16:14:15
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */
import dayjs from "dayjs";

export const weekdays = {
  0: "周日",
  1: "周一",
  2: "周二",
  3: "周三",
  4: "周四",
  5: "周五",
  6: "周六"
};

/**
 * 获取学段ID。
 * @param {string} grade - 年级名称。
 * @returns {number} 学段ID。
 */
export const getStageId = grade => {
  const stages = {
    "1年级": 1,
    "2年级": 1,
    "3年级": 1,
    "4年级": 1,
    "5年级": 1,
    "6年级": 1,
    "7年级": 2,
    "8年级": 2,
    "9年级": 2
  };
  return stages[grade] || -1; // 返回-1表示无效的年级名称
};

export function getScoreLabel(val, subjectLabel, gradeLabel) {
  let label = "";
  if (subjectLabel === "数学") {
    switch (gradeLabel) {
      case "1年级":
      case "2年级":
        label = val === "A" ? "85分以下" : "85分以上";
        break;
      case "3年级":
      case "4年级":
      case "5年级":
      case "6年级":
        // eslint-disable-next-line no-case-declarations
        const labelMap = {
          A: "80分以下",
          "A+": "80分-95分",
          S: "95分以上"
        };
        label = labelMap[val];
        break;

      default:
        break;
    }
  } else {
    label = val === "A" ? "90分以下" : "90分以上";
  }
  return label;
}

const commonEnglishDescriptions = {
  weakness: {
    intro: "学习基础较为薄弱，校内成绩不稳定。因此需要",
    end: "夯实本年级学习基础，全面提升校内成绩"
  },
  solid: {
    intro: "学习基础较为牢固，校内成绩优异。因此建议",
    end: "提前学习校内知识，搞定高分难点题"
  }
};

const commonMathDescriptions = {
  weakness: {
    intro: "学习基础较为薄弱，校内成绩不稳定。因此需要",
    end: "夯实本年级学习基础，全面提升校内成绩"
  },
  secure: {
    intro: "学习基础较为牢固，校内成绩优异。因此建议",
    end: "提前学习校内知识，搞定高分难点题"
  },
  solid: {
    intro: "学习基础非常扎实，校内成绩无忧。因此建议",
    end: "提前深度训练数学思维，提前掌握小初必学内容"
  }
};

const commonGradeDescriptions1_2 = (A, Agood) => {
  const descriptions = {};

  if (A) descriptions["A"] = commonMathDescriptions.weakness;
  if (Agood) descriptions["A+"] = commonMathDescriptions.secure;

  return descriptions;
};

const commonGradeDescriptions3_6 = (A, Agood, S) => {
  const descriptions = {};

  if (A) descriptions["A"] = commonMathDescriptions.weakness;
  if (Agood) descriptions["A+"] = commonMathDescriptions.secure;
  if (S) descriptions["S"] = commonMathDescriptions.solid;

  return descriptions;
};

const commonGradeEnglishDescriptions = (A, Agood) => {
  const descriptions = {};

  if (A) descriptions["A"] = commonEnglishDescriptions.weakness;
  if (Agood) descriptions["A+"] = commonEnglishDescriptions.solid;

  return descriptions;
};

// 您孩子目前「学科」学科的「成绩水平」，说明.
export const getScoreDescriptions = (subjectLabelName, gradeLabelName) => {
  if (subjectLabelName === "数学") {
    if (["1年级", "2年级"].includes(gradeLabelName)) {
      return commonGradeDescriptions1_2("A", "A+");
    }
    return commonGradeDescriptions3_6("A", "A+", "S");
  }
  if (subjectLabelName === "英语") {
    return commonGradeEnglishDescriptions("A", "A+");
  }
};

export const rank = [
  {
    value: 1,
    label: "成绩顶尖",
    subTitle: "班级50人中排名1-5"
  },
  {
    value: 2,
    label: "成绩上游",
    subTitle: "班级50人中排名5-10"
  },
  {
    value: 3,
    label: "成绩中上游",
    subTitle: "班级50人中排名10-20"
  },
  {
    value: 4,
    label: "成绩中游",
    subTitle: "班级50人中排名20-30"
  },
  {
    value: 5,
    label: "成绩中下游",
    subTitle: "班级50人中排名30-40"
  },
  {
    value: 6,
    label: "成绩下游",
    subTitle: "班级50人中排名40-50"
  }
];

// 建议3：
export const getStudyHabits = (subjectLabelName, gradeLabelName) => {
  if (subjectLabelName === "数学") return "学知识-练好题-重点巩固-改错题";

  if (["1年级", "2年级"].includes(gradeLabelName))
    return "学知识-学拼读-练发音";

  return "学知识-学拼读-练发音-背单词-读文章-试身手";
};

export const subjectImportance = {
  数学: {
    "1年级": "数学兴趣启蒙和基础能力的起点。",
    "2年级": "计算熟练度和逻辑推理的提升期。",
    "3年级": "应用和多步运算的进阶学习阶段。",
    "4年级": "全面提升思维深度和数学理解的时期。",
    "5年级": "强化综合运用和问题解决的关键时期。",
    "6年级": "为初中数学做全方位准备的总结阶段。"
  },
  英语: {
    "1年级": "培养英语兴趣和语感的启蒙阶段。",
    "2年级": "打好基础词汇和口语表达的阶段。",
    "3年级": "提高基本听说和阅读能力的成长期。",
    "4年级": "语言理解和表达能力发展的重要阶段。",
    "5年级": "提升语法和写作能力的关键时期。",
    "6年级": "全面提升英语综合能力的冲刺期。"
  }
};

/**
 * 计算指定日期距离今天的天数
 * @param {string} targetDate - 目标日期，格式为 "YYYY-MM-DD"
 * @returns {number} - 距离今天的天数
 */
export function daysBetween(targetDate) {
  const today = new Date().getTime();
  const target = new Date(targetDate.replace(/-/g, "/")).getTime();
  const difference = Math.ceil((target - today) / (1000 * 60 * 60 * 24));
  return difference;
}

export const getCurrentDate = () => new Date().toISOString().split("T")[0];

/**
 * 计算两个日期之间的星期几的总数
 *
 * @param {string} endDate - 结束日期（格式：YYYY-MM-DD）
 * @param {number[]} weekdays - 需要统计的星期几数组（0 代表星期日，1 代表星期一，依此类推）
 * @returns {number} - 指定星期几的总数
 */
export function countWeekdaysBetweenDates(endDate, weekdays, startDate?: any) {
  // 将字符串日期转换为 Date 对象
  let start = new Date(getCurrentDate());
  if (startDate) {
    start = dayjs(startDate).toDate();
  }
  const end = dayjs(endDate).toDate();

  // 调整日期的时间部分
  start.setHours(0, 0, 0, 0);
  end.setHours(0, 0, 0, 0);

  // 将 end 日期设置为前一天
  end.setDate(end.getDate() - 1);

  // 将星期几字符串转换为对应的数字
  const weekdayNumbers = weekdays.map(day => parseInt(day, 10));

  let count = 0;

  // 遍历从开始日期到结束日期之间的每一天
  while (start <= end) {
    // 检查当前日期是否在指定的星期几数组中
    if (weekdayNumbers.includes(start.getDay())) {
      count++;
    }
    // 移动到下一天
    start.setDate(start.getDate() + 1);
  }
  return count;
}

export const EntryTitle =
  "该字段表明这条学习任务来自app内哪个学习功能/场景。例如“教材同步-知识点”，其中“教材同步”即代表该内容来自APP内的“教材同步”功能，而“知识点”是这个功能下的学习内容比较适合被引用的“结构”。使用该后台的光年主要关注该字段前面的信息如“教材同步”、“日常练习”等即可。";
export const EntryContent =
  "🎺： 目前定制班已经支持的学习入口包括：1、教材同步-知识点 2、 古诗文背诵 3、 专项课程-独立章节-知识点：其实就是专项课or培优课中的内容 4、 高频错题 - 小节（暂未上线） 5、 同步刷题 - 小节 6、 试卷库 - 试卷 7、 考前突击 - 专题包 8、 学习机 - 知识点 ：目前主要是英语单词练习在用 9 、学案文件 - 学案 10、 教材作业  11、 练习作业";

export const EntryText = EntryTitle + EntryContent;

export const PriorityText =
  "1、 “优先级”字段表示该学习内容在当前班课（对应的学习规划课表）中的重要性。 2、优先级与难度、复杂度等都无关，同一个学习内容在不同班课中的优先级也有可能不同。";

export const CustomText =
  "“自定义标签”是课程老师在设计每个班课的规划课表时，用来标记班课中每个学习内容的特点的标签。因为不同班课的规划课表服务的目标用户、学习诉求等都不同，所以不同班课中使用的自定义标签可能也都不同。";

export const WeekText =
  "“内容周标签”的数字，代表课程老师在设计这个规划课表的时候，建议该内容应该在班课开始的第几周被学习。如果字段为空，就代表“这个内容在第几周学习”不是一个重要的信息。目前小学学段的大部分班课内容，都会建议在开班的第几周学习，而初中学段的大部分内容该字段都为空。";

export const FinishText =
  "常用内容完成度计算方式：【1、】 视频观看完成度：视频观看完成度等于视频观看的进度+课后练习正确率的综合判定。（可用于教材同步、专项课程的学习入口内容类型）【2、】 题目练习完成度：题目练习完成度等于完成该组题目的整体正确率 【3、】 考前突击完成度：完成1次考前突击专题即达成100%完成度 【4、】试卷答题完成度：试卷答题完成度等于完成该套试卷的整体正确率 【5、】 作业速攻完成度：查看1次作业速攻题型即达成100%完成度 【6、】 学案完成度：查看1次学案即达成100%完成度 【7、】 快背地理完成度：快背地理完成度等于本次练习掌握的知识点/总共练习的知识点 【8、】 词汇训练完成度：词汇练习完成度等于本次练习掌握的词汇/总共练习的词汇";

export const StudyTime =
  "“学习时间”信息表明了学习本内容预计需要花费的时间，而非用户实际花费的时间";

export const holidayLearningDesc = {
  数学: {
    "1年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间系统学习夯实校内知识，适度拓展解决教材重点。",
        desc: "一年级是数学兴趣启蒙和基础能力的起点。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内重难点，拓展思维搞定高分难题。",
        desc: "一年级是数学兴趣启蒙和基础能力的起点。"
      }
    },
    "2年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间系统学习夯实校内知识，适度拓展解决教材重点。",
        desc: "二年级是计算熟练度和逻辑推理的提升期。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内重难点，拓展思维搞定高分难题。",
        desc: "二年级是计算熟练度和逻辑推理的提升期。"
      }
    },
    "3年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间系统学习夯实校内知识，适度拓展解决教材重点。",
        desc: "三年级是应用和多步运算的进阶学习阶段。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内重难点，拓展思维搞定高分难题。",
        desc: "三年级是应用和多步运算的进阶学习阶段。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间深度锻炼核心数学思维，提前掌握小初必学内容，见识重点考试新题型。",
        desc: "三年级是应用和多步运算的进阶学习阶段。"
      }
    },
    "4年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间系统学习夯实校内知识，适度拓展解决教材重点。",
        desc: "四年级是全面提升思维深度和数学理解的时期。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内重难点，拓展思维搞定高分难题。",
        desc: "四年级是全面提升思维深度和数学理解的时期。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间深度锻炼核心数学思维，提前掌握小初必学内容，见识重点考试新题型。",
        desc: "四年级是全面提升思维深度和数学理解的时期。"
      }
    },
    "5年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间系统学习夯实校内知识，适度拓展解决教材重点。",
        desc: "五年级是强化综合运用和问题解决的关键时期。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内重难点，拓展思维搞定高分难题。",
        desc: "五年级是强化综合运用和问题解决的关键时期。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间深度锻炼核心数学思维，提前掌握小初必学内容，见识重点考试新题型。",
        desc: "五年级是强化综合运用和问题解决的关键时期。"
      }
    },
    "6年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间系统学习夯实校内知识，适度拓展解决教材重点。",
        desc: "六年级是为初中数学做全方位准备的总结阶段。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内重难点，拓展思维搞定高分难题。",
        desc: "六年级是为初中数学做全方位准备的总结阶段。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间深度锻炼核心数学思维，提前掌握小初必学内容，见识重点考试新题型。",
        desc: "六年级是为初中数学做全方位准备的总结阶段。"
      }
    },
    "6年级(五四制)": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间夯实本学期基础，不给下学期学习留漏洞！预习下学期新知，为后续课程提前做准备！",
        desc: "六年级是打好数学基础、建立基本运算和思维方式的关键阶段。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间高起点强化本学期重点，把期末卷不会的搞清楚！预习下学期新知，为后续课程提前做准备！",
        desc: "六年级是打好数学基础、建立基本运算和思维方式的关键阶段。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间进一步学好本学期难点，争取把本学期难点一网打尽！预习下学期新知，为后续课程提前做准备！",
        desc: "六年级是打好数学基础、建立基本运算和思维方式的关键阶段。"
      }
    },
    "7年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间夯实本学期基础，不给下学期学习留漏洞！预习下学期新知，为后续课程提前做准备！",
        desc: "七年级是打好数学基础、建立基本运算和思维方式的关键阶段。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间高起点强化本学期重点，把期末卷不会的搞清楚！预习下学期新知，为后续课程提前做准备！",
        desc: "七年级是打好数学基础、建立基本运算和思维方式的关键阶段。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间进一步学好本学期难点，争取把本学期难点一网打尽！预习下学期新知，为后续课程提前做准备！",
        desc: "七年级是打好数学基础、建立基本运算和思维方式的关键阶段。"
      }
    },
    "8年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间夯实本学期基础，不给下学期学习留漏洞！预习下学期新知，为后续课程提前做准备！",
        desc: "八年级是深化代数、几何和数据分析技能的提升期。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间高起点强化本学期重点，把期末卷不会的搞清楚！预习下学期新知，为后续课程提前做准备！",
        desc: "八年级是深化代数、几何和数据分析技能的提升期。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间进一步学好本学期难点，争取把本学期难点一网打尽！预习下学期新知，为后续课程提前做准备！",
        desc: "八年级是深化代数、几何和数据分析技能的提升期。"
      }
    },
    "9年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间夯实本学期基础，不给下学期学习留漏洞！预习下学期新知，为后续课程提前做准备！",
        desc: "九年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间高起点强化本学期重点，把期末卷不会的搞清楚！预习下学期新知，为后续课程提前做准备！",
        desc: "九年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间进一步学好本学期难点，争取把本学期难点一网打尽！预习下学期新知，为后续课程提前做准备！",
        desc: "九年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。"
      }
    }
  },
  英语: {
    "1年级": "一年级是培养英语兴趣和语感的启蒙阶段。",
    "2年级": "二年级是打好基础词汇和口语表达的阶段。",
    "3年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间做好复习打好地基，提前预习开学重点。",
        desc: "三年级是提高基本听说和阅读能力的成长期。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内知识，强化运用攻克难点。",
        desc: "三年级是提高基本听说和阅读能力的成长期。"
      }
    },
    "4年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间做好复习打好地基，提前预习开学重点。",
        desc: "四年级是语言理解和表达能力发展的重要阶段。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内知识，强化运用攻克难点。",
        desc: "四年级是语言理解和表达能力发展的重要阶段。"
      }
    },
    "5年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间做好复习打好地基，提前预习开学重点。",
        desc: "五年级是提升语法和写作能力的关键时期。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内知识，强化运用攻克难点。",
        desc: "五年级是提升语法和写作能力的关键时期。"
      }
    },
    "6年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间做好复习打好地基，提前预习开学重点。",
        desc: "六年级是全面提升英语综合能力的冲刺期。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间提前学习校内知识，强化运用攻克难点。",
        desc: "六年级是全面提升英语综合能力的冲刺期。"
      }
    },
    "6年级(五四制)": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间复习上册重点语法，预习下册词汇和语法内容，打好基础，让春季学习更省力！",
        desc: "六年级是打好英语基础，培养听说读写基本能力的起点。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间复习上册重点语法，提前预习下册词汇和语法内容，夯实基础的同时，提升答题技能。",
        desc: "六年级是打好英语基础，培养听说读写基本能力的起点。"
      }
    },
    "7年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间复习上册重点语法，预习下册词汇和语法内容，打好基础，让春季学习更省力！",
        desc: "七年级是打好英语基础，培养听说读写基本能力的起点。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间复习上册重点语法，提前预习下册词汇和语法内容，夯实基础的同时，提升答题技能。",
        desc: "七年级是打好英语基础，培养听说读写基本能力的起点。"
      }
    },
    "8年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间复习上册重点语法，预习下册词汇和语法内容，打好基础，让春季学习更省力！",
        desc: "八年级是深化词汇和语法理解，提升阅读和写作水平的关键阶段。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间复习上册重点语法，提前预习下册词汇和语法内容，夯实基础的同时，提升答题技能。",
        desc: "八年级是深化词汇和语法理解，提升阅读和写作水平的关键阶段。"
      }
    },
    "9年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间复习上册重点语法，预习下册词汇和语法内容，打好基础，让春季学习更省力！",
        desc: "九年级是强化综合应用能力，为高中英语学习打下坚实基础的时期。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间巩固上学期重点题型和概念，查漏补缺；系统预习下学期核心新课，培养预复习好习惯。",
        desc: "九年级是强化综合应用能力，为高中英语学习打下坚实基础的时期。"
      }
    }
  },
  语文: {
    "7年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间夯实教材同步基础，掌握核心方法。",
        desc: "七年级是打下语文基础、培养阅读理解和写作能力的关键阶段。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间掌握重点题型方法，培养思维习惯。",
        desc: "七年级是打下语文基础、培养阅读理解和写作能力的关键阶段。"
      }
    },
    "8年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间夯实教材同步基础，掌握核心方法。",
        desc: "八年级是深化文学知识、提高写作技巧和分析能力的时期。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间掌握重点题型方法，培养思维习惯。",
        desc: "八年级是深化文学知识、提高写作技巧和分析能力的时期。"
      }
    },
    "9年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间夯实教材同步基础，掌握核心方法。",
        desc: "九年级是提升综合语文能力、为高中语文学习做好准备的关键阶段。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间掌握重点题型方法，培养思维习惯。",
        desc: "九年级是提升综合语文能力、为高中语文学习做好准备的关键阶段。"
      }
    }
  },
  生物: {
    "7年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间预习新知，为后续课程做铺垫；复习重夯实基础同时掌握一些解题方法。",
        desc: "七年级是打好生物基础、培养科学探究思维的起点。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间预习新知，为后续课程做铺垫；复习重难点题型，掌握重要解题方法。",
        desc: "七年级是打好生物基础、培养科学探究思维的起点。"
      }
    },
    "8年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间预习新知，为后续课程做铺垫；复习重夯实基础同时掌握一些解题方法。",
        desc: "八年级是深入理解生物系统和生态知识，提升分析能力的关键阶段。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间预习新知，为后续课程做铺垫；复习重难点题型，掌握重要解题方法。",
        desc: "八年级是深入理解生物系统和生态知识，提升分析能力的关键阶段。"
      }
    }
  },
  地理: {
    "7年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间预习新知，夯实基础，进阶解题，积累信心，培养能力。",
        desc: "七年级是掌握地理基础知识和技能、理解自然环境的起点。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间深化概念理解，提升解题能力，提前进入新学期学习状态。",
        desc: "七年级是掌握地理基础知识和技能、理解自然环境的起点。"
      }
    },
    "8年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间预习新知，夯实基础，进阶解题，积累信心，培养能力。",
        desc: "八年级是深入学习地理要素与人类活动关系，提升分析和应用能力的关键阶段。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间通关八下知识，深化概念理解，提升解题能力，中考复习快人一步。",
        desc: "八年级是深入学习地理要素与人类活动关系，提升分析和应用能力的关键阶段。"
      }
    }
  },
  物理: {
    "8年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间复习重点题型，预习核心知识，培养复习预习好习惯！",
        desc: "八年级是建立物理基本概念和规律，培养实验思维的关键阶段。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间复习核心题型，预习新知，强化应用，培养自学能力！",
        desc: "八年级是建立物理基本概念和规律，培养实验思维的关键阶段。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间深化新知学习，重视知识拓展，强化题型讲解，提升考试技能。",
        desc: "八年级是建立物理基本概念和规律，培养实验思维的关键阶段。"
      }
    },
    "9年级": {
      A: {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间夯实电学核心知识，梳理中考力学重点内容，提前进入中考复习。",
        desc: "九年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。"
      },
      "A+": {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间掌握电学核心题型解题方法，复习中考力学重点题型，提高中考备考效率。",
        desc: "九年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。"
      },
      S: {
        proposal:
          "学习基础非常扎实，校内成绩无忧。因此建议在寒假期间系统梳理中考力学核心概念，系统复习中考力学核心题型，突破中考力学难点。",
        desc: "九年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。"
      }
    }
  },
  化学: {
    "8年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间巩固、拓展上学期重难点题型，查漏补缺；系统预习下学期核心新课，培养预复习好习惯。",
        desc: "八年级是建立化学基础知识和实验技能的关键阶段。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间巩固、拓展上学期重难点题型，查漏补缺；系统预习下学期核心新课，培养预复习好习惯",
        desc: "八年级是建立化学基础知识和实验技能的关键阶段。"
      }
    },
    "9年级": {
      "A+": {
        proposal:
          "学习基础较为薄弱，校内成绩不稳定。因此建议在寒假期间巩固、拓展上学期重难点题型，查漏补缺；系统预习下学期核心新课，培养预复习好习惯。",
        desc: "九年级是深化化学原理的理解，提升解题和应用能力的关键时期。"
      },
      S: {
        proposal:
          "学习基础较为牢固，校内成绩优异。因此建议在寒假期间巩固、拓展上学期重难点题型，查漏补缺；系统预习下学期核心新课，培养预复习好习惯",
        desc: "九年级是深化化学原理的理解，提升解题和应用能力的关键时期。"
      }
    }
  }
};

export const learningTargetSystem = {
  "1年级_数学_998-系统学_A": {
    class_goal: "夯实校内同步知识点，培养预习好习惯",
    grade_description: "一年级是数学兴趣启蒙和基础能力的起点。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "1年级_数学_998-系统学_A+": {
    class_goal: "搞定高分重难点，养成学练拓展好习惯",
    grade_description: "一年级是数学兴趣启蒙和基础能力的起点。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "2年级_数学_998-系统学_A": {
    class_goal: "夯实校内同步知识点，培养预习好习惯",
    grade_description: "二年级是计算熟练度和逻辑推理的提升期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "2年级_数学_998-系统学_A+": {
    class_goal: "搞定高分重难点，养成学练拓展好习惯",
    grade_description: "二年级是计算熟练度和逻辑推理的提升期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "3年级_数学_998-系统学_A": {
    class_goal: "夯实校内同步知识点，培养预习好习惯",
    grade_description: "三年级是应用和多步运算的进阶学习阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "3年级_数学_998-系统学_A+": {
    class_goal: "搞定高分重难点，养成学练拓展好习惯",
    grade_description: "三年级是应用和多步运算的进阶学习阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "3年级_数学_998-系统学_S": {
    class_goal: "数学思维深度训练，提前掌握小初必学内容",
    grade_description: "三年级是应用和多步运算的进阶学习阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "4年级_数学_998-系统学_A": {
    class_goal: "夯实校内同步知识点，培养预习好习惯",
    grade_description: "4年级是全面提升思维深度和数学理解的时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "4年级_数学_998-系统学_A+": {
    class_goal: "搞定高分重难点，养成学练拓展好习惯",
    grade_description: "4年级是全面提升思维深度和数学理解的时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "4年级_数学_998-系统学_S": {
    class_goal: "数学思维深度训练，提前掌握小初必学内容",
    grade_description: "4年级是全面提升思维深度和数学理解的时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "5年级_数学_998-系统学_A": {
    class_goal: "夯实校内同步知识点，培养预习好习惯",
    grade_description: "5年级是强化综合运用和问题解决的关键时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "5年级_数学_998-系统学_A+": {
    class_goal: "搞定高分重难点，养成学练拓展好习惯",
    grade_description: "5年级是强化综合运用和问题解决的关键时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "5年级_数学_998-系统学_S": {
    class_goal: "数学思维深度训练，提前掌握小初必学内容",
    grade_description: "5年级是强化综合运用和问题解决的关键时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "6年级_数学_998-系统学_A": {
    class_goal: "夯实校内同步知识点，培养预习好习惯",
    grade_description: "6年级是为初中数学做全方位准备的总结阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "6年级_数学_998-系统学_A+": {
    class_goal: "搞定高分重难点，养成学练拓展好习惯",
    grade_description: "6年级是为初中数学做全方位准备的总结阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "6年级_数学_998-系统学_S": {
    class_goal: "数学思维深度训练，提前掌握小初必学内容",
    grade_description: "6年级是为初中数学做全方位准备的总结阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "1年级_英语_998-系统学_": {
    class_goal: "无痛入门英语，培养英语学习兴趣",
    grade_description: "一二年级是培养英语兴趣和语感的启蒙阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "2年级_英语_998-系统学_": {
    class_goal: "无痛入门英语，培养英语学习兴趣",
    grade_description: "一二年级是培养英语兴趣和语感的启蒙阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "3年级_英语_998-系统学_A": {
    class_goal: "夯实校内知识，提升英语学习兴趣",
    grade_description: "三年级是提高基本听说和阅读能力的成长期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "3年级_英语_998-系统学_A+": {
    class_goal: "高效搞定难点题目，提前掌握小初必学内容",
    grade_description: "三年级是提高基本听说和阅读能力的成长期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "4年级_英语_998-系统学_A": {
    class_goal: "夯实校内知识，提升英语学习兴趣",
    grade_description: "4年级是语言理解和表达能力发展的重要阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "4年级_英语_998-系统学_A+": {
    class_goal: "高效搞定难点题目，提前掌握小初必学内容",
    grade_description: "4年级是语言理解和表达能力发展的重要阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "5年级_英语_998-系统学_A": {
    class_goal: "夯实校内知识，提升英语学习兴趣",
    grade_description: "5年级是提升语法和写作能力的关键时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "5年级_英语_998-系统学_A+": {
    class_goal: "高效搞定难点题目，提前掌握小初必学内容",
    grade_description: "5年级是提升语法和写作能力的关键时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "6年级_英语_998-系统学_A": {
    class_goal: "夯实校内知识，提升英语学习兴趣",
    grade_description: "6年级是全面提升英语综合能力的冲刺期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "6年级_英语_998-系统学_A+": {
    class_goal: "高效搞定难点题目，提前掌握小初必学内容",
    grade_description: "6年级是全面提升英语综合能力的冲刺期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "1年级_语文_998-系统学_": {
    class_goal: "夯实语文学习基础，培养语文学习兴趣",
    grade_description: "一二年级是培养语文兴趣和基础的启蒙阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "2年级_语文_998-系统学_": {
    class_goal: "夯实语文学习基础，培养语文学习兴趣",
    grade_description: "一二年级是培养语文兴趣和基础的启蒙阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "3年级_语文_998-系统学_A": {
    class_goal: "提炼校内同步知识点，成绩素养双提升",
    grade_description: "三年级是增强阅读理解和写作能力，拓展语言运用的时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "3年级_语文_998-系统学_A+": {
    class_goal: "专注读写重难点，掌握高阶技巧",
    grade_description: "三年级是增强阅读理解和写作能力，拓展语言运用的时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "4年级_语文_998-系统学_A": {
    class_goal: "提炼校内同步知识点，成绩素养双提升",
    grade_description: "4年级是深化语法知识，培养较高的写作和阅读能力的阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "4年级_语文_998-系统学_A+": {
    class_goal: "专注读写重难点，掌握高阶技巧",
    grade_description: "4年级是深化语法知识，培养较高的写作和阅读能力的阶段。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "5年级_语文_998-系统学_A": {
    class_goal: "提炼校内同步知识点，成绩素养双提升",
    grade_description:
      "5年级是提高语言分析与应用能力，为更复杂的语言学习做准备。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "5年级_语文_998-系统学_A+": {
    class_goal: "专注读写重难点，掌握高阶技巧",
    grade_description:
      "5年级是提高语言分析与应用能力，为更复杂的语言学习做准备。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "6年级_语文_998-系统学_A": {
    class_goal: "提炼校内同步知识点，成绩素养双提升",
    grade_description:
      "6年级是总结语文知识，提升综合语文能力，做好升学准备的关键时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "6年级_语文_998-系统学_A+": {
    class_goal: "专注读写重难点，掌握高阶技巧",
    grade_description:
      "6年级是总结语文知识，提升综合语文能力，做好升学准备的关键时期。",
    learning_goal: "培养良好的学习习惯，成绩水平得到全面提升。"
  },
  "6年级(五四制)_数学_998-系统学_A": {
    class_goal: "夯实基础，稳步提升",
    grade_description:
      "6年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "6年级(五四制)_数学_998-系统学_A+": {
    class_goal: "学练结合，重点突破",
    grade_description:
      "6年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "6年级(五四制)_数学_998-系统学_S": {
    class_goal: "常考重点攻坚，提升解题思维",
    grade_description:
      "6年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_数学_998-系统学_A": {
    class_goal: "夯实基础，稳步提升",
    grade_description:
      "7年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_数学_998-系统学_A+": {
    class_goal: "学练结合，重点突破",
    grade_description:
      "7年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_数学_998-系统学_S": {
    class_goal: "常考重点攻坚，提升解题思维",
    grade_description:
      "7年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_数学_998-系统学_A": {
    class_goal: "夯实基础，稳步提升",
    grade_description: "8年级是深化代数、几何和数据分析技能的提升期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_数学_998-系统学_A+": {
    class_goal: "学练结合，重点突破",
    grade_description: "8年级是深化代数、几何和数据分析技能的提升期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_数学_998-系统学_S": {
    class_goal: "常考重点攻坚，提升解题思维",
    grade_description: "8年级是深化代数、几何和数据分析技能的提升期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_数学_998-系统学_A": {
    class_goal: "夯实基础，稳步提升",
    grade_description:
      "9年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_数学_998-系统学_A+": {
    class_goal: "学练结合，重点突破",
    grade_description:
      "9年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_数学_998-系统学_S": {
    class_goal: "难题攻坚，提升思维",
    grade_description:
      "9年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_语文_998-系统学_A+": {
    class_goal: "夯实基础，稳步提升",
    grade_description:
      "7年级是打下语文基础、培养阅读理解和写作能力的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_语文_998-系统学_S": {
    class_goal: "学练结合，重点突破",
    grade_description:
      "7年级是打下语文基础、培养阅读理解和写作能力的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_语文_998-系统学_A+": {
    class_goal: "夯实基础，稳步提升",
    grade_description: "8年级是深化文学知识、提高写作技巧和分析能力的时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_语文_998-系统学_S": {
    class_goal: "学练结合，重点突破",
    grade_description: "8年级是深化文学知识、提高写作技巧和分析能力的时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_语文_998-系统学_A+": {
    class_goal: "夯实基础，稳步提升",
    grade_description:
      "9年级是提升综合语文能力、为高中语文学习做好准备的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_语文_998-系统学_S": {
    class_goal: "学练结合，重点突破",
    grade_description:
      "9年级是提升综合语文能力、为高中语文学习做好准备的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_生物_998-系统学_A+": {
    class_goal: "夯实基础，稳步提升",
    grade_description: "7年级是打好生物基础、培养科学探究思维的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_生物_998-系统学_S": {
    class_goal: "学练结合，重、难点突破",
    grade_description: "7年级是打好生物基础、培养科学探究思维的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_生物_998-系统学_A+": {
    class_goal: "夯实基础，稳步提升",
    grade_description:
      "8年级是深入理解生物系统和生态知识，提升分析能力的关键阶段。\n",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_生物_998-系统学_S": {
    class_goal: "学练结合，重、难点突破",
    grade_description:
      "8年级是深入理解生物系统和生态知识，提升分析能力的关键阶段。\n",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "6年级(五四制)_地理_998-系统学_A+": {
    class_goal: "夯实基础知识，养成预习复习好习惯",
    grade_description: "6年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "6年级(五四制)_地理_998-系统学_S": {
    class_goal: "学得透，练得多，考前不用临时抱佛脚",
    grade_description: "6年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_地理_998-系统学_A+": {
    class_goal: "夯实基础知识，养成预习复习好习惯",
    grade_description: "7年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_地理_998-系统学_S": {
    class_goal: "学得透，练得多，考前不用临时抱佛脚",
    grade_description: "7年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_地理_998-系统学_A+": {
    class_goal: "逐步进阶至中考题型演练",
    grade_description:
      "8年级是深入学习地理要素与人类活动关系，提升分析和应用能力的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_地理_998-系统学_S": {
    class_goal: "融入中考题型演练",
    grade_description:
      "8年级是深入学习地理要素与人类活动关系，提升分析和应用能力的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_物理_998-系统学_A": {
    class_goal: "同步校内，夯实基础",
    grade_description:
      "8年级是建立物理基本概念和规律，培养实验思维的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_物理_998-系统学_A+": {
    class_goal: "学练结合，重点突破",
    grade_description:
      "8年级是建立物理基本概念和规律，培养实验思维的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_物理_998-系统学_S": {
    class_goal: "难点攻坚，提升思维",
    grade_description:
      "8年级是建立物理基本概念和规律，培养实验思维的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_物理_998-系统学_A": {
    class_goal: "夯实基础，查漏补缺",
    grade_description:
      "9年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_物理_998-系统学_A+": {
    class_goal: "讲练结合，重点突破",
    grade_description:
      "9年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_物理_998-系统学_S": {
    class_goal: "讲练结合，难点攻坚",
    grade_description:
      "9年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "6年级(五四制)_英语_998-系统学_A+": {
    class_goal: "夯实教材基础知识",
    grade_description: "6年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "6年级(五四制)_英语_998-系统学_S": {
    class_goal: "学练高度结合，掌握重点知识",
    grade_description: "6年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_英语_998-系统学_A+": {
    class_goal: "夯实教材基础知识",
    grade_description: "7年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "7年级_英语_998-系统学_S": {
    class_goal: "学练高度结合，掌握重点知识",
    grade_description: "7年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_英语_998-系统学_A+": {
    class_goal: "夯实教材基础知识",
    grade_description:
      "8年级是深化词汇和语法理解，提升阅读和写作水平的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_英语_998-系统学_S": {
    class_goal: "学练高度结合，掌握重点知识",
    grade_description:
      "8年级是深化词汇和语法理解，提升阅读和写作水平的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_英语_998-系统学_A+": {
    class_goal: "查漏补缺重点知识",
    grade_description:
      "9年级是强化综合应用能力，为高中英语学习打下坚实基础的时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_英语_998-系统学_S": {
    class_goal: "巩固重点知识点",
    grade_description:
      "9年级是强化综合应用能力，为高中英语学习打下坚实基础的时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_化学_998-系统学_A+": {
    class_goal: "夯实基础，稳步提升",
    grade_description: "8年级是建立化学基础知识和实验技能的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "8年级_化学_998-系统学_S": {
    class_goal: "学练结合，重点突破",
    grade_description: "8年级是建立化学基础知识和实验技能的关键阶段。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_化学_998-系统学_A+": {
    class_goal: "夯实基础，稳步提升",
    grade_description:
      "9年级是深化化学原理的理解，提升解题和应用能力的关键时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "9年级_化学_998-系统学_S": {
    class_goal: "学练结合，重点突破",
    grade_description:
      "9年级是深化化学原理的理解，提升解题和应用能力的关键时期。",
    learning_goal: "巩固校内的学习内容，查漏补缺校内的重点内容。"
  },
  "6年级(五四制)_数学_大会员-系统学_A": {
    class_goal: "全面梳理期中期末常考题型",
    grade_description:
      "6年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "6年级(五四制)_数学_大会员-系统学_A+": {
    class_goal: "全面提升对重难点题型的解题能力",
    grade_description:
      "6年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "6年级(五四制)_数学_大会员-系统学_S": {
    class_goal: "突破压轴题，提升核心能力",
    grade_description:
      "6年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_数学_大会员-系统学_A": {
    class_goal: "全面梳理期中期末常考题型",
    grade_description:
      "7年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_数学_大会员-系统学_A+": {
    class_goal: "全面提升对重难点题型的解题能力",
    grade_description:
      "7年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_数学_大会员-系统学_S": {
    class_goal: "突破压轴题，提升核心能力",
    grade_description:
      "7年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_数学_大会员-系统学_A": {
    class_goal: "全面梳理期中期末常考题型",
    grade_description: "8年级是深化代数、几何和数据分析技能的提升期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_数学_大会员-系统学_A+": {
    class_goal: "全面提升对重难点题型的解题能力",
    grade_description: "8年级是深化代数、几何和数据分析技能的提升期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_数学_大会员-系统学_S": {
    class_goal: "突破压轴题，提升核心能力",
    grade_description: "8年级是深化代数、几何和数据分析技能的提升期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_数学_大会员-系统学_A": {
    class_goal: "全面掌握中考高频考点",
    grade_description:
      "9年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_数学_大会员-系统学_A+": {
    class_goal: "全面掌握中考重难点题型答题技巧",
    grade_description:
      "9年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_数学_大会员-系统学_S": {
    class_goal: "全面掌握压轴题和创新题的答题技巧",
    grade_description:
      "9年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_语文_大会员-系统学_A+": {
    class_goal: "同步校内重点，适度拓展提升",
    grade_description:
      "7年级是打下语文基础、培养阅读理解和写作能力的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_语文_大会员-系统学_S": {
    class_goal: "把握核心方法，培养解题思维",
    grade_description:
      "7年级是打下语文基础、培养阅读理解和写作能力的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_语文_大会员-系统学_A+": {
    class_goal: "同步校内重点，适度拓展提升",
    grade_description: "8年级是深化文学知识、提高写作技巧和分析能力的时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_语文_大会员-系统学_S": {
    class_goal: "把握核心方法，培养解题思维",
    grade_description: "8年级是深化文学知识、提高写作技巧和分析能力的时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_语文_大会员-系统学_A+": {
    class_goal: "对标中考新题型，提升解题能力",
    grade_description:
      "9年级是提升综合语文能力、为高中语文学习做好准备的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_语文_大会员-系统学_S": {
    class_goal: "对标中考新题型，全面培养解题思维",
    grade_description:
      "9年级是提升综合语文能力、为高中语文学习做好准备的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_生物_大会员-系统学_A+": {
    class_goal: "夯实基础知识，拓展常见题型",
    grade_description: "7年级是打好生物基础、培养科学探究思维的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_生物_大会员-系统学_S": {
    class_goal: "学练结合，重、难点题型突破",
    grade_description: "7年级是打好生物基础、培养科学探究思维的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_生物_大会员-系统学_A+": {
    class_goal: "新知同步学，逐步过渡到中考复习",
    grade_description:
      "8年级是深入理解生物系统和生态知识，提升分析能力的关键阶段。\n",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_生物_大会员-系统学_S": {
    class_goal: "重难点突破，逐步过渡到综合题型练习",
    grade_description:
      "8年级是深入理解生物系统和生态知识，提升分析能力的关键阶段。\n",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "6年级(五四制)_地理_大会员-系统学_A+": {
    class_goal: "夯实基础，循序渐进，搭建知识结构",
    grade_description: "6年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "6年级(五四制)_地理_大会员-系统学_S": {
    class_goal: "重难点结合新题型，高效学习",
    grade_description: "6年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_地理_大会员-系统学_A+": {
    class_goal: "夯实基础，循序渐进，搭建知识结构",
    grade_description: "7年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_地理_大会员-系统学_S": {
    class_goal: "重难点结合新题型，高效学习",
    grade_description: "7年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_地理_大会员-系统学_A+": {
    class_goal: "扎实基础，提升中考必备的解题技能",
    grade_description:
      "8年级是深入学习地理要素与人类活动关系，提升分析和应用能力的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_地理_大会员-系统学_S": {
    class_goal: "难点突破，中考难题抢先学",
    grade_description:
      "8年级是深入学习地理要素与人类活动关系，提升分析和应用能力的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_物理_大会员-系统学_A": {
    class_goal: "扎实基础，提升中考必备的解题技能",
    grade_description:
      "8年级是建立物理基本概念和规律，培养实验思维的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_物理_大会员-系统学_A+": {
    class_goal: "突破易错题型，提升解题能力",
    grade_description:
      "8年级是建立物理基本概念和规律，培养实验思维的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_物理_大会员-系统学_S": {
    class_goal: "解决常考难点，提升核心能力",
    grade_description:
      "8年级是建立物理基本概念和规律，培养实验思维的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_物理_大会员-系统学_A": {
    class_goal: "夯实基础，梳理中考高频考点",
    grade_description:
      "9年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_物理_大会员-系统学_A+": {
    class_goal: "锁定题型，掌握中考重点题型答题技巧",
    grade_description:
      "9年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_物理_大会员-系统学_S": {
    class_goal: "突破难点，培养思维，解决中考创新题",
    grade_description:
      "9年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "6年级(五四制)_英语_大会员-系统学_A+": {
    class_goal: "夯实基础知识，锻炼解题技能",
    grade_description: "6年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "6年级(五四制)_英语_大会员-系统学_S": {
    class_goal: "拓展高分难题，提升解题技能",
    grade_description: "6年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_英语_大会员-系统学_A+": {
    class_goal: "夯实基础知识，锻炼解题技能",
    grade_description: "7年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "7年级_英语_大会员-系统学_S": {
    class_goal: "拓展高分难题，提升解题技能",
    grade_description: "7年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_英语_大会员-系统学_A+": {
    class_goal: "夯实基础知识，锻炼解题技能",
    grade_description:
      "8年级是深化词汇和语法理解，提升阅读和写作水平的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_英语_大会员-系统学_S": {
    class_goal: "拓展高分难题，提升解题技能",
    grade_description:
      "8年级是深化词汇和语法理解，提升阅读和写作水平的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_英语_大会员-系统学_A+": {
    class_goal: "提升解题技能，冲击考试高分",
    grade_description:
      "9年级是强化综合应用能力，为高中英语学习打下坚实基础的时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_英语_大会员-系统学_S": {
    class_goal: "加强学练结合，冲刺考试高分",
    grade_description:
      "9年级是强化综合应用能力，为高中英语学习打下坚实基础的时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_化学_大会员-系统学_A+": {
    class_goal: "全面梳理期中期末常考题型",
    grade_description: "8年级是建立化学基础知识和实验技能的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "8年级_化学_大会员-系统学_S": {
    class_goal: "全面提升对重难点题型的解题能力",
    grade_description: "8年级是建立化学基础知识和实验技能的关键阶段。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_化学_大会员-系统学_A+": {
    class_goal: "全面梳理模拟考常考题型",
    grade_description:
      "9年级是深化化学原理的理解，提升解题和应用能力的关键时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "9年级_化学_大会员-系统学_S": {
    class_goal: "全面提升重难点题型的解题能力",
    grade_description:
      "9年级是深化化学原理的理解，提升解题和应用能力的关键时期。",
    learning_goal: "稳步提升解题能力，成绩水平取得明显进步。"
  },
  "高一_数学_998-系统学_A": {
    class_goal: "夯实基础题型，稳步提升解题能力",
    grade_description:
      "高一是巩固基础知识的关键期，为后续学习奠定扎实的数学根基。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_数学_998-系统学_A+": {
    class_goal: "突破重点题型，掌握易错题型",
    grade_description:
      "高一是巩固基础知识的关键期，为后续学习奠定扎实的数学根基。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_数学_998-系统学_S": {
    class_goal: "提升综合问题的解题能力",
    grade_description:
      "高一是巩固基础知识的关键期，为后续学习奠定扎实的数学根基。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_数学_998-系统学_A": {
    class_goal: "夯实基础题型，稳步提升解题能力",
    grade_description:
      "高二是加深理解和应用能力的提升期，确保学生能够应对更复杂的数学问题。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_数学_998-系统学_A+": {
    class_goal: "突破重点题型，掌握易错题型",
    grade_description:
      "高二是加深理解和应用能力的提升期，确保学生能够应对更复杂的数学问题。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_数学_998-系统学_S": {
    class_goal: "提升综合问题的解题能力",
    grade_description:
      "高二是加深理解和应用能力的提升期，确保学生能够应对更复杂的数学问题。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_物理_998-系统学_A": {
    class_goal: "夯实基础，理解概念",
    grade_description:
      "高一是巩固物理基础概念和基本公式的阶段，为后续更深入的学习打好基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_物理_998-系统学_A+": {
    class_goal: "梳理常考题型，提升解题能力",
    grade_description:
      "高一是巩固物理基础概念和基本公式的阶段，为后续更深入的学习打好基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_物理_998-系统学_S": {
    class_goal: "攻克常考重难点，培养物理思维",
    grade_description:
      "高一是巩固物理基础概念和基本公式的阶段，为后续更深入的学习打好基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_物理_998-系统学_A": {
    class_goal: "夯实基础，理解概念",
    grade_description:
      "高二是加深物理知识的理解与应用，通过复习提高解题能力，增强对复杂问题的分析和解决能力。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_物理_998-系统学_A+": {
    class_goal: "梳理常考题型，提升解题能力",
    grade_description:
      "高二是加深物理知识的理解与应用，通过复习提高解题能力，增强对复杂问题的分析和解决能力。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_物理_998-系统学_S": {
    class_goal: "攻克常考重难点，培养物理思维",
    grade_description:
      "高二是加深物理知识的理解与应用，通过复习提高解题能力，增强对复杂问题的分析和解决能力。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_化学_998-系统学_A": {
    class_goal: "夯实基础，理解概念",
    grade_description:
      "高一是巩固化学基础知识和概念的阶段，为后续学习打下坚实基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_化学_998-系统学_A+": {
    class_goal: "梳理常考题型，提升解题能力",
    grade_description:
      "高一是巩固化学基础知识和概念的阶段，为后续学习打下坚实基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_化学_998-系统学_S": {
    class_goal: "攻克常考难点，构建化学思维",
    grade_description:
      "高一是巩固化学基础知识和概念的阶段，为后续学习打下坚实基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_化学_998-系统学_A": {
    class_goal: "夯实基础，理解概念",
    grade_description:
      "高二是深化化学原理的理解与应用，通过复习提高解题能力，掌握更多的化学知识和技能。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_化学_998-系统学_A+": {
    class_goal: "梳理常考题型，提升解题能力",
    grade_description:
      "高二是深化化学原理的理解与应用，通过复习提高解题能力，掌握更多的化学知识和技能。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_化学_998-系统学_S": {
    class_goal: "攻克常考难点，构建化学思维",
    grade_description:
      "高二是深化化学原理的理解与应用，通过复习提高解题能力，掌握更多的化学知识和技能。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_生物_998-系统学_A+": {
    class_goal: "夯实基础，加深知识理解，稳步提升",
    grade_description:
      "高一是巩固生物基础知识和基本概念的阶段，为后续深入学习打好基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_生物_998-系统学_S": {
    class_goal: "常考重点攻坚，提升解题思维",
    grade_description:
      "高一是巩固生物基础知识和基本概念的阶段，为后续深入学习打好基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_生物_998-系统学_A+": {
    class_goal: "夯实基础，加深知识理解，稳步提升",
    grade_description:
      "高二是加深生物学原理的理解和应用，通过复习提升解题技巧，为更复杂的生物问题做准备。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_生物_998-系统学_S": {
    class_goal: "常考重点攻坚，提升解题思维",
    grade_description:
      "高二是加深生物学原理的理解和应用，通过复习提升解题技巧，为更复杂的生物问题做准备。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_语文_998-系统学_A+": {
    class_goal: "夯实古诗文基础，全面稳步提升",
    grade_description:
      "高一是巩固语文基础知识，提升阅读理解和基础写作能力的阶段，为更高层次的学习打下基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_语文_998-系统学_S": {
    class_goal: "夯实古诗文基础，聚焦重难点突破",
    grade_description:
      "高一是巩固语文基础知识，提升阅读理解和基础写作能力的阶段，为更高层次的学习打下基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_语文_998-系统学_A+": {
    class_goal: "夯实古诗文基础，全面稳步提升",
    grade_description:
      "高二是加深对文学作品的理解，强化写作技巧和语言表达能力，为高三的语文复习和考试做准备。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_语文_998-系统学_S": {
    class_goal: "夯实古诗文基础，聚焦重难点突破",
    grade_description:
      "高二是加深对文学作品的理解，强化写作技巧和语言表达能力，为高三的语文复习和考试做准备。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_英语_998-系统学_A+": {
    class_goal: "夯实词汇语法基础，全面稳步提升",
    grade_description:
      "高一是巩固英语基础，提升听说读写基本能力的阶段，为后续学习打下扎实的基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_英语_998-系统学_S": {
    class_goal: "聚焦重点难点，提升综合运用能力",
    grade_description:
      "高一是巩固英语基础，提升听说读写基本能力的阶段，为后续学习打下扎实的基础。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_英语_998-系统学_A+": {
    class_goal: "夯实词汇语法基础，全面稳步提升",
    grade_description:
      "高二是加深词汇、语法和阅读理解的掌握，通过复习提升写作和翻译能力，为高三的英语学习和备考做准备。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高二_英语_998-系统学_S": {
    class_goal: "聚焦重点难点，提升综合运用能力",
    grade_description:
      "高二是加深词汇、语法和阅读理解的掌握，通过复习提升写作和翻译能力，为高三的英语学习和备考做准备。",
    learning_goal: "夯实基础题型，提升解题能力。"
  },
  "高一_数学_大会员-系统学_A": {
    class_goal: "夯实基础，锻炼解题技能",
    grade_description:
      "高一是巩固数学基础，掌握核心概念和方法，为提升数学解题能力打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_数学_大会员-系统学_A+": {
    class_goal: "突破易错常考，提升解题能力",
    grade_description:
      "高一是巩固数学基础，掌握核心概念和方法，为提升数学解题能力打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_数学_大会员-系统学_S": {
    class_goal: "解决常考难点，完善方法体系",
    grade_description:
      "高一是巩固数学基础，掌握核心概念和方法，为提升数学解题能力打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_数学_大会员-系统学_A": {
    class_goal: "夯实基础，锻炼解题技能",
    grade_description:
      "高二是深化数学知识的应用，通过提高解题技巧和思维方式，提升综合解题能力，为高三提分做好准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_数学_大会员-系统学_A+": {
    class_goal: "突破易错常考，提升解题能力",
    grade_description:
      "高二是深化数学知识的应用，通过提高解题技巧和思维方式，提升综合解题能力，为高三提分做好准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_数学_大会员-系统学_S": {
    class_goal: "解决常考难点，完善方法体系",
    grade_description:
      "高二是深化数学知识的应用，通过提高解题技巧和思维方式，提升综合解题能力，为高三提分做好准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_物理_大会员-系统学_A": {
    class_goal: "夯实基础，理解概念",
    grade_description:
      "高一是打牢物理基础，掌握基本概念和公式，为后续的高难度题目解答奠定基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_物理_大会员-系统学_A+": {
    class_goal: "梳理常考题型，提升解题能力",
    grade_description:
      "高一是打牢物理基础，掌握基本概念和公式，为后续的高难度题目解答奠定基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_物理_大会员-系统学_S": {
    class_goal: "攻克常考重难点，培养物理思维",
    grade_description:
      "高一是打牢物理基础，掌握基本概念和公式，为后续的高难度题目解答奠定基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_物理_大会员-系统学_A": {
    class_goal: "夯实基础，理解概念",
    grade_description:
      "高二是深化物理原理的理解，提升解题技巧和思维能力，为高三提分和挑战复杂问题做准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_物理_大会员-系统学_A+": {
    class_goal: "梳理常考题型，提升解题能力",
    grade_description:
      "高二是深化物理原理的理解，提升解题技巧和思维能力，为高三提分和挑战复杂问题做准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_物理_大会员-系统学_S": {
    class_goal: "攻克常考重难点，培养物理思维",
    grade_description:
      "高二是深化物理原理的理解，提升解题技巧和思维能力，为高三提分和挑战复杂问题做准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_化学_大会员-系统学_A": {
    class_goal: "夯实基础，理解概念，锻炼解题能力",
    grade_description:
      "高一是巩固化学基础，理解基本概念和原理，为解决更复杂问题打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_化学_大会员-系统学_A+": {
    class_goal: "梳理常考题型，提升解题思维",
    grade_description:
      "高一是巩固化学基础，理解基本概念和原理，为解决更复杂问题打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_化学_大会员-系统学_S": {
    class_goal: "攻克常考难点，构建化学思维",
    grade_description:
      "高一是巩固化学基础，理解基本概念和原理，为解决更复杂问题打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_化学_大会员-系统学_A": {
    class_goal: "夯实基础，理解概念，锻炼解题能力",
    grade_description:
      "高二是深化化学理论的理解，提升解题技巧和综合应用能力，为高三提分和应对难题做准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_化学_大会员-系统学_A+": {
    class_goal: "梳理常考题型，提升解题思维",
    grade_description:
      "高二是深化化学理论的理解，提升解题技巧和综合应用能力，为高三提分和应对难题做准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_化学_大会员-系统学_S": {
    class_goal: "攻克常考难点，构建化学思维",
    grade_description:
      "高二是深化化学理论的理解，提升解题技巧和综合应用能力，为高三提分和应对难题做准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_生物_大会员-系统学_A+": {
    class_goal: "夯实基础，总结易混概念",
    grade_description:
      "高一是巩固生物基础知识，理解基本概念和原理，为更高难度的内容打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_生物_大会员-系统学_S": {
    class_goal: "高效复习高频考点，拓展更多题型",
    grade_description:
      "高一是巩固生物基础知识，理解基本概念和原理，为更高难度的内容打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_生物_大会员-系统学_A+": {
    class_goal: "夯实基础，总结易混概念",
    grade_description:
      "高二是加深生物学原理的应用，提升解题技巧和分析能力，为高三提分和攻克难题做准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_生物_大会员-系统学_S": {
    class_goal: "高效复习高频考点，拓展更多题型",
    grade_description:
      "高二是加深生物学原理的应用，提升解题技巧和分析能力，为高三提分和攻克难题做准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_英语_大会员-系统学_A+": {
    class_goal: "夯实词汇语法基础，加深知识理解",
    grade_description:
      "高一是巩固英语基础，提升词汇、语法和阅读能力，为后续学习打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高一_英语_大会员-系统学_S": {
    class_goal: "聚焦常考重难点，拓展更多题型",
    grade_description:
      "高一是巩固英语基础，提升词汇、语法和阅读能力，为后续学习打下坚实基础。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_英语_大会员-系统学_A+": {
    class_goal: "夯实词汇语法基础，加深知识理解",
    grade_description:
      "高二是加深语言应用，提升写作和听力技巧，通过提高解题能力为高三提分做好准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  },
  "高二_英语_大会员-系统学_S": {
    class_goal: "聚焦常考重难点，拓展更多题型",
    grade_description:
      "高二是加深语言应用，提升写作和听力技巧，通过提高解题能力为高三提分做好准备。",
    learning_goal: "解决重点题型和易错题型，拓展更多考试题型。"
  }
};

export const learningTargetExam = {
  "6年级(五四制)_数学_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description:
      "6年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_数学_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description:
      "7年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_数学_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description: "8年级是深化代数、几何和数据分析技能的提升期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_数学_模拟考": {
    class_goal: "强化知识点，突破难题",
    grade_description:
      "9年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_语文_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description:
      "7年级是打下语文基础、培养阅读理解和写作能力的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_语文_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description: "8年级是深化文学知识、提高写作技巧和分析能力的时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_语文_模拟考": {
    class_goal: "强化知识点，突破难题",
    grade_description:
      "9年级是提升综合语文能力、为高中语文学习做好准备的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_生物_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description: "7年级是打好生物基础、培养科学探究思维的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_生物_模拟考": {
    class_goal: "强化知识点，突破难题",
    grade_description:
      "8年级是深入理解生物系统和生态知识，提升分析能力的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "6年级(五四制)_地理_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description: "6年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_地理_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description: "7年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_地理_模拟考": {
    class_goal: "强化知识点，突破难题",
    grade_description:
      "8年级是深入学习地理要素与人类活动关系，提升分析和应用能力的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_物理_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description:
      "8年级是建立物理基本概念和规律，培养实验思维的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_物理_模拟考": {
    class_goal: "强化知识点，突破难题",
    grade_description:
      "9年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "6年级(五四制)_英语_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description: "6年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_英语_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description: "7年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_英语_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description:
      "8年级是深化词汇和语法理解，提升阅读和写作水平的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_英语_模拟考": {
    class_goal: "强化知识点，突破难题",
    grade_description:
      "9年级是强化综合应用能力，为高中英语学习打下坚实基础的时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_化学_期中考试": {
    class_goal: "冲刺突破难点，提升解题能力",
    grade_description: "8年级是建立化学基础知识和实验技能的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_化学_模拟考": {
    class_goal: "强化知识点，突破难题",
    grade_description:
      "9年级是深化化学原理的理解，提升解题和应用能力的关键时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "6年级(五四制)_数学_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description:
      "6年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_数学_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description:
      "7年级是打好数学基础、建立基本运算和思维方式的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_数学_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description: "8年级是深化代数、几何和数据分析技能的提升期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_数学_中考": {
    class_goal: "突破重点难点，强化解题技巧",
    grade_description:
      "9年级是为高中数学打下坚实基础、提高综合应用能力的关键时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_语文_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description:
      "7年级是打下语文基础、培养阅读理解和写作能力的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_语文_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description: "8年级是深化文学知识、提高写作技巧和分析能力的时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_语文_中考": {
    class_goal: "突破重点难点，强化解题技巧",
    grade_description:
      "9年级是提升综合语文能力、为高中语文学习做好准备的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_生物_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description: "7年级是打好生物基础、培养科学探究思维的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_生物_中考": {
    class_goal: "突破重点难点，强化解题技巧",
    grade_description:
      "8年级是深入理解生物系统和生态知识，提升分析能力的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "6年级_地理_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description: "6年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_地理_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description: "7年级是掌握地理基础知识和技能、理解自然环境的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_地理_中考": {
    class_goal: "突破重点难点，强化解题技巧",
    grade_description:
      "8年级是深入学习地理要素与人类活动关系，提升分析和应用能力的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_物理_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description:
      "8年级是建立物理基本概念和规律，培养实验思维的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_物理_中考": {
    class_goal: "突破重点难点，强化解题技巧",
    grade_description:
      "9年级是深化物理原理的理解，提升解题技巧和应用能力的关键时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "6年级(五四制)_英语_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description: "6年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "7年级_英语_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description: "7年级是打好英语基础，培养听说读写基本能力的起点。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_英语_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description:
      "8年级是深化词汇和语法理解，提升阅读和写作水平的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_英语_中考": {
    class_goal: "突破重点难点，强化解题技巧",
    grade_description:
      "9年级是强化综合应用能力，为高中英语学习打下坚实基础的时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "8年级_化学_期末考试": {
    class_goal: "精准查缺补漏，强化关键难点",
    grade_description: "8年级是建立化学基础知识和实验技能的关键阶段。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  "9年级_化学_中考": {
    class_goal: "突破重点难点，强化解题技巧",
    grade_description:
      "9年级是深化化学原理的理解，提升解题和应用能力的关键时期。",
    learning_goal: "高频知识查漏补缺，高效突破重难点题型。"
  },
  高三_数学_高考: {
    class_goal: "针对性复习，提升答题技巧",
    grade_description:
      "高三是数学冲刺的关键阶段，通过精准复习和强化训练，提升解题能力，为高考取得理想成绩做好准备。",
    learning_goal: "完善应对高考的方法体系，进一步提升备考能力。"
  },
  高三_物理_高考: {
    class_goal: "针对性复习，提升答题技巧",
    grade_description:
      "高三是物理冲刺的关键阶段，通过梳理重点、攻克难点，提升解题能力，为高考物理高分奠定基础。",
    learning_goal: "完善应对高考的方法体系，进一步提升备考能力。"
  },
  高三_化学_高考: {
    class_goal: "针对性复习，提升答题技巧",
    grade_description:
      "高三是化学冲刺的关键阶段，通过系统复习和突破难点，提升解题技巧，为高考化学高分做好准备。",
    learning_goal: "完善应对高考的方法体系，进一步提升备考能力。"
  },
  高三_生物_高考: {
    class_goal: "针对性复习，提升答题技巧",
    grade_description:
      "高三是生物冲刺的关键阶段，通过强化知识点、提升解题能力，为高考生物高分奠定基础。",
    learning_goal: "完善应对高考的方法体系，进一步提升备考能力。"
  },
  高三_英语_高考: {
    class_goal: "针对性复习，提升答题技巧",
    grade_description:
      "高三是英语冲刺的关键阶段，通过强化听说读写，提升综合能力，为高考英语高分做好准备。",
    learning_goal: "完善应对高考的方法体系，进一步提升备考能力。"
  }
};

export const transformDescBlock = desc => {
  const match = desc.match(/^(.*?[年级|高一｜高二｜高三]是)(.*)$/);
  if (match && desc) {
    return {
      text: match[1] || "",
      block: match[2] || ""
    };
  }
  return {
    text: "",
    block: ""
  };
};
