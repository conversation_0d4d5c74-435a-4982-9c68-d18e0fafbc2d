/*
 * @Date         : 2024-12-25 12:06:04
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { TableColumns } from "/@/components/ReTable/types";

export const colunms: TableColumns[] = [
  {
    field: "contentName",
    desc: "学习内容"
  },
  {
    field: "gradeLabelName",
    desc: "年级"
  },

  {
    field: "classSubjectLabel",
    desc: "学科"
  },

  {
    field: "publisherLabelName",
    desc: "教材版本"
  },

  {
    field: "completeRate",
    desc: "完成度",
    customRender: ({ text }) => {
      return `${text}%`;
    }
  },
  {
    field: "studyDuration",
    desc: "学习时间(分钟)"
  },
  {
    field: "className",
    desc: "该记录对应的班"
  }
];
