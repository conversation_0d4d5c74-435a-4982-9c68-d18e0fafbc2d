<!--
 * @Date         : 2024-12-24 16:48:51
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<template>
  <section>
    <!-- 暑假 + 寒假- 小学 and 初中 -->
    <section v-if="isHoliday">
      <template v-if="isStageID1or2AndSubjectEnglish">
        <div>
          1、
          <p v-html="state.text2" />
        </div>
        <div>
          2、
          <p v-html="state.text3" />
        </div>
      </template>
      <template v-else>
        <div>
          1、
          <p v-html="state.text1" />
        </div>
        <div>
          2、
          <p v-html="state.text2" />
        </div>
        <div>
          3、
          <p v-html="state.text3" />
        </div>
      </template>
    </section>
    <section v-else>
      <!-- 秋 已结束 不处理  -->
      <!-- 春 -->
      <template v-if="isStageID1or2AndSubjectEnglish">
        <div>
          1、
          <p v-html="state.text5" />
        </div>
        <div>
          2、
          <p v-html="state.text6" />
        </div>
      </template>
      <template v-else>
        <div>
          1、
          <p v-html="state.text4" />
        </div>
        <div>
          2、
          <p v-html="state.text5" />
        </div>
        <div>
          3、
          <p v-html="state.text6" />
        </div>
      </template>
    </section>
  </section>
</template>

<script setup lang="ts">
import {
  getStageId,
  getStudyHabits,
  holidayLearningDesc,
  learningTargetSystem,
  learningTargetExam,
  transformDescBlock
} from "./data/index";

const props = defineProps<{
  data: any;
}>();
const state = reactive({
  text1: "",
  text2: "",
  text3: "",
  //
  text4: "",
  text5: "",
  text6: ""
});

const holidays = ["暑假", "寒假"];

const isHoliday = holidays.includes(props.data.semesterLabelName);

const isStageID1or2AndSubjectEnglish =
  ["1年级", "2年级"].includes(props.data.gradeLabelName) &&
  props.data.subjectLabelName === "英语";
const stageId = getStageId(props.data.gradeLabelName);

const getHolidayLearningDesc = () => {
  let desc = "";
  // console.log("? 11", isStageID1or2AndSubjectEnglish)
  // console.log("? 35",  props.data.subjectLabelName, props.data.gradeLabelName)
  if (isStageID1or2AndSubjectEnglish) {
    desc =
      holidayLearningDesc[props.data.subjectLabelName]?.[
        props.data.gradeLabelName
      ];
  } else {
    desc =
      holidayLearningDesc[props.data.subjectLabelName]?.[
        props.data.gradeLabelName
      ]?.[props.data.levelLabelName]?.desc || "";
  }
  // console.log("? 44", desc)

  const match = desc.match(/^(.*?年级是)(.*)$/);
  return {
    text: match ? match[1] : "",
    block: match ? match[2] : ""
  };
};

const contentTypeCustomLabels = props.data.adviceLabels
  .map(item => item.name)
  .join("、");
console.log("contentTypeCustomLabels", contentTypeCustomLabels);

const habits = getStudyHabits(
  props.data.subjectLabelName,
  props.data.gradeLabelName
);
console.log("habits", habits);

const targetSystem =
  learningTargetSystem[
    `${props.data.gradeLabelName}_${props.data.subjectLabelName}_${props.data.authLabelName}_${props.data.levelLabelName}`
  ];

const targetExam =
  learningTargetExam[
    `${props.data.gradeLabelName}_${props.data.subjectLabelName}_${props.data.recentExamName}`
  ];

// 寒暑假班 - 小学 and 初中
if (isHoliday) {
  const text = `${getHolidayLearningDesc().text}${
    getHolidayLearningDesc().block
  }建议你在<b>${
    props.data.semesterLabelName
  }</b>结束前掌握<b>${contentTypeCustomLabels}</b>相关的重要知识点。`;
  const textS = `<b>${props.data.semesterLabelName} </b>是培养复习+预习良好学习习惯的重要时期。<b>${props.data.semesterLabelName}</b>结束前，我们会为你提供总计<b>${props.data.customDays.length}天</b>的班课内容，全力帮助你：强化本学期重点，把期末不会的都搞清楚；预习下学期新知，为后续课程提前做准备`;

  if (!isStageID1or2AndSubjectEnglish) {
    // const text1 = `你目前${ props.data.subjectLabelName }学科的成绩是 ${props.data.levelLabelName}`
    const text1 = props.data?.suggest || "---";
    state.text1 = text1;
    state.text2 = text;
    state.text3 = textS;
  } else {
    state.text2 = text;
    state.text3 = textS;
  }
} else {
  let text5 = ``;
  if (props.data.authLabelName !== "大会员-备考学") {
    const str1 = transformDescBlock(targetSystem?.grade_description || "").text;
    const str2 = transformDescBlock(
      targetSystem?.grade_description || ""
    ).block;
    text5 = `${str1}${str2}建议你在${props.data.recentExamName}结束前掌握 ${contentTypeCustomLabels} 相关的重要知识点。`;
  } else {
    const str1 = transformDescBlock(targetExam?.grade_description || "").text;
    const str2 = transformDescBlock(targetExam?.grade_description || "").block;
    text5 = `${str1}${str2}建议你在${props.data.recentExamName}结束前掌握 ${contentTypeCustomLabels} 相关的重要知识点。`;
  }

  // console.log('112', `${props.data.gradeLabelName}_${props.data.subjectLabelName}_${props.data.authLabelName}_${props.data.levelLabelName}`)
  // console.log('targetSystem', targetSystem)

  let text6 = "";
  if (props.data.authLabelName !== "大会员-备考学") {
    text6 = `从本学期的开始到结束，我们每周都会为你提供科学有效的学习内容。全力帮助你${
      targetSystem?.learning_goal || ""
    }`;
  } else {
    text6 = `在考试来临前，我们会为你提供用于高效备考的必要学习内容。全力帮助你${
      targetSystem?.learning_goal || ""
    }`;
  }

  console.log(
    "112",
    `${props.data.gradeLabelName}_${props.data.subjectLabelName}_${props.data.authLabelName}_${props.data.levelLabelName}`
  );

  console.log("targetSystem", targetSystem);

  if (!isStageID1or2AndSubjectEnglish) {
    state.text4 = props.data?.suggest;
  }

  state.text5 = text5;
  state.text6 = text6;
  // // <!-- 小学 -->
  // console.log('stageId', stageId)
  // // <!-- 一二年英语隐藏该条 -->
  // if (stageId === 1) {
  //   const score = getScoreLabel(props.data.levelLabelName, props.data.subjectLabelName, props.data.gradeLabelName)
  //   // console.log('score', score)
  //   // console.log('score', props.data.subjectLabelName)
  //   // console.log('score', props.data.gradeLabelName)
  //   // console.log('score', props.data.levelLabelName)

  //   const { intro, end } = getScoreDescriptions(props.data.subjectLabelName, props.data.gradeLabelName)[props.data.levelLabelName]
  //   state.text1 = `你目前<b>${props.data.subjectLabelName}</b>学科的校内得分率<b>${score}</b>说明<b>${intro}${end}</b>`
  //   const importT = subjectImportance[props.data.subjectLabelName][props.data.gradeLabelName]
  //   state.text2 = `<b>${props.data.gradeLabelName}</b>是${importT}建议您孩子在本学期结束前全面掌握${contentTypeCustomLabels}相关的校内同步知识点`
  //   state.text3 = `<b>${ props.data.gradeLabelName } </b>也是培养良好学习习惯的重要年级。本学期结束前，每周都会为你提供基于<b>${habits}</b>循环的班课内容，全力帮助你养成主动学习的好习惯`
  // } else {
  //   // <!-- 初中 -->
  //   const label = rank.find((i) => i.subTitle === '班级50人中排名5-10')?.label

  //   const text1 = props.data?.suggest || '---'
  //   // `你目前<b>${props.data.subjectLabelName}</b>的班级成绩是目前处于<b>${'??????'}</b>,你计划在紧接着的<b>${ props.data.recentExamName }</b>中将班级排名 studyTargetLabel`

  //   console.log('text1', text1)

  //   const text2 = `为了达到这一考试目标，建议你提前掌握<b>${ props.data.subjectLabelName }${ props.data.recentExamName }</b>前必学的 <b>${contentTypeCustomLabels} </b>相关的校内同步知识点`

  //   state.text1 = text1
  //   state.text2 = text2
  //   state.text3 = `距离<b>${ props.data.subjectLabelName }${ props.data.recentExamName } </b>还有<b>${ daysBetween(props.data.examTime) }</b>天，我们根据您孩子的学习现状定制了<b>${countWeekdaysBetweenDates(props.data.examTime, props.data.userStudyWeek)}</b>天专属班课。期望可以帮助您孩子高效复习，取得理想成绩`
  // }
}
</script>

<style scoped lang="scss">
div {
  display: flex;
  margin-top: 8px;
  line-height: 20px;
}

span > b {
  display: inline-block;
  margin-right: 10px;
}
</style>
