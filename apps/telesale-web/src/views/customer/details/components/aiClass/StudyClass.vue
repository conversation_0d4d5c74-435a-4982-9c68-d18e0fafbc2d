<template>
  <section v-loading="loading">
    <div class="flex justify-between">
      <div>
        <div>
          <div class="time">
            <el-button size="small" round :icon="ArrowLeft" @click="prev">
              上一周
            </el-button>
            <p>{{ start }} - {{ end }}</p>
            <el-button size="small" round @click="next">
              下一周
              <el-icon class="ml-5px">
                <ArrowRight />
              </el-icon>
            </el-button>
          </div>
        </div>
        <div class="m-20px font-bold" v-if="week">
          周完成度 {{ week.weekCompletionRate }}%，和上周相比
          <span
            :class="[week.weekCompletionRateGrowth > 0 ? 'c-red' : 'c-green']"
          >
            {{
              week.weekCompletionRateGrowth > 0
                ? "+" + week.weekCompletionRateGrowth
                : week.weekCompletionRateGrowth
            }}%
          </span>
          ，周学习时间 {{ week.weekStudyDuration }} 分钟，和上周相比
          <span
            :class="[week.weekStudyDurationGrowth > 0 ? 'c-red' : 'c-green']"
          >
            {{
              week.weekStudyDurationGrowth > 0
                ? "+" + week.weekStudyDurationGrowth
                : week.weekStudyDurationGrowth
            }}
            分钟
          </span>
        </div>
        <div class="m-20px font-bold" v-if="week">
          日完成度 {{ dayData.studyCompletedRate }}%，日学习时间
          {{ dayData.studyDuration }} 分钟
        </div>
      </div>
      <div>
        <div>
          <b style="padding-bottom: 20px">每日状态备注说明</b>
          <br />
          <b>未开始：</b>
          当天有班课且当天班课的完成度=0%
          <br />
          <b>可以：</b>
          当天有班课且当天班课的完成度>0%，&lt;50%
          <br />
          <b>不错：</b>
          当天有班课且当天班课的完成度>=50%，&lt;80%
          <br />
          <b>超棒：</b>
          当天有班课且当天班课的完成度>=80%，&lt;100%
          <br />
          <b>完美：</b>
          当天有班课且当天班课的完成度=100%
          <br />
          <b>休息：</b>
          有进行中的班课但当天没有班课
          <br />
          <b>空闲：</b>
          当天没有进行中的班课
        </div>
      </div>
    </div>
    <div class="week">
      <div class="days">
        <div
          class="days-item"
          :class="{
            active_true: activeDay === moment(item.date).format('MM-DD')
          }"
          v-for="(item, index) in userStudyCalendar?.studyCalendars"
          :key="index"
          round
          @click="clickDay({ day: moment(item.date).format('MM-DD'), index })"
        >
          周{{ index + 1 === 7 ? "日" : index + 1 }}:
          {{ item.date }}
          <div style="margin-top: 6px; text-align: center">
            <b>[{{ statusLabel(item.status) }}]</b>

            {{ moment(item.date).format("MM-DD") === today ? "（今天）" : "" }}
          </div>
        </div>
      </div>
    </div>
    <section class="data">
      <ReTable :dataList="tableData" :listHeader="colunms" />
    </section>
  </section>
</template>

<script setup lang="ts">
import { computed } from "vue";
import moment from "dayjs";

import { colunms } from "./data/listData";

import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";
import { forwardApi } from "/@/api/common";

const props = defineProps<{
  userId: string;
}>();

const start = ref("");
const end = ref("");
const tableData = ref([]);
const activeDay = ref("");
const indexDay = ref(moment().day());
const week = ref();
const userStudyCalendar = ref();
const loading = ref(false);
const dayData = ref();

const today = moment().format("MM-DD");
// 限制前后22周
const prevDay = moment().startOf("week").subtract(22, "week");
const nexDay = moment().add(22, "week");

const getData = async ({ startTime, endTime }) => {
  loading.value = true;
  await forwardApi({
    targetPath: "/study-plan/admin/userStudyWeekStatistic",
    target: "studyPlan",
    uid: props.userId,
    startTime,
    endTime
  }).then(res => {
    week.value = res.data;
  });

  await forwardApi({
    targetPath: "/study-plan/admin/userStudyCalendar",
    target: "studyPlan",
    userId: props.userId,
    startTime,
    endTime
  }).then(res => {
    userStudyCalendar.value = res.data;
  });

  await forwardApi({
    targetPath: "/study-plan/admin/userDailyCourseContent",
    target: "studyPlan",
    uid: props.userId,
    date: moment().format("YYYY") + "-" + activeDay.value
  }).then(res => {
    tableData.value = res.data.list;
  });
  loading.value = false;
};

const showDetail = () => {
  activeDay.value = today;

  // 默认本周
  start.value = moment().startOf("week").add(1, "day").format("YYYY-MM-DD");
  end.value = moment().endOf("week").add(1, "day").format("YYYY-MM-DD");

  //
  getData({
    startTime: start.value,
    endTime: end.value
  });
};

showDetail();

const prev = () => {
  const day = moment(start.value).subtract(7, "day");

  if (day.isBefore(prevDay)) {
    ElMessage.error("限制前后22周");
    return;
  }

  start.value = day.startOf("week").add(1, "day").format("YYYY-MM-DD");
  end.value = day.endOf("week").add(1, "day").format("YYYY-MM-DD");

  activeDay.value = moment(start.value)
    .add(indexDay.value - 1, "day")
    .format("MM-DD");
  getData({
    startTime: start.value,
    endTime: end.value
  });
};

const next = () => {
  const day = moment(start.value).add(7, "day");

  if (day.isAfter(nexDay)) {
    ElMessage.error("限制前后22周");
    return;
  }

  start.value = day.startOf("week").add(1, "day").format("YYYY-MM-DD");
  end.value = day.endOf("week").add(1, "day").format("YYYY-MM-DD");
  activeDay.value = moment(start.value)
    .add(indexDay.value - 1, "day")
    .format("MM-DD");
  getData({
    startTime: start.value,
    endTime: end.value
  });
};

const days = computed(() => {
  const days = [];
  for (let index = 0; index < 7; index++) {
    days.push(moment(start.value).add(index, "day").format("MM-DD"));
  }
  return days;
});

const clickDay = ({ day, index }) => {
  activeDay.value = day;
  indexDay.value = index + 1;

  loading.value = true;
  forwardApi({
    targetPath: "/study-plan/admin/userDailyCourseContent",
    target: "studyPlan",
    uid: props.userId,
    date: moment().format("YYYY") + "-" + day
  })
    .then(res => {
      tableData.value = res.data.list;
    })
    .finally(() => {
      loading.value = false;
    });
};

const statusLabel = status => {
  const statusMap = {
    USER_STUDY_RATE_STATUS_NOT_START: "未开始",
    USER_STUDY_RATE_STATUS_CAN: "可以",
    USER_STUDY_RATE_STATUS_GOOD: "不错",
    USER_STUDY_RATE_STATUS_EXCELLENT: "超棒",
    USER_STUDY_RATE_STATUS_PERFECT: "完美",
    USER_STUDY_RATE_STATUS_REST: "休",
    USER_STUDY_RATE_STATUS_IDLE: "空"
  };
  return statusMap[status] || "";
};

watch(
  () => [activeDay.value, week.value],
  () => {
    if (!activeDay.value) return;
    const date = moment().format("YYYY") + "-" + activeDay.value;

    const itemData = week.value?.list?.find(item => item.date === date);
    dayData.value = itemData || {
      studyCompletedRate: 0,
      studyDuration: 0
    };
  },
  {
    immediate: true
  }
);
</script>

<style scoped lang="scss">
.list {
  text-align: left;
}

.data {
  height: 60vh;
  overflow-y: scroll;
}

.week {
  margin: 20px 0;
}
.time {
  display: flex;
  align-items: center;
  margin-left: 20px;
}
.time p {
  margin: 0 20px;
}
.days {
  display: flex;
  margin-left: 20px;
  gap: 20px;
}

.active_true {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

.days-item {
  padding: 10px;
  border: 1px solid #409eff;
  border-radius: 10px;
}
</style>
