<!--
 * @Date         : 2024-12-24 15:28:16
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import ClassList from "./ClassList.vue";
import { forwardApi } from "/@/api/common/index";

const props = defineProps<{
  userId: string;
}>();

const currentData = ref([]);
const historyData = ref([]);
const loading = ref(false);

const getData = () => {
  loading.value = true;
  forwardApi({
    targetPath: "/study-plan/admin/userClassList",
    target: "studyPlan",
    userId: props.userId
  })
    .then(res => {
      const cData = [];
      const hData = [];
      res.data.list.forEach(item => {
        if (item.userClassStatus === "CLASS_USER_STATUS_NORMAL") {
          cData.push(item);
        } else {
          hData.push(item);
        }
      });
      currentData.value = cData;
      historyData.value = hData;
      console.log(cData, hData);
    })
    .finally(() => {
      loading.value = false;
    });
};

getData();

defineExpose({
  getData
});
</script>

<template>
  <div v-loading="loading" class="min-h-400px">
    <template v-if="currentData.length > 0">
      <div class="text-16px mb-10px font-bold">当前在的班课</div>
      <ClassList :data="currentData" :userId="props.userId" />
    </template>
    <template v-if="historyData.length > 0">
      <div class="text-16px mb-10px font-bold">历史在的班课</div>
      <ClassList :data="historyData" :userId="props.userId" />
    </template>
  </div>
</template>

<style lang="scss" scoped></style>
