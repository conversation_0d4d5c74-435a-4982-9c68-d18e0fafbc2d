<!--
 * @Date         : 2024-12-24 15:53:11
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import Advice from "./Advice.vue";
import { weekdays } from "./data";
import { forwardApi } from "/@/api/common";

const props = defineProps<{
  data: any[];
  userId: string;
}>();

const addData = () => {
  props.data.forEach(item => {
    getDayStudyTime(item);
    getClassCompleteRate(item);
  });
};

const getDayStudyTime = row => {
  row.studyTimeLoading = true;
  forwardApi({
    targetPath: "/study-plan/admin/getUserClassContentStudyDuration",
    target: "studyPlan",
    classId: row.classId,
    userId: props.userId,
    method: "post",
    hideError: true
  })
    .then(res => {
      row.studyTimeStr = res.data.contentStudyDurationMinutesDaily;
    })
    .finally(() => {
      row.studyTimeLoading = false;
    });
};

const getClassCompleteRate = row => {
  row.completeRateLoading = true;
  forwardApi({
    targetPath: "/study-plan/admin/userClassCompleteRate",
    target: "studyPlan",
    classId: row.classId,
    userId: props.userId,
    hideError: true
  })
    .then(res => {
      row.completeRateStr = res.data.completeRate;
      row.labelList = res.data.labelList;
    })
    .finally(() => {
      row.completeRateLoading = false;
    });
};

watch(
  () => props.data?.length,
  n => {
    if (n) {
      addData();
    }
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div
    v-for="item in props.data"
    :key="item.classId"
    class="flex mb-20px max-h-400px card"
  >
    <div class="h-320px min-w-500px">
      <el-descriptions :column="1" border>
        <el-descriptions-item label="班课名称" align="center">
          {{ item.className }}
        </el-descriptions-item>
        <el-descriptions-item label="教材版本" align="center">
          {{ item.publisherLabelName }}
        </el-descriptions-item>
        <el-descriptions-item label="班课时间" align="center">
          {{ item.classStartTime }} 至 {{ item.classEndTime }}
        </el-descriptions-item>
        <el-descriptions-item label="每周学习日" align="center">
          {{ item.userStudyWeek.map(item => weekdays[item]).join("、") }}
        </el-descriptions-item>
        <el-descriptions-item label="预估每日学习时长" align="center">
          <div v-loading="item.studyTimeLoading">
            {{
              `${
                !item.studyTimeStr
                  ? "暂无"
                  : `${item.studyTimeStr < 6 ? "<5" : item.studyTimeStr}分钟`
              }`
            }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="在班累计学习时长" align="center">
          {{ item.studyTimeMinutes }}分钟
        </el-descriptions-item>
        <el-descriptions-item label="学习水平" align="center">
          {{ item.levelLabelName }}
        </el-descriptions-item>
        <el-descriptions-item label="最近考试日期" align="center">
          {{ item.examTime }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="w-60% h-321px flex box-border">
      <div class="w-50%">
        <div class="title">班课内容及完成度</div>
        <div v-loading="item.completeRateLoading" class="content">
          <p class="mb-10px">
            <b class="mr-20px">总完成度</b>
            {{ item.completeRateStr || 0 }}%
          </p>
          <template v-if="!item.labelList?.length">暂无</template>
          <template v-else>
            <b>分别完成</b>
            <p
              class="ml-20px"
              v-for="(label, index) in item.labelList"
              :key="index"
            >
              <b style="padding-right: 20px">{{ label.name }}</b>
              {{ label.completeRate }}%
            </p>
          </template>
        </div>
      </div>
      <div class="w-50%">
        <div class="title">学习建议</div>
        <div class="content">
          <Advice :data="item" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.title {
  font-size: 14px;
  height: 40px;
  line-height: 23px;
  text-align: center;
  border: 1px solid var(--el-border-color-lighter);
  padding: 8px 11px;
  color: var(--el-text-color-regular);
  background: #f5f7fa;
  box-sizing: border-box;
  border-bottom: none;
}

.content {
  font-size: 14px;
  height: calc(100% - 40px);
  padding: 8px 11px;
  border: 1px solid var(--el-border-color-lighter);
  margin-left: -1px;
  overflow-y: auto;
  box-sizing: border-box;
}

.card {
  border: 1px solid #f5f7fa;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 4px 12px;
}
</style>
