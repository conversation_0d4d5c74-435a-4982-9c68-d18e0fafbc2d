<!--
 * @Date         : 2024-10-25 16:08:29
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="课程到期"
    width="800px"
    close-on-click-modal
  >
    <div class="flex flex-col justify-center items-center pt-30px">
      <div v-for="(courseStage, idx) in courseStageList" :key="idx">
        <div class="mb-10px text-20px">
          {{
            subjectType.find(
              item => item.targetType === courseStage[0].targetType
            ).title
          }}
        </div>

        <div v-if="courseStage.length !== 0" class="mb-30px">
          <el-table :data="courseStage" border>
            <el-table-column prop="name" label="授权名称" width="200" />
            <el-table-column prop="name" label="剩余时长" width="180">
              <template #default="{ row }">
                {{ calcTime(row) }}
              </template>
            </el-table-column>
            <el-table-column prop="address" label="到期时间" width="180">
              <template #default="{ row }">
                {{ dayjs(row.expireTime).format("YYYY-MM-DD") }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineModel } from "vue";
import dayjs from "dayjs";
import { subjectType } from "/@/views/customer/details/data";

const visible = defineModel<boolean>("visible");

const props = defineProps<{
  courseList: any;
}>();

const courseStageList: any = computed(() => {
  return props.courseList.reduce((pre, cur) => {
    if (pre[cur.targetType]) {
      pre[cur.targetType].push(cur);
    } else {
      pre[cur.targetType] = [cur];
    }

    return pre;
  }, {});
});

function calcTime(time) {
  const hour = dayjs(time.expireTime).diff(dayjs(), "hour");
  return `${Math.floor(hour / 24)}天${hour % 24}小时`;
}
</script>

<style scoped lang="scss">
.text1 {
  border-right: 1px solid #3fbaff;
}

.text2 {
  border-top: 1px solid #3fbaff;
}

.content-box {
  > div {
    text-align: center;
  }
}
</style>
