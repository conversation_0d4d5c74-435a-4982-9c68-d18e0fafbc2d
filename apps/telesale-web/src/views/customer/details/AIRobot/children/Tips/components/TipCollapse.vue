<template>
  <el-collapse v-model="activeNames">
    <el-collapse-item
      :name="tip.knowledgeID"
      v-for="(tip, index) in tips"
      :key="index"
    >
      <template #title>
        <el-text truncated>
          {{ getDetail(tip.knowledgeID)?.baseQuestion }}
        </el-text>
      </template>

      <div v-html="getDetail(tip.knowledgeID)?.answer" />
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts" setup>
import { getMget } from "/@/api/AISupport/Library";

const props = defineProps<{
  tips: any[];
}>();

const activeNames = ref([]);
const tipDetail = ref([]);

function getDetail(id: string) {
  return tipDetail.value?.find(item => item.id === id);
}

watch(
  () => props.tips,
  async newV => {
    console.log("newV", newV);
    const ids = newV.map(item => item.knowledgeID);
    if (ids.length === 0) return;
    const res: any = await getMget({
      ids: ids
    });
    tipDetail.value = res.data.list;
  },
  { deep: true, immediate: true }
);
</script>

<style scoped lang="scss"></style>
