<!--
 * @Date         : 2024-11-11 12:22:36
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <el-input
      @input="search"
      v-model="searchVal"
      :prefix-icon="Search"
      placeholder="请输入你需要搜索的话术问题"
      clearable
    />

    <div class="choose-container">
      <div
        v-for="(qa, index) in chooseList"
        :key="index"
        @click="choose(qa)"
        class="choose-box p-5px flex items-center cursor-pointer"
      >
        <el-text truncated>
          {{ qa.baseQuestion }}
        </el-text>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import useGetCurrentState from "../../../hooks/useGetCurrentState";
import { Search } from "@element-plus/icons-vue";
import { getQuery, getLibraryUUID } from "/@/api/AISupport/Library";
import { defineModel } from "vue";

const activeNames = defineModel<number[]>("activeNames");
const emit = defineEmits(["choose"]);

const searchVal = ref("");
const chooseList = ref([]);

const { robotState } = useGetCurrentState();

async function search() {
  if (searchVal.value === "") {
    chooseList.value = [];
    return;
  }

  const res: any = await getQuery({
    libraryUUID: getLibraryUUID(),
    content: searchVal.value,
    searchMode: "question",
    pages: 1,
    pageSize: 100
  });

  chooseList.value = res.data.list;
  console.log("res", res.data.list);
}

function choose(qa) {
  robotState.tips.unshift({
    content: qa.answer,
    question: qa.baseQuestion,
    score: 0
  });

  activeNames.value.push(robotState.tips.length);

  chooseList.value = [];
  searchVal.value = "";

  emit("choose");
}
</script>

<style scoped lang="scss">
.choose-container {
  box-shadow: 0px 5px 10px 0px #999;
  position: relative;
}

.choose-box {
  &:hover {
    background-color: #409eff;
    :deep(.el-text) {
      color: #fff !important;
    }
  }
}
</style>
