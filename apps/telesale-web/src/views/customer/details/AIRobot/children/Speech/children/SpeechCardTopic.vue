<!--
 * @Date         : 2024-12-03 10:34:27
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane
        :label="it.topic"
        :name="idx"
        v-for="(it, idx) in topicSort"
        :key="idx"
      >
        <span class="content" v-html="it.content" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";

const props = defineProps<{
  topic: any;
}>();

const activeName = ref(0);
const customMsg: any = inject("customMsg", ref());

console.log("innnnn", customMsg);

// 排序顺序定义
const order = [
  "初一",
  "初二",
  "初三",
  "注册时长小于三个月（小于等于）",
  "注册时长大于三个月"
];

function sort() {
  if (customMsg.grade === "八年级") {
    order.splice(0, 3, "初二", "初一", "初三");
  } else if (customMsg.grade === "九年级") {
    order.splice(0, 3, "初三", "初一", "初二");
  }

  if (dayjs().diff(customMsg.regTime, "month") > 3) {
    order.splice(3, 2, "注册时长大于三个月", "注册时长小于三个月（小于等于）");
  }
}
sort();

const topicSort = computed(() => {
  if (!props.topic) return [];
  return props.topic!.sort(
    (a: any, b: any) => order.indexOf(a.condition) - order.indexOf(b.condition)
  );
});
</script>

<style scoped lang="scss">
.content {
  font-size: 14px;
  color: #606266;
  word-break: break-all;
}
</style>
