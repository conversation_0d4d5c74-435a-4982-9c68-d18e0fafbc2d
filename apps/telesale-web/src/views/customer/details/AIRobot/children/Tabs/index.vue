<!--
 * @Date         : 2024-08-28 14:51:02
 * @Description  : 常用功能
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <el-card class="card-box">
    <el-collapse v-model="activeNames">
      <el-collapse-item title="基础信息" name="1">
        <template #title>
          <div class="flex items-center">
            <div class="mr-5px">
              <el-text class="font-600">基础信息</el-text>
            </div>
          </div>
        </template>

        <el-divider class="mt-0!" />

        <el-tabs
          type="border-card"
          v-model="tabName"
          :style="{ width: `${offsetWidth - 75}px` }"
        >
          <el-tab-pane label="基础信息" name="baseInfo" lazy>
            <BaseMsg
              v-if="customMsg"
              :customMsg="customMsg || null"
              @success="getInfo"
            />
          </el-tab-pane>

          <el-tab-pane label="观看记录" name="lookRecord" lazy>
            <div
              class="overflow-auto"
              :style="{ width: `${offsetWidth - 75}px` }"
            >
              <VedioList
                :customMsg="customMsg || {}"
                :key="customMsg?.userid ? 1 : 2"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane
            label="学情报告"
            name="learnReport"
            lazy
            v-if="props.customMsg?.userid"
          >
            <Report :userId="props.customMsg.userid" />
          </el-tab-pane>

          <el-tab-pane label="订单记录" name="orderRecord" lazy>
            <OrderList
              :userid="props.customMsg?.userid || ''"
              :key="props.customMsg?.userid ? 1 : 2"
            />
          </el-tab-pane>

          <el-tab-pane
            v-if="getAuth('telesale_admin_repurchase')"
            label="续购"
            name="repurchase"
            lazy
          >
            <div v-loading="loadingRepurchase">
              <DiffFind
                :userid="props.customMsg?.userid || ''"
                :key="props.customMsg?.userid ? 1 : 2"
                v-model:loading="loadingRepurchase"
                :isShowOperation="true"
                type="repurchase"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane label="呼叫记录" name="callRecord" lazy>
            <CallRecord
              method="agent"
              from-call
              :phone="props.customMsg.phone || ''"
              :workerId="props.customMsg.workerid || 0"
              :infoUuid="props.customMsg?.infoUuid"
            />
          </el-tab-pane>

          <el-tab-pane label="跟单记录" name="documentaryRecord" lazy>
            <DocumentaryRecord
              ref="documentaryRecordRef"
              :infoUuid="props.customMsg?.infoUuid || ''"
              :key="props.customMsg?.infoUuid ? 1 : 2"
              :type="1"
            />
          </el-tab-pane>

          <el-tab-pane label="线索追溯" name="history" lazy>
            <HistoryList
              :infoUuid="props.customMsg?.infoUuid || ''"
              :key="props.customMsg?.infoUuid ? 1 : 2"
              type="customer"
            />
          </el-tab-pane>

          <el-tab-pane label="客服工单" name="customerOrder" lazy>
            <CustomerOrder :userId="props.customMsg.userid || ''" />
          </el-tab-pane>

          <el-tab-pane
            label="操作"
            name="tool"
            lazy
            v-if="props.customMsg?.userid"
          >
            <Tool
              :userid="props.customMsg?.userid || ''"
              :key="props.customMsg?.userid ? 1 : 2"
              :hasRisk="props.hasRisk"
            />
          </el-tab-pane>
        </el-tabs>
      </el-collapse-item>
    </el-collapse>
  </el-card>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import VedioList from "/@/views/customer/details/components/tabs/VedioList.vue";
import DocumentaryRecord from "/@/views/customer/details/components/tabs/DocumentaryRecord.vue";
import BaseMsg from "/@/views/customer/details/components/BaseMsg.vue";
import useCalcVideoListWidth from "./hooks/useCalcVideoListWidth";
import OrderList from "/@/views/customer/details/components/tabs/OrderList.vue";
import AddPad from "/@/views/customer/details/components/tabs/AddPad.vue";
import CustomerOrder from "/@/views/customer/details/components/tabs/CustomerOrder.vue";
import HistoryList from "/@/views/customer/details/components/tabs/HistoryList.vue";
import Tool from "/@/views/customer/details/components/tabs/Tool.vue";
import { getAuth } from "/@/utils/auth";
import DiffFind from "/@/components/DiffFind/index.vue";
import CallRecord from "/@/components/CallRecord/index.vue";
import Report from "../../../components/tabs/report.vue";

const tabName = ref("baseInfo");

const loading = ref(true);
const loadingNew = ref(true);
const loadingRepurchase = ref(true);

const props = defineProps<{
  customMsg: any;
  getInfo: any;
  hasRisk: boolean;
}>();

const { offsetWidth } = useCalcVideoListWidth();

const activeNames = ref("1");
</script>

<style scoped lang="scss">
.card-box {
  :deep(.el-collapse) {
    border: none;
  }
  :deep(.el-collapse-item__wrap) {
    border: none;
  }
  :deep(.el-card__body) {
    padding-top: 10px;
  }
  :deep(.el-collapse-item__arrow) {
    font-size: 18px;
  }
  :deep(.el-tabs__content) {
    overflow: auto !important;
  }
}
</style>
