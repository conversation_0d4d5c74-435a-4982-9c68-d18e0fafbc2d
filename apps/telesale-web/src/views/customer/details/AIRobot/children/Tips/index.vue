<!--
 * @Date         : 2024-08-28 12:18:50
 * @Description  : 智能话术
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="w-100%">
    <el-card header="话术推荐" class="tips-container">
      <template #header>
        <div class="flex justify-between">
          <span class="font-500">话术推荐</span>

          <el-button
            type="primary"
            size="small"
            @click="tipsManageVisible = true"
          >
            话术管理
          </el-button>
        </div>
      </template>

      <div class="flex w-100% question-box">
        <el-tabs
          v-model="tipTab"
          type="border-card"
          class="w-100%"
          :style="{ width: `${offsetWidth - 75}px` }"
        >
          <el-tab-pane label="默认推荐" name="default">
            <QuestionSearch
              v-model:activeNames="activeNames"
              @choose="backTop"
              class="mb-10px"
            />

            <el-empty
              :image-size="100"
              description="暂无话术推荐"
              v-if="robotState.tips.length === 0"
            />

            <div v-else>
              <div class="overflow-auto max-h-300px" ref="QuestionCollapse">
                <el-collapse v-model="activeNames">
                  <el-collapse-item
                    v-for="(item, index) in robotState.tips"
                    :key="index"
                    :name="robotState.tips.length - index"
                  >
                    <template #title>
                      <el-text truncated>
                        {{ item.question }}
                      </el-text>
                    </template>

                    <div v-html="item.content" />
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane
            :label="tip.name"
            :name="tip.name"
            v-for="(tip, index) in tipSets"
            :key="index"
          >
            <div class="overflow-auto max-h-300px">
              <TipCollapse :tips="tip.knowledgeSets" />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>

    <TipsManageDialog
      v-model:visible="tipsManageVisible"
      @confirm="updateTips"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import useGetCurrentState from "../../hooks/useGetCurrentState";
import { useAIRobotState } from "../../store";
import TipsManageDialog from "./components/TipsManageDialog.vue";
import { getTipSet } from "/@/api/AISupport/Tips";
import TipCollapse from "./components/TipCollapse.vue";
import QuestionSearch from "./components/QuestionSearch.vue";
import useCalcVideoListWidth from "../Tabs/hooks/useCalcVideoListWidth";

const { offsetWidth } = useCalcVideoListWidth();
const { robotState } = useGetCurrentState();
const activeNames = ref([0]);
watch(
  () => robotState.tips,
  newV => {
    activeNames.value.splice(0, 1, newV.length);
    console.log("innn", activeNames.value, newV);
  },
  {
    deep: true
  }
);

const tipTab = ref("default");
const tipsManageVisible = ref(false);

const tipSets = ref();
async function getTips() {
  const res = await getTipSet({
    workerId: useAIRobotState().workerID.value
  });

  tipSets.value = res.data.tipSets;

  console.log("res", res);
}

onMounted(() => {
  getTips();
});

async function updateTips() {
  const tipTabCopy = tipTab.value;
  console.log("tipTabCopy", tipTabCopy);
  await getTips();
  nextTick(() => {
    tipTab.value = tipTabCopy;
    console.log("tipTabCopy", tipTabCopy, tipTab.value);
  });
}

const QuestionCollapse = ref();
function backTop() {
  nextTick(() => {
    QuestionCollapse.value.scrollTop = 0;
  });
}
</script>

<style scoped lang="scss">
.question-box {
  :deep(.el-text) {
    max-width: 500px !important;
  }
}
</style>
