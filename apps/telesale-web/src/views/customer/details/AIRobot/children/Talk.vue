<!--
 * @Date         : 2024-06-25 12:16:54
 * @Description  : 对话转译
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="py-20px w-100%">
    <div class="bubble-box" id="msgBox">
      <div v-for="(item, index) in robotState.messageList" :key="index">
        <div
          v-if="item.content"
          :class="[item.role == 'user' ? 'bubble you' : 'bubble me']"
        >
          {{ item.content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, nextTick } from "vue";
import useGetCurrentState from "../hooks/useGetCurrentState";

const { robotState } = useGetCurrentState();
watch(
  () => robotState.messageList,
  newV => {
    nextTick(() => {
      const msgBox = document.querySelector("#msgBox");
      msgBox.scrollTop = msgBox?.scrollHeight;
    });
  },
  { deep: true }
);
</script>

<style scoped lang="scss">
.bubble-box {
  height: 600px;
  overflow-y: auto;

  :deep(.el-card__body) {
    overflow: auto;
  }
}

.bubble {
  position: relative;
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 8px;
  max-width: 70%;
  word-wrap: break-word;
}

.you {
  background-color: #e6e6e6;
  float: left;
  clear: both;
  margin-left: 20px;
}

.me {
  background-color: #00b43c;
  color: white;
  float: right;
  clear: both;
  margin-right: 20px;
}

.bubble::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.you::before {
  top: 50%;
  left: -15px;
  border-width: 10px 15px 10px 0;
  border-color: transparent #e6e6e6 transparent transparent;
  transform: translateY(-50%);
}

.me::before {
  top: 50%;
  right: -15px;
  border-width: 10px 0 10px 15px;
  border-color: transparent transparent transparent #00b43c;
  transform: translateY(-50%);
}
</style>
