<!--
 * @Date         : 2024-09-25 10:20:20
 * @Description  : 用户画像-地区相关
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-card v-if="regionVal" class="card-box">
    <el-collapse v-model="activeName">
      <el-collapse-item name="1">
        <template #title>
          <el-text class="font-600">地区相关</el-text>
        </template>

        <el-divider class="mt-0!" />

        <el-radio-group v-model="areaRadio" class="mb-10px">
          <el-radio-button label="入校相关" value="入校相关" />
          <el-radio-button
            label="中考相关"
            value="中考相关"
            v-if="middleExamDataList.length > 0"
          />
        </el-radio-group>

        <EnterSchool
          v-show="areaRadio === '入校相关'"
          :isNewQuestions="isNewQuestions"
          :regionVal="regionVal"
          :areaData="areaData"
          :activityData="activityData"
        />

        <MiddleExam
          v-show="areaRadio === '中考相关' && middleExamDataList.length > 0"
          :regionVal="regionVal"
          v-model:dataList="middleExamDataList"
        />
      </el-collapse-item>
    </el-collapse>
  </el-card>
</template>

<script lang="ts" setup>
import EnterSchool from "./EnterSchool/index.vue";
import MiddleExam from "./MiddleExam/index.vue";

const props = defineProps<{
  isNewQuestions: any;
  regionVal: any;
  areaData: any;
  activityData: any;
}>();

const activeName = ref("1");
const areaRadio = ref("入校相关");

const middleExamDataList = ref([]);
</script>

<style scoped lang="scss">
.point {
  color: #e6a23c;
  font-weight: bold;
}

.card-box {
  :deep(.el-collapse) {
    border: none;
  }
  :deep(.el-collapse-item__wrap) {
    border: none;
  }
  :deep(.el-card__body) {
    padding-top: 10px;
  }
  :deep(.el-collapse-item__arrow) {
    font-size: 18px;
  }
  :deep(a) {
    color: #409eff !important;
    text-decoration: revert !important;
  }
}
</style>
