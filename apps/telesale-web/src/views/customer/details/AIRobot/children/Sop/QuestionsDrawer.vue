<!--
 * @Date         : 2024-11-04 16:08:36
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-drawer
    v-model="visible"
    @open="openDrawer"
    close-on-click-modal
    :title="info?.name"
    direction="ltr"
    size="40%"
  >
    <el-collapse v-model="activeNames">
      <el-collapse-item
        :name="String(index + 1)"
        v-for="(question, index) in questionData"
        :key="index"
      >
        <template #title>
          <el-text truncated>{{ question.baseQuestion }}</el-text>
        </template>

        <div v-html="question.answer" />
      </el-collapse-item>
    </el-collapse>
  </el-drawer>
</template>

<script lang="ts" setup>
import { defineModel } from "vue";
import { getMget } from "/@/api/AISupport/Library";

const visible = defineModel<boolean>("visible");

const props = defineProps<{
  info: any;
}>();

const activeNames = ref(["1"]);

const questionData = ref([]);
async function getData() {
  const res: any = await getMget({
    ids: props.info.questions.map(item => item.id)
  });

  questionData.value = res.data.list;

  activeNames.value = Array.from(
    { length: questionData.value.length - 1 + 1 },
    (_, index) => String(1 + index)
  );
}

function openDrawer() {
  getData();
}
</script>

<style scoped lang="scss"></style>
