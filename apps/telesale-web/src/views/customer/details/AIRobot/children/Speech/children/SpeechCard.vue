<!--
 * @Date         : 2024-11-29 16:08:16
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-card class="mb-10px">
    <div class="c-#606266 font-bold text-16px mb-10px">{{ info.step }}</div>

    <div v-for="(item, index) in info.topics" :key="index">
      <div v-if="item.topic.length === 1">
        <div class="c-#E6A23C text-14px font-bold mb-5px">
          {{ item.topic[0].topic }}
        </div>

        <div class="content" v-html="item.topic[0].content" />
      </div>

      <div v-else>
        <SpeechCardTopic :topic="item.topic" />
      </div>

      <el-divider v-if="info.topics.length - 1 !== index" class="my-10px!" />
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import SpeechCardTopic from "./SpeechCardTopic.vue";

const props = defineProps<{
  info: any;
}>();
</script>

<style scoped lang="scss">
.content {
  font-size: 14px;
  color: #606266;
  word-break: break-all;
}
</style>
