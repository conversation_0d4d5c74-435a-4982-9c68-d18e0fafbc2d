<!--
 * @Date         : 2024-11-05 11:09:26
 * @Description  : 学习明细
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-drawer
    v-model="visible"
    title="学习明细"
    direction="ltr"
    close-on-click-modal
    size="40%"
    @open="openDrawer"
  >
    <div class="table-box">
      <el-table :data="dataList" border :row-class-name="tableRowClassName">
        <el-table-column prop="semesterName" label="学期" />
        <el-table-column prop="subjectName" label="学科" />
        <el-table-column prop="publisherName" label="教材" />
        <el-table-column label="视频名称" :formatter="row => row.video?.name" />
        <el-table-column
          label="知识点名称"
          :formatter="row => row.topic?.name"
        />
        <el-table-column
          label="是否付费内容"
          :formatter="
            row =>
              row.topic?.isFreeTime ? '限免' : row.topic?.pay ? '付费' : '免费'
          "
        />
        <el-table-column
          label="观看时间"
          :formatter="
            row => dayjs.unix(row.timestamp).format('YYYY-MM-DD HH:mm:ss')
          "
        />
      </el-table>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { defineModel } from "vue";
import { videoHistory } from "/@/api/customerDetails";
import dayjs from "dayjs";

const visible = defineModel<boolean>("visible");
const customMsg: any = inject("customMsg");

const props = defineProps<{
  dateType: string;
}>();

const dataList = ref([]);
async function getList() {
  const data: any = await videoHistory(customMsg.userid);
  dataList.value = data.data.filter(item => item?.timestamp);
}

function openDrawer() {
  getList();
}

function tableRowClassName({ row }) {
  const dayLabel =
    props.dateType === "周"
      ? dayjs().subtract(1, "week").startOf("day")
      : dayjs().startOf("day");

  if (dayLabel.isBefore(dayjs.unix(row?.timestamp))) {
    return "hilight-row";
  }
  return "";
}
</script>

<style scoped lang="scss">
.table-box {
  :deep(.el-table .hilight-row) {
    color: #409eff;
  }
}
</style>
