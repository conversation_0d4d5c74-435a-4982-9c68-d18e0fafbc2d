import {
  getAuthEnd<PERSON>pi,
  getExamPaperPkgApi,
  getSubjectsListApi
} from "/@/api/customer/authContent";
import { subjectType, targetChange } from "/@/views/customer/details/data";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import specialCourseListMath from "/@/utils/asyn/getSpecialCourseList";
import findSubject from "/@/utils/asyn/findSubject";

export default async function (userid) {
  const treeData = ref([]);
  const getTreeData = async () => {
    try {
      const res = await getSubjectsListApi({ userId: userid });
      treeData.value = res.data;
    } catch (error) {
      console.log(error);
    }
  };

  const examPaperData = ref([]);
  const getExamPaperData = async () => {
    try {
      const res = await getExamPaperPkgApi({ userId: userid });
      examPaperData.value = res.data.list;
    } catch (error) {
      console.log(error);
    }
  };

  const endList = ref<any[]>([]);
  const getEndData = (targetType: string) => {
    if (endList.value.length === 0) return [];
    return endList.value[targetType];
  };

  const getAuthEndData = async () => {
    const data = await getAuthEndApi({ userId: userid });
    endList.value = data.data.data;
  };

  const useUser = useUserStore();
  const { specialCourseList, subjectList } = storeToRefs(useUser);
  async function getList() {
    if (!specialCourseList.value.length) {
      useUser.setSpecialCourseList(await specialCourseListMath());
    }
    if (!subjectList.value.length) {
      useUser.setSubjectList(await findSubject());
    }
  }

  const target = ref([]);

  await getTreeData();
  await getExamPaperData();
  await getAuthEndData();
  await getList();

  for (const item of subjectType) {
    getEndData(item.targetType).forEach(element => {
      element.targetType = item.targetType;
      element.targetId = element.id;
      console.log(element);
      element.name = targetChange(element, treeData.value, examPaperData.value);
      target.value.push(element);
    });
  }

  return target.value;
}
