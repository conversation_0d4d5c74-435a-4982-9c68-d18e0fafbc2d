/*
 * @Date         : 2024-09-04 11:06:09
 * @Description  : 计算观看记录的宽度
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref, onMounted, onUnmounted } from "vue";

export default function () {
  const offsetWidth = ref(0);
  onMounted(() => {
    calcWidth();
  });

  window.addEventListener("resize", calcWidth);

  function calcWidth() {
    offsetWidth.value =
      (document.querySelector(".AIRobot-container") as any)?.offsetWidth -
      (document.querySelector(".AICard-container") as any)?.offsetWidth -
      10;
  }

  onUnmounted(() => {
    window.removeEventListener("resize", calcWidth);
  });

  return { offsetWidth };
}
