<!--
 * @Date         : 2024-09-25 10:20:32
 * @Description  : 用户画像-活跃相关
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <el-card class="card-box">
      <el-collapse v-model="activeName">
        <el-collapse-item name="1">
          <template #title>
            <div class="flex items-center">
              <div class="mr-5px">
                <el-text class="font-600">活跃相关</el-text>
              </div>
              <el-tooltip
                effect="dark"
                content="周数据为当前自然周数据，日数据为昨日数据"
                placement="bottom"
              >
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>

          <el-divider class="mt-0!" />

          <el-radio-group v-model="dateType">
            <el-radio-button label="周" value="周" />
            <el-radio-button label="日" value="日" />
          </el-radio-group>

          <div class="my-10px">
            <div class="mb-5px flex items-center">
              <span>学习时长：</span>
              <el-button
                type="primary"
                link
                size="small"
                @click="studyDurationDetailVisible = true"
              >
                查看明细
              </el-button>
            </div>
            <div>
              {{ dateType === "周" ? "本周" : "昨日" }}总共学习
              <span class="point">{{ activityData?.learningTime }}</span>
              分钟 ，在同学段洋葱用户中学习时长排名
              <span class="point">{{ activityData?.videoRank }}</span>
              位 ，已经超过了
              <span class="point">{{ StudyRankPercent }}%</span>
              的同类用户。
            </div>
          </div>

          <div>
            <div class="mb-5px flex items-center">
              <span>答题数量：</span>
              <el-button
                type="primary"
                link
                size="small"
                @click="answerDetailVisible = true"
              >
                查看明细
              </el-button>
            </div>
            <div>
              {{ dateType === "周" ? "本周" : "昨日" }}总共答题
              <span class="point">{{ activityData?.quizAttempts }}</span>
              道 ，总正确率：
              <span class="point">
                {{
                  calcPercent(
                    activityData?.correctAnswers,
                    activityData?.quizAttempts
                  )
                }}%
              </span>
              ，在同学段洋葱用户中答题量排名
              <span class="point">{{ activityData?.quizRank }}</span>
              位 ，已经超过了
              <span class="point">{{ AnswerRankPercent }}%</span>
              的同类用户。
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <StudyDurationDetailDrawer
      :dateType="dateType"
      v-model:visible="studyDurationDetailVisible"
    />
    <AnswerDetailDrawer
      :dateType="dateType"
      v-model:visible="answerDetailVisible"
    />
  </div>
</template>

<script lang="ts" setup>
import { QuestionFilled } from "@element-plus/icons-vue";
import StudyDurationDetailDrawer from "./StudyDurationDetailDrawer.vue";
import AnswerDetailDrawer from "./AnswerDetailDrawer.vue";

const props = defineProps<{
  dailyActivityData: any;
  weeklyActivityData: any;
}>();

const dateType = ref("周");

const activityData = computed(() => {
  if (dateType.value === "周") return props.weeklyActivityData;
  else return props.dailyActivityData;
});

function calcPercent(a, b) {
  if (b === 0) return 0;
  return ((a / b) * 100).toFixed(2);
}

//  1. （学习总人数-当前学习排名数）/学习总人数
const StudyRankPercent = computed(() => {
  if (activityData.value?.totalVideoUsers === 0) return 0;

  return (
    ((activityData.value?.totalVideoUsers - activityData.value?.videoRank) /
      activityData.value?.totalVideoUsers) *
    100
  ).toFixed(2);
});

//  1. （答题总人数-当前答题排名数）/答题总人数
const AnswerRankPercent = computed(() => {
  if (activityData.value?.totalQuizUsers === 0) return 0;

  return (
    ((activityData.value?.totalQuizUsers - activityData.value?.quizRank) /
      activityData.value?.totalQuizUsers) *
    100
  ).toFixed(2);
});

const activeName = ref("1");

const studyDurationDetailVisible = ref(false);
const answerDetailVisible = ref(false);
</script>

<style scoped lang="scss">
.point {
  color: #e6a23c;
  font-weight: bold;
}
.card-box {
  :deep(.el-collapse) {
    border: none;
  }
  :deep(.el-collapse-item__wrap) {
    border: none;
  }
  :deep(.el-card__body) {
    padding-top: 10px;
  }
  :deep(.el-collapse-item__arrow) {
    font-size: 18px;
  }
}
</style>
