<!--
 * @Date         : 2024-11-05 16:04:02
 * @Description  : 答题明细
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-drawer
    v-model="visible"
    title="答题明细"
    direction="ltr"
    close-on-click-modal
    size="40%"
    @open="openDrawer"
  >
    <el-table :data="dataList" border>
      <el-table-column
        label="学科"
        prop="subjectId"
        :formatter="
          row => subjectList.find(item => item.id === row.subjectId).name
        "
      />
      <el-table-column label="知识点" prop="topicName" />
      <el-table-column label="做题数" prop="number" />
      <el-table-column
        label="正确率"
        prop="rate"
        :formatter="row => String(Math.round(row.rate)) + '%'"
      />
    </el-table>
  </el-drawer>
</template>

<script lang="ts" setup>
import { defineModel } from "vue";
import { getLearningDetail } from "/@/api/AISupport/Learning";
import { findSubject } from "/@/api/user";

const visible = defineModel<boolean>("visible");
const customMsg: any = inject("customMsg");

const dataList = ref([]);
const subjectList = ref([]);
async function findSubjectHandle() {
  const res: any = await findSubject();
  subjectList.value = res.data?.subjects;
}

async function openDrawer() {
  findSubjectHandle();

  const res: any = await getLearningDetail({
    userId: customMsg.userid
  });
  console.log("res", res.data);
  dataList.value = res.data?.problem?.items;
}
</script>

<style scoped lang="scss"></style>
