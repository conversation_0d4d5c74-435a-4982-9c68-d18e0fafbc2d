<!--
 * @Date         : 2024-11-12 18:22:01
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-card class="card-box" v-show="showCard">
    <el-collapse v-model="activeNames">
      <el-collapse-item name="学情分析">
        <template #title>
          <div class="flex items-center">
            <div class="mr-5px">
              <el-text class="font-600">学情分析</el-text>
            </div>
          </div>
        </template>

        <div
          v-loading="loading"
          element-loading-text="学情生成中～"
          :style="{ width: `${offsetWidth - 75}px` }"
        >
          <el-radio-group v-model="timeRadio" class="mb-10px">
            <el-radio-button label="本周" value="本周" />
            <el-radio-button label="上周" value="上周" />
          </el-radio-group>

          <el-table :data="parseMarkdownTable" border>
            <el-table-column
              :label="key"
              :resizable="false"
              :prop="key"
              v-for="(key, index) in parseKeysArray"
              :key="index"
            />
          </el-table>
        </div>
      </el-collapse-item>
    </el-collapse>
  </el-card>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import updateLocale from "dayjs/plugin/updateLocale";
import { getLearningAnalyze } from "/@/api/AISupport/Portraits";
import useCalcVideoListWidth from "../Tabs/hooks/useCalcVideoListWidth";

const { offsetWidth } = useCalcVideoListWidth();

dayjs.extend(updateLocale);
dayjs.updateLocale("en", {
  weekStart: 1 // 配置为周一
});

const props = defineProps<{
  customMsg: any;
}>();

const activeNames = ref(["学情分析"]);
const timeRadio = ref("本周");
const loading = ref(false);
const showCard = ref(true);

const parseMarkdownTable = computed(() => {
  const mdString =
    timeRadio.value === "本周" ? currentweekData.value : lastweekData.value;
  if (!mdString) return [];

  const rows = mdString.split("\n").filter(line => line.includes("|"));

  // 解析表头
  const headers = rows[0]
    .split("|")
    .map(header => header.trim())
    .filter(header => header.length > 0);

  // 解析表格数据
  const data = rows.slice(2).map(row => {
    const columns = row
      .split("|")
      .map(cell => cell.trim())
      .filter(cell => cell.length > 0);
    return headers.reduce((obj, header, index) => {
      obj[header] = columns[index] || "";
      return obj;
    }, {});
  });

  return data;
});

const parseKeysArray = computed(() => {
  if (parseMarkdownTable.value[0]) {
    return Object.keys(parseMarkdownTable.value[0]);
  }

  return [];
});

const lastweekData = ref();
const currentweekData = ref();

async function getData() {
  loading.value = true;
  const userId = props.customMsg.userid;
  // const userId = "66eb9aebae8e0f00014c11a8";

  try {
    const res: any = await Promise.all([
      getLearningAnalyze({
        userId: userId,
        startTime: dayjs().startOf("week").unix(),
        endTime: dayjs().endOf("week").unix()
      }),
      getLearningAnalyze({
        userId: userId,
        startTime: dayjs().subtract(1, "week").startOf("week").unix(),
        endTime: dayjs().subtract(1, "week").endOf("week").unix()
      })
    ]);

    currentweekData.value = res[0].data?.content
      ?.replaceAll("<br>", "\n")
      .trim();
    lastweekData.value = res[1].data?.content?.replaceAll("<br>", "\n").trim();

    // 如果两个都为空，就不显示卡片
    if (!currentweekData.value && !lastweekData.value) {
      showCard.value = false;
    }
  } catch (error) {
    console.log("error", error);
  }

  loading.value = false;
}

onMounted(() => {
  getData();
});
</script>

<style scoped lang="scss">
.card-box {
  :deep(.el-collapse) {
    border: none;
  }
  :deep(.el-collapse-item__wrap) {
    border: none;
  }
  :deep(.el-card__body) {
    padding-top: 10px;
  }
  :deep(.el-collapse-item__arrow) {
    font-size: 18px;
  }
}
</style>
