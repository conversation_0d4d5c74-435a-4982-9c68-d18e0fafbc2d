<!--
 * @Date         : 2024-11-29 14:11:12
 * @Description  : 专属话术
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div
    class="min-h-200px max-h-[calc(100vh-330px)] overflow-auto"
    v-loading="loading"
  >
    <div v-for="(item, index) in topicList" :key="index">
      <SpeechCard :info="item" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import SpeechCard from "./children/SpeechCard.vue";
import { getSopTopicByUserId } from "/@/api/AISupport/SopTopic";

const customMsg: any = inject("customMsg", ref());

const loading = ref(false);
const topicList = ref();

async function getTopic() {
  loading.value = true;
  try {
    const res: any = await getSopTopicByUserId(customMsg.userid);
    // const res: any = await getSopTopicByUserId("66eb9aebae8e0f00014c11a8");
    topicList.value = res.data.topicArrays;
  } catch (error) {
    console.log(error);
  }
  loading.value = false;
}

onMounted(() => {
  getTopic();
});
</script>

<style scoped lang="scss"></style>
