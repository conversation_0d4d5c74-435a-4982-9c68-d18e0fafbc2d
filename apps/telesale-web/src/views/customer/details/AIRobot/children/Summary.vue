<!--
 * @Date         : 2024-07-10 11:19:45
 * @Description  : AI通话总结
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-collapse v-model="activeNames">
    <el-collapse-item title="AI通话总结" name="AI通话总结">
      <template #title>
        <span>AI通话总结</span>
        <SummaryRetry />
      </template>
      <div
        class="mt-10px text-box"
        v-loading="robotState.summaryRetryLoading"
        element-loading-text="AI通话总结重新生成中"
      >
        <el-input
          v-model="robotState.summary"
          :rows="6"
          type="textarea"
          :disabled="!robotState.summaryFlag"
          @blur="saveSummary"
        />
      </div>
    </el-collapse-item>
  </el-collapse>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import { updateSummary } from "/@/api/AISupport/Quality";
import { ElMessage } from "element-plus";
import useGetCurrentState from "../hooks/useGetCurrentState";
import SummaryRetry from "../components/SummaryRetry.vue";

const { robotState } = useGetCurrentState();
const activeNames = ref(["AI通话总结"]);

async function saveSummary() {
  const param = {
    conversation: {
      id: robotState.conversation.id,
      summary: {
        content: robotState.summary
      }
    }
  };
  await updateSummary(param);

  ElMessage.success("保存成功");
}
</script>

<style scoped lang="scss">
.red-text {
  :deep(textarea) {
    color: #f56c6c !important;
  }
}

.text-box {
  :deep(.el-textarea.is-disabled .el-textarea__inner) {
    color: #606266 !important;
  }
}
</style>
