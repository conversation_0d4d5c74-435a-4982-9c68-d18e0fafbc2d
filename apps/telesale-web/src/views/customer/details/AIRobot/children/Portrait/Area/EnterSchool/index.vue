<!--
 * @Date         : 2024-11-13 12:19:30
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->
<template>
  <div>
    <el-descriptions :column="2">
      <el-descriptions-item :label="city">
        <span class="point">{{ isNewQuestions ? "" : "非" }}新中考</span>
      </el-descriptions-item>
      <el-descriptions-item :label="`${city}洋葱APP用户`">
        <span class="point">{{ activityData?.regUsers }}</span>
        人
      </el-descriptions-item>
      <el-descriptions-item :label="`${city}洋葱合作院校`">
        <span class="point">{{ areaData?.length }}</span>
        所
      </el-descriptions-item>
    </el-descriptions>

    <div>合作学校明细</div>
    <el-table :data="props.areaData?.slice(0, 4)">
      <el-table-column prop="schoolName" label="学校名称" />
      <el-table-column label="所在省市区">
        <template #default="{ row }">
          {{ row.province + row.city + row.area }}
        </template>
      </el-table-column>
      <el-table-column prop="cooperateYears" label="已合作年份" />
    </el-table>

    <div v-if="areaData?.length >= 4">
      <div class="flexD p-10px cursor-pointer" @click="showMore = true">
        <el-icon class="mr-10px"><ArrowDownBold /></el-icon>
        <div>点击展开明细</div>
      </div>
    </div>

    <el-dialog title="合作学校明细" v-model="showMore" close-on-click-modal>
      <el-table :data="props.areaData" max-height="300">
        <el-table-column prop="schoolName" label="学校名称" />
        <el-table-column label="所在省市区">
          <template #default="{ row }">
            {{ row.province + row.city + row.area }}
          </template>
        </el-table-column>
        <el-table-column prop="cooperateYears" label="已合作年份" />
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="showMore = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ArrowDownBold, ArrowUpBold } from "@element-plus/icons-vue";

const props = defineProps<{
  isNewQuestions: any;
  regionVal: any;
  areaData: any;
  activityData: any;
}>();

const city = computed(() => {
  if (props.regionVal?.province?.name === props.regionVal?.city?.name) {
    return props.regionVal?.city?.name;
  }

  return props.regionVal?.province?.name + props.regionVal?.city?.name;
});

const showMore = ref(false);

const activeName = ref("1");
</script>

<style scoped lang="scss"></style>
