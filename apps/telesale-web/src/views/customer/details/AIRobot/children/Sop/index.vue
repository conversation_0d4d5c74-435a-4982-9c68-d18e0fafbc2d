<!--
 * @Date         : 2024-08-28 12:11:21
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-collapse v-model="activeNames">
    <el-collapse-item name="SOP核验">
      <template #title>
        <span
          v-if="
            (robotState.isFinish || robotState.AIRobotMode === 'info') && score
          "
        >
          SOP总分：{{ score }}
        </span>
        <span v-else>SOP核验</span>
        <SopRetry />
      </template>

      <div>
        <el-steps
          space="50px"
          direction="vertical"
          align-center
          v-loading="robotState.sopRetryLoading"
          element-loading-text="SOP核验重新生成中"
        >
          <el-step
            v-for="(sop, index) in robotState.AIRobotMode === 'info' &&
            robotState?.historySopList &&
            robotState?.historySopList?.length !== 0 &&
            !robotState.isCalled
              ? robotState.historySopList
              : config?.sopList"
            :key="index"
            :title="sop.name"
            :status="checkSop(sop)?.reason ? 'success' : ''"
          >
            <template #title>
              <div class="flex items-center justify-between">
                <div class="flexD">
                  <el-tooltip effect="dark" placement="bottom">
                    <template #default>
                      <div class="flexD">
                        <span class="mr-5px">{{ sop.name }}</span>
                      </div>
                    </template>

                    <template #content>
                      <div
                        v-for="(stand, idx) in sop.desc.map(
                          (item, index) => `${index + 1}. ${item}`
                        )"
                        :key="idx"
                      >
                        {{ stand }}
                      </div>
                    </template>
                  </el-tooltip>

                  <el-tooltip
                    v-if="sop.questions.length !== 0"
                    effect="dark"
                    content="点击查看推荐话术"
                    placement="top"
                  >
                    <el-icon
                      @click="
                        chooseSop = sop;
                        QuestionVisible = true;
                      "
                      size="24px"
                      :color="checkSop(sop)?.reason ? '#67C23A' : '#999'"
                    >
                      <ChatDotSquare />
                    </el-icon>
                  </el-tooltip>
                </div>

                <el-tooltip
                  effect="dark"
                  v-if="checkSop(sop)?.reason"
                  :content="checkSop(sop)?.reason ?? ''"
                  placement="bottom"
                >
                  <el-icon size="24px" color="#eebe77"><Opportunity /></el-icon>
                </el-tooltip>
              </div>
            </template>
          </el-step>
        </el-steps>
      </div>
    </el-collapse-item>
  </el-collapse>

  <QuestionsDrawer v-model:visible="QuestionVisible" :info="chooseSop" />
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";
import useGetCurrentState from "../../hooks/useGetCurrentState";
import SopRetry from "../../components/SopRetry.vue";
import { useAIRobotState } from "../../store";
import { Opportunity, ChatDotSquare } from "@element-plus/icons-vue";
import QuestionsDrawer from "./QuestionsDrawer.vue";

const { robotState } = useGetCurrentState();
const { config } = useAIRobotState();
const activeNames = ref(["AI通话总结", "SOP核验"]);

function checkSop(info) {
  return robotState.sop?.sops?.find(s => s.step === info.name);
}

const score = computed(() => {
  const socreNum = robotState?.sop?.score;
  console.log("thisss", robotState?.sop?.score);
  if (socreNum === 0) return false;

  let prompt = "";
  if (socreNum <= 50) {
    prompt = "套路才能得人心～";
  } else if (socreNum <= 70) {
    prompt = "哎呦，不错哦～";
  } else if (socreNum <= 100) {
    prompt = "太棒啦👍";
  }

  return `${socreNum}分，${prompt}`;
});

const chooseSop = ref();
const QuestionVisible = ref(false);
</script>

<style scoped lang="scss">
.description-box {
  :deep(tr:first-child) {
    .el-descriptions__content {
      font-weight: 700;
      color: var(--el-text-color-regular) !important;
      background: var(
        --el-descriptions-item-bordered-label-background
      ) !important;
    }
  }
}
</style>
