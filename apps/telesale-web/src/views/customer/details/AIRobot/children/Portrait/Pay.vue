<!--
 * @Date         : 2024-09-25 10:20:39
 * @Description  : 用户画像-付费意向
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-card class="card-box">
    <el-collapse v-model="activeName">
      <el-collapse-item name="1">
        <template #title>
          <el-text class="font-600">付费意向</el-text>
        </template>

        <el-divider class="mt-0!" />

        <el-descriptions :column="2">
          <el-descriptions-item label="注册时间">
            <span class="point">{{ regStr }}（{{ regTime }}）</span>
          </el-descriptions-item>
          <el-descriptions-item label="历史是否付费">
            <span class="point">{{ usertype }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="付费类型">
            <span class="point">{{ payType }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="最近购买时间">
            <span class="point">
              {{
                customMsg.lastPaidTime
                  ? dayjs(customMsg.lastPaidTime * 1000).format(
                      "YYYY-MM-DD hh:mm:ss"
                    )
                  : "历史无购买订单"
              }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="累计购买金额">
            <span class="point">
              {{
                customMsg.historyAmount
                  ? customMsg.historyAmount + "元"
                  : "历史无购买订单"
              }}
            </span>
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="!hasBigVip || authWithin6Month.length !== 0">
          <div class="mb-10px">续费机会</div>
          <div v-if="!hasBigVip">1、暂未开通大会员用户</div>
          <div v-if="authWithin6Month.length !== 0">
            <span>
              {{ !hasBigVip ? "2" : "1" }}、用户权益最近到期时间：
              <span class="point">{{ authRecent }}</span>
              ，到期
              <span class="point">{{ authWithin6Month.length }}</span>
              门，
            </span>

            <el-button type="primary" link @click="ExpireDialogVisible = true">
              查看明细
            </el-button>
            <!-- <div>
              分别是{{ authWithin6Month.map(item => item.name).join("、") }}
            </div> -->
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </el-card>

  <ExpireDialog
    v-model:visible="ExpireDialogVisible"
    :courseList="authWithin6Month"
  />
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import ExpireDialog from "./ExpireDialog.vue";
import getAuthList from "./getAuthList";

const props = defineProps<{
  customMsg: any;
  bigVipData: any;
}>();

const ExpireDialogVisible = ref(false);

const regTime = computed(() => {
  return dayjs(props.customMsg?.regTime).format("YYYY-MM-DD hh:mm:ss");
});

const regStr = computed(() => {
  const year = dayjs().diff(props.customMsg?.regTime, "year");
  if (year === 0) return "1年内";
  else return year + "年前";
});

const usertype = computed(() => {
  return props.customMsg?.usertype === 2 ? "是" : "否";
});

const hasBigVip = computed(() => {
  return !props.bigVipData?.bigVipExpired && props.bigVipData?.hadBigVip;
});
const hadBigVipRef = computed(() => {
  return props.bigVipData?.hadBigVip;
});
const payType = computed(() => {
  if (props.customMsg?.usertype === 1) return "无付费";
  if (hadBigVipRef.value) return "大会员付费";
  else return "非大会员付费";
});

const authList = ref([]);
const authWithin6Month = computed(() => {
  return authList.value.filter(item => {
    return dayjs(item.expireTime).diff(dayjs(), "month") < 6;
  });
});

const authRecent = computed(() => {
  const expireTime = authWithin6Month.value?.sort((a, b) => {
    return dayjs(a.expireTime).unix() - dayjs(b.expireTime).unix();
  })[0]?.expireTime;
  return dayjs(expireTime).format("YYYY-MM-DD");
});
watch(
  () => props.customMsg,
  async newV => {
    if (newV) {
      authList.value = await getAuthList(newV.userid);
    }
  },
  {
    immediate: true
  }
);

const activeName = ref("1");
</script>

<style scoped lang="scss">
.point {
  color: #e6a23c;
  font-weight: bold;
}

.card-box {
  :deep(.el-collapse) {
    border: none;
  }
  :deep(.el-collapse-item__wrap) {
    border: none;
  }
  :deep(.el-card__body) {
    padding-top: 10px;
  }
  :deep(.el-collapse-item__arrow) {
    font-size: 18px;
  }
}
</style>
