<!--
 * @Date         : 2024-11-13 12:25:40
 * @Description  : 中考相关
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <div class="flex items-center justify-between mb-10px">
      <el-text size="large">{{ city }}中考分数线</el-text>

      <div>
        <el-input placeholder="请输入学校名称" v-model="searchVal" />
      </div>
    </div>

    <el-table :data="filterTableData">
      <el-table-column label="学校名称" prop="schoolName" />
      <el-table-column label="2024年录取分数线" prop="score" />
      <el-table-column label="数据来源">
        <template #default="{ row }">
          <div
            v-for="(source, index) in row.dataSource.split('；')"
            :key="index"
          >
            <span v-if="!isUrl(source)" v-html="source" />
            <a :href="source" target="_blank" v-else>查看明细</a>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="flexD cursor-pointer">
      <el-text @click="showDetail = true">
        <el-icon><ArrowDown /></el-icon>
        点击展开明细
      </el-text>
    </div>

    <el-dialog v-model="showDetail" title="中考分数线明细" close-on-click-modal>
      <el-table :data="tableData">
        <el-table-column label="学校名称" prop="schoolName" />
        <el-table-column label="2024年录取分数线" prop="score" />
        <el-table-column label="数据来源">
          <template #default="{ row }">
            <div
              v-for="(source, index) in row.dataSource.split('；')"
              :key="index"
            >
              <span v-if="!isUrl(source)" v-html="source" />
              <a :href="source" target="_blank" v-else>查看明细</a>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- <MiddleExamDetailDialog v-model:visible="showDetail" /> -->
  </div>
</template>

<script lang="ts" setup>
import { ArrowDown } from "@element-plus/icons-vue";
import { getAdmissionScore } from "/@/api/AISupport/ScoreLine";
import { defineModel } from "vue";
// import MiddleExamDetailDialog from "./MiddleExamDetailDialog.vue";

const props = defineProps<{
  regionVal: any;
}>();

const dataList = defineModel<any[]>("dataList");
const showDetail = ref(false);

const searchVal = ref("");
const tableData = ref([]);

watch(
  () => props.regionVal,
  async (newV, oldV) => {
    if (!oldV && newV) {
      const res: any = await getAdmissionScore({
        pages: 1,
        pageSize: 99999999,
        sortBy: "score",
        regionCode: newV.city.code
      });
      tableData.value = res.data.list;
      dataList.value = tableData.value;
    }
  },
  {
    immediate: true
  }
);

async function search() {
  const res: any = await getAdmissionScore({
    pages: 1,
    pageSize: 99999999,
    sortBy: "score",
    schoolName: searchVal.value
  });

  tableData.value = res.data.list;
}

const filterTableData = computed(() => {
  return tableData.value
    .filter((item: any) => {
      return item.schoolName.includes(searchVal.value);
    })
    .slice(0, 4);
});

const city = computed(() => {
  if (props.regionVal?.province?.name === props.regionVal?.city?.name) {
    return props.regionVal?.city?.name;
  }

  return props.regionVal?.province?.name + props.regionVal?.city?.name;
});

function isUrl(str) {
  try {
    new URL(str);
    return true;
  } catch (err) {
    return false;
  }
}
</script>

<style scoped lang="scss"></style>
