<!--
 * @Date         : 2024-11-19 18:32:55
 * @Description  : 中考分数线明细
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    close-on-click-modal
    title="中考分数线明细"
    @open="openDialog"
  >
    <NexusTable
      un-mounted
      :get-list="getAdmissionScore"
      :resFormat="data => data?.data"
      ref="TableRef"
    >
      <el-table-column label="学校名称" prop="schoolName" />
      <el-table-column label="2024年录取分数线" prop="score" />
      <el-table-column label="数据来源">
        <template #default="row">
          <div v-html="row.dataSource" />
        </template>
      </el-table-column>
    </NexusTable>
    <template #footer>
      <el-button type="primary" @click="visible = false">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineModel } from "vue";
import { getAdmissionScore } from "/@/api/AISupport/ScoreLine";
import NexusTable from "/@/views/AISupport/components/NexusTable/index.vue";

const visible = defineModel<boolean>("visible");

const TableRef = ref();

function openDialog() {
  TableRef.value?.search({
    sortBy: "score"
  });
}
</script>

<style scoped lang="scss"></style>
