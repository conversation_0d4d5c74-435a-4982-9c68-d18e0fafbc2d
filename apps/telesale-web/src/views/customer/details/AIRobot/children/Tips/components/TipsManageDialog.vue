<!--
 * @Date         : 2024-11-07 17:38:02
 * @Description  : 话术管理
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog v-model="visible" title="话术管理" @open="openDialog">
    <el-form-item label="话术分组配置" required>
      <el-button
        v-if="groupData.length === 0"
        type="primary"
        size="small"
        @click="groupManageVisible = true"
      >
        新增分组
      </el-button>
      <div v-else class="relative w-100% team-manage-box">
        <el-button
          size="small"
          type="primary"
          class="absolute right-5px top-12px z-99"
          link
          @click="groupManageVisible = true"
        >
          分组管理
        </el-button>

        <el-tabs v-model="nowTab" type="border-card" class="w-100%">
          <el-tab-pane
            v-for="(item, index) in groupData"
            :key="index"
            :name="index"
            :label="item.groupName"
          >
            <el-table
              ref="solutionSettingTableRef"
              :data="item.questionList"
              class="solution-setting-table"
              style="width: 100%"
            >
              <el-table-column label="排序" width="80px">
                <el-icon class="move"><Rank /></el-icon>
              </el-table-column>
              <el-table-column prop="baseQuestion" label="问题名称" />
              <el-table-column label="操作" width="80px">
                <template #default="{ $index }">
                  <el-icon
                    class="c-red! cursor-pointer"
                    @click="groupData[nowTab]?.questionList.splice($index, 1)"
                  >
                    <Delete />
                  </el-icon>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>

        <el-button
          v-if="groupData.length !== 0"
          size="small"
          class="absolute bottom-10px left-15px"
          type="primary"
          @click="addCorrelationProblemDialogVisible = true"
        >
          <el-icon><Plus /></el-icon>
          <span class="ml-10px">选择问题</span>
        </el-button>
      </div>
    </el-form-item>
    <template #footer>
      <el-button type="danger" @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </template>
  </el-dialog>

  <AddCorrelationProblemDialog
    v-model:visible="addCorrelationProblemDialogVisible"
    :data-list="groupData[nowTab]?.questionList"
    @confirm-cb="confirmQuestion"
  />
  <GroupManageDialog
    v-model:visible="groupManageVisible"
    v-model="groupData"
    :max-length="10"
    :confirm-cb="confirmGroup"
  />
</template>

<script lang="ts" setup>
import { defineModel } from "vue";
import AddCorrelationProblemDialog from "/@/views/AISupport/SopConfig/components/AddCorrelationProblemDialog.vue";
import GroupManageDialog from "./GroupManageDialog.vue";
import Sortable from "sortablejs";
import { Plus, Delete, Rank } from "@element-plus/icons-vue";
import { useAIRobotState } from "../../../store";
import { getTipSet, updateTipSet } from "/@/api/AISupport/Tips";
import { getMget } from "/@/api/AISupport/Library";

const visible = defineModel<boolean>("visible");
const groupManageVisible = ref(false);

const emit = defineEmits(["confirm"]);

// 关联问题
const addCorrelationProblemDialogVisible = ref(false);
const nowTab = ref(0);
function confirmQuestion(val) {
  groupData.value[nowTab.value].questionList = val;
  console.log("thissss", groupData);
  sortableInit();
}

const groupData: any = ref([]);
function confirmGroup() {
  nowTab.value = 0;
}

// 排序初始化
function sortableInit() {
  nextTick(() => {
    document
      .querySelectorAll(".solution-setting-table .el-table__body-wrapper tbody")
      .forEach(item => {
        const sortable = new Sortable(item, {
          handle: ".move",
          animation: 150,
          onEnd({ oldIndex, newIndex }) {
            const arr = groupData.value[nowTab.value].questionList;
            const currRow = arr.splice(oldIndex, 1)[0];
            arr.splice(newIndex, 0, currRow);
            groupData.value[nowTab.value].questionList = [];
            nextTick(() => {
              groupData.value[nowTab.value].questionList = arr;
            });
          }
        });
      });
  });
}

async function openDialog() {
  const res = await getTipSet({
    workerId: useAIRobotState().workerID.value
  });

  const tipSets = res.data.tipSets;

  if (tipSets.length !== 0) {
    groupData.value = tipSets.map(item => {
      return {
        id: item.id,
        groupName: item.name,
        questionList: item.knowledgeSets?.map((it, index) => {
          return {
            id: it.knowledgeID,
            knowledgeID: it.knowledgeID
          };
        })
      };
    });
    nowTab.value = 0;

    groupData.value.forEach(async (item, index) => {
      const ids = item.questionList?.map(it => it.knowledgeID);

      if (ids.length !== 0) {
        const res: any = await getMget({
          ids: ids
        });

        item.questionList.forEach(it => {
          it.baseQuestion = res.data.list.find(
            itm => itm.id === it.knowledgeID
          )?.baseQuestion;
        });
      }
    });

    console.log("groupData.value.", groupData.value);
  } else {
    groupData.value = [];
  }

  sortableInit();
}

async function confirm() {
  console.log("groupData.value", groupData.value);
  // if (groupData.value.length === 0) {
  //   ElMessage.error("常见问题不能为空");
  //   throw Error("常见问题不能为空");
  // }
  const solutionSet = groupData.value.map(item => {
    // if (item.questionList.length === 0) {
    //   ElMessage.error("分组问题不能为空");
    //   throw Error("分组问题不能为空");
    // }

    return {
      id: item.id,
      name: item.groupName,
      workerId: useAIRobotState().workerID.value,
      knowledgeSets: item.questionList.map(it => {
        return {
          knowledgeID: it.id
        };
      })
    };
  });

  const param = {
    workerId: useAIRobotState().workerID.value,
    tipSets: solutionSet
  };
  console.log("solutionSet", param);

  await updateTipSet(param);
  emit("confirm");
  ElMessage.success("修改成功");
  visible.value = false;
}
</script>

<style scoped lang="scss">
.team-manage-box {
  :deep(.el-tabs__nav-wrap) {
    margin-right: 60px;
  }
  :deep(.el-tabs) {
    padding-bottom: 30px;
  }
  :deep(.el-table__empty-block) {
    height: 200px !important;
  }
}
</style>
