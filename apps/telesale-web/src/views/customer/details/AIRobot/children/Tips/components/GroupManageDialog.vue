<!--
 * @Date         : 2023-07-04 11:47:58
 * @Description  : 新增方案-常见问题设置-分组管理
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog v-model="groupManageVisible" title="分组管理" @open="openDialog">
    <div>
      <el-table
        :data="groupTable"
        class="group-manage-table"
        style="width: 100%"
      >
        <el-table-column label="排序" width="80px">
          <el-icon class="move"><Rank /></el-icon>
        </el-table-column>
        <el-table-column prop="name" label="问题名称">
          <template #default="{ row }">
            <div v-if="row.groupName">{{ row.groupName }}</div>
            <el-input
              v-else
              ref="groupInputRef"
              v-model="groupInputVal"
              placeholder="请输入组名"
              show-word-limit
              :maxlength="maxLength"
              @blur="
                () =>
                  (row.groupName =
                    groupInputVal.length > maxLength
                      ? groupInputVal.slice(0, maxLength)
                      : groupInputVal)
              "
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80px">
          <template #default="{ row, $index }">
            <el-icon class="cursor-pointer mr-10px" @click="editGroupName(row)">
              <Edit />
            </el-icon>
            <el-icon
              class="c-red cursor-pointer"
              @click="groupTable.splice($index, 1)"
            >
              <Delete />
            </el-icon>
          </template>
        </el-table-column>
      </el-table>

      <el-button
        type="primary"
        class="mt-10px"
        :disabled="haveEmpty"
        v-if="groupTable.length < 10"
        size="small"
        @click="addGroup"
      >
        <el-icon><Plus /></el-icon>
        <span>添加分组</span>
      </el-button>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="groupManageVisible = false">取消</el-button>
        <el-button type="primary" :disabled="haveEmpty" @click="confirm">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import Sortable from "sortablejs";
import { cloneDeep } from "lodash";
import { defineModel } from "vue";
import { Rank, Edit, Plus, Delete } from "@element-plus/icons-vue";

const props = defineProps<{
  confirmCb: Function;
  maxLength: number;
}>();

const groupData = defineModel<{ groupName: string; questionList: any }[]>();
const groupManageVisible = defineModel<boolean>("visible");

const groupInputVal = ref("");
const groupTable = ref<any>([]);

const haveEmpty = computed(() => {
  return Boolean(groupTable.value.find(item => item.groupName === ""));
});
function addGroup() {
  groupTable.value.push({
    groupName: "",
    questionList: []
  });
  groupInputVal.value = "";
}

const groupInputRef = ref();
function editGroupName(info) {
  if (groupInputRef.value) groupInputRef.value.blur();

  groupInputVal.value = info.groupName;
  info.groupName = "";

  nextTick(() => {
    groupInputRef.value.focus();
  });
}

let groupSortable = null;
// 排序初始化
function sortableInit() {
  nextTick(() => {
    if (groupSortable) return;
    groupSortable = new Sortable(
      document.querySelector(
        ".group-manage-table .el-table__body-wrapper tbody"
      ),
      {
        handle: ".move",
        animation: 150,
        onEnd({ oldIndex, newIndex }) {
          const arr = groupTable.value;
          const currRow = arr.splice(oldIndex, 1)[0];
          arr.splice(newIndex, 0, currRow);
          groupTable.value = [];
          nextTick(() => {
            groupTable.value = arr;
          });
        }
      }
    );
  });
}

function openDialog() {
  groupTable.value = cloneDeep(groupData.value);
  sortableInit();
}

function confirm() {
  groupData.value = groupTable.value;
  groupManageVisible.value = false;
  props.confirmCb(groupTable.value);
}
</script>

<style scoped lang="scss"></style>
