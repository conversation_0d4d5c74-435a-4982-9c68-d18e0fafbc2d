export default class TipsHandle {
  pool: any[];
  wordList: any[];
  max: number;
  cb: any;
  constructor(cb) {
    this.pool = [];
    this.wordList = [];
    this.max = 5;
    this.cb = cb;
  }

  pushWord(obj) {
    const splitArr = this.splitByPunctuation(obj.content).map(item => {
      return { ...obj, content: item };
    });
    console.log("splitArr", splitArr);
    this.wordList.push(...splitArr);
    while (this.pool.length < this.max && this.wordList.length > 0) {
      this._setTask(this.wordList.shift());
    }
    const race = Promise.race(this.pool);
    this._run(race);
  }

  private splitByPunctuation(str: string) {
    // 使用正则表达式匹配。！？任意一个字符，并保留分隔符
    return str
      .split(/([。！？])/)
      .filter(Boolean)
      .map((s, i, arr) => {
        // 如果当前元素是标点符号，将它与前面的文字合并
        if (i % 2 === 1 && arr[i - 1]) {
          return arr[i - 1] + s;
        }
        // 过滤掉标点符号后不单独保留它
        return i % 2 === 0 ? undefined : s;
      })
      .filter(Boolean);
  }

  private _run(race: any) {
    race.then(() => {
      const word = this.wordList.shift();
      this._setTask(word);
      this._run(Promise.race(this.pool));
    });
  }

  private _setTask(path: string | undefined) {
    if (!path) return;
    const promise = this.cb(path);
    this.pool.push(promise);

    promise.then(() => {
      this.pool.splice(this.pool.indexOf(promise), 1);
    });
  }
}

export function splitByPunctuation(str: string) {
  // 使用正则表达式匹配。！？任意一个字符，并保留分隔符
  const symbolReg = /([，。！？])/;
  return str.split(symbolReg).reduce((result, current, index, array) => {
    if (index % 2 === 0) {
      // 如果是文字部分，合并当前文字和下一个标点符号
      const nextPunctuation = array[index + 1] || "";

      // 判断是否重复
      if (
        !result.find(
          item =>
            item === current ||
            (item.includes(current) &&
              item.length === current.length + 1 &&
              symbolReg.test(item.substr(-1))) // 除标点符号部分 是否重复
        )
      ) {
        result.push(current + nextPunctuation);
      }
    }
    return result;
  }, []);
}
