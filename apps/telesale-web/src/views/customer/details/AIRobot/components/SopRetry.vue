<!--
 * @Date         : 2024-08-01 11:56:48
 * @Description  : sop重试组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div
    v-if="
      robotState.showSopRetryBtn &&
      robotState.messageList.length !== 0 &&
      !robotState.sopHasRetry
    "
    class="flexD"
  >
    <el-tooltip effect="dark" content="调用超时，请重试" placement="bottom">
      <el-button
        type="primary"
        size="small"
        class="mx-10px"
        @click.stop="retry"
        :loading="robotState.sopRetryLoading"
      >
        重新生成
      </el-button>
    </el-tooltip>
  </div>
</template>

<script lang="ts" setup>
import useGetCurrentState from "../hooks/useGetCurrentState";
import { useAIRobotState } from "../store";
import { retrySop } from "/@/api/AISupport/Quality";

const { resetSopTimeout } = useAIRobotState();
const { robotState, infoUuid } = useGetCurrentState();

async function retry() {
  robotState.sopRetryLoading = true;
  resetSopTimeout(infoUuid);

  try {
    const res: any = await retrySop({
      conversation: {
        id: robotState.conversation.id
      }
    });

    robotState.sop = res.data.sop;
  } catch (error) {
    console.log(error);
  }

  robotState.sopHasRetry = true;
  robotState.sopRetryLoading = false;
}
</script>

<style scoped lang="scss"></style>
