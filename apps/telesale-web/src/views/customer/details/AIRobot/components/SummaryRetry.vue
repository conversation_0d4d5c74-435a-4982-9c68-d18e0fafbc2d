<!--
 * @Date         : 2024-08-01 11:57:04
 * @Description  : 通话总结重试组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->
<template>
  <div
    v-if="
      robotState.showSummaryRetryBtn &&
      robotState.messageList.length !== 0 &&
      !robotState.summaryHasRetry
    "
    class="flexD"
  >
    <el-button
      type="primary"
      size="small"
      class="mx-10px"
      @click.stop="retry"
      :loading="robotState.summaryRetryLoading"
      >重新生成</el-button
    >
    <el-text type="danger">调用超时，请重试</el-text>
  </div>
</template>

<script lang="ts" setup>
import useGetCurrentState from "../hooks/useGetCurrentState";
import { useAIRobotState } from "../store";
import { retrySummary } from "/@/api/AISupport/Quality";

const { resetSummaryTimeout } = useAIRobotState();
const { robotState, infoUuid } = useGetCurrentState();

async function retry() {
  robotState.summaryRetryLoading = true;
  resetSummaryTimeout(infoUuid);

  if (!robotState?.conversation?.id) return;

  try {
    const res: any = await retrySummary({
      conversation: {
        id: robotState.conversation.id
      }
    });

    robotState.summary = res.data.summary.content.replaceAll("<br>", "\n");
    robotState.summaryFlag = true;
  } catch (error) {
    console.log(error);
  }

  robotState.summaryHasRetry = true;
  robotState.summaryRetryLoading = false;
}
</script>

<style scoped lang="scss"></style>
