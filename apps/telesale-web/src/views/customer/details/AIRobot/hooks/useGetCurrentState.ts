/*
 * @Date         : 2024-08-01 12:11:55
 * @Description  : 获取当前线索的公共信息
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { inject } from "vue";
import { useAIRobotState } from "../store";

export default function () {
  const { AIRobotStore } = useAIRobotState();

  const infoUuid = inject("infoUuid", "");
  const robotState = AIRobotStore.value[infoUuid];

  return { robotState, infoUuid };
}
