/*
 * @Date         : 2024-09-26 16:06:00
 * @Description  : 用户画像
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { getCustomerState } from "/@/api/AISupport/Portraits";
import { region } from "/@/api/customerDetails";
import { getNewQueApi } from "/@/api/customer/details";

export default function (customMsg) {
  const bigVipData = ref();
  const isNewQuestions = ref(false);
  const regionVal = ref();
  const areaData = ref([]);
  const dailyActivityData = ref();
  const weeklyActivityData = ref();

  watch(
    () => customMsg,
    async newV => {
      if (newV) {
        await getRegion(newV.userid);
        console.log("regionVal", regionVal.value);
        const res = await getCustomerState({
          userId: newV.userid,
          phone: newV.phone,
          city: regionVal.value.city.name.replace("市", ""),
          regionCode: regionVal.value.city.code
        });

        bigVipData.value = res.data.customer.bigVip;
        areaData.value = res.data.customer.schools;
        dailyActivityData.value = res.data.customer.dailyActivity;
        weeklyActivityData.value = res.data.customer.weeklyActivity;
      }
    },
    {
      immediate: true
    }
  );

  //查询客户所在地
  async function getRegion(userid: string) {
    if (!userid) return;
    const { data }: any = await region({ userid: userid });
    regionVal.value = data;
    if (data.province?.code || data.city?.code || data.district?.code) {
      getIsNewQuestions(
        data.district?.code || data.city?.code || data.province?.code
      );
    }
  }

  const getIsNewQuestions = (code: string) => {
    getNewQueApi({ regionCode: code })
      .then(res => {
        isNewQuestions.value = res.data.isNewExamArea;
      })
      .catch(() => {
        isNewQuestions.value = false;
      });
  };

  return {
    bigVipData,
    isNewQuestions,
    regionVal,
    areaData,
    dailyActivityData,
    weeklyActivityData
  };
}
