/*
 * @Date         : 2024-08-28 12:13:58
 * @Description  : 获取sop/关键信息配置
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { onMounted, ref } from "vue";
import { getConversationConfig } from "/@/api/AISupport/Quality";
import { useAIRobotState } from "../store";

export default function () {
  const { config } = useAIRobotState();

  async function getConfig() {
    const res: any = await getConversationConfig();
    config.value = res.data.config;
  }

  onMounted(() => {
    getConfig();
  });
}
