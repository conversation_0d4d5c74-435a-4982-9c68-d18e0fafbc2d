/*
 * @Date         : 2024-07-31 11:00:56
 * @Description  : 接收ws消息并处理
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { dayjs, ElMessage } from "element-plus";
import { sendSop, sendTips, finishCoversation } from "/@/api/AISupport/Quality";
import useGetInfo from "./useGetInfo";
import { useAIRobotState } from "../store";

export default function (robotState, { pushHandle, finishPush }) {
  const { openSopAuth, workerID, finishedActionIdList } = useAIRobotState();
  const { getCallInfo } = useGetInfo(robotState);

  async function sendSopHandle(shouldTimeout: boolean = true) {
    const msgTarget = getMsgTarget();
    if (msgTarget.length === 0) {
      robotState.waitSop = false;
      return;
    }

    try {
      await sendSop({
        workerId: workerID.value,
        messages: msgTarget,
        actionId: robotState.actionID
      });
      if (shouldTimeout) createSopTimeout();
    } catch (error) {
      ElMessage.error("SOP接口调用失败");
    }
  }

  function createSopTimeout() {
    // sop超时30s处理
    robotState.sopTimeout = setTimeout(async () => {
      console.log("sop timeout");
      if (!robotState.waitSop) return;

      if (robotState.isFinish) {
        const callInfo = await getCallInfo();
        if (callInfo.sopStatus === "gen" || callInfo.sopStatus === "fail")
          robotState.showSopRetryBtn = true;
      } else {
        sendSopHandle();
      }
    }, 30000);
  }

  function getMsgTarget() {
    return robotState.messageList
      .filter(item => item.content)
      .map(item => {
        return {
          content: item.content,
          start: String(item.start),
          role: item.role,
          is_finished: item.is_finished
        };
      });
  }

  async function finishHandle() {
    robotState.isFinish = true;
    // 避免finish未结束时，立即开启下一通对话，导致上一通的finish变量更新为下一通
    robotState = { ...robotState };
    // const actionID = robotState.actionID;

    // 已经结束的会话直接返回
    if (finishedActionIdList.value.includes(robotState.actionID)) {
      console.log("该会话已经finish");
      return;
    }

    finishedActionIdList.value.push(robotState.actionID);
    console.log("会话结束");
    console.log("通话时长:", robotState.count);
    pushHandle();
    finishPush();

    console.log("robotState.callStatus", robotState.isCalled);

    // 未接通的会话直接finish 或者 通话时长小于10s
    if (!robotState.isCalled || robotState.count < 10) {
      console.log("finish");
      if (!robotState.isCalled) console.log("未接通的会话");
      if (robotState.count < 10) console.log("通话时长小于10s");

      await finishCoversation({
        workerid: workerID.value,
        actionId: robotState.actionID,
        skipSummary: robotState.count < 60
      });
      robotState.summary = "无效会话 不触发对话总结~";
      return;
    }

    // try {
    //   console.log(
    //     "waitSop start",
    //     robotState.waitSop,
    //     dayjs().format("HH:mm:ss")
    //   );
    //   // 等待sop返回
    //   await checkSopWait();
    //   console.log(
    //     "waitSop end",
    //     robotState.waitSop,
    //     dayjs().format("HH:mm:ss")
    //   );

    //   console.log("last sop start", dayjs().format("HH:mm:ss"));
    //   // 结束前全量触发一次
    //   await sendSopHandle(false);
    //   console.log("last sop end", dayjs().format("HH:mm:ss"));
    // } catch (error) {
    //   console.log(error);
    // }

    console.log("finish");
    await finishCoversation({
      workerid: workerID.value,
      actionId: robotState.actionID,
      skipSummary: robotState.count < 60
    });

    if (robotState.count < 60) return; // 通话时间过短跳过总结
    // if (!openSopAuth.value) return;
    robotState.summary = "AI总结生成中";
    createSummaryTimeout();
  }

  function createSummaryTimeout() {
    // 总结超时30s处理
    robotState.summaryTimeout = setTimeout(async () => {
      console.log("summary timeout");

      if (!robotState.summaryFlag) {
        const callInfo = await getCallInfo();
        if (callInfo.status === "summaring") {
          robotState.showSummaryRetryBtn = true;
          robotState.summary = "调用超时，请重新生成～";
        }
      }
    }, 30000);
  }

  return { sendSopHandle, finishHandle };

  function checkSopWait() {
    // 循环判断workerID是否有值
    return new Promise((resolve, reject) => {
      const interval = setInterval(() => {
        if (!robotState.waitSop) {
          resolve(true);
          clearInterval(interval);
        }
      }, 500);

      setTimeout(() => {
        reject(new Error("timeout"));
      }, 30000);
    });
  }
}
