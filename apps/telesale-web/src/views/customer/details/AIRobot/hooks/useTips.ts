import { dayjs, ElMessage } from "element-plus";
import { sendTips } from "/@/api/AISupport/Quality";
import { useAIRobotState } from "../store";
import { splitByPunctuation } from "../script/tips";

export default function (robotState) {
  const { workerID } = useAIRobotState();

  function sendTipsHandle(data) {
    // 智能话术
    const msgTarget = {
      content: data.content,
      start: String(data.start),
      role: data.role
    };

    const target = splitByPunctuation(msgTarget.content).map(item => {
      return { ...msgTarget, content: item };
    });

    const wordIndex: number = robotState.messageList
      .filter(item => item.role === data.role)
      .findIndex(
        item => item.content === data.content && item.start === data.start
      );

    for (const item of target) {
      if (item.content === "") return;

      sendTips({
        workerId: workerID.value,
        actionId: robotState.actionID,
        messages: [item]
      })
        .then((res: any) => {
          if (res.data.tips.length === 0) return;
          const tip = res.data.tips[0];

          const wordIndexDiff2 = [
            wordIndex,
            wordIndex - 1,
            wordIndex - 2
          ].filter(index => index >= 0);

          const hasRepeated = robotState.tips.find(item => {
            return (
              item.question === tip.question &&
              wordIndexDiff2.includes(item.fromWordIndex) &&
              item.role === data.role
            );
          });
          console.log(
            "hasRepeated",
            hasRepeated,
            {
              ...tip,
              fromWordIndex: wordIndex,
              role: data.role
            },
            data,
            wordIndexDiff2,
            wordIndex
          );

          if (!hasRepeated) {
            robotState.tips = [
              {
                ...tip,
                fromWordIndex: wordIndex,
                role: data.role,
                origin: data
              },
              ...robotState.tips
            ];
          }
        })
        .catch(err => {
          ElMessage.error("智能话术接口调用失败");
        });
    }
  }

  return { sendTipsHandle };
}
