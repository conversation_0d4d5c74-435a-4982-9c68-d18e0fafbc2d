import { ref, ComputedRef, computed, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import { RouteConfigs } from "/@/layout/types";
import { useMultiTagsStoreHook } from "/@/store/modules/multiTags";

export default function (customMsg, tasRef) {
  const route = useRoute();
  const router = useRouter();

  const { closePage: closeDetails } = useMultiTagsStoreHook();

  const multiTags: ComputedRef<Array<RouteConfigs>> = computed(() => {
    return useMultiTagsStoreHook()?.multiTags;
  });

  function closePage() {
    const valueIndex: number = multiTags.value.findIndex((item: any) => {
      if (item.path === "/customer/details/index") {
        return item.query.infoUuid === customMsg.infoUuid;
      }
    });
    useMultiTagsStoreHook().handleTags("splice", "", {
      startIndex: valueIndex,
      length: 1
    });
    nextTick(() => {
      router.push({ name: customMsg.from });
    });
  }

  const messageModal = ref(false);
  const isModelGive = ref(false); // 体验赠送弹窗
  const isModelRelease = ref(false); // 释放弹窗
  const isModelEvaluate = ref(false);
  const isModelSend = ref(false);
  const isModelTransfer = ref(false); // 转线索弹窗
  const isModelPush = ref(false);
  const isModalProfile = ref<boolean>(false);

  function comTip() {
    if (!customMsg?.userid) {
      ElMessage.warning("该用户暂无洋葱账号");
      return true;
    }
    return false;
  }

  function openPush() {
    if (comTip()) return;
    isModelPush.value = true;
  }

  function openEvaluate() {
    if (comTip()) return;
    isModelEvaluate.value = true;
  }
  function openSendMsg() {
    if (comTip()) return;
    isModelSend.value = true;
  }

  const giveProfile = () => {
    if (comTip()) return;
    isModalProfile.value = true;
  };

  const back = async () => {
    const res = await closeDetails(route);
    if (res) return;
    closePage();
  };

  const resetPushRecord = () => {
    tasRef.value?.resetPushRecord();
  };

  return {
    closePage,
    messageModal,
    isModelGive,
    isModelRelease,
    isModelEvaluate,
    isModelSend,
    isModelTransfer,
    isModelPush,
    isModalProfile,
    tasRef,
    resetPushRecord,
    back,
    openPush,
    openEvaluate,
    openSendMsg,
    giveProfile
  };
}
