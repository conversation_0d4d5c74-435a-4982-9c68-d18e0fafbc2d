import { ref, nextTick, onMounted } from "vue";
import { useUserStoreHook } from "/@/store/modules/user";
import { useRoute } from "vue-router";
import { detail } from "/@/api/customerDetails";
import findSubject from "/@/utils/asyn/findSubject";
import sendPointMath from "/@/utils/handle/sendPoint";
import {
  getDiscoveryPageAuthApi,
  getPhoneWhiteApi
} from "/@/api/customer/details";

export default function (callRefs) {
  const route = useRoute();

  const isOwn = ref(true);
  const applyClue = ref({ ...useUserStoreHook().pointParams.applyClue });
  const infoUuid = route.query?.infoUuid || "";
  const loading = ref(false);
  const customMsg = ref();
  const from: any = route.query?.from || "";
  const type = sessionStorage.getItem(infoUuid as string) || "";
  const hasRisk = ref(false);
  const getDiscoveryPageAuth = ref(false);

  function getDetail() {
    if (!infoUuid) {
      sendPointCommon();
      isOwn.value = false;
      return;
    }
    loading.value = true;
    detail({ infoUuid: infoUuid })
      .then(({ data }: { data: any }) => {
        sendPointCommon();
        try {
          const fromData = JSON.parse(from);
          data.from = fromData.from; //来源页面 - 后续返回会跳转到原页面
          data.pushId = Number(fromData.pushId); //推送线索id
          const index = Number(fromData.important); //价值等级
          data.openMethod = fromData.openMethod; //推送线索状态 push未处理 handle已处理
          data.important = [5, 4, 3][index - 1]; //价值等级对应文字说明
          data.pushMsg = fromData.pushMsg || ""; //推荐理由
        } catch (e) {
          data.from = from;
        }
        customMsg.value = data;
        if (type) {
          sessionStorage.setItem(infoUuid as string, "");
          nextTick(() => {
            callRefs.value.fastCall();
          });
        }
        getPhoneWhite();
        getCard();
        loading.value = false;
      })
      .catch(() => {
        sendPointCommon();
        isOwn.value = false;
        loading.value = false;
      });
  }

  function sendPointCommon() {
    if (applyClue.value?.status !== "1") return;
    applyClue.value.getUserMsgTime = new Date().getTime();
    sendPointMath(
      { category: "site", eventKey: "clickApplyLeads" },
      applyClue.value
    );
  }

  onMounted(async () => {
    console.log(
      "all in time:",
      new Date().getTime() - Number(route.query?.time)
    );
    //进入详情页面时间戳存储
    if (applyClue.value?.status === "1") {
      applyClue.value.enterDetailsPageTime = new Date().getTime();
      console.log(
        "进入客户详情的时间：",
        new Date(applyClue.value.enterDetailsPageTime).toLocaleString()
      );
      useUserStoreHook().setPointParams("applyClue", {});
    }
    if (!useUserStoreHook().setSubjectList.length) {
      useUserStoreHook().setSubjectList(await findSubject());
    }
    getDetail();
  });

  const getInfo = () => {
    loading.value = true;
    detail({ infoUuid: infoUuid })
      .then(({ data }: { data: any }) => {
        customMsg.value.role = data.role;
        loading.value = false;
      })
      .catch(() => {
        loading.value = false;
      });
  };

  const getCard = () => {
    getDiscoveryPageAuthApi({ userId: customMsg.value.userid }).then(res => {
      getDiscoveryPageAuth.value = res.data.hasAuth;
    });
  };

  const getPhoneWhite = () => {
    if (!customMsg.value?.phone) {
      hasRisk.value = true;
      return;
    }
    loading.value = true;
    getPhoneWhiteApi({ userId: customMsg.value.userid })
      .then(res => {
        hasRisk.value = res.data.hasRisk;
      })
      .finally(() => {
        loading.value = false;
      });
  };

  return {
    getInfo,
    getDetail,
    isOwn,
    applyClue,
    infoUuid,
    loading,
    customMsg,
    from,
    type,
    hasRisk,
    getDiscoveryPageAuth
  };
}
