<!--
 * @Date         : 2024-08-27 17:35:55
 * @Description  : 客户详情v2
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div>
    <div v-if="!isOwn" class="g-margin-20 d-no-own" v-loading="loading">
      此线索已失效
    </div>
    <div v-if="isOwn && !customMsg" class="g-margin-20 d-loading">
      正在加载中...
    </div>

    <div v-if="isOwn && !!customMsg" class="g-margin-20" v-loading="loading">
      <ButtonBar
        ref="callRefs"
        :customMsg="customMsg"
        :getDetail="getDetail"
        :tasRef="tasRef"
        :hasRisk="hasRisk"
        :pushDiscover="getDiscoveryPageAuth"
      />
      <el-alert
        v-if="hasRisk"
        title="❗️当前用户未绑定手机号或者手机号存在风险，不支持赠送体验课！风险校验不一定100%准确，若跟用户核实无风险，可联系团长解除风险标记"
        type="error"
        :closable="false"
      />
      <div class="bg-white">
        <div class="px-20px pb-20px overflow-auto">
          <div class="bg-white">
            <el-tabs v-model="activeTab">
              <el-tab-pane
                v-if="useUserStoreHook().checkAuth('telesale_show_ai_call')"
                label="AI辅助"
                name="AI辅助"
              />
              <el-tab-pane label="客户信息" name="客户信息" />
            </el-tabs>
          </div>
          <CustomerInfo
            class="mt-10px"
            v-show="activeTab === '客户信息'"
            :getInfo="getInfo"
            :customMsg="customMsg"
            :hasRisk="hasRisk"
          />

          <AIRobot
            :customMsg="customMsg"
            :getInfo="getInfo"
            v-show="
              activeTab === 'AI辅助' &&
              useUserStoreHook().checkAuth('telesale_show_ai_call')
            "
            class="mt-10px"
            :infoUuid="customMsg.infoUuid"
            :hasRisk="hasRisk"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="customerDetails">
import { ref } from "vue";
import { useUserStoreHook } from "/@/store/modules/user";

import AIRobot from "/@/views/customer/details/AIRobot/index.vue";

import ButtonBar from "./components/ButtonBar.vue";
import CustomerInfo from "./components/CustomerInfo.vue";
import useInit from "./hooks/useInit";

const callRefs = ref();
const tasRef = ref();
const {
  getInfo,
  isOwn,
  loading,
  customMsg,
  getDetail,
  hasRisk,
  getDiscoveryPageAuth
} = useInit(callRefs);

const activeTab = ref(
  useUserStoreHook().checkAuth("telesale_show_ai_call") ? "AI辅助" : "客户信息"
);

provide("activeTab", activeTab);
</script>

<style lang="scss" scoped>
.d-loading {
  text-align: center;
  font-size: 30px;
  margin-top: 150px;
}
.d-no-own {
  text-align: center;
  font-size: 30px;
  margin-top: 150px;
  color: #f56c6c;
}
</style>
