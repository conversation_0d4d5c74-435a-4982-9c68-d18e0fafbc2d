<!--
 * @Date         : 2024-08-28 16:33:14
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { useUserStoreHook } from "/@/store/modules/user";
import AddDocumentaryRecord from "/@/views/customer/details/dialog/AddDocumentaryRecord.vue";
import PersonMsg from "/@/layout/components/person/dialog/PersonMsg.vue";
import {
  call,
  hangup,
  callState,
  setRecommendStatus
} from "/@/api/customerDetails";
import { useTimeout } from "/@/hooks/useTimeout";
import { secondsToHms } from "/@/utils/time/index";
import useCall from "../../AIRobot/hooks/useCall";
import { useAIRobotState } from "/@/views/customer/details/AIRobot/store";
import ChangeCall from "../../components/ChangeCall.vue";

interface Props {
  customMsg: any;
}

interface Emits {
  (e: "closePage"): void;
  (e: "resetPushRecord"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const { AIRobotStore } = useAIRobotState();
const route = useRoute();
const openTime = route.query.time;
const { count, start, stop } = useTimeout({
  destroyed: false
});

function closePage() {
  emit("closePage");
}

function resetPushRecord() {
  emit("resetPushRecord");
}

let authorizationMap = useUserStoreHook().authorizationMap;

const callStatusObj = {
  noCall: "一键拨打",
  calling: "呼叫中",
  called: "通话中",
  hangup: "已挂断"
};
const callColorObj = {
  noCall: "primary",
  calling: "warning",
  called: "success",
  hangup: "info"
};

const timer = ref();

let timeNum = computed(() => {
  return secondsToHms(count.value);
});

const customCall = authorizationMap.indexOf("telesale_admin_custom_call") > -1;
const callStatus = ref("noCall");
const hangupLoading = ref(false);

//销售机会识别
function pushClue() {
  let data = {
    id: props.customMsg?.pushId,
    status: 2,
    reason: ""
  };
  setRecommendStatus(data).then(() => {});
}
//挂断后三秒复原
function callReset() {
  stop();
  timer.value = setTimeout(() => {
    count.value = 0;
    useUserStoreHook().setCallDisabled(false);
    callStatus.value = "noCall";
    stop();
  }, 3000);
}
//定时查询呼叫状态
function timerPolling() {
  callStatus.value = "calling";
  callStatusFind();
}

//呼叫状态查询
function callStatusFind() {
  if (callStatus.value === "hangup") {
    if (route.name === "customerDetails" && route.query.time === openTime) {
      isModelAdd.value = true;
    }
    callReset();
  } else {
    callState().then(({ data }: { data: any }) => {
      if (data.state === 5) {
        callStatus.value = "called";
      } else if (data.state > 6 || data.state === 1) {
        callStatus.value = "hangup";
      }
    });
    start(() => {
      callStatusFind();
    }, 1000);
  }
}
//一键外呼
function fastCall() {
  if (!props.customMsg.phone) {
    ElMessage.warning("此线索没有手机号");
    return;
  }
  useUserStoreHook().setCallDisabled(true);

  call({ infoUuid: props.customMsg.infoUuid })
    .then((res: any) => {
      timerPolling();
      useCall(res.data.ActionID, props.customMsg.infoUuid);
      props.customMsg.openMethod === "push" && pushClue();
    })
    .catch((res: any) => {
      useUserStoreHook().setCallDisabled(false);
      res.response?.data?.metadata?.call && (isModelPerson.value = true);
    });
}

//挂断
function hangupMath() {
  hangupLoading.value = true;
  hangup()
    .then(() => {
      callStatus.value = "hangup";
      isModelAdd.value = true;
      hangupLoading.value = false;
      callReset();
    })
    .catch(() => {
      hangupLoading.value = false;
    });
}

let startCount = 0;
// 同步通话状态
watch(callStatus, (newV, oldV) => {
  const robotStore = AIRobotStore.value[props.customMsg.infoUuid];

  if (!robotStore) return;

  robotStore.callStatus = newV;
  if (newV === "called" && oldV === "calling") {
    robotStore.isCalled = true;
    startCount = count.value;
  }
});

// 同步时间
watch(count, newV => {
  if (callStatus.value !== "called") return;

  const robotStore = AIRobotStore.value[props.customMsg.infoUuid];
  robotStore.count = newV - startCount;
});

const isModelAdd = ref(false);
const isModelPerson = ref(false);

defineExpose({
  fastCall
});
</script>
<template>
  <el-button
    type="primary"
    :disabled="!customMsg.id"
    @click="isModelAdd = true"
    v-if="
      authorizationMap.indexOf('telesale_admin_custom_documentary_add') > -1
    "
  >
    新增追单记录
  </el-button>

  <template v-if="callStatus === 'noCall'">
    <ChangeCall
      :title="callStatusObj[callStatus]"
      :disabled="useUserStoreHook().callDisabled || !customMsg.id"
      v-if="customCall && customMsg.phone"
      @call="fastCall"
    />
  </template>
  <template v-else>
    <el-button
      :type="callColorObj[callStatus]"
      @click="fastCall"
      class="mr-10px"
      :disabled="useUserStoreHook().callDisabled || !customMsg.id"
      v-if="customCall && customMsg.phone"
    >
      {{ callStatusObj[callStatus] }}
      <span v-if="callStatus !== 'noCall'">
        {{ timeNum }}
      </span>
    </el-button>

    <el-button
      type="danger"
      @click="hangupMath"
      class="mr-10px ml-0!"
      :disabled="hangupLoading"
      v-if="callStatus === 'calling' || callStatus === 'called'"
    >
      挂断
    </el-button>
  </template>

  <AddDocumentaryRecord
    @closePage="closePage"
    @reset="resetPushRecord"
    :customId="customMsg.infoUuid || ''"
    v-model:value="isModelAdd"
    v-if="isModelAdd"
  />
  <PersonMsg v-if="isModelPerson" v-model:value="isModelPerson" />
</template>
