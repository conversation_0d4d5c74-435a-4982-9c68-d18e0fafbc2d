<!--
 * @Date         : 2024-08-28 11:19:50
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : xiaozhen
-->

<template>
  <div class="flex items-start customer-info-container">
    <div class="flex-1 overflow-auto">
      <el-card class="g-card g-margin-b-20">
        <div class="g-card__header d-pad-left">
          <span>基础信息</span>
          <div class="g-right" />
        </div>
        <BaseMsg
          v-if="customMsg"
          :customMsg="customMsg || null"
          @success="getInfo"
        />
      </el-card>
      <Tabs
        ref="tasRef"
        v-if="customMsg"
        :customMsg="customMsg || {}"
        :hasRisk="props.hasRisk"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import BaseMsg from "/@/views/customer/details/components/BaseMsg.vue";
import Tabs from "/@/views/customer/details/components/Tabs.vue";

const props = defineProps<{
  customMsg: any;
  getInfo: any;
  hasRisk: boolean;
}>();
</script>

<style scoped lang="scss">
.d-pad-left {
  padding-right: 0;
}
</style>
