<!--
 * @Date         : 2024-08-28 10:34:39
 * @Description  : 顶部按钮条及对应的dialog
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors: xia<PERSON>hen <EMAIL>
-->

<template>
  <div class="bg-white mb-10px items-center justify-center flex-wrap flex">
    <div class="mr-15px m-10px inline-block">
      <el-text class="font-bold">线索：</el-text>
      <el-button plain @click="isModelRelease = true">释放</el-button>
      <el-button plain @click="isModelTransfer = true">转线索</el-button>
      <CollectBtn
        v-if="customMsg?.workerid === userMsg.id"
        class="ml-12px"
        :infoUuid="props.customMsg.infoUuid"
      />
    </div>

    <div class="mr-15px m-10px inline-block">
      <el-text class="font-bold">运营：</el-text>
      <el-button
        v-auth="'telesale_admin_giveProfile'"
        type="primary"
        plain
        @click="giveProfile"
      >
        赠送资料
      </el-button>
      <el-button type="primary" plain @click="sendClass">体验赠送</el-button>
      <!-- <el-button type="primary" plain @click="openEvaluate">服务评价</el-button> -->
      <!-- <el-button
        type="primary"
        plain
        @click="messageModal = true"
        v-auth="'telesale_admin_custom_detailsV2_SendMessage'"
      >
        留言卡片
      </el-button> -->
      <el-button type="primary" plain @click="openSendMsg">邀约短信</el-button>
    </div>

    <div class="m-10px inline-block">
      <el-text class="font-bold">沟通：</el-text>
      <el-button type="primary" @click="openPush" v-if="customMsg?.userid">
        支付推送
      </el-button>
      <Call
        ref="callRefs"
        :customMsg="customMsg || {}"
        v-if="customMsg"
        @closePage="closePage"
        @resetPushRecord="resetPushRecord"
      />

      <el-button class="ml-0!" plain @click="back">返回</el-button>
    </div>
  </div>

  <PushPay
    v-model:value="isModelPush"
    :onionid="customMsg?.onionid || ''"
    :userid="customMsg?.userid || ''"
    :phone="customMsg?.phone || ''"
    :pushDiscover="props.pushDiscover"
    :grade="customMsg?.grade || ''"
    :stage="customMsg?.stage || ''"
    v-if="isModelPush"
  />
  <ExperienceGive
    v-model:value="isModelGive"
    :userid="customMsg?.userid || ''"
    v-if="isModelGive"
  />
  <Release
    v-model:value="isModelRelease"
    :infoUuid="customMsg?.infoUuid || ''"
    v-if="isModelRelease"
    @closePage="closePage"
  />
  <Evaluate
    v-model:value="isModelEvaluate"
    :userid="customMsg?.userid || ''"
    v-if="isModelEvaluate"
  />
  <GiveProfile
    v-if="isModalProfile"
    v-model:value="isModalProfile"
    :userId="customMsg?.userid || ''"
  />
  <SendMsg
    v-model:value="isModelSend"
    :customMsg="customMsg || {}"
    v-if="isModelSend"
  />
  <DialogTransfer
    v-model:value="isModelTransfer"
    :msg="customMsg || {}"
    :id="customMsg.infoUuid"
    :transferType="false"
    type="转线索"
    @closePage="closePage"
    @getList="getDetail"
    v-if="isModelTransfer"
  />
  <SendMessage
    v-if="messageModal"
    v-model:value="messageModal"
    :userId="customMsg?.userid"
  />
</template>

<script lang="ts" setup>
import PushPay from "/@/views/customer/details/dialog/PushPay.vue";
import ExperienceGive from "/@/views/customer/details/dialog/ExperienceGive.vue";
import Release from "/@/views/customer/details/dialog/Release.vue";
import Evaluate from "/@/views/customer/details/dialog/Evaluate.vue";
import SendMsg from "/@/views/customer/details/dialog/SendMsg.vue";
import SendMessage from "/@/views/customer/details/dialog/SendMessage.vue";
import GiveProfile from "/@/views/customer/details/dialog/GiveProfile.vue";
import Call from "./Call.vue";
import DialogTransfer from "/@/components/DialogTransfer/index.vue";
import useEvent from "../hooks/useEvent";
import { ref, nextTick } from "vue";
import CollectBtn from "../../components/collectBtn.vue";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";

const props = defineProps<{
  customMsg: any;
  tasRef: any;
  getDetail: any;
  hasRisk: boolean;
  pushDiscover: boolean;
}>();

const {
  messageModal,
  closePage,
  isModelGive,
  isModelRelease,
  isModelEvaluate,
  isModelSend,
  isModelTransfer,
  isModelPush,
  isModalProfile,
  resetPushRecord,
  back,
  openPush,
  openEvaluate,
  openSendMsg,
  giveProfile
} = useEvent(props.customMsg, props.tasRef);
const { userMsg } = storeToRefs(useUserStore());

const callRefs = ref();
function fastCall() {
  nextTick(() => {
    callRefs.value?.fastCall();
  });
}

const sendClass = () => {
  if (props.hasRisk)
    return ElMessage.error(
      "当前用户未绑定手机号或者手机号存在风险，不支持赠送体验课"
    );
  isModelGive.value = true;
};

defineExpose({ fastCall });
</script>

<style scoped lang="scss"></style>
