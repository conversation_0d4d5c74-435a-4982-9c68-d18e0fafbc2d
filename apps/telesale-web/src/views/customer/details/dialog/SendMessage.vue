<!--
 * @Date         : 2024-12-26 15:41:56
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import SendMessageForm from "../components/sendMessage/SendMessageForm.vue";
import SendMessageRecord from "../components/sendMessage/SendMessageRecord.vue";

interface Props {
  value: boolean;
  userId: string;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});
const activeName = ref("send");

function handleClose() {
  isModel.value = false;
}
</script>

<template>
  <el-dialog
    title="发送留言卡片到APP发现页"
    v-model="isModel"
    :before-close="handleClose"
    width="65%"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane label="推送留言卡片" name="send">
        <SendMessageForm :userId="props.userId" @success="handleClose" />
      </el-tab-pane>
      <el-tab-pane label="推送记录" name="record" lazy>
        <SendMessageRecord :userId="props.userId" />
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>

<style scoped lang="scss"></style>
