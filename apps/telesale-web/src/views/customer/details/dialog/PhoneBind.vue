<!--
 * @Date         : 2024-08-08 16:22:21
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { getLotteryDrawApi } from "/@/api/customerDetails";
import { getPhoneInfoApi } from "/@/api/customer/details";
import timeChange from "/@/utils/handle/timeChange";
import { getLabel } from "/@/utils/common";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";

interface Props {
  value: boolean;
  initialPhone: string;
  onionId: string;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const { allAgentObj } = storeToRefs(useUserStore());

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

const loading = ref<boolean>(false);

const dataList = ref([]);

const temaList: LabelValueOptions = [
  { value: 1, label: "入校" },
  { value: 2, label: "电销/网销" },
  { value: 3, label: "奥德赛" },
  { value: 4, label: "体验营" },
  { value: 5, label: "新媒体视频" },
  { value: 6, label: "研学" },
  { value: 7, label: "本地化" },
  { value: 8, label: "商业化-公域" },
  { value: 9, label: "商业化-APP" },
  { value: 10, label: "阿拉丁" }
];

function getData() {
  if (!props.initialPhone) return;
  loading.value = true;
  getPhoneInfoApi({ initialPhone: props.initialPhone })
    .then(({ data }: { data: any }) => {
      dataList.value = data.list.filter(item => item.onionId !== props.onionId);
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

getData();
</script>

<template>
  <el-dialog
    title="手机号换绑信息"
    v-model="isModel"
    :before-close="handleClose"
    :destroy-on-close="true"
    width="50%"
  >
    <div v-loading="loading">
      <p class="mb-10px font-bold text-16px">
        入库手机号为{{
          props.initialPhone
        }}，当前系统中为同一入库手机号的线索有：
      </p>
      <el-card class="mb-20px" v-for="(item, i) in dataList" :key="i">
        <el-form label-suffix=":">
          <el-form-item label="洋葱ID">
            {{ item.onionId }}
          </el-form-item>
          <el-form-item label="归属坐席">
            {{ allAgentObj[item.workerId].name || item.workerId }}
          </el-form-item>
          <el-form-item label="有效通话">
            {{ item.firstValidDial ? "有" : "无" }}
          </el-form-item>
          <el-form-item label="原先绑定的线索服务期状态">
            <div
              v-for="allocations in item.allocations"
              :key="allocations.teamId"
            >
              <span style="margin-right: 10px">
                {{ getLabel(allocations.teamId, temaList) }}
              </span>
              <span>
                {{ timeChange(allocations.startTime, 3) }} ～
                {{ timeChange(allocations.endTime, 3) }}
              </span>
            </div>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="handleClose">返回</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 10px 20px !important;
}
.el-form-item {
  margin-bottom: 0;
}
</style>
