<!--
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-13 15:28:09
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-15 16:00:36
 * @FilePath: /telesale-web_v2/apps/telesale-web/src/views/customer/details/dialog/ShipmentsModal.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script lang="ts" setup>
import { computed } from "vue";
import { useTable } from "/@/hooks/useTable";
import { TableColumns } from "/@/components/ReTable/types";
import { getLogisticsInfoApi } from "/@/api/customer/details";

interface Props {
  value: boolean;
  id: string;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const { dataList, loading } = useTable({
  api: getLogisticsInfoApi,
  isPages: false,
  initParams: {
    orderId: props.id
  },
  dataCallback: res => {
    res.data = res.data?.list?.[0]?.subList || [];
  }
});

const listHeader: TableColumns[] = [
  {
    field: "id",
    desc: "子发货单号"
  },
  {
    field: "status",
    desc: "状态",
    customRender: ({ row }) => {
      return subLogisticsState[row.status];
    }
  },
  {
    field: "expressCompany",
    desc: "快递公司"
  },
  {
    field: "sku",
    desc: "SKU",
    customRender: ({ row }) => {
      return row.skuNameList.join(",");
    }
  },
  {
    field: "trackingNum",
    desc: "快递单号"
  }
];

const subLogisticsState = {
  awaitingWarehouseProcessing: "待推送仓库",
  awaitingShipment: "待发货",
  canceled: "已取消",
  inTransit: "配送中",
  delivered: "已签收",
  onHold: "挂起",
  exchanging: "换机中",
  exchanged: "换机已入库",
  returning: "退货中",
  returned: "退货已入库",
  partiallyReturned: "部分退货",
  packageReturning: "包裹退回中",
  packageReturned: "包裹退回已入库"
};

function handleClose() {
  isModel.value = false;
}
</script>

<template>
  <el-dialog title="发货详情" v-model="isModel" :before-close="handleClose">
    <div v-loading="loading" class="mb-20px">
      <ReTable :dataList="dataList" :listHeader="listHeader" />
    </div>
  </el-dialog>
</template>
