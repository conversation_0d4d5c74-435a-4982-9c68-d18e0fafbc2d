<!--
 * @Date         : 2025-03-25 17:53:17
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<!--
 * @Date         : 2025-01-10 18:10:49
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { computed } from "vue";
import { getNewQueApi, getSchoolDataApi } from "/@/api/customer/details";
import { useCommonDataStore } from "/@/store/modules/commonData";
import { storeToRefs } from "pinia";
import { ArrowDownBold } from "@element-plus/icons-vue";

interface Props {
  value: boolean;
  cityInfo: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const commonData = useCommonDataStore();
const { getCityData } = commonData;
const { cityData } = storeToRefs(commonData);
const optionsValue = computed(() => {
  const list = cityData.value.map(province => {
    return {
      value: province.code,
      label: province.name,
      children: province.children.map(city => {
        return {
          value: {
            code: city.code,
            name: city.name
          },
          label: city.name
        };
      })
    };
  });
  return list;
});
const loading = ref(false);
const city = ref(props.cityInfo);
const dataList = ref([]);
const appUser = ref(0);
const isNewQuestions = ref(false);
const isMore = ref(false);
const showData = ref();

const getData = async () => {
  loading.value = true;
  console.log("city", city.value);

  try {
    const { data } = await getSchoolDataApi({
      city: city.value?.[1]?.name,
      regionCode: city.value?.[1]?.code
    });
    dataList.value = data.schools;
    appUser.value = data.regUsers;

    if (data.schools.length >= 4) {
      showData.value = data.schools.slice(0, 4);
    } else {
      showData.value = data.schools;
    }

    isMore.value = false;

    const res = await getNewQueApi({ regionCode: city.value?.[1]?.code });
    isNewQuestions.value = res.data.isNewExamArea;
  } catch (error) {
    console.log(error);
    isNewQuestions.value = false;
  } finally {
    loading.value = false;
  }
};

getData();

function handleClose() {
  isModel.value = false;
}

const getMore = () => {
  isMore.value = true;
  showData.value = dataList.value;
};

onMounted(() => {
  if (cityData.value?.length === 0) {
    loading.value = true;
    getCityData().finally(() => {
      loading.value = false;
    });
  }
});
</script>

<template>
  <el-dialog
    title="地区合作信息"
    v-model="isModel"
    :before-close="handleClose"
    :destroy-on-close="true"
    width="800px"
    center
  >
    <div v-loading="loading" class="pb-20px">
      <el-cascader
        ref="cascaderRef"
        v-model="city"
        :options="optionsValue"
        @change="getData"
        class="mb-10px"
      />
      <el-descriptions :column="2">
        <el-descriptions-item :label="city?.[1].name">
          <span class="point">{{ isNewQuestions ? "" : "非" }}新中考</span>
        </el-descriptions-item>
        <el-descriptions-item :label="`${city?.[1].name}洋葱APP用户`">
          <span class="point">{{ appUser }}</span>
          人
        </el-descriptions-item>
        <el-descriptions-item :label="`${city?.[1].name}洋葱合作院校`">
          <span class="point">{{ dataList?.length }}</span>
          所
        </el-descriptions-item>
      </el-descriptions>

      <div>合作学校明细</div>
      <el-table :data="showData">
        <el-table-column prop="schoolName" label="学校名称" />
        <el-table-column label="所在省市区">
          <template #default="{ row }">
            {{ row.province + row.city + row.area }}
          </template>
        </el-table-column>
        <el-table-column prop="cooperateYears" label="已合作年份" />
      </el-table>
      <div v-if="dataList?.length >= 4 && !isMore">
        <div class="flexD p-10px cursor-pointer" @click="getMore">
          <el-icon class="mr-10px"><ArrowDownBold /></el-icon>
          <div>点击展开明细</div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
