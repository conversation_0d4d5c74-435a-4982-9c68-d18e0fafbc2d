<!--
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-09 11:46:20
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-14 16:26:44
 * @FilePath: /telesale-web_v2/apps/telesale-web/src/views/customer/details/dialog/FamilyTeam.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script lang="ts" setup>
import { computed } from "vue";
import { TableColumns } from "/@/components/ReTable/types";

interface Props {
  value: boolean;
  data: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const statusType = {
  familyMemberStatus_remainConfirm: "被邀请待确认",
  familyMemberStatus_confirmed: "已确认",
  familyMemberStatus_remainVerify: "待交叉验证"
};

const colums: TableColumns[] = [
  {
    field: "onionId",
    desc: "洋葱ID"
  },
  {
    field: "status",
    desc: "状态",
    customRender: ({ text }) => {
      return statusType[text];
    }
  }
];

function handleClose() {
  isModel.value = false;
}
</script>

<template>
  <el-dialog
    title="家庭组信息"
    v-model="isModel"
    :before-close="handleClose"
    :destroy-on-close="true"
  >
    <div class="mb-20px">
      <ReTable :dataList="props.data.familyMembers" :listHeader="colums" />
    </div>
  </el-dialog>
</template>
