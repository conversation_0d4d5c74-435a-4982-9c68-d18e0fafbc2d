<!--
 * @Date         : 2025-01-10 18:10:49
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
import { computed } from "vue";
import { copyToClipboard } from "/@/utils/common/imgUrlTransform";
import html2canvas from "html2canvas";

interface Props {
  value: boolean;
  url: string;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "closePage"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});
const loading = ref(false);

function handleClose() {
  isModel.value = false;
}
const copy = () => {
  loading.value = true;
  html2canvas(document.getElementById("qr-card-screenshot"), {
    useCORS: true,
    backgroudColor: null
  }).then(canvas => {
    canvas.toBlob(blob => {
      copyToClipboard(blob)
        .then(res => {
          ElMessage.success("小程序二维码图片复制成功");
          isModel.value = false;
        })
        .catch(err => {
          ElMessage.error("小程序二维码图片复制失败", err.message || err.msg);
        })
        .finally(() => {
          loading.value = false;
        });
    });
  });
};
</script>

<template>
  <el-dialog
    title="小程序二维码"
    v-model="isModel"
    :before-close="handleClose"
    :destroy-on-close="true"
    width="420px"
    center
  >
    <div id="qr-card-screenshot" class="qr-card" v-loading="loading">
      <div class="qr-card__text">
        <p>
          微信长按识别二维码，进入
          <span class="qr-card__text--highlight">洋葱学习情报局</span>
          小程序
        </p>
        <p>绑定孩子洋葱账号，随时随地看学情～</p>
      </div>
      <div class="qr-card__imgBox">
        <img class="qr-card__img" :src="props.url" alt="" />
      </div>
    </div>
    <template #footer>
      <el-button type="primary" @click="copy" :loading="loading">
        复制到剪贴板
      </el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.qr-card {
  margin: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qr-card__text {
  margin-top: 8px;
  line-height: 21px;
  text-align: center;
  letter-spacing: 0em;
  color: #393548;
  font-weight: normal;
  font-size: 14px;
  font-variation-settings: "opsz" auto;
}

.qr-card__text--highlight {
  color: #518aff;
  font-weight: bold;
}
.qr-card__imgBox {
  width: 144px;
  height: 144px;
  margin: 20px auto 24px auto;
  padding: 10px;
  border-radius: 8px;
  box-sizing: border-box;
  border: 1px solid rgba(81, 138, 255, 0.4);
}
.qr-card__img {
  width: 124px;
  height: 124px;
}
</style>
