import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import stageAllList from "/@/utils/data/stageAllList";
import { getLabel } from "/@/utils/common";
import { TableColumns } from "/@/components/ReTable/types";

/*
 * @Date         : 2024-08-29 15:03:20
 * @Description  : 客户详情数据集合
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
export const subjectType = [
  {
    title: "同步课",
    subtitle: "增加会员时长",
    targetType: "vip"
  },
  {
    title: "专项课",
    subtitle: "增加专项课程包",
    targetType: "specialCourse"
  },
  {
    title: "洋葱服务",
    subtitle: "增加洋葱服务",
    targetType: "zhiYuan"
  },
  {
    title: "电子试卷",
    subtitle: "增加试卷",
    targetType: "examPaper"
  },
  {
    title: "课程商品",
    subtitle: "增加课程",
    targetType: "publicTextbook"
  },
  {
    title: "纠学",
    targetType: "correction",
    isHide: true
  },
  {
    title: "集合商品时长",
    subtitle: "增加集合商品时长",
    targetType: "collectionCourses"
  }
];

const { subjectList, specialCourseList } = storeToRefs(useUserStore());

const getVipString = row => {
  const idStr = row["targetId"].split("#")[1];
  const ids = idStr.split("-");
  const stageIndex = ids[0] - 1;
  const subjectIndex = ids[1] - 1;

  return `${stageAllList[stageIndex]?.value}${
    subjectIndex === -1 ? "全科" : subjectList.value[subjectIndex]
  }`;
};

/**
 * 根据id匹配学科、学段、教材版本、年级
 * @param {Array} treeData - 学科数据
 * @param {string} targetId - 学科学段的字符串
 * @return {Object} - 学科学段教才版本年级数据的对象
 */
export const getTreeString = (data: string, treeData: any[]) => {
  const strArr = data.replace("publicTextbook#", "").split("-");
  strArr.unshift(strArr[1]);
  strArr.splice(2, 1);
  strArr.push(strArr[2]);
  strArr.splice(2, 1);
  // [stage, subject, semester, publisher]
  try {
    const subject = treeData.find(item => item.id === +strArr[0]);
    const stage = subject.stages.find(item => item.id === +strArr[1]);
    const publisher = stage.publishers.find(item => item.id === +strArr[2]);
    const semester = publisher.semesters.find(item => item.id === +strArr[3]);
    return `${stage.name}${subject.name}${(publisher && publisher.name) || ""}${
      (semester && semester.name) || ""
    }`;
  } catch (err) {
    console.log(err);
    return "不存在的课程";
  }
};

export const targetChange = (row, treeData = [], examPaperData = []) => {
  let name = "";
  switch (row["targetType"]) {
    case "vip":
      name = getVipString(row);
      break;
    case "topic":
      name = "特定知识点";
      break;
    case "topicTrial":
      name = "体验知识点";
      break;
    case "tenant":
      name = getVipString(row);
      break;
    case "publicTextbook":
      name = getTreeString(row["targetId"], treeData);
      break;
    case "publicTextbookTrial":
      name = "公共教材授权体验版";
      break;
    case "specialCourse":
      name =
        specialCourseList.value.find(item => item.id === row["targetId"])
          ?.name || "专项课授权";
      break;
    case "specialCourseTrial":
      name = "体验券里的专项课授权";
      break;
    case "correction":
      name = getVipString(row);
      break;
    case "collectionCourses":
      name = getVipString(row);
      break;
    case "zhiYuan":
      name = "洋葱选科志愿卡";
      break;
    case "examPaper":
      name = getLabel(row["targetId"], examPaperData, "name", "id");
      break;
    default:
      name = row["targetId"];
  }
  return name;
};

export const aiPlanColumns: TableColumns[] = [
  {
    field: "studyFlowDoneTime",
    desc: "日期-时间"
  },
  {
    field: "studyFlowWeekDay",
    desc: "星期"
  },
  {
    field: "className",
    desc: "该记录对应的班"
  },
  {
    field: "publisherLabel",
    desc: "教材版本",
    customRender: ({ text }) => {
      return text?.name;
    }
  },
  {
    field: "sourceName",
    desc: "学习入口（功能类型标签）"
  },
  {
    field: "priorityLabel",
    desc: "优先级",
    customRender: ({ text }) => {
      return text?.name;
    }
  },
  {
    field: "customLabels",
    desc: "自定义标签",
    customRender: ({ text }) => {
      return text?.map(item => {
        return <div>{item?.name}</div>;
      });
    }
  },
  {
    field: "weekLabel",
    desc: "内容周标签",
    customRender: ({ text }) => {
      return text?.name;
    }
  },
  {
    field: "contentName",
    desc: "学习内容"
  },
  {
    field: "studyProgress",
    desc: "完成度"
  },
  {
    field: "studyDuration",
    desc: "学习时间（分钟）",
    customRender: ({ text }) => {
      return Math.ceil(text / 60);
    }
  }
];

export const videoTypeList = [
  {
    value: "experience",
    label: "体验课"
  },
  {
    value: "special",
    label: "专项课程"
  },
  {
    value: "summer",
    label: "暑期课"
  },
  {
    value: "refined_exercise",
    label: "精测点课程"
  },
  {
    value: "qimen",
    label: "奇门课程"
  },
  {
    value: "big_move",
    label: "期末复习"
  },
  {
    value: "college_entrance",
    label: "高考冲刺课"
  },
  {
    value: "high_school_entrance",
    label: "中考专项"
  },
  {
    value: "total_review_package",
    label: "毕业年级总复习"
  },
  {
    value: "literacy_courses",
    label: "素养通识课"
  },
  {
    value: "yc_planet_learn_practice",
    label: "洋葱星球学练模式专项课"
  },
  {
    value: "new_problem_type",
    label: "重难点培优课"
  },
  {
    value: "pre_school_enlightenment",
    label: "学前启蒙"
  },
  {
    value: "one_review_training",
    label: "一轮复习培优"
  },
  {
    value: "two_review_training",
    label: "二轮复习培优"
  },
  {
    value: "problem_excellent_training",
    label: "真题精讲培优"
  },
  {
    value: "higher_grade_training",
    label: "对口升学培优"
  },
  {
    value: "single_enrollment_training",
    label: "单招考试培优"
  }
];
