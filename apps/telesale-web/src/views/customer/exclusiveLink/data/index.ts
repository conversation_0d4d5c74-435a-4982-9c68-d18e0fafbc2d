/*
 * @Date         : 2025-01-22 17:24:06
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { TableColumns } from "/@/components/ReTable/types";
import { getLabel } from "/@/utils/common";

const statusList = [
  {
    label: "已上架",
    value: "已上架"
  },
  {
    label: "待上架",
    value: "待上架"
  },
  {
    label: "已下架",
    value: "已下架"
  }
];

export const columns: TableColumns[] = [
  {
    field: "courseId",
    desc: "商品ID"
  },
  {
    field: "name",
    desc: "商品名称"
  },
  {
    field: "amount",
    desc: "商品金额"
  },
  {
    field: "tags",
    desc: "课程标签",
    slot: {
      name: "tags"
    }
  },
  {
    field: "status",
    desc: "状态",
    filterOptions: {
      columns: statusList
    }
  }
];
