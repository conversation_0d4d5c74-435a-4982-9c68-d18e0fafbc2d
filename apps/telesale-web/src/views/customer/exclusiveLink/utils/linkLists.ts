/*
 * @Date         : 2024-04-22 14:14:15
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import {
  durationList,
  schoolYearList,
  fromList as list,
  newSchoolYearList,
  getStageDuration,
  ipadGoods,
  getStageGood
} from "@telesale/shared";
import { getAuth } from "/@/utils/auth";

export const useLinkLists = () => {
  const fromList = list.filter(item => {
    return getAuth(item.auth);
  });
  return {
    fromList,
    durationList,
    schoolYearList,
    newSchoolYearList,
    ipadGoods,
    getStageDuration,
    getStageGood
  };
};
