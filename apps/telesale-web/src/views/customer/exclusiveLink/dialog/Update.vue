<script lang="ts" setup>
import { computed, ref, reactive, onMounted } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import {
  updateLinkApi,
  addTagApi,
  delTagApi,
  getLinkInfoApi
} from "/@/api/customer";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import {
  getCourseInfoApi,
  updataGoodsApi
} from "/@/api/customer/exclusiveLink";

interface Props {
  value: boolean;
  tagList: any[];
  id: number;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
  (e: "getTagList"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const { orgData } = storeToRefs(useUserStore());

const rules = {
  whiteNode: [
    { required: true, message: "请选择可见的小组", trigger: "change" }
  ]
};

function handleClose() {
  ruleFormRef.value.resetFields();
  isModel.value = false;
}

let loading = ref<boolean>(false);
let device = useAppStoreHook().device;
const ruleFormRef = ref<FormInstance>();
const form = reactive({
  id: undefined,
  name: "",
  courseTags: [],
  limitedUse: 1,
  whiteNode: []
});
const tagName = ref<string>("");

const getInfo = () => {
  loading.value = true;
  getCourseInfoApi({ id: props.id })
    .then(({ data }: { data: any }) => {
      form.id = data.id;
      form.name = data.name;
      form.courseTags = data.courseTags;
      form.limitedUse = data.limitedUse || 1;
      form.whiteNode = data.whiteNode;
    })
    .finally(() => {
      loading.value = false;
    });
};

const addTag = () => {
  loading.value = true;
  addTagApi({ name: tagName.value })
    .then(() => {
      emit("getTagList");
      ElMessage.success("操作成功");
      tagName.value = "";
    })
    .finally(() => {
      loading.value = false;
    });
};

const delTag = (id: number) => {
  loading.value = true;
  delTagApi({ id })
    .then(() => {
      form.courseTags = form.courseTags.filter(item => item !== id);
      emit("getTagList");
      ElMessage.success("删除成功");
    })
    .finally(() => {
      loading.value = false;
    });
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      let data: any = { ...form, idList: [] };
      updataGoodsApi(data)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          emit("onSearch");
          handleClose();
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};

onMounted(() => {
  props.id && getInfo();
});
</script>

<template>
  <el-dialog
    title="编辑课程"
    v-model="isModel"
    :before-close="handleClose"
    :destroy-on-close="true"
  >
    <el-form
      :model="form"
      label-suffix="："
      ref="ruleFormRef"
      :class="{ mobile: device === 'mobile' }"
      v-loading="loading"
      :rules="rules"
    >
      <el-row>
        <el-form-item label="课程名称">
          <b>{{ form.name }}</b>
        </el-form-item>
      </el-row>
      <el-divider border-style="dashed" class="mt-0px" />
      <div class="font-bold">设置课程链接标签</div>
      <div class="text-12px my-10px">
        提示：标签可用于筛选课程，可为该课程勾选标签，若无可用的标签，可以输入新的课程标签并添加。
      </div>
      <el-row>
        <el-col :span="8">
          <el-input
            v-model.trim="tagName"
            placeholder="请输入新的课程标签"
            maxlength="10"
            show-word-limit
            clearable
          />
        </el-col>
        <el-col :push="1" :span="4">
          <el-button type="primary" @click="addTag" :disabled="!tagName">
            新增标签
          </el-button>
        </el-col>
      </el-row>
      <el-form-item
        label-width="0"
        prop="courseTags"
        :rules="[
          {
            required: true,
            message: '请选择课程标签',
            trigger: 'blur'
          }
        ]"
      >
        <div class="d-cont" v-if="props.tagList.length">
          <el-checkbox-group v-model="form.courseTags">
            <el-tag
              class="g-margin-rb-10"
              v-for="tag in props.tagList"
              :key="tag.id"
              :closable="!tag.isLock"
              @close="delTag(tag.id)"
            >
              <el-checkbox :label="tag.id">
                {{ tag.name }}
              </el-checkbox>
            </el-tag>
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-divider border-style="dashed" />
      <el-form-item label="是否开启仅部分成员可见" class="text-bold">
        <el-switch
          v-model="form.limitedUse"
          :active-value="2"
          :inactive-value="1"
        />
      </el-form-item>
      <div class="text-12px my-10px">
        提示：若未开启仅部分成员可见，则该课程链接可以被所有坐席看见；如开启，则可以根据组织架构选择可见的成员范围(多选)。
      </div>
      <el-form-item
        label-width="0"
        prop="whiteNode"
        v-if="form.limitedUse === 2"
      >
        <el-cascader
          v-model="form.whiteNode"
          :options="orgData"
          :props="{
            value: 'id',
            label: 'name',
            checkStrictly: true,
            multiple: true,
            emitPath: false
          }"
          placeholder="请选择小组"
          filterable
          clearable
          :show-all-levels="false"
          style="width: 100%"
          class="input-full"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div>
        <el-button
          :loading="loading"
          type="primary"
          @click="submitForm(ruleFormRef)"
        >
          确定
        </el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.d-cont {
  margin-top: 20px;
  width: 100%;
  border: 1px dashed #1c9bea;
  min-height: 50px;
  border-radius: 4px;
  padding: 10px 10px 0;
  text-align: left;
}
.b-action {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}
:deep(.el-form-item .input-full) {
  .el-input {
    width: 100%;
  }
}
</style>
