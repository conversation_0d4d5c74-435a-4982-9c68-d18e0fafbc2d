<script lang="ts" setup>
import { computed, ref, reactive } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { addLink, addTagApi, delTagApi } from "/@/api/customer";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import { createGoodsApi } from "/@/api/customer/exclusiveLink";

interface Props {
  value: boolean;
  tagList: any[];
}

const emits = defineEmits(["update:value", "onSearch", "getTagList"]);
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emits("update:value", val);
  }
});

const { orgData } = storeToRefs(useUserStore());
const step = ref<number>(1);

function handleClose() {
  ruleFormRef.value.resetFields();
  isModel.value = false;
}

let loading = ref<boolean>(false);
let device = useAppStoreHook().device;
const ruleFormRef = ref<FormInstance>();
const form = reactive({
  ids: [{ id: "" }],
  courseTags: [],
  limitedUse: 1,
  whiteNode: []
});
const tagName = ref<string>("");

const rules = {
  whiteNode: [
    { required: true, message: "请选择可见的小组", trigger: "change" }
  ]
};
const jump = (type: "next" | "pre") => {
  if (type === "pre") {
    step.value = 1;
    return;
  }
  ruleFormRef.value.validate(valid => {
    if (valid) {
      step.value = 2;
    }
  });
};

const addTag = () => {
  loading.value = true;
  addTagApi({ name: tagName.value })
    .then(() => {
      emits("getTagList");
      ElMessage.success("操作成功");
      tagName.value = "";
    })
    .finally(() => {
      loading.value = false;
    });
};

const delTag = (id: number) => {
  loading.value = true;
  delTagApi({ id })
    .then(() => {
      emits("getTagList");
      form.courseTags = form.courseTags.filter(item => item !== id);
      ElMessage.success("删除成功");
    })
    .finally(() => {
      loading.value = false;
    });
};

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      let data: any = { ...form, idList: [] };
      data.ids.forEach(item => {
        data.idList.push(item.id);
      });
      delete data.ids;
      createGoodsApi(data)
        .then(({ data }: { data: any }) => {
          loading.value = false;
          if (data.error) {
            ElMessage.warning(`添加成功${data.success}条，失败${data.error}条`);
          } else {
            ElMessage.success("添加成功");
          }
          emits("onSearch");
          handleClose();
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};
function addId() {
  form.ids.push({ id: "" });
}
function removeId(item) {
  let index = form.ids.indexOf(item);
  index !== -1 && form.ids.splice(index, 1);
}
</script>

<template>
  <el-dialog
    :title="step === 1 ? '添加商品' : '设置商品标签及所见范围'"
    v-model="isModel"
    :before-close="handleClose"
    :destroy-on-close="true"
  >
    <el-form
      :model="form"
      label-suffix="："
      :label-width="device !== 'mobile' ? '180px' : ''"
      ref="ruleFormRef"
      :class="{ mobile: device === 'mobile' }"
      v-loading="loading"
      :rules="rules"
    >
      <el-row>
        <el-col :lg="2" />
        <el-col :lg="16">
          <template v-if="step === 1">
            <el-form-item
              class="g-item"
              v-for="(item, index) in form.ids"
              :label="'商品ID' + (index + 1)"
              :key="index"
              :prop="'ids.' + index + '.id'"
              :rules="{
                required: true,
                message: '商品ID' + (index + 1) + '不能为空',
                trigger: 'blur'
              }"
            >
              <el-input
                v-model.trim="item.id"
                clearable
                :placeholder="'请输入商品ID' + (index + 1)"
              />
              <el-button
                circle
                plain
                type="primary"
                v-if="index === 0"
                @click="addId"
                :disabled="form.ids.length > 9"
              >
                <IconifyIconOffline icon="plus" />
              </el-button>
              <el-button
                circle
                plain
                type="danger"
                v-else
                @click.prevent="removeId(item)"
              >
                <IconifyIconOffline icon="minus" />
              </el-button>
            </el-form-item>
          </template>
        </el-col>
        <el-col :lg="4" />
      </el-row>
      <template v-if="step === 2">
        <div class="font-bold">设置商品链接标签</div>
        <div class="text-12px my-10px">
          提示：标签可用于筛选商品，可为该商品勾选标签，若无可用的标签，可以输入新的商品标签并添加。
        </div>
        <el-row>
          <el-col :span="8">
            <el-input
              v-model.trim="tagName"
              placeholder="请输入新的商品标签"
              maxlength="10"
              show-word-limit
              clearable
            />
          </el-col>
          <el-col :push="1" :span="4">
            <el-button type="primary" @click="addTag" :disabled="!tagName">
              新增标签
            </el-button>
          </el-col>
        </el-row>
        <el-form-item
          label-width="0"
          prop="courseTags"
          :rules="[
            {
              required: true,
              message: '请选择商品标签',
              trigger: 'blur'
            }
          ]"
        >
          <div class="d-cont" v-if="props.tagList.length">
            <el-checkbox-group v-model="form.courseTags">
              <el-tag
                class="g-margin-rb-10"
                v-for="tag in props.tagList"
                :key="tag.id"
                :closable="!tag.isLock"
                @close="delTag(tag.id)"
              >
                <el-checkbox :label="tag.id">
                  {{ tag.name }}
                </el-checkbox>
              </el-tag>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <el-divider border-style="dashed" />
        <el-form-item label="是否开启仅部分成员可见">
          <el-switch
            v-model="form.limitedUse"
            :active-value="2"
            :inactive-value="1"
          />
        </el-form-item>
        <div class="text-12px my-10px">
          提示：若未开启仅部分成员可见，则该商品链接可以被所有坐席看见；如开启，则可以根据组织架构选择可见的成员范围(多选)。
        </div>
        <el-form-item
          label-width="0"
          prop="whiteNode"
          v-if="form.limitedUse === 2"
        >
          <el-cascader
            v-model="form.whiteNode"
            :options="orgData"
            :props="{
              value: 'id',
              label: 'name',
              checkStrictly: true,
              multiple: true,
              emitPath: false
            }"
            placeholder="请选择小组"
            filterable
            clearable
            :show-all-levels="false"
            style="width: 100%"
            class="input-full"
          />
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <div>
        <template v-if="step === 2">
          <el-button
            :loading="loading"
            type="primary"
            @click="submitForm(ruleFormRef)"
          >
            确定
          </el-button>
          <el-button :loading="loading" type="primary" @click="jump('pre')">
            上一步
          </el-button>
        </template>
        <el-button v-if="step === 1" type="primary" @click="jump('next')">
          下一步
        </el-button>
        <el-button @click="handleClose">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped lang="scss">
.d-cont {
  margin-top: 20px;
  width: 100%;
  border: 1px dashed #1c9bea;
  min-height: 50px;
  border-radius: 4px;
  padding: 10px 10px 0;
  text-align: left;
}
.b-action {
  margin-top: 20px;
  display: flex;
  gap: 10px;
}
:deep(.el-form-item .input-full) {
  .el-input {
    width: 100%;
  }
}
</style>
