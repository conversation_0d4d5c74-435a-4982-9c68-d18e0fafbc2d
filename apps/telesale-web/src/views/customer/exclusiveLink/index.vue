<script setup lang="ts" name="newExclusiveLink">
import { ref } from "vue";
import { getTagListApi } from "/@/api/customer";
import { columns } from "./data/index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import AutoSelectLink from "./dialog/AutoSelectLink.vue";
import AddGood from "./dialog/AddGood.vue";
import Update from "./dialog/Update.vue";

import Search from "./components/Search.vue";
import { OperationObj } from "/@/components/ReTable/types";
import { deviceDetection } from "/@/utils/deviceDetection";
import { useTable } from "/@/hooks/useTable";
import AutoCode from "./dialog/AutoCode.vue";
import { getCourseListApi, syncGoodsApi } from "/@/api/customer/exclusiveLink";
import { delGoodsApi } from "/@/api/customer/exclusiveLink";
import { getAuth } from "/@/utils/auth";
import NewSyncLink from "./dialog/NewSyncLink.vue";

const { dataList, onSearch, handlerQuery, searchForm, loading, Pagination } =
  useTable({
    api: getCourseListApi
  });

const tagList = ref([]);
const tableRefs = ref();
//二维码弹窗
const isModelLink = ref<boolean>(false);

//添加商品
const isModelAdd = ref<boolean>(false);
const isModelNewLink = ref<boolean>(false);

const qrcodeModal = ref<boolean>(false);

const isModelUpdate = ref<boolean>(false);
const rowId = ref<number>();
const operation: OperationObj[] = [
  {
    text: "动态二维码",
    eventFn: row => {
      qrcodeModal.value = true;
      rowId.value = row.id;
    }
  },
  {
    text: "同步",
    isShow: () => getAuth("telesale_admin_new_exclusiveLink_addGoods"),
    eventFn: row => {
      ElMessageBox.confirm("确定同步此商品吗？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        loading.value = true;
        syncGoodsApi({ id: row.id })
          .then(() => {
            onSearch();
            ElMessage.success("操作成功");
          })
          .finally(() => {
            loading.value = false;
          });
      });
    }
  },
  {
    text: "移除",
    isShow: () => getAuth("telesale_admin_new_exclusiveLink_addGoods"),
    eventFn: row => {
      ElMessageBox.confirm("确定移除此商品吗？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        loading.value = true;
        delGoodsApi({ id: row.id })
          .then(() => {
            onSearch();
            ElMessage.success("操作成功");
          })
          .finally(() => {
            loading.value = false;
          });
      });
    }
  },
  {
    text: "编辑",
    isShow: () => getAuth("telesale_admin_new_exclusiveLink_addGoods"),
    eventFn: row => {
      rowId.value = row.id;
      isModelUpdate.value = true;
    }
  }
];

const filterHeadData = (data: any) => {
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      searchForm.value[key] = data[key];
    }
  }
  onSearch();
};

//表头重置
const resetFilter = () => {
  tableRefs.value?.clearFilterData();
  tableRefs.value?.clearSort();
  onSearch();
};

const getTagList = (isSearch = false) => {
  loading.value = true;
  getTagListApi()
    .then(({ data }: { data: any }) => {
      data.list.forEach(item => {
        item.children = [];
      });
      tagList.value = data.list;
      loading.value = false;
      if (isSearch) {
        handlerQuery();
      }
    })
    .catch(() => {
      loading.value = false;
    });
};

const getTags = row => {
  return row?.courseTags?.reduce((pre, cur, index, arr) => {
    const str = tagList.value?.find(item => item.id === cur)?.name || "";
    return (pre += str + (index === arr.length - 1 ? "" : "、"));
  }, "");
};

const add = () => {
  isModelAdd.value = true;
};

const linkActive = () => {
  isModelLink.value = true;
};

const newLinkActive = () => {
  isModelNewLink.value = true;
};

getTagList(true);
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <Search
        ref="formRefs"
        v-model:value="searchForm"
        :tagList="tagList"
        @reset-fitler="resetFilter"
        @onSearch="handlerQuery"
        @addGood="add"
        @linkActive="linkActive"
        @newLinkActive="newLinkActive"
      />
      <div class="mb-10px">
        <ReTable
          v-if="!deviceDetection()"
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="columns"
          :operation="operation"
          :width-operation="140"
          @filter-head-data="filterHeadData"
        >
          <template #tags="{ row }">
            {{ getTags(row) }}
          </template>
        </ReTable>
        <ReCardList
          v-else
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="columns"
          :operation="operation"
        >
          <template #tags="{ row }">
            {{ getTags(row) }}
          </template>
        </ReCardList>
      </div>
      <Pagination />
    </el-card>
    <AutoCode v-if="qrcodeModal" v-model:value="qrcodeModal" :id="rowId" />
    <AutoSelectLink
      ref="linkRefs"
      v-if="isModelLink"
      v-model:value="isModelLink"
    />
    <NewSyncLink v-if="isModelNewLink" v-model:value="isModelNewLink" />

    <Update
      v-if="isModelUpdate"
      v-model:value="isModelUpdate"
      :id="rowId"
      v-model:tagList="tagList"
      @onSearch="handlerQuery"
      @getTagList="getTagList"
    />
    <AddGood
      v-if="isModelAdd"
      v-model:value="isModelAdd"
      v-model:tagList="tagList"
      @getTagList="getTagList"
      @onSearch="onSearch"
    />
  </div>
</template>
