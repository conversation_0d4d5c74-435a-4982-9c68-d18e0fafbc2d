<script setup lang="ts">
import { reactive, ref, computed } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useAppStoreHook } from "/@/store/modules/app";
import statusList from "../utils/statusList";
import { cloneDeep } from "lodash-es";
import { useVisibleLink } from "/@/hooks/business/useVisibleLink";
import { getAuth } from "/@/utils/auth";

let device = useAppStoreHook().device;

interface Props {
  value: any;
  tagList: any[];
}

const props = defineProps<Props>();

interface Emits {
  (e: "update:value", val: any): void;
  (e: "onSearch", val: boolean): void;
  (e: "resetFitler"): void;
  (e: "addGood"): void;
  (e: "linkActive"): void;
  (e: "newLinkActive"): void;
}

const emit = defineEmits<Emits>();

const { loading, hasPermissions } = useVisibleLink();

const form = computed({
  get: () => {
    return props.value;
  },
  set: val => {
    emit("update:value", val);
  }
});

function onSearch(val = false) {
  if (form.value.amount === 0) {
    return ElMessage.warning("金额必须大于0");
  }
  emit("onSearch", val);
}

const formData = {
  name: undefined,
  status: undefined,
  courseTags: undefined,
  amount: undefined
};

const formRef = ref<FormInstance>();

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.resetFields();
  form.value = cloneDeep(formData);
  emit("resetFitler");
};

const add = () => {
  emit("addGood");
};

const linkActive = () => {
  emit("linkActive");
};

const newLinkActive = () => {
  emit("newLinkActive");
};

defineExpose({
  form
});
</script>
<template>
  <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
    <el-form-item prop="name">
      <el-input
        v-model="form.name"
        placeholder="请输入课程名称"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="courseTags">
      <el-select
        v-model="form.courseTags"
        placeholder="请选择课程标签"
        clearable
      >
        <el-option
          v-for="item in props.tagList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item prop="status" v-if="device === 'mobile'">
      <el-select v-model="form.status" placeholder="请选择状态" clearable>
        <el-option
          v-for="item in statusList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item prop="amount">
      <el-input-number
        v-model="form.amount"
        placeholder="请输入课程金额"
        :min="0"
        :max="99999999"
        clearable
        @keyup.enter="onSearch"
        style="width: 200px"
      />
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
      <el-button :icon="useRenderIcon('refresh')" @click="resetForm(formRef)">
        重置
      </el-button>
    </el-form-item>
    <el-form-item class="g-set-button">
      <el-button
        type="primary"
        v-if="
          getAuth('telesale_admin_new_exclusiveLink_createLink') &&
          hasPermissions &&
          !loading
        "
        @click="linkActive"
      >
        创建动态会场链接
      </el-button>
      <el-button
        type="primary"
        v-if="
          getAuth('telesale_admin_new_exclusiveLink_newCreateLink') &&
          hasPermissions &&
          !loading
        "
        @click="newLinkActive"
      >
        创建动态会场链接（新）
      </el-button>
      <el-button
        type="primary"
        @click="add"
        v-auth="'telesale_admin_new_exclusiveLink_addGoods'"
      >
        添加商品
      </el-button>
    </el-form-item>
  </el-form>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .el-input-number .el-input) {
  width: 200px;
}
</style>
