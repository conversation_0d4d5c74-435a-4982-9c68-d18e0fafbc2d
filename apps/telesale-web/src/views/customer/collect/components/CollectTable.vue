<!--
 * @Date         : 2024-10-09 11:14:34
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, onActivated, computed } from "vue";
import { storeToRefs } from "pinia";
import { useTable } from "/@/hooks/useTable";
import { useAppStore } from "/@/store/modules/app";
import { columnsList, operation, hideFiled } from "../data/index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { useDetail } from "/@/utils/handle/customDetails";
import Search from "./Search.vue";
import { cloneDeep, isBoolean } from "lodash-es";
import { TableColumns } from "/@/components/ReTable/types";
import { findLock, getOngingList, lockClue, unlockClue } from "/@/api/customer";
import SettingFiled from "/@/components/SettingFiled/index.vue";
import { getClueCollectListApi } from "/@/api/customer/collect";
import { useCollectStore } from "/@/store/modules/collect";
import CollectModal from "/@/components/CollectModal/index.vue";
import { formatTime } from "/@/utils/common";
import { getFamilyCategoryData } from "/@/utils/common";
import CustomerDrawer from "/@/views/customer/components/CustomerDrawer/index.vue";
import { useCustomerDrawer } from "/@/views/customer/hooks/useCustomerDrawer";

const props = defineProps<{
  currentId?: number;
  workerId?: number;
}>();

const { device } = storeToRefs(useAppStore());
const { setCollectClue } = useCollectStore();

const { toDetail } = useDetail();
const searchRef = ref<InstanceType<typeof Search>>();
const listHeader = computed(() => {
  return columnsList.value.filter(item => {
    if (typeof item.isShow === "function") {
      return item.isShow();
    } else {
      return true;
    }
  });
});

const columns = ref<TableColumns[]>([]);
const isModelItem = ref<boolean>(false);
const tableRefs = ref();
const rowIndex = ref<number>();
const dataMemory = ref();
const { drawerIdList, isCustomerVisible, setIdList } = useCustomerDrawer();

const { loading, dataList, onSearch, searchForm, handlerQuery, Pagination } =
  useTable({
    api: getClueCollectListApi,
    immediate: false,
    initParams: {
      callCount: -1
    },
    beforeRequest: data => {
      data.notDial = data.firstDialDuration;
      data.familyCategory = getFamilyCategoryData(data.familyCategory);
      return data;
    },
    dataCallback: async res => {
      res.data.list = res.data.infos;
      if (res.data.list.length > 0) {
        setIdList(res.data.list);
        const infoids = res.data.list.map(item => item.infoUuid);
        res.data.list = await getLock(
          infoids,
          res.data.list,
          searchRef.value?.searchForm?.sort
        );
        res.data.list = await setCollectClue(infoids, res.data.list);
      }
    },
    endCallback: () => {
      setCurrent(rowIndex.value);
    }
  });

//表头重置
function resetFilter() {
  tableRefs.value?.resetFilter();
}
//排序重置
function clearSort() {
  tableRefs.value?.clearSort();
}

const reset = () => {
  searchRef.value.resetForm();
};

//表头筛选
const filterChange = row => {
  for (const key in row) {
    searchRef.value.searchForm[key] = row[key]?.[0];
  }
  getList();
};

const filterHeadData = (filterData: Record<string, any>) => {
  for (const key in filterData) {
    searchRef.value.searchForm[key] = filterData[key];
  }
  getList();
};

const sortChange = column => {
  if (column.prop) {
    searchRef.value.searchForm.orderBy = column.prop.replace(
      /([A-Z])/g,
      function (match) {
        return "_" + match.toLowerCase();
      }
    );
    searchRef.value.searchForm.sort = column.order
      ? column.order.slice(0, -6)
      : "";
    searchRef.value.searchForm.combSort = [];
    getList();
  }
};

function getLock(infoids, list, sort) {
  return findLock({ infoUuids: infoids })
    .then(({ data }: { data: any }) => {
      list.forEach(item => {
        sort === "asc" && (item.lastDial = item.lastDial || ************);
        item.lockStatus = data.some(ele => item.infoUuid === ele.infoUuid);
      });
      return list;
    })
    .catch(() => {
      return list;
    });
}

const locking = (row: any) => {
  loading.value = true;
  lockClue({ infoUuid: row.infoUuid, familyId: row.familyId })
    .then(() => {
      handlerQuery();
      ElMessage.success("锁定成功");
    })
    .finally(() => {
      loading.value = false;
    });
};
const unlock = (row: any) => {
  loading.value = true;
  unlockClue({ infoUuid: row.infoUuid, familyId: row.familyId })
    .then(() => {
      handlerQuery();
      ElMessage.success("解锁成功");
    })
    .finally(() => {
      loading.value = false;
    });
};

const formatData = form => {
  const data = cloneDeep(form);
  data.workerid = data.workerid ? data.workerid + "" : undefined;
  data.hasOrder = isBoolean(data.hasOrder) ? data.hasOrder : undefined;
  formatTime(data, "userExpireStart", "userExpireEnd", "clueTime");
  formatTime(data, "lastActiveStart", "lastActiveEnd", "watchTime");
  formatTime(data, "start", "end", "createTime");
  formatTime(data, "lastDialStart", "lastDialEnd", "lastDialTime");
  formatTime(data, "regTimeStart", "regTimeEnd", "regTime");
  formatTime(data, "lastDealingStart", "lastDealingEnd", "lastDealing");
  formatTime(data, "lastPaidTimeStart", "lastPaidTimeEnd", "lastPaidTime");
  formatTime(data, "authEndAtStart", "authEndAtEnd", "authTime");
  if (!data.callCount && data.callCount !== 0) {
    data.callCount = -1;
  }
  data.id = props.currentId;
  data.workerId = props.workerId;
  return data;
};

const getList = () => {
  rowIndex.value = undefined;
  if (searchForm) {
    const data = formatData(searchRef.value.searchForm);
    searchForm.value = data;
  }
  onSearch();
};

//回显上一次点击详情或者申请的用户
const setCurrent = (row: any) => {
  if (!row) {
    return;
  }
  let index = dataList.value.findIndex(item => item.infoUuid === row.infoUuid);
  if (index < 0) {
    return;
  }
  tableRefs.value?.setCurrent(index);
};

function openCustomerDrawer(row) {
  dataMemory.value = row;
  isCustomerVisible.value = true;
}

function parantMath({ key, params }) {
  switch (key) {
    case "openCustomerDrawer":
      openCustomerDrawer(params);
      break;
  }
}

watch(
  [() => props.currentId, () => props.workerId],
  ([n]) => {
    if (n) {
      getList();
    }
  },
  { immediate: true, deep: true }
);

onActivated(() => {
  if (device.value !== "mobile") {
    rowIndex.value = tableRefs.value?.getClickRow();
  }
  if (props.currentId) {
    handlerQuery();
  }
});
</script>

<template>
  <div v-loading="loading">
    <Search
      ref="searchRef"
      @onSearch="getList"
      @clearSort="clearSort"
      @resetFitler="resetFilter"
    />
    <ReTable
      v-if="device !== 'mobile'"
      ref="tableRefs"
      :dataList="dataList"
      :listHeader="columns"
      :sort-change="sortChange"
      :filterChange="filterChange"
      @filterHeadData="filterHeadData"
      @parantMath="parantMath"
    >
      <template #appendColumn>
        <el-table-column fixed="right" label="操作" width="180">
          <template #header>
            <span>操作</span>
            <IconifyIconOffline
              icon="setting"
              @click="isModelItem = true"
              style="
                cursor: pointer;
                color: #409eff;
                font-size: 25px;
                position: absolute;
                right: 15px;
              "
            />
          </template>
          <template #default="{ row }">
            <template v-for="(item, index) in operation(toDetail)" :key="index">
              <template v-if="item.event === 'collect'">
                <CollectModal
                  :collectInfo="row.collectInfo"
                  successHide
                  v-if="item?.isShow?.(row)"
                  @success="handlerQuery"
                  @remove="handlerQuery"
                >
                  <el-button link type="primary">
                    {{ row.collectInfo?.id ? "已收藏" : "收藏" }}
                  </el-button>
                </CollectModal>
              </template>
              <template v-else-if="item.event === 'lock'">
                <div v-auth="'telesale_admin_custom_lock'" class="inline-block">
                  <el-popconfirm
                    title="确定锁定此线索吗？"
                    @confirm="locking(row)"
                    v-if="!row.lockStatus"
                  >
                    <template #reference>
                      <el-button link type="primary">锁定</el-button>
                    </template>
                  </el-popconfirm>
                  <el-popconfirm
                    title="确定解锁此线索吗？"
                    @confirm="unlock(row)"
                    v-else
                  >
                    <template #reference>
                      <el-button link type="primary">解锁</el-button>
                    </template>
                  </el-popconfirm>
                </div>
              </template>
              <el-button v-else type="primary" link @click="item.eventFn(row)">
                {{ item.text }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </template>
    </ReTable>
    <ReCardList
      v-else
      ref="cardRefs"
      :dataList="dataList"
      :listHeader="listHeader"
      :operation="operation(toDetail)"
      @parantMath="parantMath"
    />
    <div class="mt-10px">
      <Pagination />
    </div>
    <SettingFiled
      :localKey="'collect-column'"
      v-model:visible="isModelItem"
      v-model:columns="columns"
      :listHeader="listHeader"
      :hideFiled="hideFiled"
      @success="reset"
    />
    <CustomerDrawer
      v-if="isCustomerVisible"
      v-model:value="isCustomerVisible"
      :infoUuid="dataMemory.infoUuid"
      :idList="drawerIdList"
    />
  </div>
</template>

<style lang="scss" scoped></style>
