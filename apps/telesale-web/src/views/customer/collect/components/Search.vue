<!--
 * @Date         : 2024-10-09 11:21:07
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<!--
 * @Date         : 2024-05-29 11:28:32
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref } from "vue";
import { FormInstance } from "element-plus";
import { cloneDeep } from "lodash-es";
import { openDayList } from "/@/utils/data/common";
import { columnsList } from "../data/index";
import FormSearch from "/@/components/FormSearch/index.vue";
import SyncDatePicker from "/@/components/SyncDatePicker/index.vue";
import { haveOrNotList } from "../../ongoing/utils/data";
import { tabList } from "../../payClue/data";

interface Emits {
  (e: "onSearch"): void;
  (e: "resetFitler"): void;
  (e: "clearSort"): void;
}

const emit = defineEmits<Emits>();

const timeSortData = computed(() => {
  return columnsList.value.filter(item => item.isTimeSort);
});

const fromData = {
  id: undefined,
  onionId: undefined,
  familyId: undefined,
  phone: undefined,
  userExpireStart: undefined,
  userExpireEnd: undefined,
  orderBy: undefined,
  watchTimeStart: undefined,
  watchTimeEnd: undefined,
  sort: undefined,
  clueTime: undefined,
  watchTime: undefined,
  callCount: undefined,
  openCal: 14,
  createTime: undefined,
  createAtMin: undefined,
  createAtMax: undefined,
  historyAmountMin: undefined,
  historyAmountMax: undefined,
  isLock: undefined,
  existTicket: undefined,
  lastDialTime: undefined,
  regTime: undefined,
  lastDealing: undefined,
  lastPaidTime: undefined,
  lastDialStart: undefined,
  lastDialEnd: undefined,
  regTimeStart: undefined,
  regTimeEnd: undefined,
  lastDealingStart: undefined,
  lastDealingEnd: undefined,
  lastPaidTimeStart: undefined,
  lastPaidTimeEnd: undefined,
  giftExperience: undefined,
  authTime: undefined,
  authEndAtStart: undefined,
  authEndAtEnd: undefined,
  purchaseStatus: undefined,
  combSort: []
};

const searchForm = ref(cloneDeep(fromData));

const formRef = ref<FormInstance>();
const formOrgAgentRef = ref();

const resetForm = () => {
  if (!formRef.value) return;
  formRef.value.resetFields();
  formOrgAgentRef.value?.agentListReset();
  emit("resetFitler");
  emit("clearSort");
  searchForm.value = cloneDeep(fromData);
  onSearch();
};

const clearValue = (key: string) => {
  searchForm.value[key] = undefined;
};

function onSearch() {
  emit("onSearch");
}

const sortTime = () => {
  searchForm.value.orderBy = undefined;
  searchForm.value.sort = undefined;
  emit("clearSort");
  onSearch();
};

defineExpose({
  searchForm,
  onSearch,
  resetForm
});
</script>
<template>
  <el-form ref="formRef" :inline="true" :model="searchForm" class="clearfix">
    <FormSearch @onSearch="onSearch" @onReset="resetForm()">
      <template #btns>
        <TimeCombination
          v-if="timeSortData?.length > 0"
          v-model:value="searchForm.combSort"
          :data="timeSortData"
          @onSearch="sortTime"
        />
      </template>
      <template #show>
        <el-form-item prop="familyId">
          <el-input
            v-model="searchForm.familyId"
            placeholder="请输入家庭ID"
            clearable
            @keyup.enter="onSearch"
            style="width: 160px"
          />
        </el-form-item>
        <el-form-item prop="onionid">
          <el-input
            v-model="searchForm.onionId"
            placeholder="请输入洋葱ID"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item prop="phone">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入客户手机号"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
      </template>
      <template #hide>
        <el-form-item prop="watchTime">
          <SyncDatePicker
            v-model:value="searchForm.watchTime"
            dateRange="after"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="最近一次看课时间-开始"
            end-placeholder="最近一次看课时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="callCount">
          <div class="flex">
            <div class="input-prepend">外呼次数=</div>
            <el-input-number
              v-model="searchForm.callCount"
              :min="0"
              :max="99999999"
              :step="1"
              :precision="0"
              :controls="false"
              @keyup.enter="onSearch"
            />
          </div>
        </el-form-item>
        <el-form-item prop="historyAmountMin">
          <el-input-number
            v-model="searchForm.historyAmountMin"
            :min="0"
            :precision="2"
            @keyup.enter="onSearch"
            placeholder="最小历史付费金额"
            :controls="false"
          />
          至
          <el-input-number
            v-model="searchForm.historyAmountMax"
            :min="0"
            :precision="2"
            @keyup.enter="onSearch"
            placeholder="最大历史付费金额"
            :controls="false"
          />
        </el-form-item>
        <el-form-item prop="createTime">
          <SyncDatePicker
            v-model:value="searchForm.createTime"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="创建时间-开始"
            end-placeholder="创建时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>

        <el-form-item prop="clueTime">
          <SyncDatePicker
            v-model:value="searchForm.clueTime"
            dateRange="before"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="线索到期时间-开始"
            end-placeholder="线索到期时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="isLock">
          <el-select
            v-model="searchForm.isLock"
            placeholder="请选择锁定用户"
            clearable
            @clear="clearValue('isLock')"
          >
            <el-option label="全部" :value="false" />
            <el-option label="锁定用户" :value="true" />
          </el-select>
        </el-form-item>
        <el-form-item prop="existTicket">
          <el-select
            v-model="searchForm.existTicket"
            placeholder="客服工单提交情况"
            clearable
            @clear="clearValue('existTicket')"
          >
            <el-option label="历史提交过工单" :value="1" />
            <el-option label="未提交过工单" :value="2" />
          </el-select>
        </el-form-item>

        <el-form-item prop="openCal">
          <div class="flex suffix">
            <el-select v-model="searchForm.openCal" @change="onSearch">
              <el-option
                v-for="item in openDayList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="input-suffix">家长打开学情报告次数</div>
          </div>
        </el-form-item>
        <el-form-item prop="giftExperience">
          <el-select
            v-model="searchForm.giftExperience"
            placeholder="是否赠送体验课"
            clearable
            @clear="clearValue('giftExperience')"
          >
            <el-option
              v-for="item in haveOrNotList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item prop="purchaseStatus">
          <el-select
            v-model="searchForm.purchaseStatus"
            placeholder="用户购课情况"
            clearable
            multiple
            filterable
            collapse-tags
            @clear="clearValue('purchaseStatus')"
          >
            <el-option
              v-for="item in tabList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item prop="lastDialTime">
          <SyncDatePicker
            v-model:value="searchForm.lastDialTime"
            type="datetimerange"
            value-format="x"
            dateRange="after"
            start-placeholder="最近一次拨打时间-开始"
            end-placeholder="最近一次拨打时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="regTime">
          <SyncDatePicker
            v-model:value="searchForm.regTime"
            type="datetimerange"
            value-format="x"
            dateRange="after"
            start-placeholder="注册时间-开始"
            end-placeholder="注册时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="lastDealing">
          <SyncDatePicker
            v-model:value="searchForm.lastDealing"
            type="datetimerange"
            value-format="x"
            dateRange="after"
            start-placeholder="最近一次拨通时间-开始"
            end-placeholder="最近一次拨通时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="lastPaidTime">
          <SyncDatePicker
            v-model:value="searchForm.lastPaidTime"
            type="datetimerange"
            value-format="x"
            dateRange="after"
            start-placeholder="最近一次付费时间-开始"
            end-placeholder="最近一次付费时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="authTime">
          <SyncDatePicker
            v-model:value="searchForm.authTime"
            type="datetimerange"
            value-format="x"
            start-placeholder="权益到期时间-开始"
            end-placeholder="权益到期时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
      </template>
    </FormSearch>
  </el-form>
</template>

<style lang="scss" scoped>
.input-prepend {
  background-color: #f5f7fa;
  color: #909399;
  padding: 0 10px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  box-shadow: 1px 0 0 0 #dcdfe6 inset, 0 1px 0 0 #dcdfe6 inset,
    0 -1px 0 0 #dcdfe6 inset;
}
.suffix {
  .input-suffix {
    background-color: #f5f7fa;
    color: #909399;
    padding: 0 20px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    box-shadow: -1px 0 0 0 #dcdfe6 inset, 0 1px 0 0 #dcdfe6 inset,
      0 -1px 0 0 #dcdfe6 inset, 0 0 1px 0 #dcdfe6 inset;
  }
  :deep(.el-input) {
    width: 150px;
  }
}
</style>
