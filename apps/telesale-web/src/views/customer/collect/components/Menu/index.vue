<!--
 * @Date         : 2025-03-14 17:15:56
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { useLocalStorage } from "@vueuse/core";
import Collect from "./Collect.vue";
import Menu from "./Menu.vue";
import { CollectList, getCollectApi } from "/@/api/customer/collect";
import HamBurger from "/@/layout/components/sidebar/hamBurger.vue";
import { useCollectStore } from "/@/store/modules/collect";

const props = defineProps<{
  currentId?: number;
  workerId?: number;
}>();

const emits = defineEmits(["update:currentId", "update:workerId"]);

// 是否折叠
const isCollapse = useLocalStorage("collect-isCollapse", true);
const { setCollectList } = useCollectStore();

const toggleSideBar = () => {
  isCollapse.value = !isCollapse.value;
};

const loading = ref(false);

const dataList = ref<CollectList[]>([]); // 收藏列表

const currentId = computed({
  get() {
    return props.currentId;
  },
  set(val: number) {
    emits("update:currentId", val);
  }
});

const workerId = computed({
  get() {
    return props.workerId;
  },
  set(val: number) {
    emits("update:workerId", val);
  }
});

const getData = (isSet: boolean = true, workerId?: number) => {
  loading.value = true;
  console.log("workId", workerId);

  getCollectApi({ workerId: workerId })
    .then(res => {
      dataList.value = res.data.bookmarkInfos;
      if (dataList.value.length > 0 && isSet) {
        currentId.value = dataList.value[0].id;
      }
      setCollectList(res.data.bookmarkInfos);
    })
    .finally(() => {
      loading.value = false;
    });
};

getData();
</script>

<template>
  <div class="relative">
    <div class="cursor-pointer absolute top--10px left--15px z-100">
      <el-button
        circle
        size="default"
        @click="toggleSideBar"
        v-if="isCollapse"
        class="w-36px! h-36px!"
      >
        <HamBurger :is-active="isCollapse" class="p-0!" />
      </el-button>
      <el-popover placement="bottom" :width="270" trigger="hover" v-else>
        <template #reference>
          <el-button
            circle
            size="default"
            @click="toggleSideBar"
            class="w-36px! h-36px!"
          >
            <HamBurger :is-active="isCollapse" class="p-0px!" />
          </el-button>
        </template>
        <Collect
          class="min-w-240px mr-10px sticky top-0 left-0 max-h-[calc(100vh-280px)] overflow-y-auto"
          v-model:currentId="currentId"
          v-model:workerId="workerId"
          v-model:loading="loading"
          :dataList="dataList"
          :isCollapse="isCollapse"
          @success="getData"
        />
      </el-popover>
    </div>
    <Collect
      v-if="isCollapse"
      class="min-w-240px mr-10px sticky top-0 left-0 h-[calc(100vh-150px)]! overflow-auto"
      v-model:currentId="currentId"
      v-model:workerId="workerId"
      v-model:loading="loading"
      :dataList="dataList"
      :isCollapse="isCollapse"
      @success="getData"
    />
  </div>
</template>

<style scoped></style>
