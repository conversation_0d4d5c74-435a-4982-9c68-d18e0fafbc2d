<!--
 * @Date         : 2024-10-08 16:37:48
 * @Description  : 收藏组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { Folder, FolderAdd, StarFilled } from "@element-plus/icons-vue";
import AddOrRename from "../../dialog/AddOrRename.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { CollectList, deleteCollectApi } from "/@/api/customer/collect";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import CategoryMenu from "/@/components/CategoryMenu/index.vue";

const props = defineProps<{
  currentId?: number;
  workerId?: number;
  loading: boolean;
  dataList: CollectList[];
  isCollapse?: boolean;
}>();
const emits = defineEmits([
  "update:currentId",
  "update:workerId",
  "update:loading",
  "success"
]);
const { agentList, userMsg } = storeToRefs(useUserStore());

const currentId = computed({
  get() {
    return props.currentId;
  },
  set(val: number) {
    emits("update:currentId", val);
  }
});

const workerId = computed({
  get() {
    return props.workerId;
  },
  set(val: number) {
    emits("update:workerId", val);
  }
});

const loading = computed({
  get() {
    return props.loading;
  },
  set(val: boolean) {
    emits("update:loading", val);
  }
});

const isModal = ref(false);
const rowData = ref();

const getShowDrop = (item: CollectList) => {
  if (props.workerId && userMsg.value.id !== props.workerId) {
    return false;
  }
  if (item.bookmarkName === "默认收藏夹") {
    return false;
  }
  return true;
};

const add = () => {
  rowData.value = null;
  isModal.value = true;
};

const rename = (item: any) => {
  rowData.value = item;
  isModal.value = true;
};

const del = (item: CollectList) => {
  ElMessageBox.confirm(
    "移除文件夹后，所有该文件夹下的线索将同步被取消收藏",
    "提示",
    {
      title: "确认是否移除文件夹？",
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    loading.value = true;
    deleteCollectApi({ id: item.id })
      .then(() => {
        getData(currentId.value === item.id);
        ElMessage.success("移除成功");
      })
      .catch(() => {
        loading.value = false;
      });
  });
};

const getData = (val: boolean, workerId?: number) => {
  emits("success", val, workerId);
};
</script>

<template>
  <div class="h-full">
    <el-card :class="['overflow-auto', props.isCollapse ? 'min-h-full' : '']">
      <div class="mb-10px">
        <el-select-v2
          v-if="userMsg?.leafNode"
          v-model="workerId"
          :options="agentList"
          placeholder="请选择坐席"
          filterable
          clearable
          @change="$event => getData(true, $event)"
          :teleported="false"
        />
      </div>
      <CategoryMenu
        v-model:currentId="currentId"
        :dataList="props.dataList"
        name="bookmarkName"
        :isCollapse="props.isCollapse"
        :loading="loading"
        :dropdown-options="{
          isShow: getShowDrop,
          columns: [
            {
              label: '重命名',
              onClick: rename
            },
            {
              label: '移除',
              onClick: del
            }
          ]
        }"
      >
        <template v-slot:header>
          <!-- 头部 -->
          <div class="flex justify-between items-center text-18px font-bold">
            <div class="flex items-center">
              <el-icon :size="24" color="#ffc714">
                <StarFilled />
              </el-icon>
              <span class="ml-4px">收藏</span>
            </div>
            <div
              class="cursor-pointer c-[var(--el-color-primary)]"
              @click="add"
              v-if="!props.workerId || props.workerId === userMsg?.id"
            >
              <el-icon :size="24">
                <FolderAdd />
              </el-icon>
            </div>
          </div>
        </template>
      </CategoryMenu>
    </el-card>
    <AddOrRename
      v-if="isModal"
      v-model:value="isModal"
      :rowData="rowData"
      @onSearch="getData(false)"
    />
  </div>
</template>

<style lang="scss" scoped>
.card {
  padding: 10px;
  border-radius: 5px;
  background: #fff;
  margin-bottom: 10px;
  box-shadow: 12px 0px 12px rgba(0, 0, 0, 0.12);
  border: 1px solid #e8e8e8;
}
</style>
