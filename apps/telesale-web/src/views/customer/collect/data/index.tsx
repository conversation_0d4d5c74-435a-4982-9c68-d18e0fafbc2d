/*
 * @Date         : 2024-10-09 11:14:54
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { ElMessage } from "element-plus";
import { OperationObj, TableColumns } from "/@/components/ReTable/types";
import { gradeList } from "/@/utils/data/common";
import {
  addWeChatList,
  callStatusList,
  recommendList
} from "../../ongoing/utils/list";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import { getLabel } from "/@/utils/common";
import { clueSourceTree, roleList } from "@telesale/shared/src/data/customer";
import timeChange from "/@/utils/handle/timeChange";
import stageList from "/@/utils/data/stageList";
import { ref } from "vue";
import { durationChangeMath } from "../../ongoing/utils/listHeader";
import sourceObj from "/@/utils/data/sourceObj";
import customerTypeList from "/@/utils/data/customerTypeList";
import filterDataChange from "/@/utils/handle/filterDataChange";
import {
  familyCategoryList,
  familyCategoryListMap,
  studyInfoBindList,
  studyInfoBindListMap
} from "/@/utils/const/customer";
import { getAuth } from "/@/utils/auth";
import durationChange from "/@/utils/handle/durationChange";

const { userMsg, allAgentObj, intentionList } = storeToRefs(useUserStore());

const isList = [
  { label: "是", value: 1 },
  { label: "否", value: 2 }
];

export const isDealList = [
  { label: "未成交", value: false },
  { label: "已成交", value: true }
];

const isFamily = getAuth("telesale_admin_customer_family_detail");

export const columnsList = ref<TableColumns[]>([
  {
    field: "familyId",
    desc: "家庭ID",
    minWidth: 115,
    isFamily: true,
    isCopy: true,
    fixed: true,
    addType: "up",
    isShow: () => getAuth("telesale_admin_customer_family_detail")
  },
  {
    field: "onionId",
    desc: "洋葱ID",
    event: "openCustomerDrawer",
    minWidth: 115,
    isCopy: true,
    fixed: true
  },
  {
    field: "phone",
    desc: "客户手机号",
    minWidth: 130,
    isCopy: true,
    fixed: true
  },
  {
    field: "studyInfoBindType",
    desc: "学情被绑定状态",
    minWidth: 110,
    addType: "up",
    filterOptions: {
      columns: studyInfoBindList,
      isMultiple: true
    },
    customRender: ({ text }) => {
      return studyInfoBindListMap[text];
    },
    isShow: () => getAuth("telesale_admin_customer_family_detail")
  },
  {
    field: "familyCategory",
    desc: "所属家庭类型",
    minWidth: 110,
    addType: "up",
    filterOptions: {
      columns: familyCategoryList,
      isMultiple: true
    },
    customRender: ({ text }) => {
      return familyCategoryListMap[text];
    },
    isShow: () => getAuth("telesale_admin_customer_family_detail")
  },
  {
    field: "description",
    desc: "用户信息记录",
    minWidth: 160,
    customRender: ({ text }) => {
      return (
        <el-tooltip
          popper-class="max-w-700px"
          effect="dark"
          content={text}
          placement="top"
        >
          <div class="w-120px line-clamp-3 ">{text}</div>
        </el-tooltip>
      );
    }
  },
  {
    field: "historyAmount",
    desc: "历史付费金额总和",
    isTimeSort: true,
    headerTip: true,
    minWidth: 115,
    sortable: true
  },
  {
    field: "lastActiveTime",
    desc: "最近一次看课时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 170,
    sortable: true
  },
  {
    field: "callState",
    desc: "外呼状态",
    minWidth: 100,
    typeChange: callStatusList,
    filterOptions: {
      columns: callStatusList,
      isMultiple: true,
      label: "text"
    }
  },
  {
    field: "callCount",
    desc: "外呼次数",
    isTimeSort: true,
    minWidth: 110,
    sortable: true
  },
  {
    field: "lastDial",
    desc: "最近一次拨打时间",
    isTimeSort: true,
    filters: row => {
      if (!row.lastDial) {
        return "暂无呼叫记录";
      }
      return row.lastDial < 253370764800
        ? timeChange(row.lastDial, 2)
        : "暂无呼叫记录";
    },
    headerTip: true,
    minWidth: 130,
    sortable: true
  },
  {
    field: "source",
    desc: "来源",
    typeChange: sourceObj,
    minWidth: 90,
    filterCascaderOptions: {
      columns: clueSourceTree
    }
  },
  {
    field: "authEndAt",
    desc: "权益到期时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true
  },
  {
    field: "learnLength",
    desc: "过去30天学习时长",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true,
    customRender: ({ text }) => {
      return text ? durationChange(text) : "";
    }
  },
  {
    field: "role",
    desc: "用户身份",
    minWidth: 110,
    filterOptions: {
      columns: roleList
    },
    customRender: ({ text }) => {
      return getLabel(text, roleList);
    }
  },
  {
    field: "stage",
    desc: "学段",
    minWidth: 75,
    filterOptions: {
      columns: stageList,
      isMultiple: true,
      label: "text"
    }
  },
  {
    field: "grade",
    desc: "年级",
    minWidth: 75,
    filterOptions: {
      columns: gradeList,
      isMultiple: true,
      label: "text"
    }
  },
  {
    field: "createdAt",
    desc: "创建时间",
    isTimeSort: true,
    sortable: true,
    timeChange: 3,
    headerTip: true,
    minWidth: 100
  },
  {
    field: "userExpire",
    desc: "线索到期时间",
    isTimeSort: true,
    timeChange: 3,
    minWidth: 140,
    sortable: true
  },

  {
    field: "openCount",
    desc: "打开学情报告次数",
    minWidth: 110,
    sortable: true,
    addType: "up"
  },
  {
    field: "intention",
    desc: "意向度",
    minWidth: 85,
    typeChange: intentionList.value,
    filterOptions: {
      columns: intentionList,
      isMultiple: true,
      label: "text"
    }
  },
  {
    field: "lastDealing",
    desc: "最近一次拨通时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true
  },
  {
    field: "userType",
    desc: "客户类型",
    typeChange: customerTypeList,
    minWidth: 115,
    headerTip: true,
    filtersList: filterDataChange(customerTypeList, "name", "id")
  },
  {
    field: "isPadUser",
    desc: "是否购买平板",
    filterOptions: {
      columns: isList
    },
    minWidth: 105,
    customRender: ({ text }) => {
      return getLabel(text, isList);
    }
  },
  {
    field: "newExam",
    desc: "是否新题型",
    filterOptions: {
      columns: isList
    },
    minWidth: 105,
    customRender: ({ text }) => {
      return getLabel(text, isList);
    }
  },

  {
    field: "regTime",
    desc: "注册时间",
    isTimeSort: true,
    timeChange: 1,
    minWidth: 120,
    sortable: true
  },
  {
    field: "workerExten",
    desc: "外呼工号",
    minWidth: 90,
    idName: "workerId",
    idTransfer: "exten",
    isShow: () => userMsg.value.leafNode
  },
  {
    field: "firstDialDuration",
    desc: "首次呼叫间隔",
    filters: durationChangeMath,
    filtersList: [
      {
        text: "暂无呼叫记录",
        value: true
      }
    ],
    headerTip: true,
    minWidth: 145
  },

  {
    field: "isVIP",
    desc: "是否为会员",
    headerTip: true,
    customRender: ({ text }) => {
      return text === 1 ? "是" : "否";
    }
  },
  {
    field: "WeComOpenId",
    desc: "是否关联企微",
    customRender: ({ text }) => {
      return text ? "是" : "否";
    }
  },
  {
    field: "channel",
    desc: "推荐渠道",
    minWidth: 105,
    filtersList: recommendList
  },
  {
    field: "isAddWechat",
    desc: "是否成功加微",
    minWidth: 85,
    filterOptions: {
      columns: isList
    },
    customRender: ({ text }) => {
      return getLabel(text, isList);
    }
  },
  {
    field: "lastPaidTime",
    desc: "最近一次付费时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 170,
    sortable: true
  }
]);

export const hideFiled = [
  "openCount",
  "intention",
  "lastDealing",
  "userType",
  "isPadUser",
  "newExam",
  "regTime",
  "workerExten",
  "firstDialDuration",
  "isVIP",
  "WeComOpenId",
  "channel",
  "isAddWechat",
  "lastPaidTime"
];
export const operation = (toDetail: Function): OperationObj[] => {
  return [
    {
      text: "一键外呼",
      eventFn: params => {
        if (!params.phone) {
          ElMessage.warning("此线索没有手机号");
          return;
        }
        toDetail(params, "Collect", 1);
      }
    },
    {
      text: "查看详情",
      eventFn: params => {
        toDetail(params, "Collect", "");
      }
    },
    {
      text: "",
      event: "lock"
    },
    {
      text: "收藏线索",
      isShow: row => row.workerId === userMsg.value.id,
      event: "collect"
    }
  ];
};
