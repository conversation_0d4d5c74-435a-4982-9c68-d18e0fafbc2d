<!--
 * @Date         : 2024-10-08 16:36:28
 * @Description  : 已收藏页面
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup name="Collect">
import { storeToRefs } from "pinia";
import Menu from "./components/Menu/index.vue";
import CollectTable from "./components/CollectTable.vue";
import { useUserStore } from "/@/store/modules/user";
import intentionMath from "/@/utils/asyn/findIntention";

const currentCollectId = ref<number>(undefined);
const workerId = ref<number>(undefined);
const useUser = useUserStore();
const { setIntentionList } = useUser;
const { intentionList } = storeToRefs(useUser);

onMounted(async () => {
  if (intentionList.value?.length > 0) return;
  setIntentionList(await intentionMath());
});
</script>

<template>
  <div class="g-margin-20">
    <div class="flex">
      <Menu v-model:currentId="currentCollectId" v-model:workerId="workerId" />
      <el-card class="flex-auto">
        <CollectTable :currentId="currentCollectId" :workerId="workerId" />
      </el-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
