<!--
 * @Date         : 2024-10-08 19:00:08
 * @Description  : 新增或者重命名
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ElMessage, FormInstance, FormRules } from "element-plus";
import {
  addCollectApi,
  CollectInfo,
  updateCollectApi
} from "/@/api/customer/collect";

interface Props {
  value: boolean;
  rowData?: CollectInfo;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref(false);
const form = ref({
  id: props.rowData?.id,
  bookmarkName: props.rowData?.bookmarkName || undefined
});
const ruleFormRef = ref<FormInstance>();
const rules: FormRules = {
  bookmarkName: [
    {
      required: true,
      message: "请输入文件夹名称",
      trigger: "change"
    }
  ]
};

const handleClose = () => {
  isModel.value = false;
};

const submit = () => {
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      const fn = props.rowData?.id ? updateCollectApi : addCollectApi;
      fn(form.value)
        .then(() => {
          ElMessage.success("操作成功");
          emit("onSearch");
          handleClose();
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};
</script>

<template>
  <el-dialog
    :title="props.rowData?.id ? '重命名' : '新增文件夹'"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
    append-to-body
  >
    <div v-loading="loading">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        :rules="rules"
      >
        <el-row>
          <el-col :lg="4" />
          <el-col :lg="16">
            <el-form-item label="文件夹名称" prop="bookmarkName">
              <el-input
                v-model.trim="form.bookmarkName"
                placeholder="请输入文件夹名称"
                maxlength="10"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :lg="4" />
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
