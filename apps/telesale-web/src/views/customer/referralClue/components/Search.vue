<!--
 * @Date         : 2024-05-29 11:28:32
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref } from "vue";
import { FormInstance } from "element-plus";
import { cloneDeep } from "lodash-es";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import FormOrgAgent from "/@/components/FormOrgAgent/index.vue";
import FormSearch from "/@/components/FormSearch/index.vue";
import SyncDatePicker from "/@/components/SyncDatePicker/index.vue";
import { TabEnum, TabType, downColumnsList, notDownColumnsList } from "../data";

const props = defineProps<{
  type: TabType;
}>();

interface Emits {
  (e: "onSearch"): void;
  (e: "resetFitler"): void;
  (e: "clearSort"): void;
}

const timeSortData = computed(() => {
  if (props.type === TabEnum.not_down) {
    return notDownColumnsList.filter(item => item.isTimeSort);
  }
  return downColumnsList.filter(item => item.isTimeSort);
});

const emit = defineEmits<Emits>();

const { userMsg, agentList } = storeToRefs(useUserStore());

const fromData = {
  orgId: undefined,
  workerId: undefined,
  onionId: undefined,
  phone: undefined,
  sort: undefined,
  orderBy: undefined,
  watchTimeStart: undefined,
  watchTimeEnd: undefined,
  watchTime: undefined,
  historyAmountMin: undefined,
  historyAmountMax: undefined,
  authTime: undefined,
  authEndAtStart: undefined,
  authEndAtEnd: undefined,
  combSort: []
};

const searchForm = ref(cloneDeep(fromData));

const formRef = ref<FormInstance>();
const formOrgAgentRef = ref();

const resetForm = () => {
  if (!formRef.value) return;
  formRef.value.resetFields();
  formOrgAgentRef.value?.agentListReset();
  emit("resetFitler");
  emit("clearSort");
  searchForm.value = cloneDeep(fromData);
  onSearch();
};

function onSearch() {
  emit("onSearch");
}

const sortTime = () => {
  searchForm.value.orderBy = undefined;
  searchForm.value.sort = undefined;
  emit("clearSort");
  onSearch();
};

defineExpose({
  searchForm,
  onSearch,
  resetForm
});
</script>
<template>
  <el-form ref="formRef" :inline="true" :model="searchForm" class="clearfix">
    <FormSearch @onSearch="onSearch" @onReset="resetForm()">
      <template #btns>
        <TimeCombination
          v-if="timeSortData?.length > 0"
          v-model:value="searchForm.combSort"
          :data="timeSortData"
          @onSearch="sortTime"
        />
      </template>
      <template #show>
        <el-form-item prop="onionId">
          <el-input
            v-model="searchForm.onionId"
            placeholder="请输入洋葱ID"
            clearable
            @keyup.enter="onSearch"
            style="width: 160px"
          />
        </el-form-item>
        <el-form-item prop="phone">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入客户手机号"
            clearable
            @keyup.enter="onSearch"
            style="width: 160px"
          />
        </el-form-item>
        <FormOrgAgent
          v-if="userMsg.leafNode"
          ref="formOrgAgentRef"
          limitName="telesale_admin_done_group"
          workerId="workerId"
          v-model:form="searchForm"
        />
      </template>
      <template #hide>
        <el-form-item prop="historyAmountMin">
          <el-input-number
            v-model="searchForm.historyAmountMin"
            :min="0"
            :precision="2"
            @keyup.enter="onSearch"
            placeholder="最小历史付费金额"
            :controls="false"
          />
          至
          <el-input-number
            v-model="searchForm.historyAmountMax"
            :min="0"
            :precision="2"
            @keyup.enter="onSearch"
            placeholder="最大历史付费金额"
            :controls="false"
          />
        </el-form-item>
        <el-form-item prop="watchTime">
          <SyncDatePicker
            v-model:value="searchForm.watchTime"
            dateRange="after"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="最近一次看课时间-开始"
            end-placeholder="最近一次看课时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>

        <el-form-item prop="authTime">
          <SyncDatePicker
            v-model:value="searchForm.authTime"
            type="datetimerange"
            value-format="x"
            start-placeholder="权益到期时间-开始"
            end-placeholder="权益到期时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
      </template>
    </FormSearch>
  </el-form>
</template>
