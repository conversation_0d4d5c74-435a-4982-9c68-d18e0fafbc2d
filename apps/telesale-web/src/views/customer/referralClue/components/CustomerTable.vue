<!--
 * @Date         : 2024-05-15 16:13:18
 * @Description  : 共用表格组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, onActivated, inject, Ref, watch, nextTick, computed } from "vue";
import { storeToRefs } from "pinia";
import { useTable } from "/@/hooks/useTable";
import { useAppStore } from "/@/store/modules/app";
import {
  TabEnum,
  TabType,
  getColumnsList,
  operation,
  tabList
} from "../data/index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { useDetail } from "/@/utils/handle/customDetails";
import Search from "./Search.vue";
import { cloneDeep } from "lodash-es";
import { TableColumns } from "/@/components/ReTable/types";
import { getOngingList } from "/@/api/customer";
import SettingFiled from "/@/components/SettingFiled/index.vue";
import { useCollectStore } from "/@/store/modules/collect";
import { formatTime } from "/@/utils/common";
import { getFamilyCategoryData } from "/@/utils/common";
import { getReferralCluApi } from "/@/api/customer/referralClue";

const props = defineProps<{
  type: TabType;
}>();

const { device } = storeToRefs(useAppStore());
const { setCollectClue, updateClueCollect } = useCollectStore();
const { toDetail } = useDetail();
const searchRef = ref<InstanceType<typeof Search>>();
const columnsList = ref(getColumnsList(props.type));
const active = inject<Ref<TabType>>("active");
const tableRefs = ref();
const rowIndex = ref<number>();
const { type } = props;

const firstTab = tabList[0]?.value;

const { loading, dataList, onSearch, searchForm, handlerQuery, Pagination } =
  useTable({
    api: props.type === TabEnum.not_down ? getOngingList : getReferralCluApi,
    immediate: props.type !== firstTab,
    initParams:
      props.type === TabEnum.not_down
        ? {
            callCount: -1,
            hasPaid: true,
            noPoster: true
          }
        : {},
    beforeRequest: data => {
      data.notDial = data.firstDialDuration;

      if (data.orgId && data.workerId) {
        data.orgId = undefined;
      }
      if (props.type === TabEnum.not_down && data.workerId) {
        data.workerid = data.workerId + "";
        delete data.workerId;
      }
      data.familyCategory = getFamilyCategoryData(data.familyCategory);
      return data;
    },
    dataCallback: async res => {
      const infoids = res.data.list.map(item => item.infoUuid);
      res.data.list = await setCollectClue(infoids, res.data.list);
    },
    endCallback: () => {
      setCurrent(rowIndex.value);
    }
  });

watch(
  () => active.value,
  n => {
    nextTick(() => {
      if (device.value !== "mobile") {
        rowIndex.value = tableRefs.value.getClickRow();
      }

      if (n === type) {
        handlerQuery();
      }
    });
  }
);

//表头重置
function resetFilter() {
  tableRefs.value.resetFilter();
}
//排序重置
function clearSort() {
  tableRefs.value.clearSort();
}

const reset = () => {
  searchRef.value.resetForm();
};

//表头筛选
const filterChange = row => {
  for (const key in row) {
    searchRef.value.searchForm[key] = row[key]?.[0];
  }
  getList();
};

const filterHeadData = (filterData: Record<string, any>) => {
  for (const key in filterData) {
    searchRef.value.searchForm[key] = filterData[key];
  }
  getList();
};

const sortChange = column => {
  if (column.prop) {
    searchRef.value.searchForm.orderBy = column.prop.replace(
      /([A-Z])/g,
      function (match) {
        return "_" + match.toLowerCase();
      }
    );
    searchRef.value.searchForm.sort = column.order
      ? column.order.slice(0, -6)
      : "";
    if (!searchRef.value.searchForm.sort) {
      searchRef.value.searchForm.orderBy = undefined;
      searchRef.value.searchForm.sort = undefined;
    }
    searchRef.value.searchForm.combSort = [];
    getList();
  }
};

const formatData = form => {
  const data = cloneDeep(form);

  formatTime(data, "lastActiveStart", "lastActiveEnd", "watchTime");
  formatTime(data, "authEndAtStart", "authEndAtEnd", "authTime");

  return data;
};

const getList = () => {
  rowIndex.value = undefined;
  const data = formatData(searchRef.value.searchForm);
  searchForm.value = data;
  onSearch();
};

//回显上一次点击详情或者申请的用户
const setCurrent = (row: any) => {
  if (!row) {
    return;
  }
  let index = dataList.value.findIndex(item => item.id === row.id);
  if (index < 0) {
    return;
  }
  tableRefs.value.setCurrent(index);
};

const updateCollect = (
  id: number,
  infoUuid: string,
  action: "add" | "delete"
) => {
  dataList.value = updateClueCollect(id, infoUuid, action, dataList.value);
};

onActivated(() => {
  if (device.value !== "mobile") {
    rowIndex.value = tableRefs.value.getClickRow();
  }

  if (active.value === type) {
    handlerQuery();
  }
});
</script>

<template>
  <div v-loading="loading">
    <Search
      ref="searchRef"
      :type="props.type"
      @onSearch="getList"
      @clearSort="clearSort"
      @resetFitler="resetFilter"
    />
    <ReTable
      v-if="device !== 'mobile'"
      ref="tableRefs"
      :dataList="dataList"
      :listHeader="columnsList"
      :sort-change="sortChange"
      :filterChange="filterChange"
      @filterHeadData="filterHeadData"
    >
      <template #appendColumn>
        <el-table-column fixed="right" label="操作" width="180">
          <template #header>
            <span>操作</span>
          </template>
          <template #default="{ row }">
            <template v-for="(item, index) in operation(toDetail)" :key="index">
              <template v-if="item.event">
                <CollectModal
                  :collectInfo="row.collectInfo"
                  v-if="item?.isShow?.(row)"
                  @success="updateCollect($event, row.infoUuid, 'add')"
                  @remove="updateCollect($event, row.infoUuid, 'delete')"
                >
                  <el-button link type="primary">
                    {{ row.collectInfo?.id ? "已收藏" : "收藏" }}
                  </el-button>
                </CollectModal>
              </template>
              <el-button v-else type="primary" link @click="item.eventFn(row)">
                {{ item.text }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </template>
    </ReTable>
    <ReCardList
      v-else
      ref="cardRefs"
      :dataList="dataList"
      :listHeader="columnsList"
      :operation="operation(toDetail)"
    />
    <div class="mt-10px">
      <Pagination />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
