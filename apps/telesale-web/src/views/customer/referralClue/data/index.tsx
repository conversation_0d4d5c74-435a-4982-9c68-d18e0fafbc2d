/*
 * @Date         : 2024-03-27 14:36:11
 * @Description  :资料管理
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { ElMessage } from "element-plus";
import { OperationObj, TableColumns } from "/@/components/ReTable/types";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import { getLabel } from "/@/utils/common";
import { userPayList } from "@telesale/shared/src/data/customer";
import { getAuth } from "/@/utils/auth";
import durationChange from "/@/utils/handle/durationChange";

const { userMsg, allAgentObj } = storeToRefs(useUserStore());

export enum TabEnum {
  /** 已下载 */
  down = 1,
  /** 未下载 */
  not_down = 2
}

export type TabType = TabEnum;

export const tabList = [
  {
    label: "已下载海报",
    value: TabEnum.down
  },
  {
    label: "未下载海报",
    value: TabEnum.not_down
  }
];

export const isDealList = [
  { label: "未成交", value: false },
  { label: "已成交", value: true }
];

export const notDownColumnsList: TableColumns[] = [
  {
    field: "onionid",
    desc: "洋葱ID",
    minWidth: 115,
    isCopy: true,
    fixed: true
  },
  {
    field: "phone",
    desc: "客户手机号",
    minWidth: 130,
    isCopy: true,
    fixed: true
  },
  {
    field: "lastPaidTime",
    desc: "最近一次付费时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 170,
    sortable: true
  },
  {
    field: "historyAmount",
    desc: "历史付费金额总和",
    isTimeSort: true,
    headerTip: true,
    minWidth: 115,
    sortable: true
  },
  {
    field: "payCategory",
    desc: "用户付费分类",
    minWidth: 110,
    filterOptions: {
      columns: userPayList,
      isMultiple: true
    },
    customRender: ({ text }) => {
      return getLabel(text, userPayList);
    }
  },
  {
    field: "authEndAt",
    desc: "权益到期时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true
  },
  {
    field: "lastActiveTime",
    desc: "最近一次看课时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 170,
    sortable: true
  },
  {
    field: "learnLength",
    desc: "过去30天学习时长",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true,
    customRender: ({ text }) => {
      return text ? durationChange(text) : "";
    }
  },
  {
    field: "workerid",
    desc: "坐席名称",
    minWidth: 85,
    isShow: () => userMsg.value.leafNode,
    customRender: ({ text }) => {
      return allAgentObj.value[text]?.name || text;
    }
  }
];

export const downColumnsList: TableColumns[] = [
  {
    field: "onionId",
    desc: "洋葱ID",
    minWidth: 115,
    isCopy: true,
    fixed: true
  },
  {
    field: "phone",
    desc: "客户手机号",
    minWidth: 130,
    isCopy: true,
    fixed: true
  },
  {
    field: "lastPaidTime",
    desc: "最近一次付费时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 170,
    sortable: true
  },
  {
    field: "historyAmount",
    desc: "历史付费金额总和",
    isTimeSort: true,
    headerTip: true,
    minWidth: 115,
    sortable: true
  },
  {
    field: "payCategory",
    desc: "用户付费分类",
    minWidth: 110,
    filterOptions: {
      columns: userPayList,
      isMultiple: true
    },
    customRender: ({ text }) => {
      return getLabel(text, userPayList);
    }
  },
  {
    field: "authEndAt",
    desc: "权益到期时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true
  },
  {
    field: "lastActiveTime",
    desc: "最近一次看课时间",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 170,
    sortable: true
  },
  {
    field: "learnLength",
    desc: "过去30天学习时长",
    isTimeSort: true,
    timeChange: 2,
    minWidth: 130,
    sortable: true,
    customRender: ({ text }) => {
      return text ? durationChange(text) : "";
    }
  },
  {
    field: "userCount",
    desc: "裂变新用户",
    minWidth: 120,
    isTimeSort: true,
    sortable: true
  },
  {
    field: "payUserCount",
    desc: "新用户购课",
    minWidth: 120,
    isTimeSort: true,
    sortable: true
  },
  {
    field: "orderAmount",
    desc: "新用户累计购课金额",
    isTimeSort: true,
    minWidth: 120,
    sortable: true
  },
  {
    field: "momentPassCount",
    desc: "朋友圈审核通过次数",
    isTimeSort: true,
    minWidth: 150,
    sortable: true
  },
  {
    field: "lastPassTime",
    desc: "最近一次审核时间",
    isTimeSort: true,
    minWidth: 150,
    timeChange: 2,
    sortable: true
  },
  {
    field: "point",
    desc: "当前积分",
    isTimeSort: true,
    minWidth: 120,
    sortable: true
  },
  {
    field: "expirePoint",
    desc: "本月即将到期积分",
    minWidth: 140,
    isTimeSort: true,
    sortable: true
  },
  {
    field: "workerId",
    desc: "坐席名称",
    minWidth: 85,
    isShow: () => userMsg.value.leafNode,
    customRender: ({ text }) => {
      return allAgentObj.value[text]?.name || text;
    }
  }
];

export const getColumnsList = (type: TabType) => {
  return type === TabEnum.down ? downColumnsList : notDownColumnsList;
};

export const hideFiled = [
  "openCount",
  "studyInfoBindType",
  "familyCategory",
  "intention",
  "stage",
  "newExam",
  "createdAt",
  "firstDialDuration"
];

export const operation = (toDetail: Function): OperationObj[] => {
  return [
    {
      text: "一键外呼",
      eventFn: params => {
        if (!params.phone) {
          ElMessage.warning("此线索没有手机号");
          return;
        }
        toDetail(params, "ReferralClue", 1);
      }
    },
    {
      text: "查看详情",
      eventFn: params => {
        toDetail(params, "ReferralClue", "");
      }
    },
    {
      text: "收藏线索",
      isShow: row =>
        (row.workerid === userMsg.value.id ||
          row.workerId === userMsg.value.id) &&
        getAuth("telesale_admin_custom_collect_clue"),
      event: "collect"
    }
  ];
};
