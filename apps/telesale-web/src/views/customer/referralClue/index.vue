<!--
 * @Date         : 2024-05-09 12:27:35
 * @Description  : 专属资料
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup name="ReferralClue">
import { ref, onMounted, provide } from "vue";
import { tabList, TabEnum, TabType } from "./data/index";
import { getAuth } from "/@/utils/auth";
import CustomerTable from "./components/CustomerTable.vue";

const active = ref<TabType>(tabList[0].value);
provide("active", active);
</script>

<template>
  <div class="g-margin-20">
    <el-card>
      <el-tabs v-model="active">
        <template v-for="item in tabList" :key="item.value">
          <el-tab-pane :label="item.label" :name="item.value" lazy>
            <CustomerTable :key="item.value + '-active'" :type="active" />
          </el-tab-pane>
        </template>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
