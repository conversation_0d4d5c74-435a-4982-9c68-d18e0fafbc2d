<script setup lang="ts" name="newDiff">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { findUser } from "/@/api/customerDetails";
import DiffPrice from "/@/businessComponents/DiffPirce/index.vue";

const loading = ref(false);

//form查询
const form = reactive({
  key: "phone",
  value: ""
});

const id = ref("");
const active = ref("diffPrice");

function onSearch() {
  if (!/^1[3-9]\d{9}$/.test(form.value)) {
    ElMessage.warning("用户手机号格式有误");
    return;
  }
  loading.value = true;
  id.value = undefined;
  active.value = "1";
  findUser({ key: "phone", value: form.value })
    .then(({ data }: { data: any }) => {
      if (data && data.id) {
        id.value = data.id;
      } else {
        ElMessage.error("用户未注册");
      }
    })
    .finally(() => {
      loading.value = false;
    });
}
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card class="min-h-500px">
      <el-form
        ref="formRef"
        :inline="true"
        :model="form"
        class="clearfix"
        @submit.prevent
      >
        <el-form-item prop="value">
          <el-input
            v-model="form.value"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
      </el-form>
      <div class="g-table-box" v-if="id">
        <DiffPrice :userId="id" />
      </div>
    </el-card>
  </div>
</template>
