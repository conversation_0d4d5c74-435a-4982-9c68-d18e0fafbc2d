<!--
 * @Date         : 2024-09-03 10:53:00
 * @Description  : 操作按钮
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { ref } from "vue";
import { isDownload } from "/@/api/active";
import { ElMessage } from "element-plus";
import PushPay from "../../details/dialog/PushPay.vue";
import GiveProfile from "../../details/dialog/GiveProfile.vue";
import Download from "/@/views/order/order/dialog/Download.vue";

const props = defineProps<{
  userId: string;
  phone: string;
  onionId: string;
  grade: string;
  stage: string;
}>();

const isModelPush = ref<boolean>(false);
const isModalProfile = ref<boolean>(false);
const isModelDownload = ref<boolean>(false);
const posterLoading = ref<boolean>(false);
const dataMemory = ref<any>({});

const push = () => {
  isModelPush.value = true;
};

const giveProfile = () => {
  isModalProfile.value = true;
};

const downPoster = () => {
  isDownload({ userId: props.userId }).then(res => {
    if (!res.data.quality) return ElMessage.error("不符合转介绍条件");
    dataMemory.value.picMsg = res.data;
    dataMemory.value.userid = props.userId;
    isModelDownload.value = true;
  });
};
</script>

<template>
  <div class="w-full flex justify-center my-10px">
    <el-button
      type="primary"
      @click="push"
      v-if="props.userId"
      v-auth="'telesale_admin_custom_beyond_payPush'"
    >
      支付推送
    </el-button>
    <el-button
      type="primary"
      @click="giveProfile"
      v-auth="'telesale_admin_custom_beyond_giveProfile'"
    >
      赠送资料
    </el-button>
    <el-button
      type="primary"
      :loading="posterLoading"
      @click="downPoster"
      v-auth="'telesale_admin_custom_transferPoster'"
    >
      下载转介绍海报
    </el-button>

    <div>
      <PushPay
        v-model:value="isModelPush"
        :onionid="props.onionId"
        :userid="props.userId"
        :phone="props.phone"
        :grade="props.grade"
        :stage="props.stage"
        v-if="isModelPush"
      />
      <GiveProfile
        v-if="isModalProfile"
        v-model:value="isModalProfile"
        :userId="props.userId"
        source="info_search"
      />
      <Download
        v-model:value="isModelDownload"
        :dataMemory="dataMemory"
        type="transferPoster"
        v-if="isModelDownload"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
