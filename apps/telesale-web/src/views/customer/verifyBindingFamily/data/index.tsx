/*
 * @Date         : 2024-03-27 14:36:11
 * @Description  :资料管理
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { OperationObj, TableColumns } from "/@/components/ReTable/types";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import { getLabel } from "/@/utils/common";

import { getAuth } from "/@/utils/auth";
import { useDetail } from "/@/utils/handle/customDetails";

const { userMsg, allAgentObj } = storeToRefs(useUserStore());

export enum TabEnum {
  /** 待上传认证 */
  wait_upload = "wait_upload",
  /** 待审核 */
  wait_audit = "wait_audit",
  /** 已审核 */
  done_audit = "done_audit",
  /** 已过期 */
  expired = "expired"
}

export type TabType = TabEnum;

export const tabList = [
  {
    label: "待上传认证",
    value: TabEnum.wait_upload,
    auth: "telesale_admin_custom_verifyBindingFamily_wait_upload"
  },
  {
    label: "待审核",
    value: TabEnum.wait_audit,
    auth: "telesale_admin_custom_verifyBindingFamily_wait_audit"
  },
  {
    label: "已审核",
    value: TabEnum.done_audit,
    auth: "telesale_admin_custom_verifyBindingFamily_done_audit"
  },
  {
    label: "已过期",
    value: TabEnum.expired,
    auth: "telesale_admin_custom_verifyBindingFamily_expired"
  }
];

export const auditType = [
  {
    label: "通过",
    value: "verifyPass"
  },
  {
    label: "不通过",
    value: "verifyFail"
  }
];

const expiredReasonList = [
  {
    label: "坐席确认超时",
    value: "sellConfirmTimeout"
  },
  {
    label: "质检超时",
    value: "qualityTimeout"
  }
];

export const getColumns = (type: TabType): TableColumns[] => {
  return [
    {
      field: "holdPhone",
      desc: "客户手机号",
      minWidth: 115,
      isCopy: true,
      fixed: true
    },
    {
      field: "workerId",
      desc: "认领坐席",
      minWidth: 115,
      customRender: ({ text }) => {
        return allAgentObj.value[text]?.name;
      }
    },
    {
      field: "endTime",
      desc: "截止时间",
      minWidth: 115,
      timeChange: 3,
      isShow: type === TabEnum.wait_upload || type === TabEnum.wait_audit
    },
    {
      field: "holdFamilyId",
      desc: "占领方家庭ID",
      minWidth: 115,
      slot: {
        name: "holdFamilyId"
      }
    },
    {
      field: "heldFamilyId",
      desc: "被占领方家庭ID",
      isShow: type !== TabEnum.done_audit,
      minWidth: 115,
      slot: {
        name: "heldFamilyId"
      }
    },
    {
      field: "verifyStatus",
      desc: "审核结果",
      minWidth: 80,
      isShow: type === TabEnum.done_audit,
      filterOptions: {
        columns: auditType
      },
      customRender: ({ text }) => {
        return getLabel(text, auditType);
      }
    },
    {
      field: "holdFailReason",
      desc: "过期原因",
      minWidth: 115,
      isShow: type === TabEnum.expired,
      customRender: ({ text }) => {
        return getLabel(text, expiredReasonList);
      }
    }
  ];
};

export const operation = (type: TabType): OperationObj[] => {
  return [
    {
      text: "上传",
      event: "upload",
      isShow:
        type === TabEnum.wait_upload &&
        getAuth("telesale_admin_custom_verifyBindingFamily_upload_verify")
    },
    {
      text: "一键外呼",
      isShow:
        type === TabEnum.wait_upload &&
        getAuth("telesale_admin_custom_verifyBindingFamily_call"),
      event: "call"
    },
    {
      text: "审核",
      event: "audit",
      isShow:
        type === TabEnum.wait_audit &&
        getAuth("telesale_admin_custom_verifyBindingFamily_audit_action")
    },
    {
      text: "查看审核详情",
      event: "auditDetail",
      isShow: type === TabEnum.done_audit
    }
  ];
};
