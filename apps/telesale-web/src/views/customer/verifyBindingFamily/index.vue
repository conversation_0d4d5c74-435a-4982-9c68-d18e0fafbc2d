<!--
 * @Date         : 2024-05-09 12:27:35
 * @Description  : 专属资料
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup name="VerifyBindingFamily">
import { ref, provide } from "vue";
import { tabList, TabEnum, TabType } from "./data/index";
import { getAuth } from "/@/utils/auth";
import CustomerTable from "./components/CustomerTable.vue";

const active = ref<TabType>(TabEnum.wait_upload);
provide("active", active);
</script>

<template>
  <div class="g-margin-20">
    <el-card>
      <el-tabs v-model="active">
        <template v-for="item in tabList" :key="item.value">
          <el-tab-pane
            v-if="getAuth(item.auth)"
            :label="item.label"
            :name="item.value"
            lazy
          >
            <CustomerTable :key="item.value + '-active'" :type="active" />
          </el-tab-pane>
        </template>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
