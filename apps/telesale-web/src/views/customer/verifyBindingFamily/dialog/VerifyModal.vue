<!--
 * @Date         : 2024-12-18 12:04:51
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import ImageViewer from "/@/components/ImageViewer/index.vue";
import { useImages } from "/@/hooks/useImages";
import {
  verifyBindApi,
  nextVerifyBindApi
} from "/@/api/customer/verifyBindingFamily";
import { auditType } from "../data/index";
import { getLabel } from "/@/utils/common";
import { downloadCallRecord } from "/@/api/daily";
import { downloadFile } from "@telesale/shared";
import FamilyModal from "/@/views/customer/components/FamilyModal/index.vue";
import { getAuth } from "/@/utils/auth";

interface Props {
  value: boolean;
  data: any;
  search: any;
  tabName: string;
  type?: "detail";
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const { getImages } = useImages();

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const showViewer = ref<boolean>(false);
const urlIndex = ref<number>(0);
const urlList = ref<string[]>([]);
const msgData = ref();
const isFamilyModal = ref(false);
const familyId = ref();
const status = ref();

const ruleFormRef = ref<FormInstance | null>();
const form = ref({
  reason: "",
  rejectReason: ""
});

const rules: FormRules = {
  reason: [
    {
      required: false,
      message: "请选择拒绝原因",
      trigger: "change"
    },
    {
      validator: (rule, value, callback) => {
        if (status.value === "fail" && !value) {
          callback(new Error("请选择拒绝原因"));
        }
        callback();
      }
    }
  ],
  rejectReason: [
    {
      required: true,
      message: "请填写补充内容",
      trigger: "change"
    }
  ]
};
// function validatePass(rule, value, callback) {
//   if (form.value.reason === "其它" && value === "") {
//     callback(new Error("请填写补充内容"));
//   }
//   callback();
// }

function handleClose() {
  isModel.value = false;
}

const render = (list: string[], index: number) => {
  urlList.value = list;
  urlIndex.value = index;
  showViewer.value = true;
};

const getInfo = async dataInfo => {
  msgData.value = dataInfo;
  if (dataInfo.audios?.length) {
    msgData.value.audiosItem = dataInfo.audios[0];
  } else if (dataInfo.images?.length) {
    const list = dataInfo.images;
    const images = [];
    for (let index = 0; index < list.length; index++) {
      const element = list[index];
      if (element.imageLink) {
        const res = await getImages([element.imageLink]);
        images.push(...res);
      }
    }
    urlList.value = images;
  }
};

const submit = async (type: string) => {
  // if (!form.value.reason && type === "fail") {
  //   ElMessage.warning("请输入拒绝原因");
  //   return;
  // }
  status.value = type;
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      const data = {
        holdId: msgData.value.holdId,
        verifyResult: type,
        rejectReason:
          form.value.reason === "其它"
            ? form.value.rejectReason
            : form.value.reason
      };
      verifyBindApi(data)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          emit("onSearch");
          next();
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
};

const openModal = (id: string) => {
  familyId.value = id;
  isFamilyModal.value = true;
};

const next = () => {
  loading.value = true;
  nextVerifyBindApi({
    ...props.search,
    tabName: props.tabName,
    curHoldId: msgData.value.holdId,
    curFieldValue: msgData.value.endTime
  })
    .then(res => {
      if (!res.data.holdId) {
        handleClose();
        return;
      }
      ruleFormRef.value.resetFields();
      getInfo(res.data);
    })
    .finally(() => {
      loading.value = false;
    });
};

function downloadAudio(val) {
  if (val) {
    return;
  }
  loading.value = true;
  downloadCallRecord({ url: msgData.value?.audiosItem?.audioLink })
    .then(() => {
      downloadFile(msgData.value?.audiosItem?.audioLink, true);
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

onMounted(async () => {
  await getInfo(props.data);
});
</script>

<template>
  <el-dialog
    title="审核"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
    center
  >
    <div v-loading="loading" class="w-100%">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        label-width="140px"
        v-loading="loading"
        :rules="rules"
      >
        <el-row>
          <el-col :lg="3" />
          <el-col :lg="21">
            <el-form-item label="审核结果" v-if="props.type === 'detail'">
              <el-link
                :type="
                  msgData.verifyStatus === 'verifyPass' ? 'primary' : 'danger'
                "
              >
                {{ getLabel(msgData.verifyStatus, auditType) }}
              </el-link>
            </el-form-item>
            <el-form-item label="客户手机号">
              {{ msgData?.holdPhone }}
            </el-form-item>
            <el-form-item label="占领方家庭ID">
              <el-button
                type="primary"
                link
                @click="openModal(msgData?.holdFamilyId)"
                v-if="
                  props.type !== 'detail' &&
                  getAuth('telesale_admin_customer_family_detail')
                "
              >
                {{ msgData?.holdFamilyId }}
              </el-button>
              <div v-else>
                {{ msgData?.holdFamilyId }}
              </div>
            </el-form-item>
            <el-form-item label="被占领方家庭ID">
              <el-button
                type="primary"
                link
                @click="openModal(msgData?.heldFamilyId)"
                v-if="
                  props.type !== 'detail' &&
                  getAuth('telesale_admin_customer_family_detail')
                "
              >
                {{ msgData?.heldFamilyId }}
              </el-button>
              <div v-else>
                {{ msgData?.heldFamilyId }}
              </div>
            </el-form-item>
            <el-form-item label="时间点" v-if="msgData.audiosItem">
              <span style="word-break: break-all">
                {{ msgData?.audiosItem?.attentionTime }}
              </span>
            </el-form-item>
            <el-form-item label="录音" v-if="msgData.audiosItem">
              <ReAudio
                :audioSrc="msgData?.audiosItem?.audioLink"
                @downloadAudio="downloadAudio"
              />
            </el-form-item>
            <el-form-item label="图片" v-if="msgData.images?.length">
              <template v-for="(item, index) in urlList" :key="index">
                <el-image
                  lazy
                  :src="item"
                  class="min-w-28% max-w-28% h-100px m-1"
                  @click="render(urlList, index)"
                />
              </template>
            </el-form-item>
            <template v-if="props.type !== 'detail'">
              <el-form-item label="拒绝原因" prop="reason">
                <div>
                  <el-select
                    v-model.trim="form.reason"
                    allow-create
                    placeholder="请选择拒绝原因"
                  >
                    <el-option label="证据不足" value="证据不足" />
                    <el-option label="不符合逻辑" value="不符合逻辑" />
                    <el-option label="其它" value="其它" />
                  </el-select>
                </div>
              </el-form-item>
              <el-form-item
                prop="rejectReason"
                label="补充内容"
                v-if="form.reason === '其它'"
              >
                <el-input
                  clearable
                  type="textarea"
                  :autosize="{ minRows: 3, maxRows: 6 }"
                  placeholder="请填写补充内容"
                  v-model="form.rejectReason"
                />
              </el-form-item>
            </template>
            <template v-else>
              <el-form-item
                label="拒绝原因"
                prop="reason"
                v-if="msgData.verifyRejectReason"
              >
                {{ msgData.verifyRejectReason }}
              </el-form-item>
            </template>
          </el-col>
        </el-row>
      </el-form>
      <ImageViewer
        v-if="showViewer"
        v-model:show="showViewer"
        :list="urlList"
        :index="urlIndex"
      />
    </div>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <template v-if="props.type !== 'detail'">
        <el-button type="success" @click="submit('pass')" :disabled="loading">
          通过
        </el-button>
        <el-button type="danger" @click="submit('fail')" :disabled="loading">
          拒绝
        </el-button>
        <el-button type="info" @click="next" :disabled="loading">
          跳过
        </el-button>
      </template>
    </template>
  </el-dialog>
  <FamilyModal
    v-if="isFamilyModal"
    v-model:value="isFamilyModal"
    :familyId="familyId"
  />
</template>
