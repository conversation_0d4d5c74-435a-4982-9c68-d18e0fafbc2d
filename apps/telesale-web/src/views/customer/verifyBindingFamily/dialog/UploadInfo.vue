<!--
 * @Date         : 2024-12-17 11:59:14
 * @Description  : 占领上传信息
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import Upload from "/@/components/Upload/index.vue";
import { uploadVerifyApi } from "/@/api/customer/verifyBindingFamily";

interface Props {
  value: boolean;
  holdId: string;
  holdPhone: string;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance | null>();
const form = ref({
  verifyType: "audio",
  actionId: undefined,
  attentionTime: undefined,
  images: []
});
const verifyType = [
  {
    label: "关联通话记录",
    value: "audio"
  },
  {
    label: "上传截图",
    value: "image"
  }
];

const rules: FormRules = {
  verifyType: [
    {
      required: true,
      message: "请选择认证方式",
      trigger: "change"
    }
  ],
  actionId: [
    {
      required: true,
      message: "请输入通话ID",
      trigger: "change"
    }
  ],
  attentionTime: [
    {
      required: true,
      message: "请输入时间点",
      trigger: "change"
    }
  ],
  images: [
    {
      required: true,
      message: "请上传截图",
      trigger: "change"
    }
  ]
};
function handleClose() {
  isModel.value = false;
}

const submit = async () => {
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      const data: any = {
        holdId: props.holdId,
        verifyType: form.value.verifyType,
        holdPhone: props.holdPhone
      };
      if (data.verifyType === "image") {
        data.images = form.value.images.map(item => {
          return {
            imageLink: item
          };
        });
      } else {
        data.audios = [
          {
            actionId: form.value.actionId,
            attentionTime: form.value.attentionTime
          }
        ];
      }
      uploadVerifyApi(data)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          handleClose();
          emit("onSearch");
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
};
</script>

<template>
  <el-dialog
    title="上传认证"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
  >
    <div v-loading="loading" class="w-100%">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        label-width="120px"
        :rules="rules"
      >
        <el-row>
          <el-col :lg="3" />
          <el-col :lg="21">
            <el-form-item label="认证方式" prop="verifyType">
              <el-select v-model="form.verifyType" placeholder="请选择认证方式">
                <el-option
                  v-for="item in verifyType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <template v-if="form.verifyType === 'audio'">
              <el-form-item label="通话ID" prop="actionId">
                <el-input
                  v-model.trim="form.actionId"
                  placeholder="请输入通话ID"
                />
              </el-form-item>
              <el-form-item label="时间点" prop="attentionTime">
                <el-input
                  v-model.trim="form.attentionTime"
                  placeholder="请输入时间点"
                />
              </el-form-item>
            </template>
            <template v-if="form.verifyType === 'image'">
              <el-form-item label="上传截图" prop="images">
                <Upload
                  v-model:path="form.images"
                  :size="3"
                  multiple
                  :max="12"
                />
              </el-form-item>
            </template>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
