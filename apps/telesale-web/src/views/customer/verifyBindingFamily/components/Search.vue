<!--
 * @Date         : 2024-05-29 11:28:32
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref } from "vue";
import { FormInstance } from "element-plus";
import { useDate } from "/@/hooks/useDate";
import { cloneDeep } from "lodash-es";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import AgentSelect from "/@/components/AgentSelect/index.vue";
import { TabEnum, TabType } from "../data";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import { getAuth } from "/@/utils/auth";

interface Props {
  type?: TabType;
}
const props = defineProps<Props>();

interface Emits {
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();

const { disabeldBeforeToDay } = useDate();
const { userMsg } = storeToRefs(useUserStore());

const fromData = {
  holdPhone: undefined,
  workerId: undefined,
  deadline: undefined,
  endTimeStart: undefined,
  endTimeEnd: undefined
};

const searchForm = ref(cloneDeep(fromData));

const formRef = ref<FormInstance>();

function onSearch() {
  emit("onSearch");
}

defineExpose({
  searchForm,
  onSearch
});
</script>
<template>
  <el-form
    ref="formRef"
    :inline="true"
    :model="searchForm"
    class="clearfix"
    @submit.prevent
  >
    <el-form-item prop="holdPhone">
      <el-input
        v-model="searchForm.holdPhone"
        placeholder="请输入手机号"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <template
      v-if="
        props.type === TabEnum.wait_upload || props.type === TabEnum.wait_audit
      "
    >
      <el-form-item prop="workerId" v-if="userMsg.leafNode">
        <AgentSelect v-model="searchForm.workerId" />
      </el-form-item>
      <el-form-item prop="deadline">
        <el-date-picker
          v-model="searchForm.deadline"
          type="datetimerange"
          range-separator="至"
          start-placeholder="截止时间-开始"
          end-placeholder="截止时间-结束"
          :disabled-date="disabeldBeforeToDay"
          :default-time="[
            new Date(2000, 1, 1, 0, 0, 0),
            new Date(2000, 2, 1, 23, 59, 59)
          ]"
        />
      </el-form-item>
    </template>

    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
    </el-form-item>
  </el-form>
</template>

<style lang="scss" scoped>
.input-prepend {
  background-color: #f5f7fa;
  color: #909399;
  padding: 0 10px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  box-shadow: 1px 0 0 0 #dcdfe6 inset, 0 1px 0 0 #dcdfe6 inset,
    0 -1px 0 0 #dcdfe6 inset;
}
.suffix {
  .input-suffix {
    background-color: #f5f7fa;
    color: #909399;
    padding: 0 20px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    box-shadow: -1px 0 0 0 #dcdfe6 inset, 0 1px 0 0 #dcdfe6 inset,
      0 -1px 0 0 #dcdfe6 inset, 0 0 1px 0 #dcdfe6 inset;
  }
  :deep(.el-input) {
    width: 150px;
  }
}
</style>
