<!--
 * @Date         : 2024-05-15 16:13:18
 * @Description  : 共用表格组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, onActivated, inject, Ref, watch, nextTick, computed } from "vue";
import { storeToRefs } from "pinia";
import { useTable } from "/@/hooks/useTable";
import { useAppStore } from "/@/store/modules/app";
import { TabEnum, TabType, getColumns, operation } from "../data/index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import Search from "./Search.vue";
import { cloneDeep } from "lodash-es";
import UploadInfo from "../dialog/UploadInfo.vue";
import VerifyModal from "../dialog/VerifyModal.vue";
import { useCall } from "/@/hooks/business/useCall";
import { getVerifyBindingListApi } from "/@/api/customer/verifyBindingFamily";
import FamilyModal from "/@/views/customer/components/FamilyModal/index.vue";
import { getAuth } from "/@/utils/auth";

const props = defineProps<{
  type: TabType;
}>();

const { device } = storeToRefs(useAppStore());
const searchRef = ref<InstanceType<typeof Search>>();
const columnsList = ref(getColumns(props.type));
const active = inject<Ref<TabType>>("active");
const tableRefs = ref();
const rowIndex = ref<number>();
const isUploadModal = ref(false);
const modalType = ref(undefined);
const isAuditModal = ref(false);
const isFamilyModal = ref(false);
const familyId = ref();
const rowData = ref();
const holdPhone = ref();
const { type } = props;

const { fastCall, CallModal } = useCall(holdPhone);

const { loading, dataList, onSearch, searchForm, handlerQuery, Pagination } =
  useTable({
    api: getVerifyBindingListApi,
    immediate: props.type !== TabEnum.wait_upload,
    initParams: {
      tabName: props.type
    },
    dataCallback: res => {
      res.data.list = res.data.infos;
    },
    endCallback: () => {
      setCurrent(rowIndex.value);
    }
  });

watch(
  () => active.value,
  n => {
    nextTick(() => {
      if (device.value !== "mobile") {
        rowIndex.value = tableRefs.value?.getClickRow();
      }

      if (n === type) {
        handlerQuery();
      }
    });
  }
);

const filterHeadData = (filterData: Record<string, any>) => {
  for (const key in filterData) {
    searchRef.value.searchForm[key] = filterData[key];
  }
  getList();
};

const formatData = form => {
  const data = cloneDeep(form);
  formatTime(data, "endTimeStart", "endTimeEnd", "deadline");
  return data;
};

const formatTime = (data: any, key1: string, key2: string, value: any) => {
  data[`${key1}`] = data[value]?.[0] || undefined;
  data[`${key2}`] = data[value]?.[1] || undefined;

  delete data[value];
};

const getList = () => {
  rowIndex.value = undefined;
  const data = formatData(searchRef.value.searchForm);
  searchForm.value = data;
  onSearch();
};

//回显上一次点击详情或者申请的用户
const setCurrent = (row: any) => {
  if (!row) {
    return;
  }
  let index = dataList.value.findIndex(item => item.id === row.id);
  if (index < 0) {
    return;
  }
  tableRefs.value?.setCurrent(index);
};

const upload = () => {
  isUploadModal.value = true;
};

const audit = type => {
  modalType.value = type;
  isAuditModal.value = true;
};

const call = params => {
  holdPhone.value = params.holdPhone;
  fastCall();
};

const openFamilyModal = (id: string) => {
  familyId.value = id;
  isFamilyModal.value = true;
};

const parantMath = ({ key, params }) => {
  rowData.value = params;
  switch (key) {
    case "upload":
      upload();
      break;
    case "audit":
      audit(undefined);
      break;
    case "auditDetail":
      audit("detail");
      break;
    case "call":
      call(params);
      break;
  }
};

onActivated(() => {
  if (device.value !== "mobile") {
    rowIndex.value = tableRefs.value?.getClickRow();
  }

  if (active.value === type) {
    handlerQuery();
  }
});
</script>

<template>
  <div v-loading="loading">
    <Search ref="searchRef" :type="props.type" @onSearch="getList" />
    <ReTable
      v-if="device !== 'mobile'"
      ref="tableRefs"
      :dataList="dataList"
      :listHeader="columnsList"
      @filterHeadData="filterHeadData"
      :operation="
        props.type === TabEnum.expired ? undefined : operation(props.type)
      "
      :width-operation="200"
      @parantMath="parantMath"
    >
      <template #holdFamilyId="{ row }">
        <el-button
          type="primary"
          link
          @click="openFamilyModal(row.holdFamilyId)"
          v-if="
            getAuth('telesale_admin_customer_family_detail') &&
            (props.type === TabEnum.wait_upload ||
              props.type === TabEnum.wait_audit)
          "
        >
          <span class="whitespace-normal">
            {{ row.holdFamilyId }}
          </span>
        </el-button>
        <div v-else>
          {{ row.holdFamilyId }}
        </div>
      </template>
      <template #heldFamilyId="{ row }">
        <el-button
          type="primary"
          link
          @click="openFamilyModal(row.heldFamilyId)"
          v-if="
            getAuth('telesale_admin_customer_family_detail') &&
            (props.type === TabEnum.wait_upload ||
              props.type === TabEnum.wait_audit)
          "
        >
          <span class="whitespace-normal">
            {{ row.heldFamilyId }}
          </span>
        </el-button>
        <div v-else>
          {{ row.holdFamilyId }}
        </div>
      </template>
    </ReTable>
    <ReCardList
      v-else
      ref="cardRefs"
      :dataList="dataList"
      :listHeader="columnsList"
      :operation="
        props.type === TabEnum.expired ? undefined : operation(props.type)
      "
      @parantMath="parantMath"
    />
    <div class="mt-10px">
      <Pagination />
    </div>
    <UploadInfo
      v-if="isUploadModal"
      v-model:value="isUploadModal"
      :holdId="rowData.holdId"
      :holdPhone="rowData.holdPhone"
      @onSearch="handlerQuery"
    />
    <VerifyModal
      v-if="isAuditModal"
      v-model:value="isAuditModal"
      :type="modalType"
      :data="rowData"
      :search="searchForm"
      :tabName="props.type"
      @onSearch="handlerQuery"
    />
    <CallModal />
    <FamilyModal
      v-if="isFamilyModal"
      v-model:value="isFamilyModal"
      :familyId="familyId"
    />
  </div>
</template>

<style lang="scss" scoped></style>
