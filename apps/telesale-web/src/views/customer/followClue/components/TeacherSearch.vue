<!--
 * @Date         : 2025-03-17 15:57:37
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<!--
 * @Date         : 2024-05-29 11:28:32
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref } from "vue";
import { FormInstance } from "element-plus";
import FormOrgAgent from "/@/components/FormOrgAgent/index.vue";
import { cloneDeep } from "lodash-es";
import FormSearch from "/@/components/FormSearch/index.vue";
import SyncDatePicker from "/@/components/SyncDatePicker/index.vue";

interface Props {
  timeSortData: any[];
}
const props = defineProps<Props>();

interface Emits {
  (e: "onSearch"): void;
  (e: "resetFitler"): void;
  (e: "clearSort"): void;
}

const emit = defineEmits<Emits>();

const fromData = {
  orgId: undefined,
  groupId: undefined,
  followWorkerId: undefined,
  onionId: undefined,
  familyId: undefined,
  phone: undefined,
  orderBy: undefined,
  lastActiveTimeStart: undefined,
  lastActiveTimeEnd: undefined,
  sort: undefined,
  lastActiveTime: undefined,
  regionInfos: [],
  dealStatus: 1,
  combSort: []
};

const searchForm = ref(cloneDeep(fromData));

const formRef = ref<FormInstance>();
const formOrgAgentRef = ref<InstanceType<typeof FormOrgAgent>>();

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formOrgAgentRef.value.agentListReset();
  formEl.resetFields();
  emit("resetFitler");
  emit("clearSort");
  searchForm.value = cloneDeep(fromData);
  onSearch();
};

function onSearch() {
  emit("onSearch");
}

const sortTime = () => {
  searchForm.value.orderBy = undefined;
  searchForm.value.sort = undefined;
  emit("clearSort");
  onSearch();
};

defineExpose({
  searchForm,
  onSearch
});
</script>
<template>
  <el-form ref="formRef" :inline="true" :model="searchForm" class="clearfix">
    <FormSearch @onSearch="onSearch" @onReset="resetForm(formRef)">
      <template #btns>
        <TimeCombination
          v-if="props.timeSortData?.length > 0"
          v-model:value="searchForm.combSort"
          :data="props.timeSortData"
          @onSearch="sortTime"
        />
      </template>
      <template #show>
        <el-form-item prop="familyId">
          <el-input
            v-model="searchForm.familyId"
            placeholder="请输入家庭ID"
            clearable
            @keyup.enter="onSearch"
            style="width: 160px"
          />
        </el-form-item>
        <el-form-item prop="onionid">
          <el-input
            v-model="searchForm.onionId"
            placeholder="请输入洋葱ID"
            clearable
            @keyup.enter="onSearch"
            style="width: 160px"
          />
        </el-form-item>
        <el-form-item prop="phone">
          <el-input
            v-model="searchForm.phone"
            placeholder="请输入客户手机号"
            clearable
            @keyup.enter="onSearch"
            style="width: 160px"
          />
        </el-form-item>
        <FormOrgAgent
          ref="formOrgAgentRef"
          limitName="telesale_admin_done_group"
          v-model:form="searchForm"
          workerId="followWorkerId"
        />
      </template>
      <template #hide>
        <el-form-item prop="lastActiveTime">
          <SyncDatePicker
            v-model:value="searchForm.lastActiveTime"
            dateRange="after"
            type="datetimerange"
            value-format="x"
            range-separator="至"
            start-placeholder="最近一次看课时间-开始"
            end-placeholder="最近一次看课时间-结束"
            :default-time="[
              new Date(2000, 1, 1, 0, 0, 0),
              new Date(2000, 2, 1, 23, 59, 59)
            ]"
          />
        </el-form-item>
        <el-form-item prop="regionInfos">
          <CitySelector
            ref="citySelectorRef"
            v-model:value="searchForm.regionInfos"
            :options="{ provinceKey: 'provinceCode', cityKey: 'cityCodes' }"
          />
        </el-form-item>
      </template>
    </FormSearch>
  </el-form>
</template>

<style lang="scss" scoped>
.input-prepend {
  background-color: #f5f7fa;
  color: #909399;
  padding: 0 10px;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  box-shadow: 1px 0 0 0 #dcdfe6 inset, 0 1px 0 0 #dcdfe6 inset,
    0 -1px 0 0 #dcdfe6 inset;
}
.suffix {
  .input-suffix {
    background-color: #f5f7fa;
    color: #909399;
    padding: 0 20px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    box-shadow: -1px 0 0 0 #dcdfe6 inset, 0 1px 0 0 #dcdfe6 inset,
      0 -1px 0 0 #dcdfe6 inset, 0 0 1px 0 #dcdfe6 inset;
  }
  :deep(.el-input) {
    width: 150px;
  }
}
</style>
