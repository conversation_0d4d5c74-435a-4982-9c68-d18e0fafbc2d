<!--
 * @Date         : 2025-03-17 15:57:35
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<!--
 * @Date         : 2024-05-15 16:13:18
 * @Description  : 共用表格组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, onActivated, inject, Ref, watch, nextTick } from "vue";
import { storeToRefs } from "pinia";
import { useTable } from "/@/hooks/useTable";
import { useAppStore } from "/@/store/modules/app";
import { TabType, TeacherOperation, getColumns } from "../data/index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { FollowClueReq, getTeacherPushApi } from "/@/api/customer/followClue";
import { useDetail } from "/@/utils/handle/customDetails";
import Search from "./Search.vue";
import { cloneDeep, isBoolean, isFunction } from "lodash-es";
import { useCollectStore } from "/@/store/modules/collect";
import { formatTime, getFamilyCategoryData } from "/@/utils/common";
import { useCall } from "/@/hooks/business/useCall";
import ChatModal from "../dialog/ChatModal.vue";
import TeacherSearch from "./TeacherSearch.vue";
import CustomerDrawer from "/@/views/customer/components/CustomerDrawer/index.vue";
import { useCustomerDrawer } from "/@/views/customer/hooks/useCustomerDrawer";

const props = defineProps<{
  type: TabType;
}>();

const { device } = storeToRefs(useAppStore());
const { setCollectClue, updateClueCollect } = useCollectStore();

const { toDetail } = useDetail();

const searchRef = ref<InstanceType<typeof Search>>();
const listHeader = ref(getColumns());
const active = inject<Ref<TabType>>("active");
const rowIndex = ref<number>();
const { type } = props;
const callPhone = ref("");
const { CallModal, fastCall } = useCall(callPhone);
const isChatModal = ref(false);
const rowId = ref();
const dataMemory = ref();
const { drawerIdList, isCustomerVisible, setIdList } = useCustomerDrawer();

const timeSortData = computed(() => {
  return listHeader.value.filter(item => {
    let show = true;

    if (isFunction(item.isShow)) {
      show = item.isShow();
    }

    if (isBoolean(item.isShow)) {
      show = item.isShow;
    }

    return item.isTimeSort && show;
  });
});

const { loading, dataList, onSearch, searchForm, handlerQuery, Pagination } =
  useTable({
    api: getTeacherPushApi,
    immediate: false,
    initParams: {
      dealStatus: 1
    },
    beforeRequest: data => {
      data.groupId = data.orgId;
      if (data.groupId && data.workerId) {
        data.orgId = undefined;
        data.groupId = undefined;
      }
      data.familyCategory = getFamilyCategoryData(data.familyCategory);
      return data;
    },
    dataCallback: async res => {
      res.data.list = res.data.infos;
      setIdList(res.data.list);
      const infoids = res.data.list.map(item => item.infoUuid);
      res.data.list = await setCollectClue(infoids, res.data.list);
    },
    endCallback: () => {
      setCurrent(rowIndex.value);
    }
  });

//回显上一次点击详情或者申请的用户
const setCurrent = (row: any) => {
  if (!row) {
    return;
  }
  let index = dataList.value.findIndex(item => item.id === row.id);
  if (index < 0) {
    return;
  }
  tableRefs.value?.setCurrent(index);
};

watch(
  () => active.value,
  n => {
    nextTick(() => {
      if (device.value !== "mobile") {
        rowIndex.value = tableRefs.value?.getClickRow();
      }

      if (n === type) {
        handlerQuery();
      }
    });
  }
);

//表头重置
const tableRefs = ref();
function resetFilter() {
  tableRefs.value?.resetFilter();
}
//排序重置
function clearSort() {
  tableRefs.value?.clearSort();
}

//表头筛选
function filterChange(row) {
  for (const key in row) {
    searchRef.value.searchForm[key] = row[key]?.[0];
  }
  getList();
}

const filterHeadData = (filterData: Record<string, any>) => {
  for (const key in filterData) {
    searchRef.value.searchForm[key] = filterData[key];
  }
  getList();
};

function sortChange(column) {
  if (column.prop) {
    searchRef.value.searchForm.orderBy = column.prop.replace(
      /([A-Z])/g,
      function (match) {
        return "_" + match.toLowerCase();
      }
    );
    searchRef.value.searchForm.sort = column.order
      ? column.order.slice(0, -6)
      : "";
    searchRef.value.searchForm.combSort = [];
    getList();
  }
}

const formatData = (form: Partial<FollowClueReq>) => {
  const data = cloneDeep(form);

  formatTime(
    data,
    "lastActiveTimeStart",
    "lastActiveTimeEnd",
    "lastActiveTime"
  );
  return data;
};

const getList = () => {
  rowIndex.value = undefined;
  const data = formatData(searchRef.value.searchForm);
  searchForm.value = data;
  onSearch();
};

const updateCollect = (
  id: number,
  infoUuid: string,
  action: "add" | "delete"
) => {
  dataList.value = updateClueCollect(id, infoUuid, action, dataList.value);
};

const parantMath = ({ key, params }) => {
  switch (key) {
    case "callModal":
      callPhone.value = params.phone;
      fastCall();
      break;

    case "openCustomerDrawer":
      openCustomerDrawer(params);
      break;
  }
};

function openCustomerDrawer(row) {
  dataMemory.value = row;
  isCustomerVisible.value = true;
}

const openChat = row => {
  rowId.value = row.userId;
  isChatModal.value = true;
};

onActivated(() => {
  if (device.value !== "mobile") {
    rowIndex.value = tableRefs.value?.getClickRow();
  }

  if (active.value === type) {
    handlerQuery();
  }
});
</script>

<template>
  <div v-loading="loading">
    <TeacherSearch
      ref="searchRef"
      :type="props.type"
      :timeSortData="timeSortData"
      @onSearch="getList"
      @clearSort="clearSort"
      @resetFitler="resetFilter"
    />
    <ReTable
      v-if="device !== 'mobile'"
      ref="tableRefs"
      :dataList="dataList"
      :listHeader="listHeader"
      :width-operation="200"
      :operation="TeacherOperation(toDetail, updateCollect)"
      :sort-change="sortChange"
      :filterChange="filterChange"
      @filterHeadData="filterHeadData"
      @parant-math="parantMath"
    >
      <template #intentionDesc="{ row }">
        <div class="flex justify-between items-center w-100%">
          <div class="w-[calc(100%-80px)]">
            <el-tooltip
              :content="row.intentionDesc"
              placement="top"
              effect="dark"
            >
              <div class="truncate">
                {{ row.intentionDesc }}
              </div>
            </el-tooltip>
          </div>
          <div class="w-80px">
            <el-button type="primary" link @click="openChat(row)">
              查看详情
            </el-button>
          </div>
        </div>
      </template>
    </ReTable>
    <ReCardList
      v-else
      ref="cardRefs"
      :dataList="dataList"
      :listHeader="listHeader"
      :operation="TeacherOperation(toDetail, updateCollect)"
      @parant-math="parantMath"
    >
      <template #intentionDesc="{ row }">
        <div class="flex justify-between items-center w-50%">
          <div class="w-[calc(100%-80px)]">
            <el-tooltip
              :content="row.intentionDesc"
              placement="top"
              effect="dark"
            >
              <div class="truncate">
                {{ row.intentionDesc }}
              </div>
            </el-tooltip>
          </div>
          <div class="w-80px">
            <el-button type="primary" link @click="openChat(row)">
              查看详情
            </el-button>
          </div>
        </div>
      </template>
    </ReCardList>
    <Pagination class="mt-10px" />
    <CallModal />
    <ChatModal v-if="isChatModal" v-model:value="isChatModal" :userId="rowId" />
    <CustomerDrawer
      v-if="isCustomerVisible"
      v-model:value="isCustomerVisible"
      :infoUuid="dataMemory.infoUuid"
      :idList="drawerIdList"
    />
  </div>
</template>

<style lang="scss" scoped></style>
