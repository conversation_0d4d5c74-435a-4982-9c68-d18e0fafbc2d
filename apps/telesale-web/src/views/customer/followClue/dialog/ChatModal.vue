<!--
 * @Date         : 2025-03-13 18:34:50
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { getTeacherChatApi } from "/@/api/customer/followClue";
import ImageViewer from "/@/components/ImageViewer/index.vue";

interface Props {
  value: boolean;
  userId?: string;
}

interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "success"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModal = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const chatInfo = ref([]);
const urlList = ref([]);
const urlIndex = ref(0);
const showViewer = ref(false);

const closeModal = () => {
  isModal.value = false;
};

const getInfo = () => {
  loading.value = true;
  getTeacherChatApi({ userId: props.userId })
    .then(res => {
      res.data.userIntentionDesc.forEach(item => {
        item.images = JSON.parse(item.chatImages);
      });
      chatInfo.value = res.data.userIntentionDesc;
    })
    .finally(() => {
      loading.value = false;
    });
};

const render = (list: string[], index: number) => {
  urlList.value = list;
  urlIndex.value = index;
  showViewer.value = true;
};

getInfo();
</script>

<template>
  <div>
    <el-dialog
      title="推送意向记录"
      v-model="isModal"
      :before-close="closeModal"
      width="800px"
    >
      <div>
        <el-timeline>
          <template v-for="item in chatInfo" :key="item.time">
            <el-timeline-item
              :timestamp="item.createdAt"
              placement="top"
              type="primary"
            >
              <el-card>
                <div class="flex">
                  <span class="font-bold min-w-80px">意向记录：</span>
                  <span>
                    {{ item.intentDescription }}
                  </span>
                </div>

                <div class="flex mt-20px">
                  <span class="font-bold min-w-80px">聊天截图：</span>
                  <div class="flex flex-wrap gap-10px">
                    <template v-for="(img, index) in item.images" :key="index">
                      <el-image
                        v-if="index < 3"
                        :src="img"
                        lazy
                        @click="render(item.images, index)"
                        class="w-140px h-140px"
                      />
                    </template>
                  </div>
                  <div
                    v-if="item.images.length > 3"
                    class="flex justify-center items-center w-80px h-140px text-20px c-blueGray"
                    @click="render(item.images, 0)"
                  >
                    +{{ item.images.length - 3 }}
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </template>
        </el-timeline>
      </div>
      <template #footer>
        <el-button :loading="loading" @click="closeModal">关闭</el-button>
      </template>
    </el-dialog>
    <ImageViewer
      v-if="showViewer"
      v-model:show="showViewer"
      :list="urlList"
      :index="urlIndex"
    />
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .el-input) {
  width: 300px;
}
</style>
