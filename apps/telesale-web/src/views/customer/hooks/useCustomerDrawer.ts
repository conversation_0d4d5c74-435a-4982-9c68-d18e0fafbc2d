/*
 * @Date         : 2025-04-15 17:33:20
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

export const useCustomerDrawer = () => {
  const drawerIdList = ref([]);
  const isCustomerVisible = ref(false);

  const setIdList = (list: any) => {
    drawerIdList.value = list.map(item => {
      return {
        infoUuid: item.infoUuid,
        onionId: item.onionid || item.onionId
      };
    });
  };

  return {
    drawerIdList,
    isCustomerVisible,
    setIdList
  };
};
