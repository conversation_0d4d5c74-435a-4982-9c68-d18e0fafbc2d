<!--
 * @Date         : 2024-11-07 17:32:46
 * @Description  : 新版客户池
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup name="Collect">
import { InfoFilled } from "@element-plus/icons-vue";
import Menu from "../components/Menu/index.vue";
import NewTable from "./components/NewTable.vue";
import { useView } from "/@/hooks/business/useView";

const {
  loading,
  tableLoading,
  currentId,
  hasPermission,
  currentViewData,
  dataList,
  newTableRef,
  getData,
  initTable,
  setLoading,
  save
} = useView("客户池");
</script>

<template>
  <div class="g-margin-20" v-loading="loading || tableLoading">
    <div
      class="w-full flex justify-center items-center bg-#d1e7fd py-5px"
      v-if="currentViewData?.parentId"
    >
      <el-icon color="#3d97ff" size="16"><InfoFilled /></el-icon>
      <div class="mt-2px">当前视图存在本地变更，保存将对所有浏览者可见。</div>
      <el-button type="primary" link @click="save('save')" v-if="hasPermission">
        保存
      </el-button>
      <el-button type="primary" link @click="save('reset')">重置</el-button>
    </div>
    <div class="flex relative">
      <Menu
        v-model:currentId="currentId"
        :dataList="dataList"
        @set-view-data="initTable"
        @success="getData"
      />
      <el-card class="flex-auto">
        <NewTable
          ref="newTableRef"
          @addView="getData(false)"
          @updateLoading="setLoading"
        />
      </el-card>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
