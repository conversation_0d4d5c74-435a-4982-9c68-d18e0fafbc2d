<script setup lang="ts" name="ongoing">
import { ref, onMounted, onActivated } from "vue";
import { ElMessage } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import { getOngingList, findLock } from "/@/api/customer";
import paramsHandle from "/@/utils/handle/paramsHandle";
import intentionMath from "/@/utils/asyn/findIntention";

import RePagination from "/@/components/RePagination/index.vue";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";

import DialogTransfer from "/@/components/DialogTransfer/index.vue";
import Search from "./components/Search.vue";
import Operation from "./components/Operation.vue";
import TableItemSet from "/@/components/TableItemSet/index.vue";
import SettingFiled from "/@/components/SettingFiled/index.vue";

import { listHeader, hideFiled } from "./utils/listHeader";
import { TableColumns } from "/@/components/ReTable/types";
import { useCollectStore } from "/@/store/modules/collect";
import { getFamilyCategoryData } from "/@/utils/common";
import CustomerDrawer from "/@/views/customer/components/CustomerDrawer/index.vue";
import { useCustomerDrawer } from "/@/views/customer/hooks/useCustomerDrawer";

let device = useAppStoreHook().device;
const { setCollectClue, updateClueCollect } = useCollectStore();
const { drawerIdList, isCustomerVisible, setIdList } = useCustomerDrawer();

//带分页列表数据必备
let loading = ref(true);
let dataList = ref([]);
let total = ref(0);
const columns = ref<TableColumns[]>([]);

//分页
const rePaginationRefs = ref();
function onSearch(val = false) {
  val === true && (rePaginationRefs.value.pageSize = 20);
  rePaginationRefs.value.onSearch();
}

//表头筛选
function filterChange(row) {
  row.grade !== undefined && (formRefs.value.form.grade = row.grade[0] || "");
  row.intention !== undefined &&
    (formRefs.value.form.intention = row.intention[0] || "");
  row.callState !== undefined &&
    (formRefs.value.form.callState = row.callState[0] || undefined);
  row.usertype !== undefined &&
    (formRefs.value.form.usertype = row.usertype[0] || "");
  row.source !== undefined &&
    (formRefs.value.form.source = row.source[0] || "");
  row.stage !== undefined && (formRefs.value.form.stage = row.stage[0] || "");
  row.channel !== undefined &&
    (formRefs.value.form.channel = row.channel[0] || "");
  row.isAddWechat !== undefined &&
    (formRefs.value.form.isAddWechat = row.isAddWechat[0]);
  row.firstDialDuration !== undefined &&
    (formRefs.value.form.notDial = row.firstDialDuration[0]);
  row.isPadUser !== undefined &&
    (formRefs.value.form.isPadUser =
      row.isPadUser[0] === 1
        ? true
        : row.isPadUser[0] === 2
        ? false
        : undefined);
  row.newExam !== undefined &&
    (formRefs.value.form.newExam =
      row.newExam[0] === 1 ? true : row.newExam[0] === 2 ? false : undefined);
  onSearch();
}

const filterHeadData = (filterData: Record<string, any>) => {
  for (const key in filterData) {
    formRefs.value.form[key] = filterData[key];
  }
  onSearch();
};

//排序
function sortChange(column) {
  if (column.prop) {
    formRefs.value.form.orderBy = column.prop.replace(
      /([A-Z])/g,
      function (match) {
        return "_" + match.toLowerCase();
      }
    );
    formRefs.value.form.sort = column.order ? column.order.slice(0, -6) : "";
    formRefs.value.form.combSort = [];
  }
  getList();
}
//表头重置
const tableRefs = ref();
function resetFilter() {
  tableRefs.value?.resetFilter();
}
//排序重置
function clearSort() {
  tableRefs.value?.clearSort();
}

const reset = () => {
  formRefs.value.resetForm();
};

//form查询
const formRefs = ref();
function getList(val = false) {
  loading.value = true;
  let params = paramsHandle(formRefs.value.form, {
    time: true,
    string: ["workerid"],
    zero: ["amountMin", "amountMax", "intention", "orgId"],
    minus: ["usertype"],
    boolean: ["isAddWechat", "notDial"],
    pageIndex: rePaginationRefs.value.pageIndex,
    pageSize: rePaginationRefs.value.pageSize
  });
  !params.isLock && (params.isLock = false);
  if (params.existTicket === "") {
    params.existTicket = undefined;
  }
  if (params.orgId && params.workerid) {
    params.orgId = undefined;
  }
  if (params.clueStart) {
    params.userExpireStart = params.clueStart;
    params.userExpireEnd = params.clueEnd;
  }
  if (!params.callCount) {
    params.callCount = params.callCount === 0 ? 0 : -1;
  }

  params.familyCategory = getFamilyCategoryData(params.familyCategory);

  getOngingList(params)
    .then(async ({ data }: { data: any }) => {
      if (data.list.length) {
        setIdList(data.list);
        let infoids = data.list.map(item => item.infoUuid);
        data.list = await getLock(infoids, data.list, params.sort);
        const clueList = await setCollectClue(infoids, data.list);
        dataList.value = clueList;
      } else {
        dataList.value = [];
      }
      total.value = data.total;
      val && setCurrent(rowIndex);
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      total.value = 0;
      loading.value = false;
    });
}
function getLock(infoids, list, sort) {
  return findLock({ infoUuids: infoids })
    .then(({ data }: { data: any }) => {
      list.forEach(item => {
        sort === "asc" && (item.lastDial = item.lastDial || ************);
        item.lockStatus = data.some(ele => item.infoUuid === ele.infoUuid);
      });
      return list;
    })
    .catch(() => {
      return list;
    });
}

let dataMemory = ref();
let isModelTransfer = ref<boolean>(false);
let isModelItem = ref<boolean>(false);

const ids = ref();
function transferMore() {
  let cus = tableRefs.value?.handleSelectionChange();
  if (!cus.length) {
    ElMessage.warning("您未选择任何线索");
    return;
  }
  ids.value = cus;
  dataMemory.value = {};
  isModelTransfer.value = true;
}

function openCustomerDrawer(row) {
  dataMemory.value = row;
  isCustomerVisible.value = true;
}

function parantMath({ key, params }) {
  switch (key) {
    case "openCustomerDrawer":
      openCustomerDrawer(params);
      break;
  }
}

const updateCollect = (
  id: number,
  infoUuid: string,
  action: "add" | "delete"
) => {
  dataList.value = updateClueCollect(id, infoUuid, action, dataList.value);
};

//回显上一次点击详情或者申请的用户
function setCurrent(row) {
  if (!row) {
    return;
  }
  let index = dataList.value.findIndex(item => item.id === row.id);
  if (index < 0) {
    return;
  }
  tableRefs.value?.setCurrent(index);
}

let rowIndex = "";
onActivated(() => {
  device !== "mobile" && (rowIndex = tableRefs.value?.getClickRow());
  getList(true);
});

onMounted(async () => {
  if (!useUserStoreHook().intentionList.length) {
    useUserStoreHook().setIntentionList(await intentionMath());
  }
});
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <Search
        ref="formRefs"
        v-model:dataMemory="dataMemory"
        v-model:loading="loading"
        @onSearch="onSearch"
        @resetFitler="resetFilter"
        @clearSort="clearSort"
        @transferMore="transferMore"
        :type="1"
      />
      <div class="g-table-box">
        <ReTable
          v-if="device !== 'mobile'"
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="columns"
          :filterChange="filterChange"
          :sortChange="sortChange"
          :widthOperation="140"
          :selection="
            useUserStoreHook().authorizationMap.indexOf(
              'telesale_admin_custom_transfer'
            ) > -1
          "
          @parantMath="parantMath"
          @filterHeadData="filterHeadData"
        >
          <template #appendColumn>
            <el-table-column fixed="right" label="操作" width="180">
              <template #header>
                <span>操作</span>
                <IconifyIconOffline
                  icon="setting"
                  @click="isModelItem = true"
                  style="
                    cursor: pointer;
                    color: #409eff;
                    font-size: 25px;
                    position: absolute;
                    right: 15px;
                  "
                />
              </template>
              <template #default="scope">
                <Operation
                  :row="scope.row"
                  v-model:loading="loading"
                  @getList="getList"
                  :type="1"
                  @addCollect="updateCollect($event, scope.row.infoUuid, 'add')"
                  @removeCollect="
                    updateCollect($event, scope.row.infoUuid, 'delete')
                  "
                />
              </template>
            </el-table-column>
          </template>
        </ReTable>
        <template v-else>
          <ReCardList
            ref="cardRefs"
            :dataList="dataList"
            :listHeader="listHeader"
            @parantMath="parantMath"
            :isAppendColumn="true"
          >
            <template #appendColumn="scope: { row: any }">
              <Operation
                :row="scope.row"
                v-model:loading="loading"
                @getList="getList"
                :type="1"
                @addCollect="updateCollect($event, scope.row.infoUuid, 'add')"
                @removeCollect="
                  updateCollect($event, scope.row.infoUuid, 'delete')
                "
              />
            </template>
          </ReCardList>
        </template>
      </div>
      <RePagination ref="rePaginationRefs" :total="total" @getList="getList" />
    </el-card>
    <DialogTransfer
      v-model:value="isModelTransfer"
      :msg="dataMemory"
      :id="ids"
      :transferType="true"
      type="转线索"
      @getList="getList"
      v-if="isModelTransfer"
    />
    <CustomerDrawer
      v-if="isCustomerVisible"
      v-model:value="isCustomerVisible"
      :infoUuid="dataMemory.infoUuid"
      :idList="drawerIdList"
    />
    <!-- <TableItemSet
      v-if="isModelItem"
      v-model:value="isModelItem"
      v-model:listHeader="listHeader"
      :activeList="activeList"
      :agent="agent"
      typeName="ongoing"
    /> -->
    <SettingFiled
      localKey="ongoing-column"
      v-model:visible="isModelItem"
      v-model:columns="columns"
      :listHeader="listHeader"
      :hideFiled="hideFiled"
      @success="reset"
    />
  </div>
</template>
