<!--
 * @Date         : 2024-12-23 11:39:55
 * @Description  : 家庭信息详情
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessageBox } from "element-plus";
import {
  FamilyInfo,
  FamilyMembersInfo,
  getFamilyDetailApi,
  delPhoneClueApi,
  updateFamilyPhoneTypeApi
} from "/@/api/customer/familyNumber";
import {
  familyClueColumns,
  familyClueOperation,
  familyColumns,
  familyOperation
} from "./data/index";
import Edit from "/@/views/customer/details/components/Edit.vue";
import {
  familyCategoryListMap,
  holdStatusMap,
  phoneTypeList,
  phoneTypeMap
} from "/@/utils/const/customer";
import OrderList from "/@/views/customer/details/components/tabs/OrderList.vue";
import CallRecord from "/@/components/CallRecord/index.vue";
import DocumentaryRecord from "/@/views/customer/details/components/tabs/DocumentaryRecord.vue";
import AddPhone from "./dialog/AddPhone.vue";
import UploadInfo from "../../verifyBindingFamily/dialog/UploadInfo.vue";
import { useCall } from "/@/hooks/business/useCall";
import FamilyReport from "./components/FamilyReport.vue";
import { useRoute } from "vue-router";
import { useDetail } from "/@/utils/handle/customDetails";
import sendPoint from "/@/utils/handle/point";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";

interface Props {
  value: boolean;
  familyId: string;
  userId?: string;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const route = useRoute();
const { toDetail } = useDetail();
const { userMsg } = storeToRefs(useUserStore());

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const familyMsg = ref<FamilyInfo>({});
const activeName = ref("familyOrder");
const addPhoneModal = ref(false);
const isUploadModal = ref(false);
const rowData = ref();
const callPhone = ref("");

const { CallModal, fastCall } = useCall(callPhone);

function handleClose() {
  isModel.value = false;
}

const getInfo = (point?: boolean) => {
  loading.value = true;
  getFamilyDetailApi({ familyId: props.familyId })
    .then(res => {
      familyMsg.value = res.data;

      if (props.userId && point) {
        const current = res.data.familyMembers.find(
          item => item.userId === props.userId
        );
        res.data.familyClues.forEach(item => {
          sendPoint.h5Post("get_CRMFamilyClues", {
            u_user: props.userId,
            familyId: item.familyId,
            u_phone: item.phone,
            vistiorId: userMsg.value.id + "",
            uuid: current.infoUuid,
            userId: userMsg.value.id + "",
            unionId: current.workerId + ""
          });
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const call = params => {
  callPhone.value = params.phone;
  fastCall(props.familyId);
};

const delClue = params => {
  ElMessageBox.confirm("确认要删除该线索吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    loading.value = true;
    delPhoneClueApi({ id: params.id, phone: params.phone })
      .then(() => {
        ElMessage.success("操作成功");
        getInfo();
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

const updateFamilyPhoneType = (row: FamilyMembersInfo) => {
  loading.value = true;
  updateFamilyPhoneTypeApi({
    familyMemberId: row.id,
    phoneType: row.phoneType
  })
    .then(() => {
      ElMessage.success("操作成功");
    })
    .catch(() => {
      getInfo();
    })
    .finally(() => {
      loading.value = false;
    });
};

const addPhone = () => {
  addPhoneModal.value = true;
};

const upload = (row: any) => {
  rowData.value = row;
  isUploadModal.value = true;
};

const lookDetail = params => {
  console.log("lookDetail");
  console.log(route.name);
  params.back = "-1";
  toDetail(params, route.name, "");
};

const parantMath = ({ key, params }) => {
  switch (key) {
    case "call":
      call(params);
      break;
    case "delete":
      delClue(params);
      break;
    case "lookDetail":
      lookDetail(params);
      break;
  }
};

getInfo(true);
</script>

<template>
  <el-dialog
    title="当前用户所属家庭信息"
    v-model="isModel"
    :before-close="handleClose"
    width="90%"
    top="40px"
    class="family"
  >
    <div v-loading="loading">
      <div v-if="familyMsg.id">
        <el-form label-suffix="：" v-loading="loading" label-width="140px">
          <el-form-item label="家庭ID">
            {{ familyMsg.id }}
          </el-form-item>
          <el-form-item label="家庭类型">
            {{ familyCategoryListMap[familyMsg.category] }}
          </el-form-item>
          <el-form-item
            label="家庭手机号（指疑似与当前家庭相关的手机号线索）"
            label-width="350"
          >
            <span v-if="familyMsg.isHolding" class="c-red">
              有疑似待核实绑定的家庭
            </span>
          </el-form-item>
          <el-form-item label-width="0">
            <ReTable
              :dataList="familyMsg.familyClues"
              :listHeader="familyClueColumns"
              :operation="familyClueOperation"
              :width-operation="130"
              @parant-math="parantMath"
            >
              <template #phoneType="{ row }">
                <div class="flex justify-center">
                  <Edit
                    v-model:value="row.phoneType"
                    :options="phoneTypeList"
                    @submit="updateFamilyPhoneType(row)"
                  >
                    {{ phoneTypeMap[row.phoneType] }}
                  </Edit>
                </div>
              </template>
              <template #holdStatus="{ row }">
                <template v-if="row.holdStatus === 'holding'">
                  <span v-if="row.holdVerifyStatus === 'remainVerify'">
                    <span>占领中</span>
                    <span
                      v-auth="
                        'telesale_admin_custom_verifyBindingFamily_upload_verify'
                      "
                    >
                      ，待
                    </span>
                    <el-button
                      type="primary"
                      link
                      @click="upload(row)"
                      v-auth="
                        'telesale_admin_custom_verifyBindingFamily_upload_verify'
                      "
                    >
                      上传认证
                    </el-button>
                  </span>
                  <span v-if="row.holdVerifyStatus === 'verifying'">
                    待审核
                  </span>
                </template>
                <template v-else>
                  {{ holdStatusMap[row.holdStatus] }}
                </template>
              </template>
            </ReTable>
            <el-button class="mt-10px" type="primary" @click="addPhone">
              添加家庭手机号
            </el-button>
          </el-form-item>
          <el-form-item
            label="家庭下所有用户（指已经确定为当前家庭成员的洋葱用户）"
            label-width="400"
          />
          <ReTable
            :dataList="familyMsg.familyMembers"
            :listHeader="familyColumns"
            :operation="familyOperation"
            :width-operation="130"
            @parant-math="parantMath"
          />
        </el-form>
        <el-tabs v-model="activeName">
          <el-tab-pane label="家庭订单记录" name="familyOrder">
            <OrderList :familyId="familyMsg.id" />
          </el-tab-pane>
          <el-tab-pane label="家庭外呼记录" name="familyCall" lazy>
            <CallRecord method="agent" :familyId="familyMsg.id" />
          </el-tab-pane>
          <el-tab-pane label="家庭跟单记录" name="familyDocumentary" lazy>
            <DocumentaryRecord :familyId="familyMsg.id" :type="2" />
          </el-tab-pane>
          <el-tab-pane label="家庭学情报告" name="report" lazy>
            <FamilyReport :data="familyMsg.familyMembers" />
          </el-tab-pane>
        </el-tabs>
      </div>
      <div v-else class="flex justify-center items-center text-18px h-200px">
        <span v-if="!loading">家庭不在库，无法查看</span>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
  <AddPhone
    v-if="addPhoneModal"
    v-model:value="addPhoneModal"
    :familyId="props.familyId"
    :userId="props.userId"
    @success="getInfo"
  />
  <UploadInfo
    v-if="isUploadModal"
    v-model:value="isUploadModal"
    :holdId="rowData.holdId"
    :holdPhone="rowData.phone"
    @onSearch="getInfo"
  />
  <CallModal />
</template>

<style lang="scss">
.family {
  .el-dialog__body {
    max-height: 75vh !important;
    .el-form-item__label {
      font-weight: bold;
    }
  }
}
</style>
