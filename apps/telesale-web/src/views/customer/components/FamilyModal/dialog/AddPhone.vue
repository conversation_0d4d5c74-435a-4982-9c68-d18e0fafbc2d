<!--
 * @Date         : 2024-12-27 10:54:35
 * @Description  : 新增手机号
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";

import { phoneTypeList } from "/@/utils/const/customer";
import { addPhoneClueApi } from "/@/api/customer/familyNumber";
import { phoneReg } from "/@/utils/common/pattern";

interface Props {
  value: boolean;
  familyId: string;
  userId: string;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "success"): void;
}

const emits = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emits("update:value", val);
  }
});

const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance>();
const form = ref({
  familyId: props.familyId,
  relatedUserId: props.userId,
  phone: undefined,
  phoneType: undefined
});
const rules: FormRules = {
  phone: [
    {
      required: true,
      message: "请输入客户手机号",
      trigger: "change"
    },
    { pattern: phoneReg, message: "请输入正确的手机号", trigger: "blur" }
  ],
  phoneType: [
    {
      required: true,
      message: "请选择手机号类型",
      trigger: "change"
    }
  ]
};
function handleClose() {
  isModel.value = false;
}

const submit = async () => {
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      addPhoneClueApi(form.value)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          handleClose();
          emits("success");
        })
        .catch(err => {
          loading.value = false;
        });
    }
  });
};
</script>

<template>
  <el-dialog
    title="添加家庭手机号"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
  >
    <div v-loading="loading" class="flex justify-center">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="客户手机号" prop="phone">
          <el-input v-model.trim="form.phone" placeholder="请输入客户手机号" />
        </el-form-item>
        <el-form-item label="手机号类型" prop="phoneType">
          <el-select
            v-model="form.phoneType"
            placeholder="请选择手机号类型"
            filterable
          >
            <el-option
              v-for="item in phoneTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
