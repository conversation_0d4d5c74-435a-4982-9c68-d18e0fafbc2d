/*
 * @Date         : 2024-12-23 12:11:49
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { storeToRefs } from "pinia";
import { TableColumns, OperationObj } from "/@/components/ReTable/types";
import {
  contactStateMap,
  phoneTypeMap,
  studyInfoBindListMap
} from "/@/utils/const/customer";
import { useUserStore } from "/@/store/modules/user";
import { getAuth } from "/@/utils/auth";

const { allAgentObj } = storeToRefs(useUserStore());

export const familyClueColumns: TableColumns[] = [
  {
    field: "phone",
    desc: "家庭手机号",
    minWidth: 100,
    isCopy: true
  },
  {
    field: "phoneType",
    desc: "手机号类型",
    minWidth: 200,
    slot: {
      name: "phoneType"
    }
  },
  {
    field: "isBind",
    desc: "是否绑定学情",
    customRender: ({ text, row }) => {
      return text
        ? `是，已绑定${row.relatedUserPhone || row.relatedUserOnionId}`
        : "否";
    }
  },
  {
    field: "holdStatus",
    desc: "合并状态",
    minWidth: 150,
    slot: {
      name: "holdStatus"
    }
  }
];

export const familyClueOperation: OperationObj[] = [
  {
    text: "一键外呼",
    event: "call",
    isShow: () => getAuth("telesale_admin_custom_verifyBindingFamily_call")
  },
  {
    text: "删除",
    event: "delete"
  }
];

export const familyColumns: TableColumns[] = [
  {
    field: "onionId",
    desc: "洋葱ID"
  },
  {
    field: "phone",
    desc: "手机号"
  },
  {
    field: "phoneType",
    desc: "手机号类型",
    customRender: ({ text }) => {
      return phoneTypeMap[text];
    }
  },
  {
    field: "studyInfoBindedType",
    desc: "学情被绑定状态",
    customRender: ({ text }) => {
      return studyInfoBindListMap[text];
    }
  },
  {
    field: "contactState",
    desc: "是否已联系",
    customRender: ({ text }) => {
      return contactStateMap[text];
    }
  },
  {
    field: "isDeal",
    desc: "是否已成交",
    customRender: ({ text }) => {
      return text ? "已成交" : "未成交";
    }
  },
  {
    field: "workerId",
    desc: "所属坐席",
    customRender: ({ text }) => {
      return allAgentObj.value[text]?.name;
    }
  },
  {
    field: "userExpire",
    desc: "线索到期时间",
    timeChange: 3
  }
];

export const familyOperation: OperationObj[] = [
  {
    text: "查看详情",
    event: "lookDetail"
  }
];
