<!--
 * @Date         : 2024-12-31 16:12:47
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { FamilyMembersInfo } from "/@/api/customer/familyNumber";
import Report from "/@/views/customer/details/components/tabs/report.vue";

const props = defineProps<{
  data: FamilyMembersInfo[];
}>();
const active = ref(props.data[0]?.id);
</script>

<template>
  <div>
    <el-tabs v-model="active">
      <el-tab-pane
        v-for="item in props.data"
        :key="item.id"
        :label="item.onionId"
        :name="item.id"
        lazy
      >
        <div class="flex justify-center">
          <Report :userId="item.userId" v-if="active === item.id" />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="scss" scoped></style>
