<!--
 * @Date         : 2025-04-08 13:44:35
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { detail, region } from "/@/api/customerDetails";
import OperationButton from "./components/OperationButton.vue";
import InfoTabs from "./components/InfoTabs.vue";
import BasisInfo from "./components/BasisInfo.vue";

interface Props {
  value: boolean;
  infoUuid: string;
  idList: {
    infoUuid: string;
    onionId: string;
  }[];
}

interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "success"): void;
}

const emits = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isDrawer = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emits("update:value", val);
  }
});

const loading = ref(false);
const InfoTabsRef = ref<InstanceType<typeof InfoTabs>>();
const customMsg = ref();
const onionId = ref();
const currentIndex = ref(
  props.idList.findIndex(item => item.infoUuid === props.infoUuid)
); // 当前条目的索引

const getData = () => {
  loading.value = true;
  const current = props.idList[currentIndex.value];
  onionId.value = current.onionId;
  customMsg.value = null;
  detail({ infoUuid: current.infoUuid })
    .then(res => {
      console.log(res);
      customMsg.value = res.data;
      getRegion();
    })
    .finally(() => {
      loading.value = false;
    });
};

const getRegion = () => {
  region({ userid: customMsg.value.userid }).then(({ data }) => {
    customMsg.value.region =
      typeof data === "string"
        ? data
        : data.province.name + data.city.name + data.district.name;
  });
};

const pre = () => {
  console.log("上一条", props.idList);
  if (currentIndex.value > 0) {
    currentIndex.value--;
    const item = props.idList[currentIndex.value];
    onionId.value = item.onionId;
    getData();
  }
};

const next = () => {
  console.log("下一条");
  if (currentIndex.value < props.idList.length - 1) {
    currentIndex.value++;
    const item = props.idList[currentIndex.value];
    onionId.value = item.onionId;
    getData();
  }
};

const resetPushRecord = () => {
  InfoTabsRef.value?.resetPushRecord();
};

getData();
</script>

<template>
  <div v-loading="loading">
    <el-drawer v-model="isDrawer" close-on-click-modal size="60%">
      <template #header>
        <div>
          <span class="mr-20px">洋葱ID：{{ onionId }}</span>
          <el-button
            v-if="currentIndex !== 0"
            type="primary"
            plain
            @click="pre"
            :loading="loading"
          >
            上一条
          </el-button>
          <el-button
            v-if="currentIndex < props.idList.length - 1"
            type="primary"
            plain
            @click="next"
            :loading="loading"
          >
            下一条
          </el-button>
        </div>
      </template>
      <div class="min-h-300px" v-loading="loading">
        <div v-if="customMsg">
          <OperationButton
            :customMsg="customMsg"
            @resetPushRecord="resetPushRecord"
          />
          <BasisInfo :customMsg="customMsg" />
          <InfoTabs ref="InfoTabsRef" class="mt-20px" :customMsg="customMsg" />
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<style scoped></style>
