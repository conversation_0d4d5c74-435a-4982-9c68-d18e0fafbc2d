<!--
 * @Date         : 2025-04-08 15:59:08
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import PushPay from "/@/views/customer/details/dialog/PushPay.vue";
import SendMsg from "/@/views/customer/details/dialog/SendMsg.vue";
import Call from "/@/views/customer/details/components/Call.vue";
import { getDiscoveryPageAuthApi } from "/@/api/customer/details";

const props = defineProps<{
  customMsg: any;
}>();

const emits = defineEmits<{
  (e: "resetPushRecord"): void;
}>();

const isModelSend = ref(false);
const isModelPush = ref(false);
const pushDiscover = ref(false);

const getCard = () => {
  getDiscoveryPageAuthApi({ userId: props.customMsg.userid }).then(res => {
    pushDiscover.value = res.data.hasAuth;
  });
};

const openSendMsg = () => {
  isModelSend.value = true;
};

const openPush = () => {
  isModelPush.value = true;
};

const resetPushRecord = () => {
  emits("resetPushRecord");
};

getCard();
</script>

<template>
  <div class="flex justify-center mb-20px" v-if="props.customMsg">
    <el-button plain @click="openPush" v-if="customMsg?.userid">
      支付推送
    </el-button>
    <Call
      ref="callRefs"
      :customMsg="customMsg"
      type="list"
      @closePage="resetPushRecord"
    />
    <el-button type="primary" plain @click="openSendMsg">邀约短信</el-button>

    <SendMsg
      v-if="isModelSend"
      v-model:value="isModelSend"
      :customMsg="customMsg"
    />

    <PushPay
      v-if="isModelPush"
      v-model:value="isModelPush"
      :onionid="customMsg?.onionid"
      :userid="customMsg?.userid"
      :phone="customMsg?.phone"
      :stage="customMsg?.stage"
      :grade="customMsg?.grade"
      :pushDiscover="pushDiscover"
    />
  </div>
</template>

<style scoped></style>
