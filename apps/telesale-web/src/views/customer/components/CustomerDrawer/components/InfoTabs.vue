<!--
 * @Date         : 2025-04-08 16:19:21
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import DocumentaryRecord from "/@/views/customer/details/components/tabs/DocumentaryRecord.vue";
import CallRecord from "/@/components/CallRecord/index.vue";
import Report from "/@/views/customer/details/components/tabs/report.vue";
import OrderList from "/@/views/customer/details/components/tabs/OrderList.vue";

const props = defineProps<{
  customMsg: any;
}>();

const tabName = ref("learnReport");
const documentaryRecordRef = ref<InstanceType<typeof DocumentaryRecord>>();

defineExpose({
  resetPushRecord: () => documentaryRecordRef.value?.resetPushRecord()
});
</script>

<template>
  <div>
    <el-tabs type="border-card" v-model="tabName">
      <el-tab-pane label="学情报告" name="learnReport" lazy>
        <div class="flex justify-center">
          <Report :userId="props.customMsg.userid" />
        </div>
      </el-tab-pane>
      <el-tab-pane label="订单记录" name="orderRecord" lazy>
        <OrderList :userid="props.customMsg?.userid" />
      </el-tab-pane>
      <el-tab-pane label="跟单记录" name="documentaryRecord" lazy>
        <DocumentaryRecord
          ref="documentaryRecordRef"
          :infoUuid="props.customMsg?.infoUuid"
          :type="1"
        />
      </el-tab-pane>
      <el-tab-pane label="呼叫记录" name="callRecord" lazy>
        <CallRecord
          method="agent"
          from-call
          :phone="props.customMsg.phone"
          :workerId="props.customMsg.workerid"
          :infoUuid="props.customMsg?.infoUuid"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped></style>
