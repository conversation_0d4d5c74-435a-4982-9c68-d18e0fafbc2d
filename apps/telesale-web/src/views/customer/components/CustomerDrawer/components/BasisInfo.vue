<!--
 * @Date         : 2025-04-08 16:23:51
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { clueSource } from "@telesale/shared/src/data/customer";
import customerTypeList from "/@/utils/data/customerTypeList";
import typeChange from "/@/utils/handle/typeChange";
import RegionSchoolModal from "/@/views/customer/details/dialog/RegionSchoolModal.vue";

const props = defineProps<{
  customMsg: any;
}>();

const schoolModal = ref(false);

const openSchoolModal = () => {
  schoolModal.value = true;
};
</script>

<template>
  <el-descriptions :border="true" :column="4">
    <el-descriptions-item label="用户手机号">
      {{ props.customMsg.phone }}
    </el-descriptions-item>
    <el-descriptions-item label="年级">
      {{ props.customMsg.grade || "-" }}
    </el-descriptions-item>
    <el-descriptions-item label="客户类型">
      {{ typeChange(props.customMsg.usertype, customerTypeList) }}
    </el-descriptions-item>
    <el-descriptions-item label="来源">
      {{ clueSource[props.customMsg.source] }}
    </el-descriptions-item>
    <el-descriptions-item label="所在地">
      <div class="flex justify-between">
        <span>{{ props.customMsg.region }}</span>
        <!-- <span class="d-think-show" v-if="isNewQuestions">新题型</span> -->
        <el-button
          type="primary"
          link
          @click="openSchoolModal"
          v-if="props.customMsg?.region && props.customMsg?.city"
        >
          查看地区合作信息
        </el-button>
      </div>
    </el-descriptions-item>
  </el-descriptions>
  <RegionSchoolModal
    v-if="schoolModal"
    v-model:value="schoolModal"
    :cityInfo="[
      props.customMsg?.provinceCode,
      { code: props.customMsg?.cityCode, name: props.customMsg?.city }
    ]"
  />
</template>

<style scoped></style>
