<!--
 * @Date         : 2025-03-14 17:15:56
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { useLocalStorage } from "@vueuse/core";
import Menu from "./Menu.vue";
import HamBurger from "/@/layout/components/sidebar/hamBurger.vue";
import { useRoute } from "vue-router";

// 是否折叠
const route = useRoute();

const isCollapse = useLocalStorage(route.path, true);

const toggleSideBar = () => {
  isCollapse.value = !isCollapse.value;
};
</script>

<template>
  <div class="relative">
    <div class="cursor-pointer absolute top--10px left--15px z-100">
      <el-button
        circle
        size="default"
        @click="toggleSideBar"
        v-if="isCollapse"
        class="w-36px! h-36px!"
      >
        <HamBurger :is-active="isCollapse" class="p-0!" />
      </el-button>
      <el-popover placement="bottom" :width="250" trigger="hover" v-else>
        <template #reference>
          <el-button
            circle
            size="default"
            @click="toggleSideBar"
            class="w-36px! h-36px!"
          >
            <HamBurger :is-active="isCollapse" class="p-0px!" />
          </el-button>
        </template>
        <Menu
          class="min-w-220px mr-10px sticky top-0 left-0 max-h-[calc(100vh-280px)] overflow-y-auto"
          v-bind="$attrs as any"
          :isCollapse="isCollapse"
        />
      </el-popover>
    </div>
    <Menu
      v-if="isCollapse"
      class="min-w-220px mr-10px sticky top-0 left-0 h-[calc(100vh-150px)]! overflow-auto"
      v-bind="$attrs as any"
      :isCollapse="isCollapse"
    />
  </div>
</template>

<style scoped></style>
