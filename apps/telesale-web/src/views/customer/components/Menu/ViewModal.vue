<!--
 * @Date         : 2024-11-08 14:30:19
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { FormInstance, FormRules } from "element-plus";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import {
  createViewApi,
  getViewApi,
  updateViewApi,
  ViewData,
  ViewInfo
} from "/@/api/common/view";

interface Props {
  value: boolean;
  id?: number;
  belong: string;
  viewData: ViewData;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "success", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const { jobAllAgentList, userMsg } = storeToRefs(useUserStore());

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref(false);
const form = ref<ViewInfo>({
  id: props.id,
  belong: props.belong,
  viewType: "public",
  viewName: undefined,
  member: [userMsg.value.id],
  viewData: undefined
});
const ruleFormRef = ref<FormInstance>();
const rules: FormRules = {
  viewName: [
    {
      required: true,
      message: "请输入页面名称",
      trigger: "change"
    }
  ],
  member: [
    {
      required: true,
      message: "请选择视图管理者",
      trigger: "change"
    }
  ]
};

const handleClose = () => {
  isModel.value = false;
};

const getData = () => {
  if (props.id) {
    loading.value = true;
    getViewApi({ id: props.id, lockId: true })
      .then(res => {
        form.value = res.data;
      })
      .catch(err => {
        if (err.response.data === "视图已被删除") {
          emit("success", true);
          handleClose();
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }
};

const submit = () => {
  ruleFormRef.value.validate((valid: boolean) => {
    if (valid) {
      loading.value = true;
      const fn = props?.id ? updateViewApi : createViewApi;
      if (form.value.viewData !== JSON.stringify(props.viewData)) {
        form.value.viewData = JSON.stringify(props.viewData);
      }
      fn(form.value)
        .then(() => {
          ElMessage.success("操作成功");
          emit("success", false);
          handleClose();
        })
        .catch((err: any) => {
          if (err.response.data === "视图已被删除") {
            emit("success", true);
            handleClose();
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

getData();
</script>

<template>
  <el-dialog
    :title="props.id ? '编辑视图' : '另存为视图'"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
    append-to-body
  >
    <div v-loading="loading">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        :rules="rules"
        :label-width="120"
      >
        <el-row>
          <el-col :lg="4" />
          <el-col :lg="16">
            <el-form-item label="页面名称" prop="viewName">
              <el-input
                v-model.trim="form.viewName"
                placeholder="请输入页面名称"
                maxlength="15"
                show-word-limit
                style="width: 300px"
                clearable
              />
            </el-form-item>
            <el-form-item label="视图管理者" prop="member">
              <el-select-v2
                v-model="form.member"
                filterable
                clearable
                multiple
                :options="jobAllAgentList"
                placeholder="请选择人员"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :lg="4" />
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
