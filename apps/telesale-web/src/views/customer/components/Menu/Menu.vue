<!--
 * @Date         : 2024-11-08 11:38:33
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import ViewModal from "/@/views/customer/components/Menu/ViewModal.vue";
import { ElMessage, ElMessageBox } from "element-plus";

import CategoryMenu from "/@/components/CategoryMenu/index.vue";
import { deleteViewApi, ViewData, ViewList } from "/@/api/common/view";

const props = defineProps<{
  currentId?: number;
  dataList: ViewList[];
  isCollapse?: boolean;
}>();
const emits = defineEmits(["update:currentId", "success"]);

const currentId = computed({
  get() {
    return props.currentId;
  },
  set(val: number) {
    emits("update:currentId", val);
  }
});
const viewData = inject("viewData") as Ref<ViewData>;
const loading = ref(false);
const isModal = ref(false);
const rowId = ref<number>();

const edit = (item: any) => {
  rowId.value = item.id;
  isModal.value = true;
};

const del = (item: any) => {
  ElMessageBox.confirm(
    "该视图将彻底删除，其他用户无法访问。确认删除？",
    "确认删除该视图吗？",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  ).then(() => {
    loading.value = true;
    deleteViewApi({ id: item.id })
      .then(() => {
        getData(currentId.value === item.id);
        ElMessage.success("移除成功");
      })
      .catch((err: any) => {
        if (err.response.data === "视图已被删除") {
          emits("success", true);
        }
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

const getData = (val: boolean) => {
  emits("success", val);
};
</script>

<template>
  <div class="h-full">
    <el-card :class="['overflow-auto', props.isCollapse ? 'min-h-full' : '']">
      <CategoryMenu
        v-model:currentId="currentId"
        :dataList="dataList"
        name="viewName"
        :loading="loading"
        :isCollapse="props.isCollapse"
        :dropdownOptions="{
          isShow: row => row.hasPermission,
          columns: [
            {
              label: '编辑视图信息',
              onClick: edit,
              isShow: row => {
                return row.hasPermission;
              }
            },
            {
              label: '删除视图',
              onClick: del,
              isShow: row => {
                return row.hasPermission && row.viewType !== 'default';
              }
            }
          ]
        }"
      />
    </el-card>
    <ViewModal
      v-if="isModal"
      v-model:value="isModal"
      :id="rowId"
      belong="客户池"
      :viewData="viewData"
      @success="getData"
    />
  </div>
</template>

<style lang="scss" scoped>
.card {
  padding: 10px;
  border-radius: 5px;
  background: #fff;
  margin-bottom: 10px;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, 0.12);
  border: 1px solid #e8e8e8;
}
</style>
