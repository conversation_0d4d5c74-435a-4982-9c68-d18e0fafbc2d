<!--
 * @Date         : 2024-03-27 11:37:50
 * @Description  : 阿拉丁转介绍
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref, reactive, nextTick } from "vue";
import Download from "/@/views/order/order/dialog/Download.vue";
import { CarouselInstance, ElMessage } from "element-plus";
import { onionIdReg, phoneReg } from "/@/utils/common/pattern";
import { getPhoneInfoApi } from "/@/api/customer/beyond";
import {
  getAladdinPosterApi,
  getPosterQualityApi
} from "/@/api/active/referral";
import { PhoneInfoRes } from "/@/types/customer/beyond";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import downloadFile from "/@/utils/handle/downloadFile";

const { userMsg } = storeToRefs(useUserStore());
const loading = ref<boolean>(false);
const form = reactive({
  phone: "",
  type: 2
});
const tip = ref<string>("不符合转介绍条件");
const info = ref<Partial<PhoneInfoRes>>({
  workerid: undefined,
  phone: undefined,
  onionid: undefined
});
const picUrl = ref([]);
const isModelDownload = ref<boolean>(false);
const dataMemory = ref<any>();
const carouselRef = ref<CarouselInstance>();
const openDialog = () => {
  isModelDownload.value = true;
  picUrl.value = [];
  nextTick(() => {
    downloadMath();
  });
};

const downloadUrl = (url: string) => {
  downloadFile(url, true);
};

const changeIndex = (type: string) => {
  carouselRef.value?.[type]();
};

const downloadMath = () => {
  loading.value = true;
  let params = {
    promotionIds: dataMemory.value?.picMsg?.list.map(item => item.promotionId),
    workerId: userMsg.value.id,
    userId: dataMemory.value.userid,
    phone: dataMemory.value.phone
  };
  getAladdinPosterApi(params)
    .then(({ data }) => {
      picUrl.value = data.list;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
};

const isJoinAct = params => {
  loading.value = true;
  getPosterQualityApi({ userId: params.userid })
    .then(({ data }: { data: any }) => {
      dataMemory.value = { picMsg: data, ...params };
      info.value.workerid = params.workerid;
      info.value.phone = params.phone;
      info.value.onionid = params.onionid;
    })
    .catch(err => {
      tip.value = err.response.data.message;
      info.value = {};
    })
    .finally(() => {
      loading.value = false;
    });
};

const close = () => {
  isModelDownload.value = false;
};
const onSearch = () => {
  if (form.type === 1 && !onionIdReg.test(form.phone)) {
    ElMessage.warning("洋葱ID格式有误");
    return;
  }
  if (form.type === 2 && !phoneReg.test(form.phone)) {
    ElMessage.warning("手机号格式有误");
    return;
  }
  loading.value = true;
  tip.value = "不符合转介绍条件";
  getPhoneInfoApi({ [form.type === 1 ? "onionId" : "phone"]: form.phone })
    .then(res => {
      if (res.data.userid) {
        isJoinAct(res.data);
      } else {
        info.value = {};
      }
    })
    .catch(() => {
      info.value = {};
    })
    .finally(() => {
      loading.value = false;
    });
};
</script>

<template>
  <div class="d_container">
    <div class="d_box" v-loading="loading">
      <el-form class="d-form" :inline="true" :model="form" @submit.prevent>
        <el-form-item prop="phone">
          <el-select v-model="form.type">
            <el-option label="洋葱ID" :value="1" />
            <el-option label="手机号" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-input
            v-model="form.phone"
            :placeholder="
              form.type === 1 ? '请输入客户洋葱ID' : '请输入客户手机号'
            "
            clearable
            @keyup.enter="onSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
      </el-form>
      <div class="text" v-if="info?.workerid">
        <div>洋葱ID：{{ info?.onionid }}</div>
        <div>手机号码：{{ info?.phone }}</div>
        <div>
          下载海报：
          <el-button
            type="primary"
            link
            @click="openDialog"
            style="font-size: 20px"
          >
            <IconifyIconOffline icon="download" style="font-size: 25px" />
            下载
          </el-button>
        </div>
      </div>
      <div class="text d-not-fit" v-else>{{ tip }}</div>
    </div>
    <el-dialog
      title="下载海报"
      v-model="isModelDownload"
      center
      destroy-on-close
      :before-close="close"
    >
      <div class="flex flex-col items-center" v-loading="loading">
        <template v-if="dataMemory?.picMsg?.list?.length > 0">
          <div class="flex items-center">
            <div
              class="carousel-button mr-10px"
              @click="changeIndex('prev')"
              v-if="picUrl.length > 1"
            >
              <IconifyIconOffline icon="arrow-left" class="text-30px" />
            </div>
            <el-carousel
              ref="carouselRef"
              arrow="never"
              indicator-position="none"
              :autoplay="false"
              height="550px"
              class="w-300px"
            >
              <el-carousel-item
                v-for="(item, i) in picUrl"
                :key="i"
                class="h-550px"
              >
                <img
                  style="width: 300px; height: 500px"
                  :src="item.url"
                  alt="海报"
                />
                <div class="text-center mt-10px">
                  {{ item.posterName || "" }}
                  <el-button
                    link
                    type="primary"
                    :icon="useRenderIcon('download')"
                    @click="downloadUrl(item.url)"
                  >
                    下载
                  </el-button>
                </div>
              </el-carousel-item>
            </el-carousel>
            <div
              class="carousel-button ml-10px"
              @click="changeIndex('next')"
              v-if="picUrl.length > 1"
            >
              <IconifyIconOffline icon="arrow-right" class="text-30px" />
            </div>
          </div>
        </template>
        <div v-else style="margin: 50px 0">暂无上架的转介绍活动哦！</div>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped lang="scss">
:deep(.el-select .el-input) {
  width: 100px;
}
.d_container {
  padding: 0 0 20px;
}
.d-tip-box {
  background-color: #eaf5ff;
  font-weight: bold;
  padding: 10px 20px;
  margin-bottom: 20px;
  font-size: 15px;
  line-height: 1.7;
}
.d_box {
  margin: 0 auto;
  max-width: 500px;
  height: 320px;
  border-radius: 16px;
  background-color: #3d3e49;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.7), 0 0 20px rgba(0, 0, 0, 0.3) inset;
  position: relative;
  padding: 20px;
  text-align: center;

  .d-form {
    padding-top: 10px;
    border-bottom: 1px dashed #ccc;
    margin-bottom: 20px;
  }

  .text {
    color: #fff;
    font-size: 20px;
    margin-top: 35px;
    &.d-not-fit {
      margin-top: 75px;
    }

    > div {
      text-align: left;
      width: 260px;
      margin: 0 auto 10px;
    }
  }
}
.carousel-button {
  background-color: #3d3e49;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
}
</style>
