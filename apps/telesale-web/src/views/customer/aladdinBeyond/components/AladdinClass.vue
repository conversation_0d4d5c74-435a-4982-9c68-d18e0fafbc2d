<!--
 * @Date         : 2024-05-20 14:46:47
 * @Description  : 阿拉丁课程激活
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script setup lang="ts">
import { ref, reactive, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { phoneReg } from "/@/utils/common/pattern";
import {
  getBindOrderApi,
  bindOrderApi,
  OrderInfo
} from "/@/api/customer/beyond";

const loading = ref<boolean>(false);
const form = reactive({
  phone: ""
});
const tip = ref<string>("暂无可激活的课程");
const info = ref<OrderInfo>();

const getClass = () => {
  if (!phoneReg.test(form.phone)) {
    ElMessage.warning("手机号格式有误");
    return;
  }
  loading.value = true;
  tip.value = "暂无可激活的课程";
  getBindOrderApi({ phone: form.phone })
    .then(({ data }) => {
      info.value = data;
    })
    .catch(err => {
      tip.value = err.response.data.message;
      info.value = {
        orderId: "",
        userId: "",
        goodName: ""
      };
    })
    .finally(() => {
      loading.value = false;
    });
};

const bind = () => {
  loading.value = true;
  bindOrderApi({
    userId: info.value.userId,
    orderId: info.value.orderId
  })
    .then(() => {
      ElMessage.success("激活成功");
      getClass();
    })
    .finally(() => {
      loading.value = false;
    });
};
</script>

<template>
  <div class="d_container">
    <div class="d_box" v-loading="loading">
      <el-form class="d-form" :inline="true" :model="form" @submit.prevent>
        <el-form-item prop="phone">
          <el-input
            v-model="form.phone"
            placeholder="请输入客户手机号"
            clearable
            @keyup.enter="getClass"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getClass">查询</el-button>
        </el-form-item>
      </el-form>

      <div
        class="text flex flex-col items-center justify-center"
        v-if="info?.orderId"
      >
        <div>开通权益的商品名称是：{{ info?.goodName }}</div>
        <div class="mt-20px">
          <el-button type="primary" @click="bind"> 确认激活 </el-button>
        </div>
      </div>
      <div class="text d-not-fit" v-else>{{ tip }}</div>
    </div>
  </div>
</template>
<style scoped lang="scss">
:deep(.el-select .el-input) {
  width: 100px;
}
.d_container {
  padding: 0 0 20px;
}
.d-tip-box {
  background-color: #eaf5ff;
  font-weight: bold;
  padding: 10px 20px;
  margin-bottom: 20px;
  font-size: 15px;
  line-height: 1.7;
}
.d_box {
  margin: 0 auto;
  max-width: 500px;
  height: 320px;
  border-radius: 16px;
  background-color: #3d3e49;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.7), 0 0 20px rgba(0, 0, 0, 0.3) inset;
  position: relative;
  padding: 20px;
  text-align: center;

  .d-form {
    padding-top: 10px;
    border-bottom: 1px dashed #ccc;
    margin-bottom: 20px;
  }

  .text {
    color: #fff;
    font-size: 20px;
    margin-top: 35px;
    &.d-not-fit {
      margin-top: 75px;
    }

    > div {
      text-align: left;
    }
  }
}
</style>
