<!--
 * @Date         : 2024-09-03 13:44:35
 * @Description  : 阿拉丁线索查询
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup name="aladdinBeyond">
import { ref } from "vue";
import { getAuth } from "/@/utils/auth";
import AladdinTransfer from "./components/AladdinTransfer.vue";
import AladdinClass from "./components/AladdinClass.vue";

const activeStage = ref("1");
</script>

<template>
  <div class="g-margin-20">
    <el-card>
      <el-tabs type="card" v-model="activeStage" class="mt-20px">
        <el-tab-pane
          label="阿拉丁转介绍"
          name="1"
          v-if="getAuth('telesale_admin_custom_beyond_aladdinTransfer')"
        >
          <AladdinTransfer />
        </el-tab-pane>
        <el-tab-pane
          label="阿拉丁课程激活"
          name="2"
          v-if="getAuth('telesale_admin_custom_beyond_aladdinClass')"
        >
          <AladdinClass />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
