<!--
 * @Date         : 2024-11-14 14:55:23
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->
<template>
  <el-dialog
    v-model="visible"
    close-on-click-modal
    width="40%"
    title="导入"
    @open="downloadUrl = ''"
  >
    <div v-loading="loading" class="px-20px">
      <div class="mb-20px">
        <div class="mb-10px">1、请根据模板格式准备需要导入的数据</div>
        <el-link
          type="primary"
          :underline="false"
          class="ml-20px"
          @click="downloadTpl"
        >
          下载导入模板
        </el-link>
      </div>

      <div>
        <div class="mb-10px">2、请选择需要导入的文件</div>
        <div class="ml-20px">
          <el-upload
            ref="fileRef"
            :limit="1"
            :accept="props.accept"
            :on-exceed="handleExceed"
            :on-change="changePhoto"
            :auto-upload="false"
            @handle-remove="() => (fileUrl = '')"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #file="{ file }">
              <div class="flex justify-between items-center">
                <span>{{ file.name }}</span>
                <el-icon v-if="updateLoading" class="is-loading">
                  <i-ep-Loading />
                </el-icon>
                <el-icon v-else color="#67C23A"><i-ep-SuccessFilled /></el-icon>
              </div>
            </template>
            <template #tip>
              <div class="el-upload__tip text-red">
                支持{{ props.accept.slice(1) }}文件，文件大小不大于10M
              </div>
            </template>
          </el-upload>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          :disabled="fileUrl === ''"
          @click="importHandle"
        >
          导入
        </el-button>
      </span>
    </template>
  </el-dialog>

  <el-dialog v-model="importLoseVisible" width="40%" title="导入失败">
    <div>
      <div class="mb-10px">
        导入失败{{ downloadLostCount }}条，详细原因可下载失败原因文档查看
      </div>
      <div>
        <el-button type="primary" @click="downloadLost">下载失败原因</el-button>
      </div>
    </div>

    <template #footer>
      <el-button @click="importLoseVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import type { UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { genFileId } from "element-plus";
import { defineModel } from "vue";
import { importAdmissionScore } from "/@/api/AISupport/ScoreLine";
import {
  uploadFile as uploadFileApi,
  downloadFile as downloadFileApi
} from "/@/api/file";

const props = withDefaults(
  defineProps<{
    tplLink: string;
    accept?: string;
  }>(),
  {
    accept: ".csv"
  }
);
const emits = defineEmits(["success"]);
const visible = defineModel<boolean>("visible");

const importLoseVisible = ref(false);

const updateQuestionList = inject(
  "updateQuestionList",
  ref(() => {})
);
const loading = ref(false);

const fileRef = ref();
const fileUrl = ref("");
const handleExceed: UploadProps["onExceed"] = files => {
  fileRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  fileRef.value!.handleStart(file);
};

const updateLoading = ref(false);
async function changePhoto(uploadFile) {
  const raw = uploadFile.raw;

  if (raw.size / 1024 / 1024 > 10) {
    ElMessage.error("文件大小不大于10M");
    return false;
  }

  updateLoading.value = true;
  const res: any = await uploadFileApi(raw);
  fileUrl.value = res.data;
  updateLoading.value = false;
}

function downloadTpl() {
  window.open(props.tplLink);
}

const downloadUrl = ref("");
const downloadLostCount = ref(0);
async function importHandle() {
  if (!fileUrl.value) {
    ElMessage.error("请上传导入文件");
    return;
  }

  loading.value = true;

  const taskId: any = await importAdmissionScore({
    fileUrl: fileUrl.value
  });
  downloadUrl.value = await downloadFile(taskId.data.taskId);
  visible.value = false;

  const count = downloadUrl.value.match(/-(\d+)条-/)![1];
  if (downloadUrl.value.indexOf("导入失败") !== -1) {
    downloadLostCount.value = Number(count);
    importLoseVisible.value = true;
  } else {
    ElMessage.success(`成功导入${count}条`);
    emits("success");
  }

  fileUrl.value = "";
  loading.value = false;
  fileRef.value!.clearFiles();
  updateQuestionList.value();
}

function downloadLost() {
  window.open(downloadUrl.value);
}

function downloadFile(taskId): any {
  return new Promise((resolve, reject) => {
    async function poll() {
      const res = await downloadFileApi({
        taskId: taskId
      });

      if (!res.data) {
        setTimeout(async () => {
          await poll();
        }, 1000);
      } else {
        console.log(res.data);
        resolve(res.data);
      }
    }

    poll();
  });
}
</script>

<style scoped lang="scss"></style>
