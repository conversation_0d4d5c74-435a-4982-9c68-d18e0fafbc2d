<!--
 * @Date         : 2024-11-13 16:47:36
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="新增分数线"
    close-on-click-modal
    width="900px"
    @open="openDialog"
  >
    <NexusForm v-model="form" labelWidth="150px" ref="FormRef">
      <el-form-item
        label="所属城市"
        prop="address"
        :rules="{
          required: true,
          message: '请选择所属城市',
          trigger: 'change'
        }"
      >
        <el-cascader
          ref="SchoolCascader"
          v-model="form.address"
          placeholder="请选择所属城市"
          :props="{
            value: 'code',
            label: 'name'
          }"
          :options="addressList"
          filterable
          separator=" - "
        />
      </el-form-item>

      <el-form-item
        label="学校名称"
        prop="name"
        :rules="{
          required: true,
          message: '请输入学校名称',
          trigger: 'change'
        }"
      >
        <el-input
          v-model="form.name"
          maxlength="15"
          show-word-limit
          class="w-300px!"
        />
      </el-form-item>

      <el-form-item
        label="24年中考分数线"
        prop="score"
        :rules="{
          required: true,
          message: '请输入24年中考分数线',
          trigger: 'change'
        }"
      >
        <el-input maxlength="5" v-model="form.score" show-word-limit />
      </el-form-item>

      <el-form-item label="信息来源" prop="origin">
        <TextEditor v-model:valueHtml="form.origin" />
      </el-form-item>
    </NexusForm>

    <template #footer>
      <el-button type="danger" @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import NexusForm from "/@/views/AISupport/components/NexusForm/index.vue";
import { defineModel } from "vue";
import { getAddress } from "/@/api/system";
import removeSecondLevelChildren from "../script/removeSecondLevelChildren";
import TextEditor from "/@/components/TextEditor/index.vue";
import {
  createAdmissionScore,
  updateAdmissionScore
} from "/@/api/AISupport/ScoreLine";

const visible = defineModel<boolean>("visible");
const props = defineProps<{
  editInfo: any;
  tableRef: any;
}>();

const FormRef = ref();
const SchoolCascader = ref();
const addressList = ref([]);

const form = reactive({
  address: [],
  name: "",
  score: "",
  origin: ""
});

async function confirm() {
  await FormRef.value.validate();

  const param = {
    regionCode: form.address[1],
    schoolName: form.name,
    score: form.score,
    dataSource: form.origin,
    year: "2014",
    city: SchoolCascader.value?.cascaderPanelRef?.getCheckedNodes()?.[0]?.label
  };

  if (props.editInfo) {
    await updateAdmissionScore({
      ...param,
      id: props.editInfo.id
    });
    ElMessage.success("更新成功");
  } else {
    await createAdmissionScore(param);
    ElMessage.success("添加成功");
  }

  visible.value = false;
  props.tableRef.update();
}

onMounted(async () => {
  const res: any = await getAddress();
  addressList.value = removeSecondLevelChildren(res.data);
});

function openDialog() {
  if (props.editInfo) {
    form.address = findChildPathByCode(
      addressList.value,
      props.editInfo.regionCode
    );
    form.name = props.editInfo.schoolName;
    form.score = props.editInfo.score;
    form.origin = props.editInfo.dataSource;
  } else {
    form.address = [];
    form.name = "";
    form.score = "";
    form.origin = "";
  }
}

function findChildPathByCode(dataArray, targetCode) {
  // 遍历 RootObject 数组
  for (const root of dataArray) {
    // 遍历每个 RootObject 的 children
    for (const child of root.children) {
      // 遍历 Child 的 children
      if (child.code === targetCode) {
        // 如果找到匹配的 code，返回 Child.code 和 Child.code
        return [root.code, child.code];
      }
    }
  }
  // 如果未找到，返回 null
  return null;
}
</script>

<style scoped lang="scss"></style>
