<!--
 * @Date         : 2024-11-13 15:14:47
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->
<template>
  <div class="g-margin-20 scoreline-container">
    <el-card>
      <el-form inline class="flex items-center justify-between">
        <div>
          <el-form-item>
            <el-input
              placeholder="请输入需要搜索的城市"
              v-model="searchForm.city"
            />
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="请输入需要搜索的学校"
              v-model="searchForm.school"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search">搜索</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </div>

        <div>
          <el-form-item>
            <el-button
              type="primary"
              @click="
                ScoreLineInfoVisible = true;
                editInfo = null;
              "
            >
              新增
            </el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="ImportDataVisible = true">
              批量导入
            </el-button>
          </el-form-item>
        </div>
      </el-form>

      <NexusTable
        ref="TableRef"
        un-mounted
        :get-list="getAdmissionScore"
        :resFormat="data => data?.data"
      >
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="city" label="所属城市" />
        <el-table-column prop="schoolName" label="学校名称" />
        <el-table-column prop="score" label="2024年中考分数线" />
        <el-table-column label="信息来源">
          <template #default="{ row }">
            <div
              v-for="(source, index) in row.dataSource.split('；')"
              :key="index"
            >
              <span v-if="!isUrl(source)" v-html="source" />
              <a :href="source" target="_blank" v-else>查看明细</a>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button
              @click="
                editInfo = row;
                ScoreLineInfoVisible = true;
              "
              type="primary"
              link
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </NexusTable>
    </el-card>

    <ScoreLineInfoDialog
      v-model:visible="ScoreLineInfoVisible"
      :tableRef="TableRef"
      :edit-info="editInfo"
    />
    <ImportData
      v-model:visible="ImportDataVisible"
      accept=".xlsx"
      tpl-link="https://guanghe.feishu.cn/sheets/WfmksBKafhGYO2tWVoOcVgLPncd"
    />
  </div>
</template>

<script lang="ts" setup>
import NexusTable from "/@/views/AISupport/components/NexusTable/index.vue";
import ScoreLineInfoDialog from "./components/ScoreLineInfoDialog.vue";
import ImportData from "./components/ImportData.vue";
import { getAdmissionScore } from "/@/api/AISupport/ScoreLine";

const ScoreLineInfoVisible = ref(false);
const ImportDataVisible = ref(false);

const TableRef = ref();
const editInfo = ref();

const searchForm = reactive({
  city: "",
  school: ""
});
function search() {
  TableRef.value.search({
    city: searchForm.city,
    schoolName: searchForm.school
  });
}

function reset() {
  searchForm.city = "";
  searchForm.school = "";
  TableRef.value.search({
    city: "",
    schoolName: ""
  });
}

onMounted(() => {
  TableRef.value.search({
    sortBy: "id"
  });
});

function isUrl(str) {
  try {
    new URL(str);
    return true;
  } catch (err) {
    return false;
  }
}
</script>

<style scoped lang="scss">
.scoreline-container {
  :deep(a) {
    color: #409eff !important;
    text-decoration: revert !important;
  }
}
</style>
