/**
 * @description: 保留第一层的children 移除第二层的children
 * @param {*} arr
 * @param {*} level
 */
export default function removeSecondLevelChildren(arr, level = 1) {
  return arr.map(item => {
    // 复制当前项
    const newItem = { ...item };

    // 如果是第二层，删除 children 字段
    if (level === 2 && newItem.children) {
      delete newItem.children;
    }

    // 如果有 children 且当前层级小于 2，继续递归
    if (newItem.children && Array.isArray(newItem.children) && level < 2) {
      newItem.children = removeSecondLevelChildren(newItem.children, level + 1);
    }

    return newItem;
  });
}
