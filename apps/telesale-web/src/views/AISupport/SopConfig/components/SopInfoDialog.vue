<!--
 * @Date         : 2024-09-04 14:58:41
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog v-model="visible" title="新增SOP" width="500px" @open="openDialog">
    <NexusForm label-position="top" v-model="form" ref="FormRef">
      <el-form-item
        label="SOP阶段"
        prop="step"
        :rules="[
          { required: true, message: '请输入SOP核验标准', trigger: 'blur' }
        ]"
      >
        <el-input
          class="w-400px!"
          v-model="form.step"
          placeholder="阶段名称"
          maxlength="10"
          show-word-limit
        />
      </el-form-item>

      <el-form-item
        label="SOP核验标准"
        prop="desc"
        :rules="[
          {
            required: true,
            validator: standardValidator,
            message: '请输入SOP核验标准',
            trigger: 'blur'
          }
        ]"
      >
        <div>
          <el-text type="info" size="small">
            <div class="line-height-18px">
              标准描述需要有明确的"是/否"判断,如:
            </div>
            <div class="line-height-18px">
              a.开场白是否明确交待了公司职位和致电原因
            </div>
            <div class="line-height-18px">b.结束语是否向客户表示感谢</div>
          </el-text>

          <div v-for="(item, index) in form.desc" :key="index" class="mb-10px">
            <el-input
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 4 }"
              class="mr-5px w-400px!"
              v-model="form.desc[index]"
              maxlength="50"
              placeholder="请输入核验标准描述,包含'是/否'判断"
              show-word-limit
            />
            <el-button
              type="danger"
              :disabled="form.desc.length === 1"
              :icon="Delete"
              size="small"
              @click="form.desc.splice(index, 1)"
            />
          </div>

          <el-button
            type="primary"
            plain
            :disabled="form.desc.length >= 10"
            size="small"
            :icon="Plus"
            @click="form.desc.push('')"
          >
            新增标准
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="SOP引导问题" prop="questions">
        <div class="relative w-500px hot-question-table-box">
          <el-table
            ref="QTableRef"
            :data="form.questions"
            class="hot-question-table"
            style="width: 100%"
          >
            <el-table-column label="排序" width="80px">
              <el-icon class="moveQuestion"><Rank /></el-icon>
            </el-table-column>
            <el-table-column prop="baseQuestion" label="问题名称" />
            <el-table-column label="操作" width="80px">
              <template #default="{ $index }">
                <el-icon
                  class="c-red! cursor-pointer"
                  @click="form.questions.splice($index, 1)"
                >
                  <Delete />
                </el-icon>
              </template>
            </el-table-column>
          </el-table>

          <el-button
            size="small"
            class="bottom-10px left-15px"
            type="primary"
            @click="addCorrelationProblemDialogVisible = true"
          >
            <el-icon><Plus /></el-icon>
            <span class="ml-10px">选择问题</span>
          </el-button>
        </div>
      </el-form-item>
    </NexusForm>

    <template #footer>
      <el-button type="danger" @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </template>
  </el-dialog>

  <AddCorrelationProblemDialog
    :max="10"
    v-model:visible="addCorrelationProblemDialogVisible"
    :data-list="form.questions"
    @confirm-cb="confirmQuestion"
  />
</template>

<script lang="ts" setup>
import { defineModel } from "vue";
import { Delete, Plus, Rank } from "@element-plus/icons-vue";
import NexusForm from "../../components/NexusForm/index.vue";
import AddCorrelationProblemDialog from "./AddCorrelationProblemDialog.vue";
import Sortable from "sortablejs";
import { getSopConfigById } from "/@/api/AISupport/SopConfig";
import { getMget } from "/@/api/AISupport/Library";

const visible = defineModel<boolean>("visible");
const editInfo = defineModel<any>("editInfo");
const tableData = defineModel<any>("tableData");
const props = defineProps<{}>();

const baseInfo = {
  step: "",
  desc: [""],
  questions: []
};
const form = reactive({ ...baseInfo });

function standardValidator(rule: any, value: any, callback: any) {
  if (form.desc.filter(item => item !== "").length === 0) {
    callback(new Error("请输入SOP核验标准"));
  }
  callback();
}

const FormRef = ref();
async function confirm() {
  await FormRef.value.validate();

  form.desc = form.desc.filter(item => item !== "");

  const questions = form.questions.map(item => {
    return { id: item.id, baseQuestion: item.baseQuestion };
  });

  if (editInfo.value) {
    editInfo.value.desc = [...form.desc];
    editInfo.value.step = form.step;
    editInfo.value.questions = questions;
  } else {
    tableData.value.push({ ...form, score: 10, questions });
  }
  visible.value = false;
}

async function openDialog() {
  if (editInfo.value) {
    if (editInfo.value?.questions && editInfo.value?.questions?.length !== 0) {
      const res: any = await getMget({
        ids: editInfo.value.questions?.map(item => item.id)
      });
      form.questions = res.data.list.map(item => {
        return {
          id: item.id,
          baseQuestion: item.baseQuestion
        };
      });
    } else {
      form.questions = [];
    }

    form.desc = [...editInfo.value.desc];
    form.step = editInfo.value.step;
  } else {
    form.desc = [...baseInfo.desc];
    form.step = baseInfo.step;
    form.questions = [];
  }

  sortableInit({
    selectedList: form.questions,
    selectedTableRef: QTableRef
  });
}

const addCorrelationProblemDialogVisible = ref(false);
function confirmQuestion(val) {
  form.questions = val;
}

const QTableRef = ref();

function checkQuestions(rule: any, value: any, callback: any) {
  if (form.questions.length === 0) {
    callback(new Error("请选择SOP引导问题"));
  }

  callback();
}

function sortableInit({ selectedList, selectedTableRef }) {
  let groupSortable = null;

  nextTick(() => {
    if (groupSortable) return;
    groupSortable = new Sortable(
      selectedTableRef.value.$el.querySelector(".el-table__body-wrapper tbody"),
      {
        handle: ".moveQuestion",
        animation: 150,
        onEnd({ oldIndex, newIndex }) {
          const arr = form.questions;
          const currRow = arr.splice(oldIndex, 1)[0];
          arr.splice(newIndex, 0, currRow);
          form.questions = [];
          nextTick(() => {
            form.questions = arr;
          });
        }
      }
    );
  });
}
</script>

<style scoped lang="scss">
.hot-question-table-box {
  :deep(.el-table__empty-block) {
    height: 100px !important;
  }
}
</style>
