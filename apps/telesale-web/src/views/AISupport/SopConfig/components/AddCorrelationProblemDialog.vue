<!--
 * @Date         : 2023-06-29 11:58:13
 * @Description  : 添加关联问题
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    class="w-70vw min-w-1200px"
    :title="`全部问题列表（${AddCorrelationProblemTableRef?.info?.data?.total}）`"
    @open="openDialog"
  >
    <div class="flex">
      <div class="w-70%">
        <div class="flex justify-between items-center mb-10px">
          <el-tree-select
            v-model="categorySelect"
            :data="[{ name: '全部', id: '' }, ...categoryList]"
            node-key="id"
            placeholder="请选择分类"
            :props="{
              children: 'nodes',
              label: 'name'
            }"
            check-strictly
            :render-after-expand="false"
            @change="searchQuestion"
          />

          <el-input
            v-model="searchVal"
            class="ml-20px"
            placeholder="请输入搜索"
            @input="searchQuestion"
          />
        </div>

        <NexusTable
          unMounted
          :getList="getQuery"
          ref="AddCorrelationProblemTableRef"
          data-key="data"
          :data-format="data => data?.list"
          :class="{ 'hidden-all-check': props.max < 100 }"
          class="min-h-400px w-100%"
          @select-all="selectCheckAll"
          @select="selectCheck"
          :data-change-cb="changeData"
          totalKey="data.total"
        >
          <el-table-column
            type="selection"
            :selectable="disableSelect"
            width="55"
          />
          <el-table-column prop="answer" label="标准问法">
            <template #default="{ row }">
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="row.baseQuestion"
                placement="top"
              >
                {{
                  row.baseQuestion.length > 15
                    ? row.baseQuestion.slice(0, 15) + "..."
                    : row.baseQuestion
                }}
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="有效期">
            <template #default="{ row }">
              <span>
                {{
                  row.timeStatus
                    ? "永久"
                    : `${dayjs
                        .unix(row.effective)
                        .format("YYYY-MM-DD")} ~ ${dayjs
                        .unix(row.expire)
                        .format("YYYY-MM-DD")}`
                }}
              </span>
            </template>
          </el-table-column>
        </NexusTable>
      </div>

      <div class="w-1px bg-#eee mx-15px" />

      <div class="flex-1">
        <div class="mb-10px">
          已选择问题列表（{{ checkQuestionList.length }}）
        </div>
        <div class="flex-1">
          <div
            v-for="(item, index) in checkQuestionList"
            :key="index"
            class="my-5px flex justify-between"
          >
            <el-text class="mx-1" type="info">{{ item.baseQuestion }}</el-text>

            <el-icon
              class="c-red! cursor-pointer"
              size="large"
              @click="delcheckQuestion(item, index)"
            >
              <CircleClose />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";
import { defineModel } from "vue";
import { ElMessage } from "element-plus";
import { CircleClose } from "@element-plus/icons-vue";
import {
  getMget,
  getLibraryUUID,
  getLibraryCategory,
  getQuery
} from "/@/api/AISupport/Library";
import NexusTable from "/@/views/AISupport/components/NexusTable/index.vue";

const props = defineProps({
  confirmCb: {
    type: Function,
    default: () => {}
  },
  dataList: {
    type: Array,
    default: () => []
  },
  docId: {
    type: String,
    default: ""
  },
  max: {
    type: Number,
    default: 99999999
  },
  disableShield: {
    // IM端不显示的问题不允许选择
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["confirmCb"]);
const visible = defineModel<boolean>("visible");

const categoryList = ref([]);
async function getCategory() {
  const res: any = await getLibraryCategory({
    libraryUUID: getLibraryUUID()
  });
  categoryList.value = res.data.nodes;
}

const checkQuestionList: any = ref([]);
function selectCheck(selection, row) {
  // 单选
  const index = checkQuestionList.value.findIndex(item => {
    return item.id === row.id;
  });

  if (index === -1) {
    if (checkQuestionList.value.length >= props.max) {
      ElMessage.error(`最多仅支持选择${props.max}个问题`);
      AddCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
        row,
        false
      );
      return;
    }

    checkQuestionList.value.push(row);
  } else {
    checkQuestionList.value.splice(index, 1);
  }
}
function selectCheckAll(selection) {
  // 全选
  console.log(selection.length);
  if (checkQuestionList.value.length >= props.max) {
    ElMessage.error(`最多仅支持选择${props.max}个问题`);
    const copySelection = [...selection];
    copySelection.forEach(row => {
      console.log(row);
      AddCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
        row,
        false
      );
    });
  }

  let targetArr = selection;
  if (selection.length === 0) {
    targetArr = AddCorrelationProblemTableRef.value?.info?.value?.list;
  }

  targetArr.forEach(item => {
    const index = checkQuestionList.value.findIndex(it => {
      return it.id === item.id;
    });

    if (index === -1) {
      checkQuestionList.value.push(item);
    } else {
      if (selection.length === 0) {
        checkQuestionList.value.splice(index, 1);
      }
    }
  });
}

// 编辑时禁止选择自己
const disableSelect = (row: any) => {
  if (props.docId === row.docId) return false;

  if (props.disableShield) {
    return !row.shield;
  }

  return true;
};

const categorySelect = ref("");
const searchVal = ref();
function searchQuestion() {
  AddCorrelationProblemTableRef.value.search({
    knowledgeCategoryId: categorySelect.value,
    content: searchVal.value,
    searchMode: "question",
    libraryUUID: getLibraryUUID()
    // baseQuestion: searchVal.value
  });
}

const AddCorrelationProblemTableRef = ref();
// watch(
//   AddCorrelationProblemTableRef.value?.info,
//   newV => {
//     newV.list.forEach(item => {
//       if (checkQuestionList.value.find(it => it.id === item.id)) {
//         nextTick(() => {
//           AddCorrelationProblemTableRef.value.toggleRowSelection(item, true);
//         });
//       }
//     });
//   },
//   { deep: true }
// );

function changeData() {
  AddCorrelationProblemTableRef.value?.info?.data?.list.forEach(item => {
    if (checkQuestionList.value.find(it => it.id === item.id)) {
      nextTick(() => {
        AddCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
          item,
          true
        );
      });
    }
  });
}

function delcheckQuestion(item, index) {
  console.log("thisss", item, index);
  checkQuestionList.value.splice(index, 1);
  const target = AddCorrelationProblemTableRef.value?.info?.data?.list.find(
    it => it.id === item.id
  );
  AddCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
    target,
    false
  );
}

// 对比当前table数据和已选择数据
function compareData() {
  AddCorrelationProblemTableRef.value?.info?.data?.list.forEach(item => {
    if (checkQuestionList.value.find(it => it?.id === item.id)) {
      AddCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
        item,
        true
      );
    } else {
      AddCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
        item,
        false
      );
    }
  });
}

async function openDialog() {
  checkQuestionList.value = [...unref(props.dataList)];
  getCategory();

  if (!AddCorrelationProblemTableRef.value?.info?.data?.list) {
    await AddCorrelationProblemTableRef.value.search({
      libraryUUID: getLibraryUUID()
    });
  }
  compareData();
}

function confirm() {
  emit("confirmCb", unref(checkQuestionList));
  visible.value = false;
}
</script>

<style scoped lang="scss">
.hidden-all-check {
  :deep(thead .el-checkbox) {
    visibility: hidden;
  }
}
</style>
