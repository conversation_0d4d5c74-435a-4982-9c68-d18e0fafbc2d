/*
 * @Date         : 2024-05-14 11:43:55
 * @Description  : 拖动排序配置
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import Sortable from "sortablejs";

export default function ({ selectedList, selectedTableRef }) {
  onMounted(() => {
    sortableInit({ selectedList, selectedTableRef });
  });
}

export function sortableInit({ selectedList, selectedTableRef }) {
  let groupSortable = null;

  nextTick(() => {
    if (groupSortable) return;
    groupSortable = new Sortable(
      selectedTableRef.value.$el.querySelector(".el-table__body-wrapper tbody"),
      {
        handle: ".move",
        animation: 150,
        onEnd({ oldIndex, newIndex }) {
          const arr = selectedList.value!;
          const currRow = arr.splice(oldIndex, 1)[0];
          arr.splice(newIndex, 0, currRow);
          selectedList.value = [];
          nextTick(() => {
            selectedList.value = arr;
          });
        }
      }
    );
  });
}
