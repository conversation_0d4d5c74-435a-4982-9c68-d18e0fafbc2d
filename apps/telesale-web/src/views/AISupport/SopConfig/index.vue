<!--
 * @Date         : 2024-09-04 11:37:29
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="g-margin-20">
    <el-card>
      <h1>SOP阶段管理</h1>

      <NexusForm inline class="flex justify-end">
        <el-form-item>
          <el-button
            type="primary"
            @click="
              editInfo = null;
              SopInfoVisible = true;
            "
          >
            新增SOP
          </el-button>
        </el-form-item>
      </NexusForm>

      <el-table :data="tableData" ref="TableRef">
        <el-table-column label="排序">
          <template #default>
            <div class="move">
              <el-icon><Rank /></el-icon>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="SOP阶段" prop="step" />

        <el-table-column label="SOP评分标准" prop="desc">
          <template #header>
            <el-tooltip
              effect="dark"
              content="SOP流程的评分标准，满足对应标准后AI会根据回复进行评分"
              placement="top"
            >
              <div class="flex items-center justify-center">
                <span class="mr-5px">SOP评分标准</span>
                <el-icon><Warning /></el-icon>
              </div>
            </el-tooltip>
          </template>

          <template #default="{ row }">
            <el-tooltip effect="dark" placement="bottom">
              <el-text truncated>1. {{ row.desc[0] }}</el-text>

              <template #content>
                <div
                  v-for="(desc, idx) in row.desc.map(
                    (item, index) => `${index + 1}. ${item}`
                  )"
                  :key="idx"
                >
                  {{ desc }}
                </div>
              </template>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="权重" prop="score">
          <template #header>
            <el-tooltip
              effect="dark"
              content="SOP权重共计100，将按设置的分值权重进行加权计算"
              placement="top"
            >
              <div class="flex items-center justify-center">
                <span class="mr-5px">权重</span>
                <el-icon><Warning /></el-icon>
              </div>
            </el-tooltip>
          </template>

          <template #default="{ row }">
            <el-input-number
              :min="0"
              :max="100"
              v-model="row.score"
              :value-on-clear="0"
            />
          </template>
        </el-table-column>

        <el-table-column label="引导问题">
          <template #default="{ row }">
            <el-tooltip effect="dark" placement="bottom">
              <el-text
                truncated
                v-if="row?.questions && row?.questions?.length !== 0"
              >
                1. {{ row?.questions?.[0]?.baseQuestion }}
              </el-text>

              <template #content>
                <div class="textHideLine1">
                  <div v-for="(item, index) in row?.questions" :key="index">
                    {{ index + 1 }}、{{ item.baseQuestion }}
                  </div>
                </div>
              </template>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template #default="{ row }">
            <el-button
              link
              type="primary"
              @click="
                editInfo = row;
                SopInfoVisible = true;
              "
            >
              编辑
            </el-button>
            <el-button link type="danger" @click="del(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="mt-10px">
        <el-button type="primary" @click="save" :loading="saveLoading">
          保存
        </el-button>
      </div>
    </el-card>

    <SopInfoDialog
      v-model:editInfo="editInfo"
      v-model:visible="SopInfoVisible"
      v-model:tableData="tableData"
    />
  </div>
</template>

<script lang="ts" setup>
import { Rank, Warning } from "@element-plus/icons-vue";
import useSortable from "./hooks/useSortable";
import SopInfoDialog from "./components/SopInfoDialog.vue";
import { getSopConfig, updateSopConfig } from "/@/api/AISupport/SopConfig";
import { onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import NexusForm from "../components/NexusForm/index.vue";

const SopInfoVisible = ref(false);
const tableData = ref();
const TableRef = ref();
useSortable({
  selectedList: tableData,
  selectedTableRef: TableRef
});

const editInfo = ref();

async function del(info) {
  await ElMessageBox.confirm("删除SOP阶段后不可恢复，是否确认删除");
  tableData.value.splice(tableData.value.indexOf(info), 1);
}

async function getSopConfigHandle() {
  const res = await getSopConfig();
  tableData.value = JSON.parse(res.data.instance.content);
}

const saveLoading = ref(false);
async function save() {
  if (tableData.value.reduce((a, b) => a + b.score, 0) !== 100) {
    ElMessage.error("权重分值相加总和须为100，请调整SOP阶段权重");
    return;
  }

  try {
    saveLoading.value = true;
    await updateSopConfig(JSON.stringify(tableData.value));
    ElMessage.success("保存成功");
  } catch (error) {
    console.log(error);
    ElMessage.error("保存失败");
  }

  saveLoading.value = false;
}

onMounted(() => {
  getSopConfigHandle();
});
</script>

<style scoped lang="scss" />
