/*
 * @Date         : 2023-11-14 12:27:05
 * @Description  : 限制表格最大长度为10000条
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

export default function(pageSize, pageNum, option) {
  const oldPageNum = ref(1)
  const oldPageSize = ref(option.pageSize)

  function restrictPageNum(nVal) {  // 限制表格最大长度为10000条
    if (!option.restrict) return true

    if (nVal * pageSize.value > 10000) {
      ElMessage.error('表格最多显示10000条信息')
      nextTick(() => {
        pageNum.value = oldPageNum.value
      })
      return false
    }
    return true
  }

  function restrictPageSize(nVal) {  // 限制表格最大长度为10000条
    if (!option.restrict) return true

    if (nVal * pageNum.value > 10000) {
      ElMessage.error('表格最多显示10000条信息')
      nextTick(() => {
        pageSize.value = oldPageSize.value
      })
      return false
    }
    return true
  }

  return { oldPageNum, oldPageSize, restrictPageNum, restrictPageSize }
}
