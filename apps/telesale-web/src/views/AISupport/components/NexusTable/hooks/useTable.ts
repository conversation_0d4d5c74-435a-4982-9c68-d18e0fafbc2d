import useRestrict from './useRestrict'
import { ref, watch, onMounted, computed, nextTick, provide } from 'vue'

export function useTable(getList, optionParam?: {
  pagesKey?: string
  unMounted?: boolean
  pageSize?: number
  dataKey?: string
  dataChangeCb?: Function
  restrict?: boolean
  resFormat?: Function
}) {
  const option = {
    ...{
      pagesKey: 'pages',
      unMounted: false,
      pageSize: 10,
      dataKey: 'list',
      dataChangeCb: () => {},
      resFormat: r => r,
    },
    ...optionParam
  }

  const pageNum = ref(1)
  const pageSize = ref(option.pageSize)
  const isMounted = ref(false)
  const searchVal = ref()
  const tableLoading = ref()

  // 最大限制10000条处理
  const { oldPageNum, oldPageSize, restrictPageNum, restrictPageSize } = useRestrict(pageSize, pageNum, option)

  let param
  let info = ref<any>({
    total: 0
  })

  watch(pageSize, newV => {
    if (!restrictPageSize(newV)) {
      return
    }
    pageSize.value = newV
    oldPageSize.value = newV
    getListHandle()

  })

  watch(pageNum, newV => {
    if (!restrictPageNum(newV)) {
      return
    }
    pageNum.value = newV
    oldPageNum.value = newV
    getListHandle()
  })



  onMounted(() => {
    isMounted.value = true
    if (!option.unMounted) getListHandle()
  })

  const tableParam = computed(() => { // 表格参数
    let taget = {
      pageSize: pageSize.value,
      [option.pagesKey]: pageNum.value,
      ...param
    }
    if (searchVal.value) {
      taget = {
        ...taget,
        ...searchVal.value
      }
    }

    return taget
  })

  async function getListHandle() {
    tableLoading.value = true

    const res = await getList(tableParam.value)
    info.value = option.resFormat(res)
    info.value.total = Number(info.value.total)
    option.dataChangeCb(res)

    tableLoading.value = false
  }

  async function updateList(fn: any = null) {
    if (fn) await fn()
    await getListHandle()
  }

  async function searchHandle(searchObj) {
    searchVal.value = {
      ...searchVal.value,
      ...searchObj
    }
    pageNum.value = 1
    await getListHandle()
  }

  async function addPar(par) {
    param = par
    pageNum.value = 1
    await getListHandle()
  }

  function clearTabel() {
    info.value[option.dataKey] = []
    pageNum.value = 1
  }

  async function resetTable() {
    searchVal.value = null
    pageNum.value = 1
    await getListHandle()
  }

  return { pageNum, pageSize, searchVal, resetTable, info, updateList, searchHandle, isMounted, addPar, clearTabel, tableLoading, tableParam }
}

export function useTableFront(data: any) {
  const pageNum = ref(1)
  const pageSize = ref(10)
  const searchVal = ref('')

  const total = computed(() => {
    return tableDataFilterSearch.value.length
  })

  const tableDataFilterSearch = computed(() => {
    return data.value.filter((item: string) => {
      return item.includes(searchVal.value)
    })
  })

  const tableData = computed(() => {
    return tableDataFilterSearch.value.slice((pageNum.value - 1) * pageSize.value, pageSize.value * pageNum.value)
  })

  watch(() => data, (newV) => {
    console.log(newV)
  })

  async function handleSizeChange(val) {
    pageSize.value = val
  }

  async function handleCurrentChange(val) {
    pageNum.value = val
  }

  return { pageNum, pageSize, handleSizeChange, handleCurrentChange, total, tableData, searchVal }
}

export function useTableCheck(tableRef, list) {
  const checkList:any = ref([])

  function selectCheck(selection, row) {  // 单选
    const index = checkList.value.findIndex(item => {
      return item.id === row.id
    })

    if (index === -1) {
      checkList.value.push(row)
    } else {
      checkList.value.splice(index, 1)
    }
  }

  function selectCheckAll(selection) {  // 全选
    console.log(selection.length)
    let targetArr = selection

    if (selection.length === 0) {
      targetArr = list
    }

    targetArr?.forEach(item => {
      const index = checkList.value.findIndex(it => {
        return it.id === item.id
      })

      if (index === -1) {
        checkList.value.push(item)
      } else {
        if (selection.length === 0) {
          checkList.value.splice(index, 1)
        }
      }
    })
  }

  watch(list, newV => {
    newV?.forEach(item => {
      if (checkList.value.find(it => it.id === item.id)) {
        nextTick(() => {
          tableRef.value.toggleRowSelection(item, true)
        })
      }
    })
  }, { deep: true })

  return { checkList, selectCheck, selectCheckAll }
}
