<!--
 * @Date         : 2023-12-08 14:03:05
 * @Description  : 动态配置表头显示组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog v-model="visible"
             title="配置表格头"
             @open="openDialog">

    <div>
      <el-checkbox v-model="checkAll"
                   :indeterminate="isIndeterminate"
                   @change="handleCheckAllChange">选择全部</el-checkbox>
      <el-checkbox-group v-model="showHeaderKeyList"
                         @change="handleCheckedCitiesChange">
        <el-checkbox v-for="header in config.localList"
                     :key="header.key"
                     :label="header.key">{{
                       header.label
                     }}</el-checkbox>
      </el-checkbox-group>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary"
                   @click="save">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang='ts' setup>
import { ref, computed, onMounted, defineModel } from 'vue'

const props = defineProps<{
  config: {
    localKey: string
    localList: {
      label: string
      key: string
      defaultUnShow?: boolean
    }[]
  }
}>()

const checkAll = ref(false)
const isIndeterminate = ref(false)
const showHeaderKeyList = defineModel<string[]>('keyList') // 禁用headerKey列表
const visible = defineModel<boolean>('visible')

const headerVal = computed(() => {
  return props.config.localList.map((item:any) => item.key)
})

const handleCheckAllChange = (val) => {
  showHeaderKeyList.value = val ? headerVal.value : []
  isIndeterminate.value = false
}

const handleCheckedCitiesChange = (value) => {
  const checkedCount = value.length
  const headerLength = props.config.localList.length
  checkAll.value = checkedCount === headerLength
  isIndeterminate.value = checkedCount > 0 && checkedCount < headerLength
}

function save() {
  setLocalStorage(props.config.localKey, showHeaderKeyList.value)
  visible.value = false
}

function openDialog() {
  getHeaderShowList()
}

function getHeaderShowList() {
  const localstorageCheck = localStorage.getItem(props.config.localKey)
  if (localstorageCheck) {
    showHeaderKeyList.value = JSON.parse(localstorageCheck)
    if (localstorageCheck?.length !== 0) {
      isIndeterminate.value = true
    } else {
      isIndeterminate.value = false
    }
  } else {
    showHeaderKeyList.value = headerVal.value
    isIndeterminate.value = false
  }
}

const defaultKey = computed(() => { // 默认显示的key列表
  return props.config.localList.filter(item => !item.defaultUnShow).map(item => item.key)
})
const LocalHeader = localStorage.getItem(props.config.localKey)
if (LocalHeader === null) {
  setLocalStorage(props.config.localKey, defaultKey.value)
}

function setLocalStorage(key:string, val:any) {
  const data = JSON.stringify(val)
  return localStorage.setItem(key, data)
}

onMounted(() => {
  getHeaderShowList()
})
</script>

<style scoped lang='scss'>

</style>
