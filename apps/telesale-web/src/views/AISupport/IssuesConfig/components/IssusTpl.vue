<!--
 * @Date         : 2024-11-26 17:21:02
 * @Description  : 沟通议题模板
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="relative w-500px hot-question-table-box">
    <el-table
      ref="TableRef"
      :data="tableDataComputed"
      class="hot-question-table"
      style="width: 100%"
    >
      <el-table-column label="排序" width="80px">
        <el-icon class="move"><Rank /></el-icon>
      </el-table-column>
      <el-table-column prop="topic" label="问题名称" />
      <el-table-column label="操作" width="80px">
        <template #default="{ $index }">
          <el-icon
            class="cursor-pointer mr-5px"
            @click="
              editInfo = tableData[$index]?.topicSets;
              editIndex = $index;
              issusTplFormVisible = true;
            "
          >
            <Edit />
          </el-icon>

          <el-icon class="c-red! cursor-pointer" @click="del">
            <Delete />
          </el-icon>
        </template>
      </el-table-column>
    </el-table>

    <el-button
      size="small"
      class="bottom-10px left-15px"
      type="primary"
      @click="
        editInfo = null;
        issusTplFormVisible = true;
      "
    >
      <el-icon><Plus /></el-icon>
      <span class="ml-10px">新增议题</span>
    </el-button>

    <IssusFormDialog
      v-model:visible="issusTplFormVisible"
      v-model:data="tableData"
      :editInfo="editInfo"
      :editIndex="editIndex"
    />
  </div>
</template>

<script lang="ts" setup>
import useSortable from "../../SopConfig/hooks/useSortable";
import { Plus, Rank, Delete, Edit } from "@element-plus/icons-vue";
import IssusFormDialog from "./IssusFormDialog.vue";
import { defineModel } from "vue";
import { ElMessageBox } from "element-plus";

const issusTplFormVisible = ref(false);

const tableData = defineModel<any[]>();
const editInfo = ref();
const editIndex = ref(0);

const TableRef = ref();

const tableDataComputed = computed(() => {
  return tableData.value.map(item => {
    return item?.topicSets[0];
  });
});
useSortable({
  selectedList: tableData,
  selectedTableRef: TableRef
});

async function del($index) {
  await ElMessageBox.confirm("删除议题后无法恢复，是否确认删除议题？");
  tableData.value.splice($index, 1);
}
</script>

<style scoped lang="scss">
.hot-question-table-box {
  :deep(.el-table__empty-block) {
    height: 100px !important;
  }
}
</style>
