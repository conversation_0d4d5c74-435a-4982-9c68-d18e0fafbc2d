<!--
 * @Date         : 2024-11-26 18:42:45
 * @Description  : 议题
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-card class="issus-form py-10px mb-10px">
    <div class="relative">
      <div class="absolute right-0 cursor-pointer" v-if="form?.length > 1">
        <el-icon color="red" size="18" @click="form.splice(formIndex, 1)">
          <Delete />
        </el-icon>
      </div>

      <NexusForm label-position="top" v-model="info" ref="FormRef">
        <el-form-item
          label="标题"
          prop="topic"
          :rules="[
            { required: true, message: '请输入标题名称', trigger: 'blur' }
          ]"
        >
          <el-input
            v-model="info.topic"
            placeholder="请输入标题名称"
            maxlength="10"
            show-word-limit
          />
        </el-form-item>

        <el-form-item
          label="正文"
          prop="content"
          :rules="[
            { required: true, message: '请输入正文', trigger: 'blur' },
            { validator: checkContent, trigger: 'blur' }
          ]"
        >
          <div class="flex flex-col">
            <div class="mb-10px">
              <el-dropdown>
                <el-button type="primary" size="small">
                  插入参数
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <!-- :disabled="info.content.includes(`{{${item}}}`)" -->
                    <el-dropdown-item
                      @click="appendText(item)"
                      v-for="(item, index) in dropdownItem"
                      :key="index"
                    >
                      {{ item }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            <TextEditor
              v-model:valueHtml="info.content"
              placeholder="请输入正文"
              ref="EditorRef"
              disable-link
            />
          </div>
        </el-form-item>

        <el-form-item
          v-if="form?.length > 1"
          label="展示条件"
          prop="condition"
          :rules="[
            { required: true, message: '请选择展示条件', trigger: 'blur' }
          ]"
        >
          <el-select v-model="info.condition">
            <el-option
              v-for="item in options"
              :disabled="Boolean(form.find(it => it.condition == item))"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
      </NexusForm>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import NexusForm from "/@/views/AISupport/components/NexusForm/index.vue";
import { defineModel } from "vue";
import TextEditor from "/@/components/TextEditor/index.vue";
import { ArrowDown } from "@element-plus/icons-vue";
import { Delete } from "@element-plus/icons-vue";

const props = defineProps<{
  formIndex: number;
  dialogVisible: boolean;
}>();
const form = defineModel<any>("form");

const options = [
  "初一",
  "初二",
  "初三",
  "注册时长小于三个月（小于等于）",
  "注册时长大于三个月"
];

const dropdownItem = [
  "总学习时间",
  "重点学习科目",
  "重点学习科目知识点",
  "重点答题科目",
  "重点答题科目量",
  "重点科目答题正确率分析",
  "学习排名",
  "答题排名",
  "用户城市",
  "合作院校",
  "注册时长"
];

const info = defineModel<any>();

const EditorRef = ref();
function appendText(text: string) {
  EditorRef.value.EditorRef.insertNode({
    text: `{{${text}}}`
  });
}

const FormRef = ref();

function checkContent(rule: any, value: any, callback: any) {
  let tempDiv = document.createElement("div");
  // 设置元素的内容为HTML字符串
  tempDiv.innerHTML = value;
  // 移除空白节点
  tempDiv.textContent = tempDiv.textContent.trim();

  if (tempDiv.textContent === "") {
    callback(new Error("请输入正文"));
  }
  callback();
}

function clearEditor() {
  EditorRef.value.EditorRef.setHtml("");
  EditorRef.value.EditorRef.clear();
}

defineExpose({
  FormRef,
  clearEditor
});
</script>

<style scoped lang="scss">
.issus-form {
  :deep(.el-form-item) {
    margin-bottom: 18px;
  }
}
</style>
