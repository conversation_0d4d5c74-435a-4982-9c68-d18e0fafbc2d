<!--
 * @Date         : 2024-11-26 17:18:41
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    close-on-click-modal
    destroy-on-close
    :title="`${editInfo ? '编辑' : '新增'}SOP`"
    @open="openDialog"
  >
    <NexusForm ref="FormRef" v-model="form">
      <el-form-item
        label="话术阶段"
        prop="step"
        :rules="{ required: true, message: '请输入话术阶段', trigger: 'blur' }"
      >
        <el-input
          v-model="form.step"
          maxlength="10"
          show-word-limit
          placeholder="请输入SOP阶段名称"
        />
      </el-form-item>
      <el-form-item
        label="沟通议题模板"
        prop="topicSets"
        :rules="[
          {
            required: true,
            message: '请添加沟通议题模板',
            trigger: 'blur'
          }
        ]"
      >
        <IssusTpl v-model="form.topicSets" />
      </el-form-item>
    </NexusForm>
    <template #footer>
      <el-button type="danger" @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import NexusForm from "/@/views/AISupport/components/NexusForm/index.vue";
import IssusTpl from "./IssusTpl.vue";
import { defineModel } from "vue";

const visible = defineModel<boolean>("visible");
const tableData = defineModel<any>("tableData");
const FormRef = ref();

const props = defineProps<{
  editInfo: any;
  editIndex: number;
}>();

const form = reactive({
  step: "",
  topicSets: []
});

function openDialog() {
  if (props.editInfo) {
    form.step = props.editInfo?.step;
    form.topicSets = props.editInfo?.topicSets;
  } else {
    form.step = "";
    form.topicSets = [];
  }
}

const loading = ref(false);
async function confirm() {
  loading.value = true;
  try {
    await FormRef.value.validate();

    if (props.editInfo) {
      tableData.value[props.editIndex].step = form.step;
      tableData.value[props.editIndex].topicSets = [...form.topicSets];
    } else {
      tableData.value.push({
        step: form.step,
        topicSets: [...form.topicSets]
      });
    }

    visible.value = false;
  } catch (error) {
    console.log(error);
  }
  loading.value = false;
}
</script>

<style scoped lang="scss"></style>
