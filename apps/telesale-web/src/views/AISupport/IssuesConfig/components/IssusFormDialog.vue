<!-- eslint-disable vue/no-mutating-props -->
<!--
 * @Date         : 2024-11-26 18:46:04
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <el-dialog
      close-on-click-modal
      append-to-body
      v-model="visible"
      destroy-on-close
      :title="`${editInfo ? '编辑' : '新增'}议题`"
      @open="openDialog"
    >
      <div v-for="(item, index) in form" :key="item.id">
        <IssusForm
          ref="FormRef"
          v-model="form[index]"
          v-model:form="form"
          :formIndex="index"
          :dialogVisible="visible"
        />
      </div>

      <el-button type="primary" size="small" @click="addIssus">
        增加分支
      </el-button>

      <template #footer>
        <el-button type="danger" @click="visible = false">取消</el-button>
        <el-button type="primary" @click="confirm" :loading="loading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { defineModel } from "vue";
import IssusForm from "./IssusForm.vue";
import { v4 as uuidv4 } from "uuid";

const visible = defineModel<boolean>("visible");
const data = defineModel<any>("data");
const props = defineProps<{
  editInfo: any;
  editIndex: number;
}>();

const form = ref([]);

function openDialog() {
  if (props.editInfo) {
    form.value = props.editInfo;
  } else {
    form.value = [{ ...{ topic: "", content: "", condition: "" } }];
  }
}

function addIssus() {
  form.value.push({
    ...{
      topic: "",
      content: "",
      condition: ""
    }
  });
}

const FormRef = ref();
const loading = ref(false);
async function confirm() {
  try {
    await Promise.all(
      FormRef.value.map(item => {
        return item.FormRef.validate();
      })
    );

    if (props.editInfo) {
      const uuid = props.editInfo.find(item => item.topicUUID).topicUUID;
      data.value[props.editIndex].topicSets = form.value.map(item => {
        return {
          ...item,
          topicUUID: uuid
        };
      });
    } else {
      const uuid = uuidv4();
      data.value.push({
        topicSets: form.value.map(item => {
          return {
            ...item,
            topicUUID: uuid
          };
        })
      });
    }

    FormRef.value.forEach(item => {
      item.clearEditor();
    });
    visible.value = false;
  } catch (error) {
    console.log(error);
  }
  loading.value = false;
}
</script>

<style scoped lang="scss"></style>
