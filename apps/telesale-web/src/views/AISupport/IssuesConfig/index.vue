<!--
 * @Date         : 2024-11-26 17:00:08
 * @Description  : 议题模板管理
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->
<template>
  <div class="g-margin-20 quality-container">
    <el-card>
      <el-table :data="tableData" ref="TableRef">
        <el-table-column label="排序">
          <template #default>
            <div class="move">
              <el-icon><Rank /></el-icon>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="SOP阶段" prop="step" />

        <el-table-column label="沟通议题">
          <template #default="{ row }">
            <el-tooltip
              effect="dark"
              placement="bottom"
              popper-class="issus-tooltip"
            >
              <div class="flex textHideLine1">
                <span>1、</span>
                <span
                  class="textHideLine1"
                  v-html="row.topicSets[0].topicSets[0]?.topic"
                />
                <span>...</span>
              </div>

              <template #content>
                <div v-for="(item, index) in row.topicSets" :key="index">
                  {{ index + 1 }}、
                  <span v-html="item.topicSets[0].topic" />
                </div>
              </template>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template #default="{ $index, row }">
            <el-button
              link
              type="primary"
              @click="
                editInfo = row;
                editIndex = $index;
                SopInfoDialogVisible = true;
              "
            >
              编辑
            </el-button>
            <el-button link type="danger" @click="del($index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div
        @click="
          editInfo = null;
          SopInfoDialogVisible = true;
        "
        class="c-#409EFF b b-b-#409EFF b-dashed inline-flex py-3px px-8px cursor-pointer mt-10px"
      >
        <el-icon><Plus /></el-icon>
        <el-text class="ml-5px!" type="primary">新增SOP</el-text>
      </div>

      <div class="mt-10px">
        <el-button type="primary" @click="save" :loading="saveLoading">
          保存
        </el-button>
      </div>
    </el-card>

    <SopInfoDialog
      v-model:visible="SopInfoDialogVisible"
      v-model:tableData="tableData"
      :editInfo="editInfo"
      :editIndex="editIndex"
    />
  </div>
</template>

<script lang="ts" setup>
import useSortable from "../SopConfig/hooks/useSortable";
import { Plus, Rank } from "@element-plus/icons-vue";
import SopInfoDialog from "./components/SopInfoDialog.vue";
import { getSopTopicSet, updateSopTopicSet } from "/@/api/AISupport/SopTopic";
import { ElMessageBox } from "element-plus";
import { onBeforeRouteLeave } from "vue-router";

const SopInfoDialogVisible = ref(false);

const tableData = ref([]);
const TableRef = ref();
const editInfo = ref();
const editIndex = ref(0);
useSortable({
  selectedList: tableData,
  selectedTableRef: TableRef
});

async function getSopConfigHandle() {
  const res: any = await getSopTopicSet();
  tableData.value = res.data.sopTopicSets;
}

const saveLoading = ref(false);
async function save() {
  try {
    saveLoading.value = true;
    await updateSopTopicSet({ sopTopicSets: tableData.value });
    ElMessage.success("保存成功");
    await getSopConfigHandle();
    nextTick(() => {
      isDataChange.value = false;
    });
    console.log("保存成功", isDataChange.value);
  } catch (error) {
    console.log(error);
    ElMessage.error("保存失败");
  }

  saveLoading.value = false;
}

async function del($index) {
  await ElMessageBox.confirm("删除SOP阶段后无法恢复，是否确认删除议题？");
  tableData.value.splice($index, 1);
}

onMounted(() => {
  getSopConfigHandle();
});

const isDataChange = ref(false);
watch(
  tableData,
  (newV, oldV) => {
    console.log(newV, oldV?.length);
    if (oldV?.length === 0 || oldV === undefined) return;
    if (isDataChange.value) return;

    isDataChange.value = true;
  },
  {
    immediate: true,
    deep: true
  }
);

onBeforeRouteLeave(async (to, from) => {
  console.log(isDataChange.value);
  if (!isDataChange.value) return true;

  await ElMessageBox.confirm(
    "当前页面有内容暂未保存，切换页面后将会丢失，请确认是否需要保存？"
  );

  await save();

  return true;
});
</script>

<style lang="scss">
.issus-tooltip {
  max-width: 400px !important;
}
</style>
