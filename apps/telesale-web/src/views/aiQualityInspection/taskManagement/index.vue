<!--
 * @Date         : 2025-05-13 11:51:19
 * @Description  : 任务管理
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="g-margin-20 task-management-container">
    <el-card>
      <div class="search-form">
        <nexus-form inline class="w-full flex justify-between">
          <div class="flex">
            <el-form-item>
              <el-input
                v-model.trim="searchForm.nameKeyword"
                placeholder="请输入任务名称"
                clearable
                @keyup.enter="onSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="searchForm.statusFilter"
                placeholder="请选择任务执行状态"
                clearable
              >
                <el-option label="未开始" :value="TASK_STATUS.CREATE" />
                <el-option label="进行中" :value="TASK_STATUS.PROCESS" />
                <el-option label="已结束" :value="TASK_STATUS.END" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <agent-select
                isAll
                v-model="searchForm.workerIdFilter"
                placeholder="请输入创建人"
                @change="onSearch"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="onSearch">搜索</el-button>
              <el-button @click="onReset">重置</el-button>
            </el-form-item>
          </div>
          <div>
            <el-form-item>
              <el-button type="primary" @click="handleAddTask">
                新增任务
              </el-button>
            </el-form-item>
          </div>
        </nexus-form>
      </div>

      <div class="table-container">
        <nexus-table
          ref="tableRef"
          :get-list="getTrainingTaskList"
          :res-format="resFormat"
          data-key="tasks"
          total-key="total"
        >
          <el-table-column prop="id" label="任务ID" width="100" />
          <el-table-column prop="name" label="任务名称" min-width="150" />
          <el-table-column prop="students" label="学员" width="120">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                @click="handleViewTaskMembers(row)"
              >
                查看学员
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="任务状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="operator" label="更新人" width="120" />
          <el-table-column prop="createdAt" label="创建时间" width="180" />
          <el-table-column prop="beginAt" label="开始时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.beginAt) }}
            </template>
          </el-table-column>
          <el-table-column prop="endAt" label="截止时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.endAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button
                type="primary"
                link
                @click="handleEditTask(row)"
                v-if="row.status !== TASK_STATUS.END"
              >
                编辑
              </el-button>
              <el-button
                v-if="row.status !== TASK_STATUS.END"
                type="danger"
                link
                @click="handleFinishTask(row)"
              >
                结束
              </el-button>
            </template>
          </el-table-column>
        </nexus-table>
      </div>
    </el-card>

    <!-- 任务成员明细对话框 -->
    <task-member-detail-dialog
      v-model:visible="taskMemberDetailVisible"
      :task-id="currentTaskId"
    />

    <!-- 任务表单弹窗 -->
    <task-dialog
      v-model:visible="taskDialogVisible"
      :task-id="currentTaskId"
      @success="handleTaskSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import dayjs from "dayjs";
import { ElMessage, ElMessageBox } from "element-plus";
import NexusTable from "/@/components/Nexus/NexusTable/index.vue";
import AgentSelect from "/@/components/AgentSelect/index.vue";
import TaskDialog from "./components/TaskDialog.vue";
import TaskMemberDetailDialog from "./components/TaskMemberDetailDialog.vue";
import { useUserStoreHook } from "/@/store/modules/user";
import {
  getTrainingTaskList,
  finishTrainingTask,
  TrainingTaskQueryParams,
  TrainingTaskListReply,
  TrainingTaskFinishRequest,
  TASK_STATUS
} from "/@/api/AIQualityInspection/taskManagement";

// 表格引用
const tableRef = ref();

// 任务成员明细对话框相关
const taskMemberDetailVisible = ref(false);

// 任务弹窗相关
const taskDialogVisible = ref(false);
const currentTaskId = ref<number | undefined>(undefined);

// 搜索表单
interface SearchForm extends TrainingTaskQueryParams {}

const searchForm = reactive<SearchForm>({
  nameKeyword: "",
  statusFilter: "",
  workerIdFilter: undefined,
  pages: 1,
  pageSize: 10
});

// 格式化时间
function formatTime(timestamp: any) {
  if (!timestamp) return "";
  return dayjs.unix(timestamp).format("YYYY-MM-DD HH:mm:ss");
}

// 获取状态文本
function getStatusText(status: string): string {
  switch (status) {
    case TASK_STATUS.CREATE:
      return "未开始";
    case TASK_STATUS.PROCESS:
      return "进行中";
    case TASK_STATUS.END:
      return "已结束";
    default:
      return status || "";
  }
}

// 获取状态类型
function getStatusType(
  status: string
): "" | "info" | "success" | "warning" | "danger" {
  switch (status) {
    case TASK_STATUS.CREATE:
      return "info";
    case TASK_STATUS.PROCESS:
      return "warning"; // 使用warning替代primary，因为el-tag不支持primary类型
    case TASK_STATUS.END:
      return "success";
    default:
      return "";
  }
}

// API响应格式化
function resFormat(res: any): TrainingTaskListReply {
  // 处理API返回的数据，确保返回正确的格式
  const data = res.data || res;
  return data;
}

// 搜索
function onSearch() {
  tableRef.value?.searchHandle({
    nameKeyword: searchForm.nameKeyword,
    statusFilter: searchForm.statusFilter,
    workerIdFilter: searchForm.workerIdFilter,
    pages: 1
  });
}

// 重置
function onReset() {
  searchForm.nameKeyword = "";
  searchForm.statusFilter = "";
  searchForm.workerIdFilter = undefined;
  tableRef.value?.resetTable();
}

// 新增任务
function handleAddTask() {
  currentTaskId.value = undefined;
  taskDialogVisible.value = true;
}

// 查看任务成员明细
function handleViewTaskMembers(row: any) {
  currentTaskId.value = row.id;
  taskMemberDetailVisible.value = true;
}

// 编辑任务
function handleEditTask(row: any) {
  currentTaskId.value = row.id;
  taskDialogVisible.value = true;
}

// 任务创建/编辑成功回调
function handleTaskSuccess() {
  // 刷新表格数据
  tableRef.value?.updateList();
}

// 结束任务
async function handleFinishTask(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要结束任务 "${row.name}" 吗？`,
      "结束确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    // 获取当前用户信息
    const userStore = useUserStoreHook();

    // 调用结束任务接口
    const finishRequest: TrainingTaskFinishRequest = {
      id: row.id,
      operator: userStore.userMsg.name, // 操作人姓名
      workerId: String(userStore.userMsg.id) // 工作人员ID，转为字符串
    };

    await finishTrainingTask(finishRequest);

    ElMessage.success(`任务 "${row.name}" 已成功结束`);
    // 刷新表格数据
    tableRef.value?.updateList();
  } catch (error) {
    // 用户取消操作或接口调用失败
    if (error instanceof Error) {
      ElMessage.error(`结束任务失败: ${error.message}`);
    }
  }
}
</script>

<style scoped>
.task-management-container :deep(.el-card) {
  margin-bottom: 1.25rem;
}

.task-management-container .table-container {
  margin-top: 1.25rem;
}

.task-management-container .table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.task-management-container .table-header .table-title {
  font-size: 1rem;
  font-weight: bold;
}
</style>
