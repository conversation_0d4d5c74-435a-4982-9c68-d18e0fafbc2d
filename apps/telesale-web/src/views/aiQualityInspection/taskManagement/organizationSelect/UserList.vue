<!--
 * @Date         : 2025-05-16 11:20:00
 * @Description  : 用户列表组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div v-loading="loading">
    <el-empty v-if="userList.length === 0" description="暂无人员" />

    <template v-else>
      <el-input
        v-model="userFilterVal"
        clearable
        class="mb-5px"
        placeholder="筛选当前人员"
      />

      <el-checkbox
        v-if="userFilterVal === ''"
        v-model="checkAll"
        :indeterminate="isIndeterminate"
        @change="handleCheckAllChange"
      >
        全选
      </el-checkbox>

      <el-checkbox-group
        v-model="checkedUser"
        @change="handleCheckedUsersChange"
      >
        <el-checkbox
          v-for="user in userTarget"
          :key="user.id"
          :label="user.id"
          :disabled="
            user.status === 2 ||
            (props.isEdit && props.originalUserIds.includes(user.id))
          "
        >
          {{ user.name }}{{ user.status === 2 ? "(已离职)" : "" }}
          {{
            props.isEdit && props.originalUserIds.includes(user.id)
              ? "(不可移除)"
              : ""
          }}
        </el-checkbox>
      </el-checkbox-group>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, defineModel } from "vue";
import { findMember } from "/@/api/agent";

// 使用defineModel实现双向绑定
const checkedUserList = defineModel<any[]>("checkedUserList");

// 定义props
const props = withDefaults(
  defineProps<{
    orgId: any;
    originalUserIds?: number[]; // 原始学员ID列表，用于编辑模式下判断哪些学员不可删除
    isEdit?: boolean; // 是否为编辑模式
  }>(),
  {
    originalUserIds: () => [],
    isEdit: false
  }
);

// 加载状态
const loading = ref(false);

// 用户列表
const userList = ref<any[]>([]);

// 获取用户列表
async function getList(orgId: number | string) {
  if (!orgId) {
    userList.value = [];
    return;
  }

  loading.value = true;
  try {
    const res: any = await findMember({ id: orgId });
    userList.value = res.data.list || [];
    checkUser();
  } catch (error) {
    console.error("获取用户列表失败:", error);
  } finally {
    loading.value = false;
  }
}

// 已选择的用户ID
const checkedUser = ref<number[]>([]);

// 检查用户是否已选择
function checkUser() {
  checkedUser.value = [];
  checkedUserList.value?.forEach(item => {
    checkedUser.value.push(item.id);
  });
}

// 可用的用户列表（排除离职人员）
const userListFilter = computed(() => {
  return userList.value.filter(item => item.status !== 2);
});

// 是否全选
const checkAll = computed(() => {
  if (userListFilter.value.length === 0) return false;

  return userListFilter.value.every(item => {
    return checkedUser.value.includes(item.id);
  });
});

// 是否半选
const isIndeterminate = computed(() => {
  return (
    userListFilter.value.some(item => {
      return checkedUser.value.includes(item.id);
    }) && !checkAll.value
  );
});

// 全选切换
function handleCheckAllChange() {
  if (userListFilter.value.length === 0) return;

  if (isIndeterminate.value) {
    // 半选切换全选
    userListFilter.value.forEach(item => {
      if (!checkedUser.value.includes(item.id)) {
        checkedUser.value?.push(item.id);
        checkedUserList.value?.push(item);
      }
    });
  } else if (checkAll.value) {
    // 全选切换非全选
    // 在编辑模式下，保留原始学员
    if (props.isEdit && props.originalUserIds.length > 0) {
      // 只保留原始学员
      checkedUser.value = [...props.originalUserIds];

      // 移除非原始学员
      for (let i = checkedUserList.value!.length - 1; i >= 0; i--) {
        const user = checkedUserList.value![i];
        if (!props.originalUserIds.includes(user.id)) {
          checkedUserList.value?.splice(i, 1);
        }
      }
    } else {
      // 非编辑模式，清空所有
      checkedUser.value = [];
      userListFilter.value.forEach(item => {
        const index = checkedUserList.value!.findIndex(it => {
          return it.id === item.id;
        });
        if (index !== -1) checkedUserList.value?.splice(index, 1);
      });
    }
  } else if (!checkAll.value) {
    // 非全选切换全选
    userListFilter.value.forEach(item => {
      checkedUser.value?.push(item.id);
      checkedUserList.value?.push(item);
    });
  }
}

// 处理复选框组变化
function handleCheckedUsersChange(value: number[]) {
  // 清空已选用户列表
  if (checkedUserList.value) {
    // 在编辑模式下，确保原始学员始终被选中
    if (props.isEdit && props.originalUserIds.length > 0) {
      // 将原始学员ID添加到选中值中
      props.originalUserIds.forEach(id => {
        if (!value.includes(id)) {
          value.push(id);
        }
      });
    }

    // 移除不在选中值中的用户
    for (let i = checkedUserList.value.length - 1; i >= 0; i--) {
      const user = checkedUserList.value[i];
      if (!value.includes(user.id)) {
        // 在编辑模式下，检查是否为原始学员
        if (props.isEdit && props.originalUserIds.includes(user.id)) {
          // 原始学员不能被移除，跳过
          continue;
        }
        checkedUserList.value.splice(i, 1);
      }
    }

    // 添加新选中的用户
    value.forEach(id => {
      const user = userList.value.find(u => u.id === id);
      if (user && !checkedUserList.value!.some(u => u.id === id)) {
        checkedUserList.value.push(user);
      }
    });
  }
}

// 监听组织ID变化
watch(
  () => props.orgId,
  newVal => {
    if (newVal) getList(newVal);
  }
);

// 监听已选择用户列表变化
watch(
  checkedUserList,
  () => {
    checkUser();
  },
  { deep: true }
);

// 用户筛选
const userFilterVal = ref("");
const userTarget = computed(() => {
  return userList.value.filter(item => {
    return item.name.includes(userFilterVal.value);
  });
});
</script>

<style scoped>
/* 使用UnoCSS实现样式 */
</style>
