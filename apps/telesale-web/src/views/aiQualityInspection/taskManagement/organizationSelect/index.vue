<!--
 * @Date         : 2025-05-16 11:00:00
 * @Description  : 组织人员级联选择器
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="选择学员"
    @close="closeDialog"
    @open="openDialog"
    width="900px"
  >
    <div class="flex justify-between">
      <el-card class="w-30%">
        <el-input
          v-model="searchTreeVal"
          placeholder="筛选组织"
          class="mb-5px"
          @update:model-value="search"
        />
        <OrganizationTree
          ref="orgTreeRef"
          v-model:nodeClickVal="checkedOrg"
          :filter-node-method="filterNode"
        />
      </el-card>

      <el-card class="w-30%">
        <UserList
          v-model:checkedUserList="checkedUserList"
          :org-id="checkedOrg?.id"
          :is-edit="props.isEdit"
          :original-user-ids="props.originalUserIds"
        />
      </el-card>

      <el-card class="w-30%">
        <AgentSelect
          v-model="selectedAgentId"
          placeholder="搜索人员进行添加"
          class="mb-10px"
          :clearable="false"
          @update:model-value="changeUser"
        />
        <div class="flex flex-wrap gap-2 w-100%">
          <el-tag
            v-for="(user, index) in checkedUserList"
            :key="user.id"
            :closable="
              !(props.isEdit && props.originalUserIds.includes(user.id))
            "
            @close="handleRemoveUser(index, user)"
          >
            {{ user.name }}
          </el-tag>
        </div>
      </el-card>
    </div>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineModel } from "vue";
import { ElMessage } from "element-plus";
import OrganizationTree from "./OrganizationTree.vue";
import UserList from "./UserList.vue";
import AgentSelect from "/@/components/AgentSelect/index.vue";
import { useUserStoreHook } from "/@/store/modules/user";

const props = withDefaults(
  defineProps<{
    formatTarget?: Function;
    originalUserIds?: number[]; // 原始学员ID列表，用于编辑模式下判断哪些学员不可删除
    isEdit?: boolean; // 是否为编辑模式
  }>(),
  {
    formatTarget: target => target,
    originalUserIds: () => [],
    isEdit: false
  }
);

// 使用defineModel实现双向绑定
const visible = defineModel<boolean>("visible");
const checkedOrg = ref();

// 已选择的用户列表
const checkedUserList = ref<any[]>([]);
const userList = defineModel<any[]>("userList");

// 确认选择
function confirm() {
  if (checkedUserList.value && checkedUserList.value.length > 0) {
    userList.value = props
      .formatTarget([...checkedUserList.value])
      .map(item => {
        return {
          ...item,
          id: item?.copyId || item.id,
          workerId: item.id
        };
      });
  }

  visible.value = false;
}

// 打开弹窗时初始化数据
function openDialog() {
  if (userList.value && userList.value.length === 0) {
    checkedUserList.value.length = 0;
  }

  if (userList.value && userList.value.length > 0) {
    checkedUserList.value = [...userList.value].map(item => {
      return {
        ...item,
        id: item.workerId || item.id,
        copyId: item.id
      };
    });
  }
}

// 关闭弹窗时清空数据
function closeDialog() {
  checkedUserList.value.length = 0;
}

// 组织树搜索相关
const orgTreeRef = ref();
const searchTreeVal = ref("");

// 选中的坐席ID
const selectedAgentId = ref<number | null>(null);
function filterNode(value: string, data: any): boolean {
  if (!value) return true;
  return data.name.includes(value);
}
function search() {
  orgTreeRef.value?.filter(searchTreeVal.value);
}

// 添加用户
function changeUser(userId: number) {
  if (!userId) return;

  // 选择后重置选择框
  selectedAgentId.value = null;

  // 从用户store获取坐席列表
  const agentList = useUserStoreHook().agentList;
  const user = agentList.find(agent => agent.id === userId);

  if (user) {
    if (!checkedUserList.value.find(item => item.id === user.id)) {
      checkedUserList.value.push({
        id: user.id,
        name: user.name
      });
    } else {
      ElMessage.warning("该用户已存在");
    }
  }
}

// 移除用户
function handleRemoveUser(index: number, user: any) {
  // 在编辑模式下，检查是否为原有学员
  if (props.isEdit && props.originalUserIds.includes(user.id)) {
    ElMessage.warning("编辑任务时不允许删除已下发的学员");
    return;
  }

  // 移除用户
  checkedUserList.value.splice(index, 1);
}
</script>

<style scoped>
/* 使用UnoCSS实现样式 */
</style>
