<!--
 * @Date         : 2025-05-16 11:10:00
 * @Description  : 组织树组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-tree
    ref="treeRef"
    :data="organizationData"
    node-key="id"
    default-expand-all
    :expand-on-click-node="false"
    :props="{
      label: 'name',
      children: 'children'
    }"
    :filter-node-method="filterNodeMethod"
    @node-click="handleNodeClick"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, defineModel } from "vue";
import { useUserStoreHook } from "/@/store/modules/user";

// 使用defineModel实现双向绑定
const nodeClickVal = defineModel<any>("nodeClickVal");

// 组织数据
const organizationData = ref<any[]>([]);

// 树引用
const treeRef = ref();

// 节点点击事件
function handleNodeClick(data) {
  nodeClickVal.value = data;
}

// 获取组织数据
function getData() {
  try {
    // 从store中获取组织数据
    const userStore = useUserStoreHook();
    organizationData.value = userStore.orgData || [];
  } catch (error) {
    console.error("获取组织数据失败:", error);
  }
}

// 过滤节点方法
const filterNodeMethod = (value: string, data: any, node: any) => {
  if (!value) return true;
  // 如果提供了自定义过滤方法，则使用自定义方法
  if (props.filterNodeMethod !== (() => true)) {
    return props.filterNodeMethod(value, data, node);
  }
  // 默认过滤方法：检查节点名称是否包含搜索值
  return data.name.includes(value);
};

// 定义props
const props = withDefaults(
  defineProps<{
    filterNodeMethod: Function;
  }>(),
  {
    filterNodeMethod: () => true
  }
);

// 搜索值
const searchValue = ref("");

// 过滤树节点
function filter(value: string) {
  searchValue.value = value;
  treeRef.value?.filter(value);
}

// 组件挂载时获取数据
onMounted(() => {
  getData();
});

// 暴露方法
defineExpose({
  filter,
  treeRef
});
</script>

<style scoped>
/* 使用UnoCSS实现样式 */
</style>
