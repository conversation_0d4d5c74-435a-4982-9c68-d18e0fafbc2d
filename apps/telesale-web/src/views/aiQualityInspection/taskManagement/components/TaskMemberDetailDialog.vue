<!--
 * @Date         : 2025-05-20 10:00:00
 * @Description  : 任务管理 - 任务成员明细对话框
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="任务成员明细"
    width="900px"
    destroy-on-close
    @open="fetchTaskMemberDetails"
  >
    <div class="task-member-detail">
      <!-- 标签页 -->
      <el-tabs v-model="activeTab">
        <el-tab-pane :label="incompleteTabLabel" name="incomplete">
          <el-table :data="incompleteMemberList" border>
            <el-table-column prop="name" label="成员姓名" />
            <el-table-column prop="groupName2" label="组织架构团" />
            <el-table-column prop="groupName3" label="组织架构组" />
            <el-table-column prop="groupName4" label="组织架构小组" />
            <el-table-column prop="progress" label="任务完成进度" />
          </el-table>
        </el-tab-pane>
        <el-tab-pane :label="completeTabLabel" name="complete">
          <el-table :data="completeMemberList" border>
            <el-table-column prop="name" label="成员姓名" />
            <el-table-column prop="groupName2" label="组织架构团" />
            <el-table-column prop="groupName3" label="组织架构组" />
            <el-table-column prop="groupName4" label="组织架构小组" />
            <el-table-column prop="progress" label="任务完成进度" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <template #footer>
      <el-button @click="visible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineModel, computed } from "vue";
import { ElMessage } from "element-plus";
import { getTrainingTaskProcess } from "/@/api/AIQualityInspection/taskManagement";

// 定义API响应接口
interface TaskMemberItem {
  workerId: number;
  workerName: string;
  courseNum: number;
  finishNum: number;
  groupName2?: string; // 组织架构团
  groupName3?: string; // 组织架构组
  groupName4?: string; // 组织架构小组
}

// 使用defineModel实现双向绑定
const visible = defineModel<boolean>("visible");

// 定义props
const props = defineProps({
  taskId: {
    type: Number,
    default: undefined
  }
});

// 标签页激活状态
const activeTab = ref("incomplete");

// 成员列表
interface MemberDetail {
  id: number;
  name: string;
  groupName2: string; // 组织架构团
  groupName3: string; // 组织架构组
  groupName4: string; // 组织架构小组
  progress: string;
  finished: boolean;
}

// 成员数据
const memberList = ref<MemberDetail[]>([]);
const loading = ref(false);

// 已完成和未完成的成员列表
const completeMemberList = computed(() =>
  memberList.value.filter(member => member.finished)
);

const incompleteMemberList = computed(() =>
  memberList.value.filter(member => !member.finished)
);

// 标签页标题
const completeTabLabel = computed(
  () => `已完成 (${completeMemberList.value.length})`
);

const incompleteTabLabel = computed(
  () => `未完成 (${incompleteMemberList.value.length})`
);

// 获取任务成员详情
async function fetchTaskMemberDetails() {
  if (!props.taskId) {
    ElMessage.error("任务ID不能为空");
    return;
  }

  try {
    loading.value = true;
    const res = await getTrainingTaskProcess(props.taskId);

    // 处理API返回的数据
    const data = res.data || res;

    // 从响应中提取成员数据
    let memberItems: TaskMemberItem[] = [];

    // 使用类型断言处理API响应
    const apiResponse = data as any;
    if (apiResponse && apiResponse.items) {
      memberItems = Array.isArray(apiResponse.items)
        ? apiResponse.items
        : [apiResponse.items];
    }

    // 转换数据格式
    memberList.value = memberItems.map(item => {
      return {
        id: item.workerId || 0,
        name: item.workerName || "未知",
        groupName2: item.groupName2 || "-",
        groupName3: item.groupName3 || "-",
        groupName4: item.groupName4 || "-",
        progress: `${item.finishNum || 0}/${item.courseNum || 0}`,
        finished: item.finishNum === item.courseNum && item.courseNum > 0
      };
    });
  } catch (error) {
    console.error("获取任务成员详情失败:", error);
    ElMessage.error("获取任务成员详情失败");
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.task-member-detail {
  padding: 0 10px;
}
</style>
