<!--
 * @Date         : 2025-05-16 10:00:00
 * @Description  : 课程选择器组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="course-selector w-100%">
    <!-- 课程选择器 -->
    <el-select
      v-model="selectedCourseIds"
      multiple
      collapse-tags
      collapse-tags-tooltip
      filterable
      :clearable="!props.isEdit || props.originalCourseIds.length === 0"
      placeholder="请选择课程"
      class="w-full"
      :loading="coursesLoading"
      :multiple-limit="20"
      @change="handleCoursesChange"
      @clear="handleClear"
    >
      <template #tag="{ item }">
        <el-tag
          :closable="
            !(props.isEdit && props.originalCourseIds.includes(item.value))
          "
          :disable-transitions="false"
          @close="handleTagClose(item)"
        >
          {{ item.label }}
        </el-tag>
      </template>
      <el-option
        v-for="course in courseOptions"
        :key="course.id"
        :label="course.name"
        :value="course.id"
      />
    </el-select>
    <div class="flex flex-col">
      <div class="flex">
        <div class="text-gray-400 mt-5px mb-10px text-12px">
          最多可选择20个课程
        </div>
      </div>

      <!-- 已选择课程列表（用于排序） -->
      <div v-if="courses.length > 0" class="selected-courses w-100% mt-10px">
        <div id="course-sortable-list" class="course-list">
          <div
            v-for="(course, index) in courses"
            :key="course.id"
            class="course-item flex justify-between items-center p-10px hover:bg-gray-50 border-b border-solid border-gray-200 last:border-b-0"
          >
            <div class="flex items-center">
              <el-button
                class="move cursor-move mr-10px"
                type="primary"
                link
                :icon="Rank"
              />
              <span class="mr-10px w-150px textHideLine1">
                {{ course.name }}
              </span>
            </div>
            <el-button
              v-if="
                !(props.isEdit && props.originalCourseIds.includes(course.id))
              "
              type="danger"
              link
              :icon="Delete"
              @click="removeCourse(index)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, defineModel, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { Delete, Rank } from "@element-plus/icons-vue";
// @ts-ignore
import Sortable from "sortablejs";
import {
  getTrainingCourseList,
  TrainingCourseInstance
} from "/@/api/AIQualityInspection/courseManage";

// 使用defineModel实现双向绑定
const selectedCourseIds = defineModel<number[]>("selectedCourseIds", {
  default: []
});
const courses = defineModel<any[]>("courses", { default: [] });

// 接收props
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false
  },
  originalCourseIds: {
    type: Array as () => number[],
    default: () => []
  }
});

// 课程列表相关
const courseOptions = ref<TrainingCourseInstance[]>([]);
const coursesLoading = ref(false);

// 获取课程列表
async function fetchCourseList() {
  try {
    coursesLoading.value = true;
    const res = await getTrainingCourseList({
      pages: 1,
      pageSize: 9999
    });

    // 处理课程数据
    courseOptions.value = res.data?.courses || [];
  } catch (error) {
    console.error("获取课程列表失败:", error);
    ElMessage.error("获取课程列表失败");
  } finally {
    coursesLoading.value = false;
  }
}

// 处理课程选择变化
function handleCoursesChange(selectedIds: number[]) {
  // 保存原始的selectedCourseIds，用于后续比较
  const originalSelectedIds = [...selectedCourseIds.value];

  // 在编辑模式下，检查是否有原始课程被移除
  if (props.isEdit && props.originalCourseIds.length > 0) {
    // 找出被移除的原始课程
    const removedOriginalCourseIds = props.originalCourseIds.filter(
      id => !selectedIds.includes(id)
    );

    // 如果有原始课程被移除，恢复它们并提示用户
    if (removedOriginalCourseIds.length > 0) {
      ElMessage.warning("编辑任务时不允许删除已下发的课程");

      // 恢复被移除的原始课程，但保持原有顺序
      // 首先找出所有应该保留的ID（包括用户选择的和必须保留的原始课程）
      const idsToKeep = new Set([...selectedIds, ...removedOriginalCourseIds]);

      // 然后按照原来的顺序重建selectedIds数组
      const newSelectedIds: number[] = [];

      // 首先添加原来就有的ID（保持原顺序）
      originalSelectedIds.forEach(id => {
        if (idsToKeep.has(id)) {
          newSelectedIds.push(id);
          idsToKeep.delete(id);
        }
      });

      // 然后添加新增的ID
      idsToKeep.forEach(id => newSelectedIds.push(id));

      // 更新选中的课程ID
      selectedIds = newSelectedIds;
      selectedCourseIds.value = newSelectedIds;
    }
  }

  // 更新已选课程列表
  // 找出新增的课程ID
  const existingCourseIds = courses.value.map(c => c.id);
  const newlyAddedIds = selectedIds.filter(
    id => !existingCourseIds.includes(id)
  );

  // 找出被移除的课程ID（不包括原始课程，因为它们已经被恢复）
  const removedIds = existingCourseIds.filter(id => !selectedIds.includes(id));

  // 如果没有变化，直接返回
  if (newlyAddedIds.length === 0 && removedIds.length === 0) {
    return;
  }

  // 创建课程列表的副本
  let updatedCourses = [...courses.value];

  // 移除被删除的课程
  updatedCourses = updatedCourses.filter(
    course => !removedIds.includes(course.id)
  );

  // 添加新课程（添加到列表末尾）
  const newCourses = newlyAddedIds.map(id => {
    // 查找课程详情
    const courseInfo = courseOptions.value.find(c => c.id === id);
    return {
      id,
      name: courseInfo?.name || `课程${id}`,
      score: 1 // 默认权重
    };
  });

  // 将新课程添加到列表末尾
  updatedCourses = [...updatedCourses, ...newCourses];

  // 确保课程顺序与selectedIds一致（仅用于验证所有ID都存在）
  const allIds = updatedCourses.map(c => c.id);
  const missingIds = selectedIds.filter(id => !allIds.includes(id));

  // 如果有缺失的ID（理论上不应该发生），添加它们
  if (missingIds.length > 0) {
    const missingCourses = missingIds.map(id => {
      const courseInfo = courseOptions.value.find(c => c.id === id);
      return {
        id,
        name: courseInfo?.name || `课程${id}`,
        score: 1
      };
    });
    updatedCourses = [...updatedCourses, ...missingCourses];
  }

  // 更新课程列表
  courses.value = updatedCourses;

  // 初始化排序功能
  nextTick(() => {
    initSortable();
  });
}

// 移除课程
function removeCourse(index: number) {
  const removedCourse = courses.value[index];

  // 在编辑模式下，检查是否为原有课程
  if (props.isEdit && props.originalCourseIds.includes(removedCourse.id)) {
    ElMessage.warning("编辑任务时不允许删除已下发的课程");
    return;
  }

  // 创建数组副本
  const tempCourses = [...courses.value];
  // 从副本中移除课程
  tempCourses.splice(index, 1);

  // 先清空数组，触发视图更新
  courses.value = [];

  // 在下一个 tick 中重新赋值，确保视图正确更新
  nextTick(() => {
    courses.value = tempCourses;

    // 重新初始化排序功能
    nextTick(() => {
      initSortable();
    });
  });

  // 同步更新selectedCourseIds，但保持顺序
  if (removedCourse && removedCourse.id) {
    // 创建一个新的数组，保留所有不等于被删除ID的元素
    selectedCourseIds.value = selectedCourseIds.value.filter(
      id => id !== removedCourse.id
    );
  }
}

// 课程排序功能
let courseSortable: any = null;
function initSortable() {
  // 确保在DOM更新后执行
  nextTick(() => {
    // 销毁之前的实例
    if (courseSortable) {
      try {
        courseSortable.destroy();
      } catch (e) {
        console.error("销毁Sortable实例失败:", e);
      }
      courseSortable = null;
    }

    // 获取DOM元素
    const el = document.getElementById("course-sortable-list");
    if (!el) {
      console.warn("未找到排序列表元素");
      return;
    }

    // 创建新的Sortable实例
    try {
      courseSortable = new Sortable(el, {
        handle: ".move",
        animation: 150,
        draggable: ".course-item",
        ghostClass: "sortable-ghost",
        chosenClass: "sortable-chosen",
        forceFallback: false, // 改为false，使用原生拖拽
        fallbackClass: "sortable-fallback",
        delay: 50, // 增加延迟，防止误触
        delayOnTouchOnly: true, // 仅在触摸设备上延迟
        onStart() {
          // 拖拽开始时的处理
          console.log("开始拖拽");
        },
        onEnd({ oldIndex, newIndex }: { oldIndex: number; newIndex: number }) {
          console.log("拖拽结束", oldIndex, newIndex);
          if (oldIndex === newIndex) return;

          // 保存当前数据的副本
          const tempCourses = [...courses.value];
          const currRow = tempCourses.splice(oldIndex, 1)[0];
          tempCourses.splice(newIndex, 0, currRow);

          // 先清空数组，触发视图更新
          courses.value = [];

          // 在下一个 tick 中重新赋值，确保视图正确更新
          nextTick(() => {
            courses.value = tempCourses;
            // 重新初始化排序
            nextTick(() => {
              initSortable();
            });
          });
        }
      });
      console.log("排序功能初始化成功");
    } catch (e) {
      console.error("初始化Sortable失败:", e);
    }
  });
}

// 监听课程列表变化，确保排序功能正常
watch(
  () => courses.value.length,
  newLength => {
    if (newLength > 0) {
      nextTick(() => {
        initSortable();
      });
    }
  }
);

// 处理标签关闭事件
function handleTagClose(item: any) {
  // 在编辑模式下，检查是否为原有课程
  if (props.isEdit && props.originalCourseIds.includes(item.value)) {
    ElMessage.warning("编辑任务时不允许删除已下发的课程");
    return;
  }

  // 找到对应课程的索引
  const index = courses.value.findIndex(course => course.id === item.value);
  if (index !== -1) {
    removeCourse(index);
  }
}

// 处理清空事件
function handleClear() {
  // 在编辑模式下，如果有原始课程，则不允许清空
  if (props.isEdit && props.originalCourseIds.length > 0) {
    ElMessage.warning("编辑任务时不允许删除已下发的课程");

    // 恢复原始课程
    nextTick(() => {
      selectedCourseIds.value = [...props.originalCourseIds];

      // 保留原始课程，移除非原始课程
      const originalCourses = courses.value.filter(course =>
        props.originalCourseIds.includes(course.id)
      );

      // 查找缺失的原始课程
      const existingOriginalIds = originalCourses.map(c => c.id);
      const missingOriginalIds = props.originalCourseIds.filter(
        id => !existingOriginalIds.includes(id)
      );

      // 如果有缺失的原始课程，添加它们
      if (missingOriginalIds.length > 0) {
        const missingCourses = missingOriginalIds.map(id => {
          const courseInfo = courseOptions.value.find(c => c.id === id);
          return {
            id,
            name: courseInfo?.name || `课程${id}`,
            score: 1
          };
        });
        courses.value = [...originalCourses, ...missingCourses];
      } else {
        courses.value = originalCourses;
      }

      // 初始化排序功能
      nextTick(() => {
        initSortable();
      });
    });
  }
}

// 组件挂载时获取课程列表
onMounted(() => {
  fetchCourseList();
  initSortable();
});

// 暴露方法给父组件
defineExpose({
  fetchCourseList,
  initSortable
});
</script>

<style scoped>
.cursor-move {
  cursor: move;
}

.course-list {
  min-height: 40px;
  position: relative;
}

.course-item {
  background-color: #fff;
  transition: background-color 0.2s ease;
  position: relative;
  z-index: 1;
}

.course-item:hover {
  background-color: #f5f7fa;
}

.sortable-ghost {
  opacity: 0.5;
  background-color: #f0f9ff !important;
}

.sortable-chosen {
  background-color: #ecf5ff !important;
  z-index: 10;
}

.sortable-fallback {
  opacity: 0.8;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 100;
}
</style>
