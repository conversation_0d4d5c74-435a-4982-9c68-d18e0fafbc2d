<!--
 * @Date         : 2025-04-23 18:34:46
 * @Description  : 优秀问答库
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="g-margin-20 excellent-qa-container">
    <el-card>
      <div class="search-form">
        <nexus-form inline>
          <el-form-item>
            <nexus-date-picker
              ref="datePickerRef"
              v-model="searchForm.time"
              type="daterange"
              value-format="x"
              :show-shortcuts="true"
              :limit-type="'paramast'"
              :limit-date="{
                num: 1,
                type: 'year'
              }"
            />
          </el-form-item>
          <el-form-item>
            <el-input
              v-model.trim="searchForm.actionId"
              placeholder="请输入通话ID"
              clearable
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">搜索</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </nexus-form>
      </div>

      <nexus-table
        un-mounted
        restrict
        ref="tableRef"
        :get-list="getExcellentQandaList"
        :res-format="data => data?.data"
        data-key="items"
      >
        <el-table-column label="通话ID" prop="actionId">
          <template #default="{ row }">
            <div class="flex items-center">
              <span>{{ row.actionId }}</span>
              <el-icon
                class="ml-5px cursor-pointer"
                title="复制"
                @click="copyText(row.actionId)"
              >
                <DocumentCopy />
              </el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="生成问题数" prop="total" />
        <el-table-column label="已添加问题数" prop="finished" />
        <el-table-column label="暂不处理问答数">
          <template #default="{ row }">
            {{ getUndoCount(row.items) }}
          </template>
        </el-table-column>
        <nexus-table-filter-column
          label="AI评级"
          prop="aiAppraiseScore"
          :table-ref="tableRef"
          :option-list="[
            { text: 'A级', val: 'A' },
            { text: 'B级', val: 'B' },
            { text: 'C级', val: 'C' },
            { text: 'D级', val: 'D' }
          ]"
        >
          <template #default="{ row }">
            {{ row.aiAppraiseScore || "-" }}
          </template>
        </nexus-table-filter-column>
        <nexus-table-filter-column
          label="复检结果"
          prop="humanAppraiseReason"
          :table-ref="tableRef"
          :option-list="[
            { text: '通过', val: '通过' },
            { text: '不通过', val: '不通过' }
          ]"
        >
          <template #default="{ row }">
            {{ row.humanAppraiseReason || "-" }}
          </template>
        </nexus-table-filter-column>
        <nexus-table-filter-column
          label="复检评级"
          prop="humanAppraiseScore"
          :table-ref="tableRef"
          :option-list="[
            { text: 'A级', val: 'A' },
            { text: 'B级', val: 'B' },
            { text: 'C级', val: 'C' },
            { text: 'D级', val: 'D' }
          ]"
        >
          <template #default="{ row }">
            {{ row.humanAppraiseScore || "-" }}
          </template>
        </nexus-table-filter-column>
        <el-table-column
          label="生成时间"
          prop="createdAt"
          :formatter="row => formatDateTime(row.createdAt)"
        />
        <el-table-column label="操作" fixed="right" width="180">
          <template #default="{ row }">
            <el-link
              type="primary"
              :underline="false"
              @click="viewDetail(row)"
              class="mr-10px"
            >
              问答明细
            </el-link>
            <el-link type="primary" :underline="false" @click="viewRecord(row)">
              查看录音
            </el-link>
          </template>
        </el-table-column>
      </nexus-table>
    </el-card>

    <!-- 查看录音抽屉 -->
    <DetailDrawer v-model:visible="drawerVisible" :detail-info="currentRow" />

    <!-- 问答明细组件 -->
    <QADetails ref="qaDetailsRef" @success="refreshTable" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from "vue";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { DocumentCopy } from "@element-plus/icons-vue";
import DetailDrawer from "../excellentCases/DetailDrawer/index.vue";
import QADetails from "./QADetails/index.vue";
import NexusDatePicker from "/@/components/Nexus/NexusDatePicker/index.vue";
import NexusTableFilterColumn from "/@/components/Nexus/NexusTableFilterColumn/index.vue";
import {
  getExcellentQandaList,
  ExcellentQandaStatus
} from "/@/api/AIQualityInspection/excellentQA";

// 表格引用
const tableRef = ref();
const drawerVisible = ref(false);
const currentRow = ref();
const qaDetailsRef = ref();

// 搜索表单
interface SearchForm {
  actionId: string;
  time: any;
  beginTime?: number;
  endTime?: number;
}

const searchForm = reactive<SearchForm>({
  actionId: "",
  time: null
});

/**
 * @description: 计算暂不处理问答数
 * @param {any[]} items 问答列表
 * @return {number} 暂不处理问答数
 */
function getUndoCount(items: any[]): number {
  if (!items || !Array.isArray(items)) {
    return 0;
  }
  return items.filter(item => item.status === ExcellentQandaStatus.UNDO).length;
}

// 格式化日期时间
const formatDateTime = (timestamp: number) => {
  return dayjs(timestamp).format("YYYY-MM-DD HH:mm:ss");
};

// 复制文本
const copyText = (text: string) => {
  navigator.clipboard.writeText(text).then(
    () => {
      ElMessage.success("复制成功");
    },
    () => {
      ElMessage.error("复制失败");
    }
  );
};

const datePickerRef = ref();
// 搜索
const onSearch = () => {
  const params: any = { ...searchForm };
  console.log("params", params);
  // 处理时间范围
  params.beginTime = datePickerRef.value.unixsDatePicker[0];
  params.endTime = datePickerRef.value.unixsDatePicker[1];

  tableRef.value?.search(params);
};

// 重置
const onReset = () => {
  searchForm.actionId = "";
  searchForm.time = null;

  // 重置后自动搜索
  onSearch();
};

// 查看问答明细
const viewDetail = (row: any) => {
  console.log("查看问答明细", row);
  // 调用问答明细组件的打开方法
  qaDetailsRef.value?.openDialog(row);
};

// 查看录音
const viewRecord = (row: any) => {
  currentRow.value = row;
  drawerVisible.value = true;
};

/**
 * 刷新表格数据
 */
function refreshTable() {
  tableRef.value?.update();
}

// 生命周期钩子
onMounted(() => {
  datePickerRef.value.initDatePickToCurrentMonth();
  nextTick(() => {
    onSearch();
  });
});
</script>

<style scoped lang="scss">
.excellent-qa-container {
  .el-card {
    margin-bottom: 20px;
  }
}
</style>
