<!--
 * @Date         : 2025-04-23 18:34:46
 * @Description  : 问答明细
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div>
    <!-- 问答明细抽屉 -->
    <QADetailsDrawer
      v-model:visible="drawerVisible"
      :detail-info="currentInfo"
      @success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits } from "vue";
import QADetailsDrawer from "./components/QADetailsDrawer.vue";

/**
 * 抽屉可见性状态
 */
const drawerVisible = ref(false);

/**
 * 当前问答信息
 */
const currentInfo = ref<any>({});

/**
 * 定义事件，用于向父组件发送通知
 */
const emit = defineEmits(["success"]);

/**
 * 打开问答明细抽屉
 * @param info - 问答信息数据
 */
function openDialog(info: any) {
  currentInfo.value = info;
  drawerVisible.value = true;
}

/**
 * 处理学习问题成功的回调
 */
function handleSuccess() {
  // 触发success事件，通知父组件
  emit("success");
}

// 向父组件暴露方法
defineExpose({
  openDialog
});
</script>
