<!--
 * @Date         : 2025-04-23 18:34:46
 * @Description  : 学习问题对话框
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="dialogVisible"
    title="学习问题"
    width="800px"
    @open="openDialog"
  >
    <el-form
      ref="formRef"
      :model="questionForm"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="用户问题" prop="userQuestion">
        <el-input
          v-model="questionForm.userQuestion"
          type="textarea"
          :rows="3"
          placeholder="请输入用户问题"
        />
      </el-form-item>

      <el-form-item label="回答" prop="answerType">
        <div class="answer-container">
          <div class="answer-header">
            <el-radio v-model="questionForm.answerType" label="sales">
              销售回答
            </el-radio>
          </div>
          <el-input
            v-model="questionForm.salesAnswer"
            type="textarea"
            :rows="3"
            placeholder="请输入销售回答"
            :class="{
              'selected-answer': questionForm.answerType === 'sales',
              'unselected-answer': questionForm.answerType !== 'sales'
            }"
          />

          <div class="answer-header mt-4">
            <el-radio v-model="questionForm.answerType" label="ai">
              AI优化回答
            </el-radio>
          </div>
          <el-input
            v-model="questionForm.aiAnswer"
            type="textarea"
            :rows="3"
            placeholder="请输入AI优化后的回答"
            :class="{
              'selected-answer': questionForm.answerType === 'ai',
              'unselected-answer': questionForm.answerType !== 'ai'
            }"
          />
        </div>
      </el-form-item>

      <el-form-item label="添加标签">
        <div class="w-100% mb-2">
          <el-button
            type="primary"
            size="small"
            :icon="Plus"
            @click="openSelectLabel"
          >
            添加标签
          </el-button>
        </div>

        <div class="flex flex-wrap gap-2">
          <el-tag
            v-for="tag in questionForm.tags"
            :key="tag.id"
            closable
            @close="removeTag(tag)"
          >
            {{ tag.name }}
          </el-tag>
        </div>
      </el-form-item>

      <el-form-item label="选择分类" prop="categoryId">
        <el-tree-select
          v-model="questionForm.categoryId"
          :data="categoryList"
          node-key="id"
          placeholder="请选择分类"
          :props="{
            children: 'nodes',
            label: 'name'
          }"
          check-strictly
          filterable
          clearable
          :default-expand-level="1"
          :render-after-expand="true"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="formLoading" @click="submitForm">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>

  <SelectLabelDialog
    v-model:visible="selectLabelVisible"
    :tags="questionForm.tags"
    @update:tags="handleSelectLabel"
  />
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { Plus, Document, Folder } from "@element-plus/icons-vue";
import SelectLabelDialog from "./SelectLabelDialog.vue";
import {
  markExcellentQanda,
  queryLibraryCategory,
  addKnowledge,
  LibraryCategoryListReply,
  LibraryCategoryNodeInfo
} from "/@/api/AIQualityInspection/excellentQA";
import libraryInfo from "/@/views/aiQualityInspection/const/Library";
import dayjs from "dayjs";

const props = defineProps<{
  detailInfo: any;
  actionInfo: any;
}>();

const dialogVisible = defineModel<boolean>("visible", { default: false });

/**
 * 定义事件
 */
const emit = defineEmits(["success"]);

// 表单加载状态
const formLoading = ref(false);
const formRef = ref();

// 选择标签对话框
const selectLabelVisible = ref(false);

// 问题表单
const questionForm = reactive({
  userQuestion: "",
  answerType: "sales", // 默认选择销售回答
  salesAnswer: "",
  aiAnswer: "",
  categoryId: "",
  tags: [] as any[]
});

// 表单验证规则
const rules = {
  userQuestion: [
    { required: true, message: "请输入用户问题", trigger: "blur" }
  ],
  answerType: [
    { required: true, message: "请选择回答类型", trigger: "change" }
  ],
  categoryId: [{ required: true, message: "请选择分类", trigger: "change" }]
};

// 分类列表
const categoryList = ref([]);

// 获取分类列表数据
const fetchCategoryList = async () => {
  console.log("libraryInfo", libraryInfo);
  try {
    formLoading.value = true;
    const res = await queryLibraryCategory({
      libraryId: libraryInfo.libraryId,
      libraryUUID: libraryInfo.libraryUUID
    });
    categoryList.value = res.data.nodes;
  } catch (error) {
    console.error("获取分类列表失败", error);
    ElMessage.error("获取分类列表失败");
  } finally {
    formLoading.value = false;
  }
};

// 打开选择标签对话框
const openSelectLabel = () => {
  selectLabelVisible.value = true;
};

// 处理选择标签
const handleSelectLabel = (tags: any[]) => {
  questionForm.tags = tags;
};

// 移除标签
const removeTag = (tag: any) => {
  const index = questionForm.tags.findIndex(
    (t: any) =>
      (tag.key && t.key && tag.key === t.key) ||
      (tag.id && t.id && tag.id === t.id)
  );
  if (index > -1) {
    questionForm.tags.splice(index, 1);
  }
};

// 打开对话框初始化
const openDialog = async () => {
  // 获取分类列表
  await fetchCategoryList();

  // 如果有详情数据，填充表单
  questionForm.userQuestion = props.detailInfo.question;

  questionForm.answerType = "sales";
  questionForm.aiAnswer = props.detailInfo.aiAnswer;
  questionForm.salesAnswer = props.detailInfo.answer;

  questionForm.categoryId = "";
  questionForm.tags = props.detailInfo.tags || [];
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;

  formRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false;
    }

    // 确保根据选择的回答类型验证对应的回答字段
    if (questionForm.answerType === "sales" && !questionForm.salesAnswer) {
      ElMessage.error("请输入销售回答");
      return false;
    }

    if (questionForm.answerType === "ai" && !questionForm.aiAnswer) {
      ElMessage.error("请输入AI优化回答");
      return false;
    }

    formLoading.value = true;
    try {
      // 构造提交数据
      const answer =
        questionForm.answerType === "sales"
          ? questionForm.salesAnswer
          : questionForm.aiAnswer;

      // 提取id和构造参数对象
      const id = props.actionInfo.id;
      const tags = questionForm.tags.map((tag: any) => ({
        id: tag.groupId || null,
        key: tag.groupKey || "",
        libraryId: libraryInfo.libraryId,
        name: tag.groupName || "",
        tags: [
          {
            id: tag.id || null,
            name: tag.name,
            key: tag.key || "",
            groupId: tag.groupId || null
          }
        ]
      }));
      const params = {
        items: [
          {
            ...props.detailInfo,
            status: "mark",
            choose: questionForm.answerType === "sales" ? "answer" : "aiAnswer"
          }
        ]
      };
      // 调用API标记问答
      await addKnowledge({
        baseQuestion: questionForm.userQuestion,
        answer: answer,
        knowledgeCategoryId: Number(questionForm.categoryId),
        libraryId: libraryInfo.libraryId,
        source: "create_knowledge",
        tags: tags,
        timeStatus: true,
        effective: dayjs().unix(),
        expire: dayjs("2038-1-1").unix()
      });

      await markExcellentQanda(id, params);

      // 关闭对话框
      dialogVisible.value = false;

      // 触发成功事件
      emit("success");

      // 成功提示
      ElMessage.success("保存成功");
    } catch (error) {
      console.error("保存失败", error);
      ElMessage.error("保存失败");
    } finally {
      formLoading.value = false;
    }
  });
};
</script>

<style scoped lang="scss">
.el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.answer-container {
  width: 100%;
}

.answer-header {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.selected-answer {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.unselected-answer {
  opacity: 0.7;
}
</style>
