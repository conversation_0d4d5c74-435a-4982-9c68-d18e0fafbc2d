<!--
 * @Date         : 2025-04-23 18:34:46
 * @Description  : 问答明细抽屉
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-drawer
    v-model="drawerVisible"
    title="问答明细"
    size="70%"
    destroy-on-close
    direction="rtl"
    :before-close="handleClose"
  >
    <div class="flex justify-end">
      <!-- 查看录音按钮，点击弹出录音抽屉 -->
      <el-button type="primary" size="small" @click="onShowRecord">
        查看录音
      </el-button>
    </div>

    <div class="qa-details-drawer">
      <el-table
        border
        :data="tableData"
        :tooltip-options="{
          popperClass: 'excellent-cases-tooltip'
        }"
      >
        <el-table-column
          label="用户问题"
          prop="question"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="销售回答"
          prop="answer"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="AI优化后回答"
          prop="aiAnswer"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column label="处理状态" prop="status" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          width="180"
          v-if="hasOperationColumn"
        >
          <template #default="{ row }">
            <div
              v-if="
                row.status !== ExcellentQandaStatus.MARK &&
                row.status !== ExcellentQandaStatus.UNDO
              "
            >
              <el-button type="primary" link @click="handleLearnQuestion(row)">
                学习问题
              </el-button>
              <el-button type="warning" link @click="handleUndoQuestion(row)">
                暂不处理
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 学习问题对话框 -->
    <QuestionInfoDialog
      v-model:visible="dialogVisible"
      :action-info="detailInfo"
      :detail-info="currentRow"
      @success="handleQuestionSuccess"
    />
  </el-drawer>

  <!-- 录音详情抽屉 -->
  <DetailDrawer
    v-model:visible="recordDrawerVisible"
    :detail-info="detailInfo"
  />
</template>

<script setup lang="ts">
import {
  ref,
  defineModel,
  defineProps,
  defineEmits,
  watch,
  computed
} from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import QuestionInfoDialog from "./QuestionInfoDialog.vue";
import DetailDrawer from "../../../excellentCases/DetailDrawer/index.vue";
import {
  markExcellentQanda,
  ExcellentQandaStatus,
  type ExcellentQandaStatusType
} from "../../../../../api/AIQualityInspection/excellentQA";

/**
 * 组件属性定义
 * @property {any} detailInfo - 问答明细详情信息
 */
const props = defineProps<{
  detailInfo: any; // 详情信息
}>();

/**
 * 组件事件定义
 */
const emit = defineEmits(["update:visible", "success"]);

/**
 * 抽屉可见性双向绑定
 * @default false - 默认不可见
 */
const drawerVisible = defineModel<boolean>("visible", { default: false });

/**
 * 当前选中的行数据
 */
const currentRow = ref<any>(null);

/**
 * 对话框可见性状态
 */
const dialogVisible = ref(false);

/**
 * 间接控制录音详情抽屉的可见性
 */
const recordDrawerVisible = ref(false);

/**
 * 表格数据
 */
const tableData = ref<any[]>([]);

/**
 * 计算是否显示操作列
 */
const hasOperationColumn = computed(() => {
  return tableData.value.some(
    item =>
      item.status !== ExcellentQandaStatus.MARK &&
      item.status !== ExcellentQandaStatus.UNDO
  );
});

/**
 * 获取状态标签类型
 * @param status - 状态值
 * @returns 标签类型
 */
function getStatusTagType(status: ExcellentQandaStatusType) {
  switch (status) {
    case ExcellentQandaStatus.MARK:
      return "success";
    case ExcellentQandaStatus.UNDO:
      return "warning";
    default:
      return "info";
  }
}

/**
 * 获取状态文本
 * @param status - 状态值
 * @returns 状态文本
 */
function getStatusText(status: ExcellentQandaStatusType) {
  switch (status) {
    case ExcellentQandaStatus.MARK:
      return "已添加";
    case ExcellentQandaStatus.UNDO:
      return "暂不处理";
    default:
      return "待处理";
  }
}

/**
 * 监听详情信息变化，更新表格数据
 */
watch(
  () => props.detailInfo,
  newValue => {
    if (newValue && newValue.items) {
      tableData.value = newValue.items.map((item: any) => ({
        ...item,
        status: item.status || ExcellentQandaStatus.UNMARK
      }));
    } else {
      tableData.value = [];
    }
  },
  { immediate: true, deep: true }
);

/**
 * 处理关闭问答明细抽屉
 */
function handleClose() {
  drawerVisible.value = false;
}

/**
 * 处理学习问题按钮点击
 * @param row - 行数据
 */
function handleLearnQuestion(row: any) {
  currentRow.value = row;
  dialogVisible.value = true;
}

/**
 * 处理暂不处理按钮点击
 * @param row - 行数据
 */
async function handleUndoQuestion(row: any) {
  try {
    await ElMessageBox.confirm("是否忽略该条问答？", "提示", {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning"
    });

    // 调用接口标记为暂不处理
    await markExcellentQanda(props.detailInfo.id, {
      items: [
        {
          uuid: row.uuid,
          choose: "",
          status: ExcellentQandaStatus.UNDO
        }
      ]
    });

    // 更新本地状态
    row.status = ExcellentQandaStatus.UNDO;
    ElMessage.success("操作成功");

    // 通知父组件刷新
    emit("success");
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("操作失败");
    }
  }
}

/**
 * 处理问题学习成功回调
 */
function handleQuestionSuccess() {
  // 刷新表格数据或通知父组件更新
  emit("success");
  if (currentRow.value) {
    currentRow.value.status = ExcellentQandaStatus.MARK;
  }
}

/**
 * 处理查看录音按钮点击，弹出录音详情抽屉
 */
function onShowRecord() {
  recordDrawerVisible.value = true;
}
</script>

<style scoped lang="scss">
.qa-details-drawer {
  padding: 20px;
  height: 100%;
}
</style>

<style>
.excellent-cases-tooltip {
  max-width: 500px;
}
</style>
