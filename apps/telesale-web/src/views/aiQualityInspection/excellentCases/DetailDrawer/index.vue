<!--
 * @Date         : 2024-06-24
 * @Description  : 通话详情抽屉
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-drawer
    v-model="_visible"
    destroy-on-close
    title="查看通话"
    size="90%"
    class="drawer-box"
    @open="openDrawer"
  >
    <template #title>
      <div class="text-18px font-bold c-black">查看通话</div>
    </template>

    <div class="flexD">
      <div class="audio-box mb-10px w-800px">
        <Audio
          :action-id="detailInfo?.actionId"
          :createdAt="detailInfo?.createdAt"
          :audioSrc="detailInfo?.audioURL"
        />
      </div>
    </div>

    <div v-loading="loading" class="h-100% overflow-auto flex">
      <div class="mr-20px">
        <Talk :content="conversationData" class="w-700px" />
      </div>

      <div class="flex-1">
        <el-card class="rating-description">
          <template #header>
            <div class="text-16px font-bold">评级说明</div>
          </template>

          <div class="rating-content">
            <span v-html="caseDetail?.aiAppraise?.result || ''" />
          </div>
        </el-card>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useVModel } from "@vueuse/core";
import { ElMessage } from "element-plus";
import Audio from "/@/views/AISupport/Quality/DetailDrawer/children/Audio.vue";
import Talk from "../components/Talk.vue";
import {
  getExcellentCaseText,
  getExcellentCaseDetail,
  type ExcellentCaseDetailReply
} from "/@/api/AIQualityInspection/excellentCases";

const props = defineProps<{
  visible: boolean;
  detailInfo: any;
}>();
const emit = defineEmits(["update:visible"]);

const _visible = useVModel(props, "visible", emit);
const loading = ref(false);
const conversationData = ref();
const caseDetail = ref<ExcellentCaseDetailReply>();

async function openDrawer() {
  if (!props.detailInfo?.actionId) {
    ElMessage.warning("缺少通话ID，无法获取通话详情");
    return;
  }

  loading.value = true;
  try {
    // 并行调用API获取通话详情和案例详情
    const [textRes, detailRes] = await Promise.all([
      getExcellentCaseText(props.detailInfo.actionId),
      getExcellentCaseDetail(props.detailInfo.actionId)
    ]);

    // 将API返回的对话数据赋值给conversationData，用于传递给Talk组件
    conversationData.value = textRes.data?.text?.texts;
    // 保存案例详情
    caseDetail.value = detailRes.data?.info;
  } catch (error) {
    console.error("获取通话详情失败", error);
    ElMessage.error("获取通话详情失败");
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped lang="scss">
.audio-box {
  :deep(.audio-control) {
    width: 100%;
  }
}

.rating-description {
  width: 100%;
}

.rating-content {
  max-height: calc(100vh - 330px);
  overflow-y: auto;

  span {
    max-height: calc(100vh - 330px);
  }

  .point {
    color: #333;
    font-weight: 500;
  }

  .score-detail {
    margin-left: 20px;
  }
}

.drawer-box {
  :deep(.el-drawer__body) {
    overflow: hidden;
  }
}
</style>
