<!--
 * @Date         : 2024-06-23
 * @Description  : 优秀案例库搜索组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <nexus-form inline>
    <el-form-item prop="orgId">
      <GroupSelect v-model="searchForm.orgId" />
    </el-form-item>

    <el-form-item prop="workerId">
      <AgentSelect v-model="searchForm.workerId" @change="onAgentChange" />
    </el-form-item>

    <el-form-item prop="actionId">
      <el-input
        v-model.trim="searchForm.actionId"
        placeholder="请输入通话ID"
        clearable
      />
    </el-form-item>

    <el-form-item prop="duration">
      <el-input-number
        v-model="searchForm.minDuration"
        :min="0"
        :step="1"
        step-strictly
        placeholder="最小通话时长/s"
        :controls="false"
      />
      至
      <el-input-number
        v-model="searchForm.maxDuration"
        :min="0"
        :step="1"
        step-strictly
        placeholder="最大通话时长/s"
        :controls="false"
      />
    </el-form-item>

    <el-form-item prop="time">
      <nexus-date-picker
        ref="datePickerRef"
        v-model="searchForm.time"
        type="daterange"
        value-format="x"
        :show-shortcuts="true"
        :limit-type="'paramast'"
        :limit-date="{
          num: 1,
          type: 'year'
        }"
      />
    </el-form-item>
  </nexus-form>
  <nexus-form inline>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
      <el-button :icon="useRenderIcon('refresh')" @click="resetForm">
        重置
      </el-button>
    </el-form-item>
  </nexus-form>
</template>

<script setup lang="ts">
import { reactive, inject } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { type ExcellentCasesQueryParams } from "/@/api/AIQualityInspection/excellentCases";
import NexusDatePicker from "/@/components/Nexus/NexusDatePicker/index.vue";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import GroupSelect from "../components/GroupSelect.vue";

// 导入组件
import AgentSelect from "../components/AgentSelect.vue";

// 定义搜索表单接口
interface SearchForm {
  workerId: any;
  actionId: string;
  minDuration?: number;
  maxDuration?: number;
  time: any;
  orgId: any;
}

// 定义emits
const emit = defineEmits<{
  (e: "onSearch", params: ExcellentCasesQueryParams): void;
}>();

// 定义props
const props = defineProps<{
  tableRef?: any;
}>();

// 初始化搜索表单
const searchForm = reactive<SearchForm>({
  workerId: undefined,
  actionId: "",
  minDuration: undefined,
  maxDuration: undefined,
  time: null,
  orgId: null
});

// 坐席变更
const onAgentChange = (value: any) => {
  // 可以在这里添加特定的处理逻辑
  searchForm.workerId = value;
};

const datePickerRef = ref();
// 构建查询参数
const buildQueryParams = (): ExcellentCasesQueryParams => {
  const params: ExcellentCasesQueryParams = {
    actionId: searchForm.actionId,
    workerId: searchForm.workerId
  };

  // 处理时间范围
  if (datePickerRef.value?.unixsDatePicker) {
    params.beginTime = datePickerRef.value.unixsDatePicker[0];
    params.endTime = datePickerRef.value.unixsDatePicker[1];
  }

  // 处理通话时长范围
  if (searchForm.minDuration !== undefined) {
    params.minDuration = searchForm.minDuration;
  }
  if (searchForm.maxDuration !== undefined) {
    params.maxDuration = searchForm.maxDuration;
  }

  // 添加组织ID
  if (searchForm.orgId) {
    params.orgId = searchForm.orgId;
  }

  return params;
};

// 搜索按钮点击
const onSearch = () => {
  const params = buildQueryParams();

  // 如果父组件提供了tableRef，则直接调用表格的search方法
  if (props.tableRef) {
    props.tableRef.search(params);
  } else {
    // 否则通过emit事件将参数传递给父组件
    emit("onSearch", params);
  }
};

// 更新查询（用于分页、排序等场景）
const updateSearch = (newParams?: Partial<ExcellentCasesQueryParams>) => {
  const params = buildQueryParams();

  // 合并新参数
  if (newParams) {
    Object.assign(params, newParams);
  }

  // 如果父组件提供了tableRef，则直接调用表格的update方法
  if (props.tableRef) {
    props.tableRef.update(params);
  } else {
    // 否则通过emit事件将参数传递给父组件
    emit("onSearch", params);
  }
};

// 重置表单
const resetForm = () => {
  searchForm.workerId = undefined;
  searchForm.actionId = "";
  searchForm.minDuration = undefined;
  searchForm.maxDuration = undefined;
  searchForm.time = null;
  searchForm.orgId = null;

  // 触发搜索事件，使用重置后的表单
  props.tableRef?.resetTable();
};

// 暴露方法给父组件
defineExpose({
  onSearch,
  updateSearch,
  resetForm
});
</script>

<style scoped lang="scss"></style>
