<!--
 * @Date         : 2024-06-23
 * @Description  : 坐席选择组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-select-v2
    v-model="modelValue"
    filterable
    clearable
    :options="options"
    placeholder="请选择坐席"
    @change="onChange"
  />
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useUserStoreHook } from "/@/store/modules/user";

// 使用defineModel进行双向绑定
const modelValue = defineModel();

// 自定义事件
const emit = defineEmits<{
  (e: "change", value: any): void;
}>();

const options = ref<any[]>([]);

const initAgentList = () => {
  // 从用户store获取坐席列表
  const agentList = useUserStoreHook().agentList;

  // 将坐席列表转换为select-v2需要的格式
  options.value = agentList.map(agent => ({
    value: agent.id,
    label: agent.name
  }));
};

const onChange = (value: any) => {
  emit("change", value);
};

onMounted(() => {
  initAgentList();
});
</script>

<style scoped lang="scss"></style>
