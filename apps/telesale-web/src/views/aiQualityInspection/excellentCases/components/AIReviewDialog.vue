<!--
 * @Date         : 2024-06-24
 * @Description  : AI人工复核对话框
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="人工复检"
    width="500px"
    :before-close="handleClose"
    destroy-on-close
  >
    <nexus-form v-model="formData" :rules="rules">
      <el-form-item label="复检结果：" prop="reviewResult">
        <el-select v-model="formData.reviewResult" placeholder="请选择复检结果">
          <el-option label="通过" value="通过" />
          <el-option label="不通过" value="不通过" />
        </el-select>
      </el-form-item>

      <el-form-item
        label="复检评级："
        prop="reviewGrade"
        v-if="formData.reviewResult === '不通过'"
      >
        <el-select v-model="formData.reviewGrade" placeholder="请选择复检评级">
          <el-option label="A级" value="A" />
          <el-option label="B级" value="B" />
          <el-option label="C级" value="C" />
          <el-option label="D级" value="D" />
        </el-select>
      </el-form-item>

      <el-form-item
        label="未通过原因："
        prop="failReason"
        v-if="formData.reviewResult === '不通过'"
      >
        <el-select v-model="formData.failReason" placeholder="请选择未通过原因">
          <el-option label="信息缺失" value="信息缺失" />
          <el-option label="录音整体评级过高" value="录音整体评级过高" />
          <el-option label="录音整体评级过低" value="录音整体评级过低" />
          <el-option label="SOP评分不准" value="SOP评分不准" />
        </el-select>
      </el-form-item>
    </nexus-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, watch, ref } from "vue";
import { ElMessage } from "element-plus";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import {
  updateExcellentCase,
  type ExcellentCaseUpdateParams
} from "/@/api/AIQualityInspection/excellentCases";

// 定义表单数据接口
interface FormData {
  reviewResult: string;
  reviewGrade: string;
  failReason: string;
}

// 使用defineModel实现双向绑定
const visible = defineModel<boolean>("visible");

// 表单数据
const formData = reactive<FormData>({
  reviewResult: "",
  reviewGrade: "",
  failReason: ""
});

// 表单校验规则
const rules = {
  reviewResult: [
    { required: true, message: "请选择复检结果", trigger: "change" }
  ],
  reviewGrade: [
    { required: true, message: "请选择复检评级", trigger: "change" }
  ]
};

// 提交状态
const submitting = ref(false);

// 定义props
const props = defineProps<{
  rowData?: any;
}>();

// 定义emits
const emit = defineEmits<{
  (e: "confirm", data: any): void;
}>();

// 关闭对话框
const handleClose = () => {
  if (submitting.value) return;
  visible.value = false;
};

// 确认提交
const handleConfirm = async () => {
  if (!props.rowData || !props.rowData.id) {
    ElMessage.error("案例数据无效");
    return;
  }

  submitting.value = true;
  try {
    // 构建更新参数
    const params = {
      id: props.rowData.id,
      actionId: props.rowData.actionId,
      humanAppraise: {
        score: formData.reviewGrade,
        reason: formData.reviewResult,
        result: formData.failReason
      }
    };

    // 调用API更新案例
    await updateExcellentCase(params);

    // 回调父组件
    emit("confirm", {
      ...props.rowData,
      ...formData
    });

    ElMessage.success("提交成功");
    visible.value = false;
  } catch (error) {
    console.error("提交失败", error);
    ElMessage.error("提交失败");
  } finally {
    submitting.value = false;
  }
};

// 监听props.rowData变化，回显数据
watch(
  () => props.rowData,
  newVal => {
    if (newVal) {
      // 回显数据到表单
      formData.reviewResult = newVal?.humanAppraise?.reason || "通过";
      formData.reviewGrade = newVal?.humanAppraise?.score || "";
      formData.failReason = newVal?.humanAppraise?.result || "";
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.dialog-footer {
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 20px;
}
</style>
