<!--
 * @Date         : 2025-04-28 22:01:32
 * @Description  :
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="talk-container w-100%">
    <el-card header="对话转译">
      <template #header>
        <div class="text-16px font-bold">对话转译</div>
      </template>

      <div class="bubble-box">
        <div
          v-for="(item, index) in contentHandle"
          :key="index"
          :class="[item.role == 'user' ? 'bubble you' : 'bubble me']"
        >
          {{ item.content }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{
  content: any;
}>();

interface Message {
  role: string;
  content: string;
  start: string;
}

const showTips = [];

const contentHandle = computed(() => {
  return props.content;
});
</script>

<style scoped lang="scss">
.bubble-box {
  overflow-y: auto;
  max-height: calc(100vh - 330px);

  :deep(.el-card__body) {
    overflow: auto;
  }
}

.bubble {
  position: relative;
  margin-bottom: 10px;
  padding: 10px;
  border-radius: 8px;
  max-width: 70%;
  word-wrap: break-word;
}

.you {
  background-color: #e6e6e6;
  float: left;
  clear: both;
  margin-left: 20px;
}

.me {
  background-color: #00b43c;
  color: white;
  float: right;
  clear: both;
  margin-right: 20px;
}

.bubble::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.you::before {
  top: 50%;
  left: -15px;
  border-width: 10px 15px 10px 0;
  border-color: transparent #e6e6e6 transparent transparent;
  transform: translateY(-50%);
}

.me::before {
  top: 50%;
  right: -15px;
  border-width: 10px 0 10px 15px;
  border-color: transparent transparent transparent #00b43c;
  transform: translateY(-50%);
}
</style>

<style lang="scss">
body {
  .quality-tooltip {
    max-width: 400px !important;
    z-index: 999999;
  }
}
</style>
