<!--
 * @Date         : 2024-06-23
 * @Description  : 小组选择组件
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-cascader
    v-model="modelValue"
    placeholder="请选择小组"
    :options="orgData"
    :props="{
      value: 'id',
      label: 'name',
      checkStrictly: true,
      emitPath: false
    }"
    filterable
    clearable
    :show-all-levels="false"
    @change="onChange"
  />
</template>

<script setup lang="ts">
import { useUserStoreHook } from "/@/store/modules/user";
import { storeToRefs } from "pinia";

const { orgData } = storeToRefs(useUserStoreHook());

// 使用defineModel进行双向绑定
const modelValue = defineModel();

// 自定义事件
const emit = defineEmits<{
  (e: "change", value: any): void;
}>();

const onChange = (value: any) => {
  emit("change", value);
};
</script>

<style scoped lang="scss"></style>
