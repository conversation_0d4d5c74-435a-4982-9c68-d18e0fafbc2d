<!--
 * @Date         : 2025-04-23 18:34:55
 * @Description  : 优秀案例库
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="g-margin-20 excellent-cases-container">
    <el-card>
      <Search @on-search="onSearch" :tableRef="TableRef" ref="searchRef" />

      <nexus-table
        un-mounted
        ref="TableRef"
        :resFormat="data => data?.data"
        :get-list="getExcellentCasesList"
        data-key="list"
      >
        <el-table-column label="通话ID" prop="actionId">
          <template #default="{ row }">
            <div class="flex items-center">
              <span>{{ row.actionId }}</span>
              <el-icon
                class="ml-5px cursor-pointer"
                title="复制"
                @click="copyText(row.actionId)"
              >
                <DocumentCopy />
              </el-icon>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="客户手机号" prop="ph" />
        <el-table-column label="创建时间" prop="createdAt" />
        <el-table-column label="坐席名称" prop="workerName" />
        <el-table-column label="所属小组" prop="lastGroupName" />
        <el-table-column label="通话时长" prop="callTimeLength">
          <template #default="{ row }">
            {{ durationChange(row.callTimeLength) }}
          </template>
        </el-table-column>
        <nexus-table-filter-column
          label="通话评级"
          prop="aiAppraise.score"
          paramKey="aiAppraiseScore"
          :tableRef="TableRef"
          :optionList="[
            { text: 'A级', val: 'A' },
            { text: 'B级', val: 'B' },
            { text: 'C级', val: 'C' },
            { text: 'D级', val: 'D' }
          ]"
        >
          <template #default="{ row }">
            <span v-if="row.aiAppraiseStatus === 'process'">AI生成中</span>
            <span v-else>{{ row.aiAppraise?.score || "-" }}</span>
          </template>
        </nexus-table-filter-column>
        <el-table-column label="评级说明" prop="aiAppraise.result">
          <template #default="{ row }">
            <span v-if="row.aiAppraiseStatus === 'process'">AI生成中</span>
            <el-tooltip
              v-else
              effect="dark"
              placement="bottom"
              append-to="body"
              popper-class="excellent-cases-tooltip"
              :hide-after="0"
            >
              <template #content>
                <div v-html="row.aiAppraise?.result || '-'" />
              </template>
              <div
                class="truncate max-w-200px textHideLine2"
                v-html="row.aiAppraise?.result || '-'"
              />
            </el-tooltip>
          </template>
        </el-table-column>
        <nexus-table-filter-column
          label="复检结果"
          prop="humanAppraise.reason"
          paramKey="humanAppraiseResult"
          :tableRef="TableRef"
          :optionList="[
            { text: '通过', val: '通过' },
            { text: '不通过', val: '不通过' }
          ]"
        >
          <template #default="{ row }">
            {{ row.humanAppraise?.reason || "-" }}
          </template>
        </nexus-table-filter-column>
        <nexus-table-filter-column
          label="复检评级"
          prop="humanAppraise.score"
          :tableRef="TableRef"
          paramKey="humanAppraiseScore"
          :optionList="[
            { text: 'A级', val: 'A' },
            { text: 'B级', val: 'B' },
            { text: 'C级', val: 'C' },
            { text: 'D级', val: 'D' }
          ]"
        >
          <template #default="{ row }">
            {{ row.humanAppraise?.score || "-" }}
          </template>
        </nexus-table-filter-column>
        <el-table-column label="未通过原因" prop="humanAppraise.result">
          <template #default="{ row }">
            <el-tooltip
              effect="dark"
              placement="top-start"
              append-to="body"
              popper-class="excellent-cases-tooltip"
              :hide-after="0"
            >
              <template #content>
                <div v-html="row.humanAppraise?.result || '-'" />
              </template>
              <div
                class="truncate max-w-200px"
                v-html="row.humanAppraise?.result || '-'"
              />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="150">
          <template #default="{ row }">
            <el-link
              type="primary"
              :underline="false"
              @click="handleAIReview(row)"
              class="mr-10px"
            >
              人工复核
            </el-link>
            <el-link
              type="primary"
              :underline="false"
              @click="viewConversation(row)"
            >
              查看通话
            </el-link>
            <el-link
              v-if="row.aiAppraiseStatus === 'fail'"
              type="primary"
              :underline="false"
              @click="handleRetryAudio(row)"
              :disabled="retryLoading[row.actionId]"
            >
              {{ retryLoading[row.actionId] ? "生成中..." : "重新生成" }}
            </el-link>
          </template>
        </el-table-column>
      </nexus-table>
    </el-card>

    <!-- 人工复核对话框 -->
    <AIReviewDialog
      v-model:visible="reviewDialogVisible"
      :row-data="currentRow"
      @confirm="handleReviewConfirm"
    />

    <!-- 查看通话抽屉 -->
    <DetailDrawer v-model:visible="drawerVisible" :detail-info="currentRow" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { DocumentCopy } from "@element-plus/icons-vue";
import durationChange from "/@/utils/handle/durationChange";
import NexusTable from "/@/components/Nexus/NexusTable/index.vue";
import NexusTableFilterColumn from "/@/components/Nexus/NexusTableFilterColumn/index.vue";

// 导入组件
import Search from "./children/Search.vue";
import AIReviewDialog from "./components/AIReviewDialog.vue";
import DetailDrawer from "./DetailDrawer/index.vue";
import {
  getExcellentCasesList,
  retryExcellentCaseAudio,
  type ExcellentCasesQueryParams
} from "/@/api/AIQualityInspection/excellentCases";

// 状态定义
const TableRef = ref();
const searchRef = ref();
const reviewDialogVisible = ref(false);
const drawerVisible = ref(false);
const currentRow = ref();
const retryLoading = ref<Record<string, boolean>>({}); // 记录每个行的重新生成按钮loading状态

// 格式化日期时间
const formatDateTime = (timestamp: number) => {
  return dayjs(timestamp).format("YYYY-MM-DD HH:mm:ss");
};

// 复制文本
const copyText = (text: string) => {
  navigator.clipboard.writeText(text).then(
    () => {
      ElMessage.success("通话ID已复制到剪切板");
    },
    () => {
      ElMessage.error("复制失败");
    }
  );
};

// 搜索功能
function onSearch(params: ExcellentCasesQueryParams) {
  // 直接使用nexus-table的search方法
  // 在搜索组件中已经处理了tableRef的情况，这里作为备用
  if (!TableRef.value) {
    console.warn("TableRef未初始化");
    return;
  }

  TableRef.value.search(params);
}

// 人工复核
function handleAIReview(row: any) {
  if (!row || !row.id) {
    ElMessage.warning("案例数据不完整，无法进行复核");
    return;
  }

  currentRow.value = row;
  reviewDialogVisible.value = true;
}

// 处理复核确认
function handleReviewConfirm(data: any) {
  console.log("复核确认", data);
  // 复核成功后刷新表格数据
  TableRef.value?.search();
}

// 查看通话
function viewConversation(row: any) {
  currentRow.value = row;
  drawerVisible.value = true;
}

// 重新生成音频
async function handleRetryAudio(row: any) {
  if (!row || !row.actionId) {
    ElMessage.warning("案例数据不完整，无法重新生成");
    return;
  }

  // 设置当前行的loading状态为true
  retryLoading.value[row.actionId] = true;

  try {
    await retryExcellentCaseAudio({
      actionIds: [row.actionId]
    });
    ElMessage.success("重新生成请求已提交，请稍后刷新查看");
    // 刷新表格数据
    setTimeout(() => {
      TableRef.value?.update();
    }, 1000);
  } catch (error) {
    console.error("重新生成失败", error);
    ElMessage.error("重新生成失败");
  } finally {
    // 无论成功或失败，都将loading状态设置为false
    setTimeout(() => {
      retryLoading.value[row.actionId] = false;
    }, 1500); // 稍微延迟一下，让用户看到状态变化
  }
}

// 获取通话评级标签类型
const getCallRatingTagType = (rating: string | undefined) => {
  if (!rating) return "info";

  switch (rating) {
    case "A":
    case "A级":
      return "success";
    case "B":
    case "B级":
      return "warning";
    case "C":
    case "C级":
      return "danger";
    case "D":
    case "D级":
      return "info";
    default:
      return "info";
  }
};

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    // 初始化时执行搜索
    if (searchRef.value) {
      searchRef.value.onSearch();
    } else {
      TableRef.value?.update();
    }
  });
});
</script>

<style scoped lang="scss">
.excellent-cases-container {
  .el-card {
    margin-bottom: 20px;
  }
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>

<style lang="scss">
body {
  .excellent-cases-tooltip {
    max-width: 1000px !important;
    z-index: 999999;
  }
}
</style>
