<!--
 * @Date         : 2025-05-13 11:51:19
 * @Description  : 课程管理
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="g-margin-20">
    <el-card v-loading="loading">
      <!-- 搜索区域 -->
      <NexusForm v-model="searchForm" inline>
        <el-form-item>
          <NexusDatePicker
            limitType="paramast"
            ref="datePickerRef"
            v-model="time"
            type="daterange"
            show-shortcuts
            @change="handleDateChange"
          />
        </el-form-item>
      </NexusForm>

      <!-- 课程列表 -->
      <div class="flex flex-wrap gap-5 my-5">
        <!-- 创建课程卡片 -->
        <el-card
          :body-style="{ height: '100%' }"
          class="w-180px h-150px cursor-pointer"
          shadow="hover"
          @click="handleAddCourse"
        >
          <div class="h-full flex flex-col justify-center items-center">
            <el-icon size="40px" class="mb-20px">
              <Plus />
            </el-icon>
            <div class="text-16px text-gray-500">创建课程</div>
          </div>
        </el-card>

        <!-- 课程卡片列表 -->
        <el-card
          v-for="course in courseList"
          :key="course.id"
          class="w-180px h-150px cursor-pointer"
          :body-style="{ height: '100%' }"
          shadow="hover"
          @click="handleEditCourse(course)"
        >
          <div class="h-full flex flex-col justify-center items-center">
            <el-icon size="40px" class="mb-20px">
              <Reading />
            </el-icon>

            <div class="text-16px text-gray-500">{{ course.name }}</div>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 课程表单弹窗 -->
    <CourseFormDialog
      v-model:visible="courseDialogVisible"
      :edit-mode="editMode"
      :edit-data="currentCourse"
      :course-list="courseList"
      @success="fetchCourseList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { Plus, Reading } from "@element-plus/icons-vue";
import CourseFormDialog from "./components/CourseFormDialog.vue";
import NexusDatePicker from "/@/components/Nexus/NexusDatePicker/index.vue";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import {
  getTrainingCourseList,
  getTrainingCourseInfo,
  type TrainingCourseInstance,
  type TrainingCourseQueryParams
} from "/@/api/AIQualityInspection/courseManage";

// 加载状态
const loading = ref(false);

// 课程列表
const courseList = ref<TrainingCourseInstance[]>([]);

// 总记录数
const total = ref(0);

// 日期选择器引用
const datePickerRef = ref();

const time = ref([]);
const searchForm = reactive<TrainingCourseQueryParams>({
  pages: 1,
  pageSize: 9999,
  beginTime: undefined,
  endTime: undefined
});

// 编辑模式
const editMode = ref(false);

// 当前编辑的课程
const currentCourse = ref<TrainingCourseInstance | null>(null);

// 课程表单弹窗显示状态
const courseDialogVisible = ref(false);

// 监听弹窗关闭事件
watch(courseDialogVisible, newVal => {
  if (!newVal) {
    // 弹窗关闭时，重置编辑模式和当前课程
    editMode.value = false;
    currentCourse.value = null;
  }
});

// 获取课程列表
const fetchCourseList = async () => {
  try {
    loading.value = true;
    const res = await getTrainingCourseList(searchForm);
    courseList.value = res.data.courses || [];
    total.value = parseInt(res.data.total || "0");
  } catch (error) {
    console.error("获取课程列表失败:", error);
    ElMessage.error("获取课程列表失败");
  } finally {
    loading.value = false;
  }
};

// 处理日期变化
const handleDateChange = (_: any) => {
  nextTick(() => {
    searchForm.beginTime = datePickerRef.value.unixsDatePicker[0];
    searchForm.endTime = datePickerRef.value.unixsDatePicker[1];
    fetchCourseList();
  });
};

// 新增课程
const handleAddCourse = () => {
  editMode.value = false;
  currentCourse.value = null;
  courseDialogVisible.value = true;
};

// 编辑课程
const handleEditCourse = async (course: TrainingCourseInstance) => {
  try {
    loading.value = true;
    // 获取课程详情
    const res = await getTrainingCourseInfo(course.id!);
    currentCourse.value = res.data.course;
    editMode.value = true;
    courseDialogVisible.value = true;
  } catch (error) {
    console.error("获取课程详情失败:", error);
    ElMessage.error("获取课程详情失败");
  } finally {
    loading.value = false;
  }
};

// 组件挂载时获取课程列表
onMounted(() => {
  fetchCourseList();
});
</script>
