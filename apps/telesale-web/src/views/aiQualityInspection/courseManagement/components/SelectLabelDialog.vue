<!--
 * @Date         : 2025-05-15 10:34:46
 * @Description  : 选择标签弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="选择标签"
    width="600px"
    @open="openDialog"
  >
    <div class="flex flex-col gap-4">
      <el-input
        v-model="searchText"
        placeholder="搜索标签"
        clearable
        @input="handleSearch"
      />

      <div v-loading="loading" class="flex flex-col gap-4">
        <div
          v-for="(group, groupIndex) in filteredTagGroups"
          :key="group.title || group.id"
          class="flex flex-col gap-2"
        >
          <div class="text-sm font-medium text-gray-500">
            {{ group.title || group.name }}
          </div>

          <div class="relative">
            <!-- 标签列表 -->
            <div class="flex flex-wrap gap-2">
              <el-check-tag
                v-for="(tag, tagIndex) in group.tags"
                v-show="tagIndex < 10 || expandedGroups[groupIndex]"
                :key="tag.id"
                type="primary"
                class="cursor-pointer"
                :checked="tag.checked"
                @click="toggleTag(tag)"
              >
                {{ tag.name }}
              </el-check-tag>
            </div>

            <!-- 显示更多按钮 -->
            <div v-if="group.tags.length > 10" class="mt-2">
              <el-button
                link
                type="primary"
                @click="toggleGroupExpand(groupIndex)"
              >
                {{
                  expandedGroups[groupIndex]
                    ? "收起"
                    : `显示更多 (${group.tags.length - 10}个)`
                }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue";
import { ElMessage } from "element-plus";
import {
  getTagQuery,
  TagGroupInstance,
  TagInstance
} from "/@/api/AIQualityInspection/excellentCases";
import libraryInfo from "/@/views/aiQualityInspection/const/Library";

const visible = defineModel<boolean>("visible", { default: false });
const props = defineProps<{
  tags: any[];
}>();

const searchText = ref("");
const selectedTags = ref<any[]>([]);
const loading = ref(false);
// 记录每个组是否展开
const expandedGroups = ref<Record<number, boolean>>({});

// 标签组数据
interface TagWithChecked extends TagInstance {
  checked: boolean;
  groupKey?: string;
  groupName?: string;
}

interface TagGroupWithTitle extends TagGroupInstance {
  title?: string;
  tags: TagWithChecked[];
}

const tagGroups = ref<TagGroupWithTitle[]>([]);

// 切换组的展开状态
function toggleGroupExpand(groupIndex: number) {
  expandedGroups.value[groupIndex] = !expandedGroups.value[groupIndex];
}

// 过滤后的标签组
const filteredTagGroups = computed(() => {
  if (!searchText.value) return tagGroups.value;

  return tagGroups.value
    .map(group => ({
      ...group,
      tags: group.tags.filter(tag =>
        tag.name.toLowerCase().includes(searchText.value.toLowerCase())
      )
    }))
    .filter(group => group.tags.length > 0);
});

function toggleTag(tag: TagWithChecked) {
  tag.checked = !tag.checked;

  // 更新选中标签列表
  if (tag.checked) {
    // 添加标签到选中列表
    selectedTags.value.push({
      id: tag.id,
      name: tag.name,
      key: tag.key,
      groupId: tag.groupId,
      groupKey: tag.groupKey,
      groupName: tag.groupName,
      libraryId: libraryInfo.libraryId
    });
  } else {
    // 从选中列表中移除标签
    selectedTags.value = selectedTags.value.filter(
      t => !(t.id === tag.id || (t.key && tag.key && t.key === tag.key))
    );
  }
}

function handleConfirm() {
  visible.value = false;

  // 按照父节点分组
  const groupedTags: Record<string, any> = {};

  // 将标签按照父节点分组
  selectedTags.value.forEach(tag => {
    const groupId = tag.groupId;
    if (!groupedTags[groupId]) {
      // 创建父节点
      groupedTags[groupId] = {
        id: groupId,
        key: tag.groupKey || "",
        name: tag.groupName || "",
        libraryId: tag.libraryId,
        tags: []
      };
    }

    // 添加标签到父节点的tags数组中
    groupedTags[groupId].tags.push({
      id: tag.id,
      key: tag.key,
      name: tag.name
    });
  });

  // 转换为数组
  const result = Object.values(groupedTags);

  emit("update:tags", result);
}

const emit = defineEmits<{ (e: "update:tags", tags: any[]): void }>();

async function openDialog() {
  // 先清空已选标签
  selectedTags.value = [];

  // 重置所有组的展开状态
  expandedGroups.value = {};

  // 加载标签数据
  loading.value = true;
  try {
    const res = await getTagQuery(libraryInfo.libraryId);

    // 转换API返回的数据结构，为了兼容原有格式
    tagGroups.value = res.data.tags.map(group => ({
      ...group,
      title: group.name, // 保留title字段以兼容模板中的使用
      tags: group.tags.map(tag => ({
        ...tag,
        checked: false,
        groupKey: group.key,
        groupName: group.name
      }))
    }));

    // 加载完成后初始化选中状态
    updateSelectedTags();
  } catch (error) {
    console.error("获取标签失败:", error);
    ElMessage.error("获取标签失败");
    // 失败时使用空数组，避免页面报错
    tagGroups.value = [];
  } finally {
    loading.value = false;
  }
}

// 更新选中标签状态
function updateSelectedTags() {
  // 初始化所有标签的选中状态
  tagGroups.value.forEach(group => {
    group.tags.forEach(tag => {
      // 使用id或key来匹配标签
      // 需要遍历每个父节点的tags数组
      let isSelected = false;

      for (const groupTag of props.tags) {
        if (groupTag.tags && Array.isArray(groupTag.tags)) {
          isSelected = groupTag.tags.some(
            t =>
              (tag.id && t.id && tag.id === t.id) ||
              (tag.key && t.key && tag.key === t.key) ||
              (tag.name &&
                t.name &&
                tag.name === t.name &&
                (!tag.key || !t.key))
          );

          if (isSelected) break;
        }
      }

      tag.checked = isSelected;

      // 如果标签被选中，添加到selectedTags
      if (isSelected) {
        selectedTags.value.push({
          id: tag.id,
          name: tag.name,
          key: tag.key,
          groupId: group.id,
          groupKey: group.key || "",
          groupName: group.name,
          libraryId: libraryInfo.libraryId
        });
      }
    });
  });
}

function handleSearch() {
  // 实时搜索，不需要额外处理，computed会自动过滤
}
</script>

<style scoped>
.el-check-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
