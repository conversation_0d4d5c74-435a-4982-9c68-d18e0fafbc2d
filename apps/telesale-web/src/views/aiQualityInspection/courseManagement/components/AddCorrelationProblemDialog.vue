<!--
 * @Date         : 2025-05-13 11:51:19
 * @Description  : 添加参考问答弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="_visible"
    append-to-body
    class="w-70vw min-w-1200px"
    :title="`全部问题列表（${tableTotal}）`"
    @open="openDialog"
  >
    <div class="flex">
      <div class="w-70%">
        <NexusForm v-model="searchForm" inline class="mb-4">
          <el-form-item>
            <el-tree-select
              v-model="searchForm.qandaCategoryId"
              :data="[{ name: '全部', id: '' }, ...categoryList]"
              node-key="id"
              placeholder="请选择分类"
              :props="{
                children: 'nodes',
                label: 'name'
              }"
              check-strictly
              :render-after-expand="false"
              @change="searchQuestion"
            />
          </el-form-item>

          <el-form-item>
            <el-input
              v-model="searchForm.content"
              placeholder="请输入搜索"
              @input="searchQuestion"
            />
          </el-form-item>

          <el-form-item>
            <div class="flex items-center">
              <el-button
                type="primary"
                plain
                size="default"
                @click="labelDialogVisible = true"
              >
                选择标签
              </el-button>

              <div class="ml-2 flex flex-wrap gap-1">
                <template v-for="parentTag in selectedTags" :key="parentTag.id">
                  <el-tag
                    v-for="childTag in parentTag.tags"
                    :key="childTag.id"
                    closable
                    @close="removeChildTag(parentTag, childTag)"
                  >
                    {{ childTag.name }}
                  </el-tag>
                </template>
              </div>
            </div>
          </el-form-item>
        </NexusForm>

        <!-- 标签选择弹窗 -->
        <SelectLabelDialog
          v-model:visible="labelDialogVisible"
          :tags="selectedTags"
          @update:tags="updateSelectedTags"
        />

        <NexusTable
          ref="addCorrelationProblemTableRef"
          :get-list="getQuestion"
          :class="{ 'hidden-all-check': props.max < 100 }"
          class="min-h-400px w-100%"
          row-key="qandaUniqueId"
          :table-data-param="tableDataParam"
          @select-all="selectCheckAll"
          @select="selectCheck"
          unMounted
        >
          <el-table-column
            type="selection"
            :selectable="disableSelect"
            width="55"
          />
          <el-table-column prop="answer" label="标准问法">
            <template #default="{ row }">
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="row.baseQuestion"
                placement="top"
                popper-class="max-w-500px"
              >
                {{
                  row.baseQuestion.length > 15
                    ? row.baseQuestion.slice(0, 15) + "..."
                    : row.baseQuestion
                }}
              </el-tooltip>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="answer" label="回答">
            <template #default="{ row }">
              <el-tooltip
                class="box-item"
                effect="dark"
                placement="top"
                popper-class="max-w-500px"
              >
                <template #content>
                  <div v-html="row.answer" />
                </template>
                <div
                  v-html="
                    row.answer.length > 15
                      ? row.answer.replace(/<[^>]*>/g, '').slice(0, 15) + '...'
                      : row.answer
                  "
                  class="answer-preview"
                />
              </el-tooltip>
            </template>
          </el-table-column> -->
        </NexusTable>
      </div>

      <div class="w-1px bg-#eee mx-15px" />

      <div class="flex-1">
        <div class="mb-10px">
          已选择问题列表（{{ checkQuestionList.length }}）
        </div>
        <div class="flex-1">
          <div
            v-for="(item, index) in checkQuestionList"
            :key="index"
            class="my-5px flex justify-between"
          >
            <el-text class="mx-1" type="info">{{ item.baseQuestion }}</el-text>

            <el-icon
              class="c-red cursor-pointer"
              size="large"
              @click="delcheckQuestion(item, index)"
            >
              <CircleClose />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch, nextTick, unref, reactive, computed } from "vue";
import { ElMessage } from "element-plus";
import { CircleClose } from "@element-plus/icons-vue";
import { getQuery } from "/@/api/AISupport/Library";
import {
  queryLibraryCategory,
  LibraryCategoryListReply,
  LibraryCategoryNodeInfo
} from "/@/api/AIQualityInspection/excellentQA";
import { TagInstance } from "/@/api/AIQualityInspection/excellentCases";
import libraryInfo from "/@/views/aiQualityInspection/const/Library";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import NexusTable from "/@/components/Nexus/NexusTable/index.vue";
import SelectLabelDialog from "./SelectLabelDialog.vue";

const props = defineProps({
  confirmCb: {
    type: Function,
    default: () => {}
  },
  dataList: {
    type: Array,
    default: () => []
  },
  docId: {
    type: String,
    default: ""
  },
  max: {
    type: Number,
    default: 99999999
  },
  visible: {
    type: Boolean,
    default: false
  },
  disableShield: {
    // IM端不显示的问题不允许选择
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(["confirmCb", "update:visible"]);

// 双向绑定visible
const _visible = ref(props.visible);
watch(
  () => props.visible,
  val => {
    _visible.value = val;
  }
);
watch(
  () => _visible.value,
  val => {
    emit("update:visible", val);
  }
);

// 表格数据
const info = ref({
  list: [],
  total: 0
});

// 计算属性：获取表格的总数
const tableTotal = computed(() => {
  // 直接使用NexusTable暴露的total计算属性
  const tableRef = addCorrelationProblemTableRef.value;
  if (tableRef && tableRef.total) {
    return tableRef.total;
  }
  return 0;
});

// 搜索表单
const searchForm = reactive({
  qandaCategoryId: "",
  content: "",
  tags: []
});

// 标签选择相关
const labelDialogVisible = ref(false);
const selectedTags = ref<any[]>([]);

// 更新选中的标签
function updateSelectedTags(tags: any[]) {
  selectedTags.value = tags;

  // 更新搜索表单中的标签
  searchForm.tags = tags;

  // 触发搜索
  searchQuestion();
}

// 移除子标签
function removeChildTag(parentTag: any, childTag: any) {
  // 从父标签的tags数组中移除子标签
  const parentIndex = selectedTags.value.findIndex(
    (t: any) => t.id === parentTag.id
  );
  if (parentIndex !== -1) {
    const parent = selectedTags.value[parentIndex];
    parent.tags = parent.tags.filter((t: any) => t.id !== childTag.id);

    // 如果父标签的tags数组为空，则移除父标签
    if (parent.tags.length === 0) {
      selectedTags.value.splice(parentIndex, 1);
    }
  }

  // 更新搜索表单中的标签
  searchForm.tags = selectedTags.value;

  // 触发搜索
  searchQuestion();
}

// 表格数据参数
const tableDataParam = reactive({
  dataKey: "list",
  totalKey: "total",
  pagesKey: "pages",
  resFormat: (res: any) => {
    try {
      const responseData = res.data as {
        list: Array<{
          id: string;
          baseQuestion: string;
          answer: string;
        }>;
        total: string;
      };

      return {
        list:
          responseData?.list?.map(item => ({
            qandaUniqueId: item.id,
            docId: item.id,
            baseQuestion: item.baseQuestion,
            answer: item.answer,
            shield: false
          })) || [],
        total: parseInt(responseData?.total || "0")
      };
    } catch (error) {
      console.error("处理返回数据失败:", error);
      return {
        list: [],
        total: 0
      };
    }
  }
});

// 获取问题列表
async function getQuestion(par: {
  pages: number;
  pageSize: number;
  [key: string]: any;
}) {
  try {
    // 构建请求参数
    const requestParams = {
      libraryId: libraryInfo.libraryId,
      libraryUUID: libraryInfo.libraryUUID,
      content: searchForm.content || "",
      pages: par.pages,
      pageSize: par.pageSize,
      knowledgeCategoryId: searchForm.qandaCategoryId
        ? Number(searchForm.qandaCategoryId)
        : undefined,
      searchMode: "question",
      // 只有当有标签时才传递标签参数
      tags:
        searchForm.tags && searchForm.tags.length > 0
          ? searchForm.tags
          : undefined
    };

    return await getQuery(requestParams);
  } catch (error) {
    console.error("获取问题列表失败:", error);
    ElMessage.error("获取问题列表失败");
    throw error;
  }
}

// 分类列表
const categoryList = ref([]);

/**
 * 获取分类列表数据
 */
async function getCategory() {
  try {
    const res = await queryLibraryCategory({
      libraryId: libraryInfo.libraryId,
      libraryUUID: libraryInfo.libraryUUID
    });
    categoryList.value = res.data.nodes;
  } catch (error) {
    console.error("获取分类列表失败", error);
    ElMessage.error("获取分类列表失败");
  }
}

// 定义问题项的接口
interface QuestionItem {
  qandaUniqueId: string;
  docId: string;
  baseQuestion: string;
  answer: string;
  shield: boolean;
  [key: string]: any;
}

// 已选择的问题列表
const checkQuestionList = ref<QuestionItem[]>([]);

function selectCheck(_selection: any[], row: QuestionItem) {
  // 单选
  const index = checkQuestionList.value.findIndex(item => {
    return item.qandaUniqueId === row.qandaUniqueId || item.docId === row.docId;
  });

  if (index === -1) {
    if (checkQuestionList.value.length >= props.max) {
      ElMessage.error(`最多仅支持选择${props.max}个问题`);
      if (
        addCorrelationProblemTableRef.value &&
        addCorrelationProblemTableRef.value.TableRef
      ) {
        addCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
          row,
          false
        );
      }
      return;
    }

    checkQuestionList.value.push(row);
  } else {
    checkQuestionList.value.splice(index, 1);
  }
}

function selectCheckAll(selection: QuestionItem[]) {
  // 全选
  if (checkQuestionList.value.length >= props.max) {
    ElMessage.error(`最多仅支持选择${props.max}个问题`);
    const copySelection = [...selection];
    if (
      addCorrelationProblemTableRef.value &&
      addCorrelationProblemTableRef.value.TableRef
    ) {
      copySelection.forEach(row => {
        addCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
          row,
          false
        );
      });
    }
    return;
  }

  let targetArr = selection;
  if (selection.length === 0) {
    // 获取表格组件内部的数据
    if (
      addCorrelationProblemTableRef.value &&
      addCorrelationProblemTableRef.value.TableRef
    ) {
      targetArr = addCorrelationProblemTableRef.value.TableRef.data || [];
    } else {
      targetArr = [];
    }
  }

  targetArr.forEach((item: QuestionItem) => {
    const index = checkQuestionList.value.findIndex(it => {
      return it.qandaUniqueId === item.qandaUniqueId;
    });

    if (index === -1) {
      checkQuestionList.value.push(item);
    } else {
      if (selection.length === 0) {
        checkQuestionList.value.splice(index, 1);
      }
    }
  });
}

// 编辑时禁止选择自己
const disableSelect = (row: QuestionItem) => {
  if (props.docId === row.docId) return false;

  if (props.disableShield) {
    return !row.shield;
  }

  return true;
};

function searchQuestion() {
  updateList({
    qandaCategoryId: searchForm.qandaCategoryId,
    content: searchForm.content,
    searchMode: "question",
    tags: searchForm.tags.length > 0 ? searchForm.tags : undefined
  });
}

const addCorrelationProblemTableRef = ref();
watch(
  info,
  newV => {
    newV.list.forEach((item: any) => {
      if (
        checkQuestionList.value.find(
          it => it.qandaUniqueId === item.qandaUniqueId
        )
      ) {
        nextTick(() => {
          addCorrelationProblemTableRef.value.toggleRowSelection(item, true);
        });
      }
    });
  },
  { deep: true }
);

function delcheckQuestion(item: QuestionItem, index: number) {
  checkQuestionList.value.splice(index, 1);

  // 使用 TableRef 来操作表格选择状态
  if (
    addCorrelationProblemTableRef.value &&
    addCorrelationProblemTableRef.value.TableRef
  ) {
    addCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
      item,
      false
    );
  }
}

// 对比当前table数据和已选择数据
function compareData() {
  // 获取表格组件内部的数据
  if (
    addCorrelationProblemTableRef.value &&
    addCorrelationProblemTableRef.value.TableRef
  ) {
    const tableData = addCorrelationProblemTableRef.value.TableRef.data;
    if (Array.isArray(tableData)) {
      tableData.forEach((item: any) => {
        if (
          checkQuestionList.value.find(
            it =>
              it?.qandaUniqueId === item.qandaUniqueId ||
              it?.docId === item.docId
          )
        ) {
          addCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
            item,
            true
          );
        } else {
          addCorrelationProblemTableRef.value.TableRef.toggleRowSelection(
            item,
            false
          );
        }
      });
    }
  }
}

// 更新列表
async function updateList(params = {}) {
  // 使用 NexusTable 的 searchHandle 方法，而不是 updateList
  if (addCorrelationProblemTableRef.value) {
    addCorrelationProblemTableRef.value.searchHandle(params);
  }
}

async function openDialog() {
  // 重置选中的问题列表
  checkQuestionList.value = [...(unref(props.dataList) as QuestionItem[])];

  // 重置标签筛选
  selectedTags.value = [];
  searchForm.tags = [];

  // 重置其他搜索条件
  searchForm.content = "";
  searchForm.qandaCategoryId = "";

  // 获取分类列表
  await getCategory();

  // 初始化表格数据
  nextTick(async () => {
    if (addCorrelationProblemTableRef.value) {
      // 重置表格并等待数据加载
      await addCorrelationProblemTableRef.value.resetTable();
      // 延迟执行compareData，确保表格数据已加载
      setTimeout(() => {
        compareData();
      }, 300);
    }
  });
}

function confirm() {
  emit("confirmCb", unref(checkQuestionList));
  _visible.value = false;
}
</script>

<style scoped lang="scss">
.hidden-all-check {
  :deep(thead .el-checkbox) {
    visibility: hidden;
  }
}

.answer-preview {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
