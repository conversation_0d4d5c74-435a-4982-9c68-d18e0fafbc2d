<!--
 * @Date         : 2025-05-13 11:51:19
 * @Description  : 课程表单弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="课程设置"
    width="900px"
    destroy-on-close
    close-on-click-modal
    @open="openDialog"
  >
    <NexusForm ref="formRef" v-model="form" label-width="120px" class="px-4">
      <!-- 课程名称 -->
      <el-form-item
        label="课程名称"
        prop="name"
        :rules="[
          { required: true, message: '请输入课程名称', trigger: 'blur' },
          { validator: validateCourseName, trigger: 'blur' }
        ]"
      >
        <el-input
          v-model="form.name"
          placeholder="请输入课程名称"
          maxlength="20"
          show-word-limit
          clearable
        />
      </el-form-item>

      <!-- 对话时间上限 -->
      <el-form-item
        label="对话时间上限"
        prop="maxDuration"
        :rules="[
          { required: true, message: '请输入对话时间上限', trigger: 'blur' },
          {
            type: 'number',
            min: 1,
            message: '请输入最小对话时间上限',
            trigger: 'blur'
          }
        ]"
      >
        <div class="flex items-center">
          <el-input-number
            v-model="form.maxDuration"
            :min="1"
            :max="9999"
            :precision="0"
            class="flex-1"
            placeholder="请输入最小对话时间上限"
          />
          <span class="ml-2">分钟</span>
        </div>
      </el-form-item>

      <!-- 角色配置 -->
      <div class="my-4 border-b border-gray-200" />
      <div class="mb-4 text-16px font-bold text-gray-700">角色配置</div>

      <!-- 角色姓名 -->
      <el-form-item
        label="角色姓名"
        prop="botName"
        :rules="[
          { required: true, message: '请输入角色姓名', trigger: 'blur' }
        ]"
      >
        <el-input
          class="w-100%!"
          v-model="form.botName"
          placeholder="请输入角色姓名"
          maxlength="20"
          show-word-limit
          clearable
        />
      </el-form-item>

      <!-- 角色身份 -->
      <el-form-item
        label="角色身份"
        prop="botRole"
        :rules="[
          { required: true, message: '请选择角色身份', trigger: 'change' }
        ]"
      >
        <el-select
          v-model="form.botRole"
          placeholder="请选择角色身份"
          class="w-100%!"
        >
          <el-option label="孩子爸爸" value="孩子爸爸" />
          <el-option label="孩子妈妈" value="孩子妈妈" />
        </el-select>
      </el-form-item>

      <!-- 背景描述 -->
      <el-form-item
        label="背景描述"
        prop="backgroundDesc"
        :rules="[
          { required: true, message: '请输入背景描述', trigger: 'blur' }
        ]"
      >
        <el-input
          v-model="form.backgroundDesc"
          type="textarea"
          :rows="4"
          placeholder="描述角色的特征、需求、沟通风格等背景信息，尽量将每个描述限制在一行内"
        />
      </el-form-item>

      <!-- 对话要求 -->
      <el-form-item
        label="对话要求"
        prop="conversationReq"
        :rules="[
          { required: true, message: '请输入对话要求', trigger: 'blur' }
        ]"
      >
        <el-input
          v-model="form.conversationReq"
          type="textarea"
          :rows="4"
          placeholder="请输入对话要求"
        />
      </el-form-item>

      <!-- 参考问答 -->
      <el-form-item
        label="参考问答"
        prop="referenceQas"
        :rules="[
          { required: true, message: '请添加参考问答', trigger: 'blur' }
        ]"
      >
        <div class="relative w-full">
          <el-table
            :data="questionDetails"
            class="w-full"
            style="margin-bottom: 50px"
          >
            <el-table-column label="排序" width="60px">
              <template #default>
                <el-icon class="move cursor-pointer"><Rank /></el-icon>
              </template>
            </el-table-column>
            <el-table-column prop="baseQuestion" label="问题" min-width="200px">
              <template #default="{ row }">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  placement="top"
                  popper-class="max-w-500px"
                >
                  <template #content>
                    <div>{{ row.baseQuestion }}</div>
                  </template>
                  <span>{{ row.baseQuestion }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="answer" label="答案" min-width="300px">
              <template #default="{ row }">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  placement="top"
                  popper-class="max-w-500px"
                >
                  <template #content>
                    <div v-html="row.answer" />
                  </template>
                  <span v-html="row.answer" />
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="60px">
              <template #default="{ $index }">
                <el-icon
                  color="red"
                  class="text-red-500 cursor-pointer"
                  @click="removeQuestion($index)"
                >
                  <Delete />
                </el-icon>
              </template>
            </el-table-column>
          </el-table>

          <el-button
            size="small"
            class="absolute bottom-0 left-0"
            type="primary"
            @click="addCorrelationProblemDialogVisible = true"
          >
            <el-icon><Plus /></el-icon>
            <span class="ml-2">选择问题</span>
          </el-button>
        </div>
      </el-form-item>

      <!-- 对话目标 -->
      <el-form-item
        label="对话目标"
        prop="target"
        :rules="[
          { required: true, message: '请输入对话目标', trigger: 'blur' }
        ]"
      >
        <el-input
          v-model="form.target"
          type="textarea"
          :rows="4"
          placeholder="请输入对话目标"
        />
      </el-form-item>
    </NexusForm>

    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确认
      </el-button>
    </template>

    <!-- 添加参考问答弹窗 -->
    <AddCorrelationProblemDialog
      v-model:visible="addCorrelationProblemDialogVisible"
      :data-list="questionDetailsForDialog"
      :max="40"
      @confirmCb="confirmCb"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineModel, nextTick, watch, computed } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { Delete, Plus, Rank } from "@element-plus/icons-vue";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import AddCorrelationProblemDialog from "./AddCorrelationProblemDialog.vue";
// @ts-ignore
import Sortable from "sortablejs";
import { getMget } from "/@/api/AISupport/Library";
import libraryInfo from "/@/views/aiQualityInspection/const/Library";
import {
  createTrainingCourse,
  updateTrainingCourse,
  type TrainingCourseInstance,
  type TrainingCourseCreateRequest,
  type TrainingCourseReferenceQAInstance
} from "/@/api/AIQualityInspection/courseManage";

// 使用defineModel实现双向绑定
const visible = defineModel<boolean>("visible");

// 监听弹窗关闭事件
watch(visible, newVal => {
  if (!newVal) {
    // 弹窗关闭时，重置表单状态
    // 注意：我们不能直接修改props，但可以在下次打开时通过openDialog重置
  }
});

// 定义props
const props = defineProps({
  editMode: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object as () => TrainingCourseInstance | null,
    default: null
  },
  courseList: {
    type: Array as () => TrainingCourseInstance[],
    default: () => []
  }
});

// 定义emit
const emit = defineEmits(["success"]);

// 表单引用
const formRef = ref<FormInstance>();

// 加载状态
const loading = ref(false);

// 表单数据
const form = reactive<TrainingCourseCreateRequest>({
  name: "",
  maxDuration: 60,
  botName: "",
  botRole: "",
  backgroundDesc: "",
  conversationReq: "",
  target: "",
  referenceQas: []
});

// 课程名称重名校验
function validateCourseName(_rule: any, value: string, callback: any) {
  if (!value) {
    callback();
    return;
  }

  // 使用父组件传递的课程列表进行验证
  const courses = props.courseList || [];

  // 检查是否有重名课程
  // 在编辑模式下，排除当前正在编辑的课程
  const duplicateCourse = courses.find(
    course =>
      course.name === value &&
      (!props.editMode || course.id !== props.editData?.id)
  );

  if (duplicateCourse) {
    callback(new Error("课程名称已存在，请更换名称"));
  } else {
    callback();
  }
}

// 参考问答弹窗
const addCorrelationProblemDialogVisible = ref(false);

// 问题详情列表
interface QuestionDetail {
  id: string;
  baseQuestion: string;
  answer: string;
  referenceQaId: string;
  score: number;
}
const questionDetails = ref<QuestionDetail[]>([]);

// 转换为AddCorrelationProblemDialog组件需要的格式
const questionDetailsForDialog = computed(() => {
  return questionDetails.value.map(item => ({
    qandaUniqueId: item.id,
    docId: item.id,
    baseQuestion: item.baseQuestion,
    answer: item.answer,
    shield: false
  }));
});

// 监听编辑数据变化
watch(
  () => props.editData,
  async newVal => {
    if (newVal && props.editMode) {
      // 编辑模式下，填充表单数据
      form.name = newVal.name || "";
      form.maxDuration = newVal.maxDuration || 60;
      form.botName = newVal.botName || "";
      form.botRole = newVal.botRole || "";
      form.backgroundDesc = newVal.backgroundDesc || "";
      form.conversationReq = newVal.conversationReq || "";
      form.target = newVal.target || "";
      form.referenceQas = newVal.referenceQas || [];

      // 加载问题详情
      if (newVal.referenceQas && newVal.referenceQas.length > 0) {
        try {
          const ids = newVal.referenceQas.map(
            (item: { referenceQaId: string }) => item.referenceQaId
          );
          const response = await getMget({
            libraryId: libraryInfo.libraryId,
            libraryUUID: libraryInfo.libraryUUID,
            ids
          });

          // 定义返回数据类型
          interface KnowledgeItem {
            id: string;
            baseQuestion: string;
            answer: string;
          }

          const responseData = response.data as { list: KnowledgeItem[] };
          if (responseData && responseData.list) {
            // 更新问题详情列表
            questionDetails.value = responseData.list.map(
              (item: KnowledgeItem) => ({
                id: item.id,
                baseQuestion: item.baseQuestion,
                answer: item.answer,
                referenceQaId: item.id,
                score: 0
              })
            );
          }
        } catch (error) {
          console.error("获取问题详情失败:", error);
          ElMessage.error("获取问题详情失败");
        }
      } else {
        questionDetails.value = [];
      }

      // 初始化排序功能
      nextTick(() => {
        initSortable();
      });
    }
  },
  { immediate: true }
);

// 打开弹窗时初始化表单
const openDialog = () => {
  if (!props.editMode) {
    // 新增模式下，重置表单
    form.name = "";
    form.maxDuration = 1;
    form.botName = "";
    form.botRole = "";
    form.backgroundDesc = "";
    form.conversationReq = "";
    form.target = "";
    form.referenceQas = [];

    // 重置问题详情
    questionDetails.value = [];
  } else {
    // 编辑模式下，确保maxDuration正确设置
    if (props.editData) {
      form.maxDuration = props.editData.maxDuration || 1;
    }
  }

  // 初始化排序功能
  nextTick(() => {
    initSortable();
  });
};

// 参考问答排序功能
let questionSortable = null;
function initSortable() {
  nextTick(() => {
    const el = document.querySelector(".el-table__body-wrapper tbody");
    if (!el) return;

    if (questionSortable) {
      questionSortable.destroy();
    }

    questionSortable = new Sortable(el, {
      handle: ".move",
      animation: 150,
      onEnd({ oldIndex, newIndex }) {
        // 保存当前数据的副本
        const tempDetails = [...questionDetails.value];
        const currDetailRow = tempDetails.splice(oldIndex, 1)[0];
        tempDetails.splice(newIndex, 0, currDetailRow);

        // 先清空数组，触发视图更新
        questionDetails.value = [];

        // 在下一个 tick 中重新赋值，确保视图正确更新
        nextTick(() => {
          questionDetails.value = tempDetails;

          // 更新表单数据
          form.referenceQas = questionDetails.value.map(item => ({
            referenceQaId: item.referenceQaId,
            score: item.score
          }));
        });
      }
    });
  });
}

// 添加参考问答回调
async function confirmCb(val: any[]) {
  if (!val || val.length === 0) {
    form.referenceQas = [];
    questionDetails.value = [];
    return;
  }

  // 将选择的问题转换为API需要的格式
  form.referenceQas = val.map(item => ({
    referenceQaId: item.docId || item.qandaUniqueId,
    score: 0 // 默认分数为0
  }));

  // 获取问题详情
  try {
    const ids = val.map(item => item.docId || item.qandaUniqueId);
    const response = await getMget({
      libraryId: libraryInfo.libraryId,
      libraryUUID: libraryInfo.libraryUUID,
      ids
    });

    // 定义返回数据类型
    interface KnowledgeItem {
      id: string;
      baseQuestion: string;
      answer: string;
    }

    const responseData = response.data as { list: KnowledgeItem[] };
    if (responseData && responseData.list) {
      // 更新问题详情列表
      questionDetails.value = responseData.list.map((item: KnowledgeItem) => ({
        id: item.id,
        baseQuestion: item.baseQuestion,
        answer: item.answer,
        referenceQaId: item.id,
        score: 0
      }));
    }
  } catch (error) {
    console.error("获取问题详情失败:", error);
    ElMessage.error("获取问题详情失败");
  }
}

// 移除问题
function removeQuestion(index: number) {
  // 创建数组副本
  const tempDetails = [...questionDetails.value];
  // 从副本中移除问题
  tempDetails.splice(index, 1);

  // 先清空数组，触发视图更新
  questionDetails.value = [];

  // 在下一个 tick 中重新赋值，确保视图正确更新
  nextTick(() => {
    questionDetails.value = tempDetails;
    // 同步更新 referenceQas
    form.referenceQas = questionDetails.value.map(item => ({
      referenceQaId: item.referenceQaId,
      score: item.score
    }));
  });
}

// 确认按钮点击事件
const handleConfirm = async () => {
  if (!formRef.value) return;

  try {
    loading.value = true;
    await formRef.value.validate();

    if (props.editMode && props.editData?.id) {
      // 编辑模式
      const res = await updateTrainingCourse(props.editData.id, form);
      if (res.data) {
        ElMessage.success("课程更新成功");
      }
    } else {
      // 新增模式
      const res = await createTrainingCourse(form);
      if (res.data) {
        ElMessage.success("课程创建成功");
      }
    }

    // 关闭弹窗并通知父组件刷新列表
    visible.value = false;
    emit("success");
  } catch (error) {
    console.error("保存课程失败:", error);
    ElMessage.error("保存课程失败");
  } finally {
    loading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  openDialog
});
</script>

<style scoped>
/* 使用UnoCSS实现样式 */
</style>
