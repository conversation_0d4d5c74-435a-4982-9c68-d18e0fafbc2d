<!--
 * @Date         : 2025-05-20 15:00:00
 * @Description  : 我的任务页面
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="g-margin-20 my-task-container">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area mb-20px">
        <nexus-form inline>
          <el-form-item>
            <el-input
              v-model="searchForm.nameKeyword"
              placeholder="请输入任务名称"
              clearable
              @keyup.enter="onSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="searchForm.statusFilter"
              placeholder="请选择任务执行状态"
              clearable
            >
              <!-- <el-option label="未开始" :value="TASK_STATUS.CREATE" /> -->
              <el-option label="进行中" :value="TASK_STATUS.PROCESS" />
              <el-option label="已结束" :value="TASK_STATUS.END" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">搜索</el-button>
            <el-button @click="onReset">重置</el-button>
          </el-form-item>
        </nexus-form>
      </div>

      <!-- 表格区域 -->
      <nexus-table
        ref="tableRef"
        :get-list="getWorkerTrainingTaskList"
        :res-format="resFormat"
        data-key="items"
        total-key="total"
        :un-mounted="true"
      >
        <!-- 任务名称 -->
        <el-table-column prop="task.name" label="任务名称">
          <template #default="{ row }">
            {{ row.task?.name || "-" }}
          </template>
        </el-table-column>

        <!-- 任务状态 -->
        <el-table-column prop="task.status" label="任务状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.task?.status)">
              {{ getStatusText(row.task?.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 任务进度 -->
        <el-table-column prop="progress" label="进度">
          <template #default="{ row }">
            {{ row.finishNum || 0 }}/{{ row.courseNum || 0 }}
          </template>
        </el-table-column>

        <!-- 开始时间 -->
        <el-table-column prop="task.beginAt" label="开始时间">
          <template #default="{ row }">
            {{ formatTime(row.task?.beginAt) }}
          </template>
        </el-table-column>

        <!-- 截止时间 -->
        <el-table-column prop="task.endAt" label="截止时间">
          <template #default="{ row }">
            {{ formatTime(row.task?.endAt) }}
          </template>
        </el-table-column>

        <!-- 操作 -->
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <!-- 任务已结束，显示详情按钮 -->
            <el-button
              v-if="row.task?.status === TASK_STATUS.END"
              type="primary"
              link
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            <!-- 任务进行中且进度未完成，显示去练习按钮 -->
            <el-button
              v-else-if="row.finishNum < row.courseNum"
              type="primary"
              link
              @click="handlePractice(row)"
            >
              去练习
            </el-button>
            <!-- 任务进行中且进度已完成，显示详情按钮 -->
            <el-button
              v-else
              type="primary"
              link
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </nexus-table>
    </el-card>

    <!-- 任务课程详情弹窗 -->
    <TaskCourseDetailDialog
      v-model:visible="taskCourseDialogVisible"
      :task-id="currentTaskId"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import dayjs from "dayjs";
import NexusTable from "/@/components/Nexus/NexusTable/index.vue";
import NexusForm from "/@/components/Nexus/NexusForm/index.vue";
import TaskCourseDetailDialog from "./components/TaskCourseDetailDialog.vue";
import {
  getWorkerTrainingTaskList,
  WorkerTrainingTaskQueryParams,
  WorkerTrainingTaskListReply,
  TASK_STATUS
} from "/@/api/AIQualityInspection/taskManagement";
import { useUserStoreHook } from "/@/store/modules/user";

// 表格引用
const tableRef = ref();

// 任务课程详情弹窗
const taskCourseDialogVisible = ref(false);
const currentTaskId = ref<number>();

// 搜索表单
interface SearchForm extends WorkerTrainingTaskQueryParams {
  nameKeyword?: string;
  statusFilter?: string;
}

const searchForm = reactive<SearchForm>({
  nameKeyword: "",
  statusFilter: "",
  workerId: useUserStoreHook().userMsg.id, // 当前登录用户ID
  pages: "1",
  pageSize: "10"
});

// 格式化时间
function formatTime(timestamp: any) {
  if (!timestamp) return "-";
  return dayjs.unix(Number(timestamp)).format("YYYY-MM-DD HH:mm:ss");
}

// 获取状态文本
function getStatusText(status: string): string {
  switch (status) {
    case TASK_STATUS.CREATE:
      return "未开始";
    case TASK_STATUS.PROCESS:
      return "进行中";
    case TASK_STATUS.END:
      return "已结束";
    default:
      return status || "-";
  }
}

// 获取状态类型
function getStatusType(
  status: string
): "" | "info" | "success" | "warning" | "danger" {
  switch (status) {
    case TASK_STATUS.CREATE:
      return "info";
    case TASK_STATUS.PROCESS:
      return "warning"; // 使用warning替代primary，因为el-tag不支持primary类型
    case TASK_STATUS.END:
      return "success";
    default:
      return "";
  }
}

// API响应格式化
function resFormat(res: any): WorkerTrainingTaskListReply {
  // 处理API返回的数据，确保返回正确的格式
  const data = res.data || res;
  return data;
}

// 搜索
function onSearch() {
  tableRef.value?.searchHandle({
    nameKeyword: searchForm.nameKeyword,
    statusFilter: searchForm.statusFilter,
    workerId: searchForm.workerId,
    pages: "1"
  });
}

// 重置
function onReset() {
  searchForm.nameKeyword = "";
  searchForm.statusFilter = "";
  // 调用表格的search方法，保留workerId参数
  tableRef.value?.searchHandle({
    nameKeyword: "",
    statusFilter: ""
  });
}

// 去练习
function handlePractice(row: any) {
  // 设置当前任务ID
  currentTaskId.value = row.id;
  // 打开任务课程详情弹窗
  taskCourseDialogVisible.value = true;
}

// 查看详情
function handleViewDetail(row: any) {
  // 设置当前任务ID
  currentTaskId.value = row.id;
  // 打开任务课程详情弹窗
  taskCourseDialogVisible.value = true;
}

// 刷新任务列表
function handleRefresh() {
  // 调用表格的search方法，传入workerId参数
  tableRef.value?.searchHandle({
    workerId: useUserStoreHook().userMsg.id
  });
}

// 组件挂载后初始化表格数据
onMounted(() => {
  // 调用表格的search方法，传入workerId参数
  tableRef.value?.searchHandle({
    workerId: useUserStoreHook().userMsg.id
  });
});
</script>

<style scoped>
.my-task-container {
  background-color: #fff;
}
</style>
