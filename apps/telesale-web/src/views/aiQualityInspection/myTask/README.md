# AI质检我的任务模块 (MyTask Module)

> 一个完整的AI质检任务管理系统，为销售人员提供任务查看、课程练习和结果评估的全流程体验

## 📋 目录

- [业务概述](#-业务概述)
- [技术架构](#️-技术架构)
- [文件结构](#-文件结构)
- [核心功能](#-核心功能)
- [业务流程](#-业务流程)
- [技术实现](#️-技术实现)
- [使用指南](#-使用指南)
    - [使用流程图](#使用流程图)
    - [操作说明](#操作说明)
- [子模块文档](#-子模块文档)

## 🎯 业务概述

### 业务背景

AI质检我的任务模块是电销质检系统的用户端核心模块，为销售人员提供完整的任务管理和练习体验。通过任务列表、课程练习、结果评估的完整流程，帮助销售人员系统性地提升业务能力。

### 主要功能

- **任务管理**: 查看分配的训练任务和完成进度
- **课程练习**: 支持语音+文本和纯文本两种练习模式
- **实时评估**: 练习完成后的即时评估和反馈
- **进度跟踪**: 实时显示任务完成情况和学习进度
- **多模式支持**: 灵活的练习模式选择
- **音频回放**: 完整的对话录音和回放功能

### 应用场景

- 销售人员日常技能训练
- 新员工入职培训体系
- 定期技能考核和评估
- 个人学习进度管理
- 团队培训效果跟踪

## 🏗️ 技术架构

### 架构设计原则

- **模块化设计**: 任务管理、练习、评估功能独立
- **状态统一管理**: 使用VueUse全局状态管理
- **组件复用**: 抽屉式设计支持多场景复用
- **响应式设计**: 适配不同屏幕尺寸和设备

### 技术栈

- **前端框架**: Vue 3 + TypeScript
- **状态管理**: VueUse createGlobalState
- **UI组件**: Element Plus
- **样式方案**: UnoCSS
- **时间处理**: Day.js
- **音频处理**: Web Audio API
- **实时通信**: WebSocket

### 技术架构图

```mermaid
graph TB
    subgraph "用户界面层 (UI Layer)"
        A[index.vue<br/>任务列表主页]
        B[TaskCourseDetailDialog<br/>任务课程详情弹窗]
        C[ExerciseModeDialog<br/>练习模式选择弹窗]
    end

    subgraph "业务模块层 (Business Modules)"
        D[Exercise Module<br/>练习模块]
        E[Evaluation Module<br/>评估模块]
    end

    subgraph "练习模块内部 (Exercise Internal)"
        F[Exercise Store<br/>练习状态管理]
        G[Exercise Components<br/>练习UI组件]
        H[Exercise Hooks<br/>业务逻辑Hook]
        I[Exercise Utils<br/>工具函数]
    end

    subgraph "状态管理层 (State Management)"
        J[VueUse Global State<br/>全局状态管理]
        K[Exercise Mode State<br/>练习模式状态]
        L[Course Info State<br/>课程信息状态]
        M[Practice State<br/>练习状态]
    end

    subgraph "API服务层 (API Services)"
        N[Task Management API<br/>任务管理接口]
        O[Course API<br/>课程接口]
        P[Exercise API<br/>练习接口]
        Q[Evaluation API<br/>评估接口]
    end

    subgraph "外部服务 (External Services)"
        R[WebSocket<br/>语音识别服务]
        S[AI Chat API<br/>AI对话服务]
        T[TTS Service<br/>语音合成服务]
        U[Audio Storage<br/>音频存储服务]
    end

    A --> B
    B --> C
    B --> D
    B --> E

    D --> F
    D --> G
    D --> H
    D --> I

    F --> J
    F --> K
    F --> L
    F --> M

    A --> N
    B --> O
    D --> P
    E --> Q

    H --> R
    H --> S
    H --> T
    E --> U

    style J fill:#e3f2fd
    style D fill:#fff3e0
    style E fill:#f3e5f5
    style R fill:#ffebee
    style S fill:#ffebee
    style T fill:#ffebee
    style U fill:#ffebee
```

## 📁 文件结构

```text
myTask/
├── index.vue                          # 主入口组件 - 任务列表
├── components/                         # 公共组件
│   ├── TaskCourseDetailDialog.vue     # 任务课程详情弹窗
│   └── ExerciseModeDialog.vue          # 练习模式选择弹窗
├── exercise/                           # 练习模块
│   ├── README.md                       # 练习模块详细文档 ⭐
│   ├── index.vue                       # 练习主组件
│   ├── store.ts                        # 练习状态管理
│   ├── components/                     # 练习UI组件
│   ├── hooks/                          # 练习业务逻辑
│   └── utils/                          # 练习工具函数
├── evaluation/                         # 评估模块
│   ├── index.vue                       # 评估主组件
│   └── components/                     # 评估UI组件
└── README.md                           # 本文档
```

## ⚡ 核心功能

### 1. 任务管理

- **任务列表**: 显示分配的所有训练任务
- **状态筛选**: 支持按任务状态筛选（进行中/已结束）
- **进度显示**: 实时显示任务完成进度
- **操作分流**: 根据任务状态提供不同操作入口

### 2. 课程练习

- **模式选择**: 支持语音+文本和纯文本两种练习模式
- **实时对话**: 与AI进行实时语音或文本对话
- **智能评估**: 自动检测违禁词和话术质量
- **进度管理**: 实时显示练习进度和剩余时间

### 3. 结果评估

- **即时反馈**: 练习完成后立即显示评估结果
- **多维度评估**: 包含评级、优势、改进建议
- **音频回放**: 支持完整对话录音的回放
- **AI评估**: 基于AI的智能评估和建议

### 4. 状态管理

- **全局状态**: 统一管理练习状态和数据
- **模式切换**: 灵活的练习模式切换
- **数据持久化**: 练习数据的保存和恢复
- **错误处理**: 完善的异常处理机制

## 🔄 业务流程

### 完整使用流程

1. **任务查看阶段**
   - 用户登录查看分配的训练任务
   - 筛选和搜索特定任务
   - 查看任务进度和状态

2. **课程选择阶段**
   - 点击"去练习"进入课程列表
   - 选择具体课程进行练习
   - 选择练习模式（语音+文本/纯文本）

3. **练习执行阶段**
   - 进入练习界面开始对话
   - 实时语音识别和AI回复
   - 违禁词检测和提醒

4. **评估反馈阶段**
   - 练习完成后自动评估
   - 查看评估结果和建议
   - 回放对话录音

### 状态流转

```text
任务列表 → 课程选择 → 模式选择 → 练习执行 → 结果评估 → 任务列表
    ↓         ↓         ↓         ↓         ↓         ↓
  查看任务   选择课程   选择模式   AI对话    查看结果   继续练习
```

### 模块间交互

- **任务管理 ↔ 课程详情**: 任务选择和课程展示
- **课程详情 ↔ 练习模块**: 练习启动和完成回调
- **练习模块 ↔ 评估模块**: 练习结果传递和评估展示
- **评估模块 ↔ 任务管理**: 评估完成后的任务刷新

## 🛠️ 技术实现

### 1. 状态管理架构

```text
// 全局状态管理伪代码
MyTask模块状态 {
  任务列表状态: { 搜索条件, 分页信息, 任务数据 }
  练习状态: { 课程信息, 练习模式, 对话数据 }
  评估状态: { 评估结果, 音频数据, 回放状态 }
  
  状态同步: {
    任务完成 -> 刷新任务列表
    练习完成 -> 打开评估界面
    评估完成 -> 更新任务状态
  }
}
```

### 2. 组件通信机制

```text
// 组件通信伪代码
父子组件通信 {
  Props传递: 任务ID, 课程信息, 评估数据
  Events回调: 练习完成, 评估完成, 状态刷新
  
  抽屉式设计: {
    TaskCourseDetailDialog -> ExerciseDrawer
    ExerciseDrawer -> EvaluationDrawer
    状态保持和数据传递
  }
}
```

### 3. 模式切换实现

```text
// 练习模式切换伪代码
模式选择流程 {
  1. 显示模式选择弹窗
  2. 用户选择练习模式
  3. 设置全局练习模式状态
  4. 根据模式配置练习组件
  5. 启动对应的练习流程
}
```

## 📖 使用指南

### 使用流程图

以下流程图展示了用户使用AI质检我的任务模块的完整操作流程：

```mermaid
flowchart TD
    A[用户登录系统] --> B[进入我的任务页面]
    B --> C[查看任务列表]
    C --> D{任务状态筛选}

    D -->|进行中| E[显示进行中任务]
    D -->|已结束| F[显示已结束任务]
    D -->|全部| G[显示所有任务]

    E --> H{任务进度检查}
    F --> I[点击详情按钮]
    G --> H

    H -->|未完成| J[点击去练习按钮]
    H -->|已完成| I

    J --> K[打开任务课程详情弹窗]
    I --> K

    K --> L[查看课程列表]
    L --> M{选择操作}

    M -->|练习课程| N[点击练习按钮]
    M -->|查看详情| O[点击详情按钮]

    N --> P[打开练习模式选择弹窗]
    O --> Q[打开评估抽屉]

    P --> R{选择练习模式}
    R -->|语音+文本| S[选择语音+文本模式]
    R -->|纯文本| T[选择纯文本模式]

    S --> U[确认模式选择]
    T --> U
    U --> V[打开练习抽屉]

    V --> W[开始AI对话练习]
    W --> X[实时语音识别/文本输入]
    X --> Y[AI智能回复]
    Y --> Z[违禁词检测]
    Z --> AA{检测结果}

    AA -->|有违禁词| BB[显示违禁词提示]
    AA -->|无违禁词| CC[继续对话]
    BB --> CC

    CC --> DD{练习是否继续}
    DD -->|继续| X
    DD -->|完成| EE[练习完成]
    DD -->|时间到| FF[自动完成练习]

    EE --> GG[保存练习结果]
    FF --> GG
    GG --> HH[关闭练习抽屉]
    HH --> II[自动打开评估抽屉]

    Q --> JJ[查看评估结果]
    II --> JJ

    JJ --> KK[查看评级和建议]
    KK --> LL[播放对话录音]
    LL --> MM[查看AI评估详情]
    MM --> NN{继续操作}

    NN -->|继续练习| OO[关闭评估抽屉]
    NN -->|查看其他课程| OO
    NN -->|返回任务列表| PP[关闭所有弹窗]

    OO --> L
    PP --> C

    style A fill:#e3f2fd
    style PP fill:#f3e5f5
    style BB fill:#ffebee
    style GG fill:#e8f5e8
    style II fill:#fff3e0
    style JJ fill:#e8f5e8
```

### 操作说明

#### 🎯 任务管理

1. **任务查看**: 登录后查看分配的训练任务列表
2. **状态筛选**: 可按任务状态（进行中/已结束）筛选
3. **进度跟踪**: 实时显示任务完成进度（已完成/总数）

#### 📚 课程练习

1. **课程选择**: 从任务课程列表中选择要练习的课程
2. **模式选择**: 选择语音+文本或纯文本练习模式
3. **AI对话**: 与AI进行实时对话练习
4. **智能检测**: 自动检测违禁词并提供提醒

#### 📊 结果评估

1. **即时评估**: 练习完成后自动显示评估结果
2. **多维反馈**: 查看评级、优势和改进建议
3. **音频回放**: 播放完整的对话录音
4. **AI建议**: 查看基于AI分析的详细建议

### 基本使用

```vue
<template>
  <!-- 任务列表页面 -->
  <MyTaskIndex />
  
  <!-- 或者在其他页面中使用任务详情弹窗 -->
  <TaskCourseDetailDialog
    v-model:visible="dialogVisible"
    :task-id="taskId"
    @refresh="handleRefresh"
  />
</template>

<script setup>
import MyTaskIndex from './myTask/index.vue'
import TaskCourseDetailDialog from './myTask/components/TaskCourseDetailDialog.vue'
</script>
```

### 状态管理使用

```text
// 练习状态管理使用伪代码
导入 { useExerciseStore }
创建 store实例 = useExerciseStore()

设置练习模式: store.setExerciseMode(mode)
设置课程信息: store.setCourseInfo(courseInfo)
开始练习: store.startPractice()
完成练习: store.finishPractice()
```

### 组件集成

```text
// 组件集成伪代码
集成步骤 {
  1. 导入所需组件
  2. 设置必要的Props
  3. 监听组件Events
  4. 处理状态更新
  5. 管理组件生命周期
}
```

## 📚 子模块文档

### 练习模块 (Exercise)

**详细文档**: [AI质检练习组件文档](./exercise/README.md)

练习模块是myTask的核心子模块，提供完整的AI对话练习功能：

- **实时语音对话**: WebSocket语音识别和TTS语音合成
- **AI智能回复**: 基于角色设定的流式对话系统
- **违禁词检测**: 实时敏感词检测和提醒
- **边界情况处理**: 智能的超时和异常处理机制
- **多模式支持**: 语音+文本和纯文本两种练习模式

### 评估模块 (Evaluation)

评估模块负责练习结果的展示和分析：

- **即时评估**: 练习完成后的自动评估
- **多维度反馈**: 评级、优势、改进建议
- **音频回放**: 完整对话录音的播放功能
- **AI智能评估**: 基于AI的评估结果和建议

### 公共组件 (Components)

- **TaskCourseDetailDialog**: 任务课程详情弹窗，支持课程列表展示和操作
- **ExerciseModeDialog**: 练习模式选择弹窗，提供模式选择界面
  - **详细文档**: [AI练习模式选择弹窗组件](./components/ExerciseModeDialog.md)

### 练习子组件 (Exercise Components)

- **ForbiddenWordMessage**: 违禁词提示组件，不响应ESC键的自定义提示
  - **详细文档**: [违禁词提示组件](./exercise/components/ForbiddenWordMessage.md)

### 业务逻辑Hook (Business Hooks)

- **useForbiddenWordCheck**: 违禁词检测Hook，处理EventStream接口
  - **详细文档**: [违禁词检测Hook](./exercise/hooks/useForbiddenWordCheck.md)

## 📊 性能优化

### 1. 组件懒加载

- 抽屉组件按需加载
- 大型子模块异步导入
- 减少初始加载时间

### 2. 状态管理优化

- 全局状态统一管理
- 避免重复状态创建
- 及时清理无用状态

### 3. 音频处理优化

- 音频资源预加载
- 播放状态智能管理
- 内存使用优化

## 🔧 开发调试

### 调试工具

```text
// 调试命令
window.myTaskDebug.showTaskList()      // 显示任务列表状态
window.myTaskDebug.showExerciseState() // 显示练习状态
window.myTaskDebug.testModeSwitch()    // 测试模式切换
```

### 日志系统

- 📋 任务管理日志
- 🎯 练习流程日志
- 📊 评估结果日志
- 🔄 状态同步日志

## 📝 总结

AI质检我的任务模块是一个功能完整、架构清晰的业务系统。通过模块化设计和统一状态管理，实现了：

- **完整的业务流程**: 从任务查看到练习完成的全流程覆盖
- **灵活的模式支持**: 多种练习模式满足不同需求
- **优秀的用户体验**: 流畅的交互和及时的反馈
- **可维护的架构**: 清晰的模块划分和状态管理
- **可扩展的设计**: 便于功能扩展和模块复用

该模块为电销质检系统提供了强大的用户端功能支持，帮助销售人员系统性地提升业务能力和沟通技巧。

---

*本文档详细介绍了AI质检我的任务模块的完整功能和技术实现。更多详细信息请参考各子模块的专门文档。*
