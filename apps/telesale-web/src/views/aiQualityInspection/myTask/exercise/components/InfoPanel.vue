<!--
 * @Date         : 2025-05-22 10:00:00
 * @Description  : 课程练习 - 左侧信息面板
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div
    :class="[
      'bg-gray-50 rounded-8px flex flex-col transition-all duration-300 relative',
      collapsed ? 'w-50px p-5px' : 'w-30% p-15px'
    ]"
  >
    <!-- 折叠按钮 -->
    <div
      class="collapse-btn absolute cursor-pointer bg-white shadow-md rounded-full flex items-center justify-center z-10"
      :style="{
        right: '-12px',
        top: '50%',
        transform: 'translateY(-50%)'
      }"
      @click="togglePanel"
    >
      <el-icon :class="{ 'transform rotate-180': collapsed }">
        <ArrowLeft />
      </el-icon>
    </div>
    <!-- 课程信息区域 -->
    <template v-if="!collapsed">
      <div
        class="p-15px bg-white rounded-8px shadow-sm mb-15px"
        v-loading="loading"
      >
        <div
          class="flex justify-between items-center cursor-pointer mb-15px"
          @click="toggleRequirementCollapse"
        >
          <span class="text-16px font-bold">课程信息</span>
          <el-icon :class="{ 'transform rotate-180': !requirementCollapsed }">
            <ArrowDown />
          </el-icon>
        </div>

        <div v-show="!requirementCollapsed">
          <el-descriptions :column="1">
            <el-descriptions-item label="课程名称">
              {{ courseDetail?.name || "未命名课程" }}
            </el-descriptions-item>

            <el-descriptions-item label="最大持续时间">
              {{ courseDetail?.maxDuration || 60 }} 分钟
            </el-descriptions-item>

            <el-descriptions-item label="机器人名称">
              {{ courseDetail?.botName || "未设置" }}
            </el-descriptions-item>

            <el-descriptions-item label="机器人角色">
              {{ courseDetail?.botRole || "未设置" }}
            </el-descriptions-item>

            <!-- <el-descriptions-item label="背景描述">
              <div class="whitespace-pre-wrap break-words">
                {{ courseDetail?.backgroundDesc || "无背景描述" }}
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="对话要求">
              <div class="whitespace-pre-wrap break-words">
                {{ courseDetail?.conversationReq || "无对话要求" }}
              </div>
            </el-descriptions-item> -->

            <el-descriptions-item label="练习目标">
              <div class="whitespace-pre-wrap break-words">
                {{ courseDetail?.target || "无练习目标" }}
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="创建时间">
              {{
                courseDetail?.createdAt
                  ? new Date(courseDetail.createdAt).toLocaleString()
                  : "未知"
              }}
            </el-descriptions-item>

            <el-descriptions-item label="用户标签">
              <div
                v-loading="tagsLoading"
                class="whitespace-pre-wrap break-words"
              >
                <el-tag
                  v-for="tag in userTags"
                  :key="tag.id"
                  effect="plain"
                  class="mr-5px mb-5px"
                >
                  {{ tag.name }}
                </el-tag>
                <span
                  v-if="userTags.length === 0 && !tagsLoading"
                  class="text-gray-400"
                >
                  暂无标签
                </span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </template>
    <template v-else>
      <!-- 折叠状态下显示图标 -->
      <div class="flex flex-col items-center py-10px">
        <el-tooltip
          content="课程信息"
          placement="right"
          popper-class="max-w-500px"
        >
          <div class="icon-btn" @click="togglePanel">
            <el-icon><ChatLineRound /></el-icon>
          </div>
        </el-tooltip>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { ArrowDown, ArrowLeft, ChatLineRound } from "@element-plus/icons-vue";
import {
  WorkerTrainingTaskCourseInstance,
  getWorkerTrainingTaskCourseInfo
} from "/@/api/AIQualityInspection/taskManagement";
import { TrainingCourseInstance } from "/@/api/AIQualityInspection/courseManage";
import { getMget } from "/@/api/AISupport/Library";
import { TagInstance } from "/@/api/AIQualityInspection/excellentCases";
import libraryInfo from "/@/views/aiQualityInspection/const/Library";

// 导入统一状态管理
import { useExerciseStore } from "../store";

// 移除props，直接使用store

// 使用统一状态管理
const store = useExerciseStore();

// 折叠状态
const requirementCollapsed = ref(false);
const collapsed = ref(false);

// 加载状态
const loading = ref(false);

// 课程详情数据
const courseDetail = ref<TrainingCourseInstance | null>(null);

/**
 * 用户标签数据
 */
const userTags = ref<TagInstance[]>([]);

/**
 * 标签加载状态
 */
const tagsLoading = ref(false);

// 折叠/展开对话要求
function toggleRequirementCollapse() {
  requirementCollapsed.value = !requirementCollapsed.value;
}

// 不再需要用户画像相关功能

// 折叠/展开左侧面板
function togglePanel() {
  collapsed.value = !collapsed.value;
}

/**
 * 获取用户标签数据
 */
async function fetchUserTags() {
  if (
    !courseDetail.value?.referenceQas ||
    courseDetail.value.referenceQas.length === 0
  ) {
    userTags.value = [];
    return;
  }

  // 提取所有referenceQaId
  const referenceQaIds = courseDetail.value.referenceQas
    .map(qa => qa.referenceQaId)
    .filter(id => id); // 过滤掉空值

  if (referenceQaIds.length === 0) {
    userTags.value = [];
    return;
  }

  tagsLoading.value = true;
  try {
    const response = await getMget({
      libraryUUID: libraryInfo.libraryUUID,
      ids: referenceQaIds
    });

    // 定义返回数据类型
    interface TagGroupInstance {
      id: number;
      name: string;
      key: string;
      tags: TagInstance[];
    }

    interface KnowledgeItem {
      id: string;
      tags?: TagGroupInstance[];
    }

    interface ResponseData {
      list: KnowledgeItem[];
    }

    // 提取所有标签数据
    const allTags: TagInstance[] = [];
    const responseData = response.data as ResponseData;
    if (responseData?.list) {
      responseData.list.forEach((item: KnowledgeItem) => {
        if (item.tags && Array.isArray(item.tags)) {
          // 从标签组中提取实际的标签
          item.tags.forEach((tagGroup: TagGroupInstance) => {
            if (tagGroup.tags && Array.isArray(tagGroup.tags)) {
              allTags.push(...tagGroup.tags);
            }
          });
        }
      });
    }

    // 去重标签（根据key去重）
    const uniqueTags = allTags.filter(
      (tag, index, self) => index === self.findIndex(t => t.key === tag.key)
    );

    userTags.value = uniqueTags;
  } catch (error) {
    console.error("获取用户标签失败:", error);
    ElMessage.error("获取用户标签失败");
    userTags.value = [];
  } finally {
    tagsLoading.value = false;
  }
}

// 获取课程详情
async function fetchCourseDetail() {
  // 使用store中的课程信息
  const courseInfo = store.courseInfo.value;

  if (!courseInfo?.workerTrainingTaskId || !courseInfo?.trainingTaskCourseId) {
    console.error("缺少必要的课程ID信息");
    return;
  }

  loading.value = true;
  try {
    const response = await getWorkerTrainingTaskCourseInfo({
      workerTrainingTaskId: courseInfo.workerTrainingTaskId,
      trainingTaskCourseId: courseInfo.trainingTaskCourseId
    });

    // 从响应中提取课程详情
    if (response.data?.conversation?.trainingTaskCourseId) {
      // 如果有会话数据，则从courseInfo中获取课程信息
      courseDetail.value = courseInfo?.course?.course || null;
    } else {
      // 如果没有会话数据，则使用courseInfo中的课程信息
      courseDetail.value = courseInfo?.course?.course || null;
    }

    console.log("课程详情:", courseDetail.value);

    // 获取用户标签
    await fetchUserTags();
  } catch (error) {
    console.error("获取课程详情失败:", error);
    ElMessage.error("获取课程详情失败");
  } finally {
    loading.value = false;
  }
}

// 组件挂载时获取课程详情
onMounted(() => {
  fetchCourseDetail();
});
</script>

<style scoped>
.transform {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}

/* 折叠按钮样式 */
.collapse-btn {
  width: 24px;
  height: 24px;
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  color: #409eff;
}

.collapse-btn:hover {
  background-color: #f0f9ff;
  color: #1677ff;
}

/* 图标按钮样式 */
.icon-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f9ff;
  color: #409eff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.icon-btn:hover {
  background-color: #e6f4ff;
  color: #1677ff;
  transform: scale(1.05);
}

/* 限制tooltip最大宽度 */
:global(.max-w-500px) {
  max-width: 500px !important;
}
</style>
