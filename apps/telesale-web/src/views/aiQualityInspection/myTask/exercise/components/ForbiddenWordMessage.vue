<!--
  自定义违禁词提示组件
  仿照Element Plus Message样式，但不响应ESC键关闭
-->
<template>
  <Teleport to="body">
    <Transition
      name="forbidden-word-message-fade"
      @enter="onEnter"
      @leave="onLeave"
    >
      <div
        v-if="visible"
        :class="[
          'forbidden-word-message',
          `forbidden-word-message--${type}`,
          {
            'is-center': center
          }
        ]"
        :style="customStyle"
        role="alert"
      >
        <!-- 图标 -->
        <el-icon
          v-if="iconComponent"
          :class="`forbidden-word-message-icon--${type}`"
        >
          <component :is="iconComponent" />
        </el-icon>

        <!-- 内容 -->
        <div class="forbidden-word-message__content ml-10px">
          <slot>
            <span v-if="!dangerouslyUseHTMLString">{{ message }}</span>
            <span v-else v-html="message" />
          </slot>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount } from "vue";
import { ElIcon } from "element-plus";
import {
  CircleCheck,
  InfoFilled,
  Warning,
  CircleClose
} from "@element-plus/icons-vue";

export interface ForbiddenWordMessageProps {
  message?: string;
  type?: "success" | "warning" | "info" | "error";
  iconComponent?: any;
  dangerouslyUseHTMLString?: boolean;
  customClass?: string;
  duration?: number;
  center?: boolean;
  onClose?: () => void;
  offset?: number;
  zIndex?: number;
}

const props = withDefaults(defineProps<ForbiddenWordMessageProps>(), {
  message: "",
  type: "error",
  dangerouslyUseHTMLString: false,
  customClass: "",
  duration: 0, // 默认不自动关闭
  center: false,
  offset: 20, // 默认offset，实际使用时会动态计算避免与Element Plus message重叠
  zIndex: 1000001 // 设置比Element Plus message更高的z-index（Element Plus error message z-index: 1000000）
});

const emit = defineEmits<{
  destroy: [];
}>();

const visible = ref(false);
let timer: NodeJS.Timeout | null = null;

// 计算图标组件
const iconComponent = computed(() => {
  if (props.iconComponent) {
    return props.iconComponent;
  }

  const iconMap = {
    success: CircleCheck,
    warning: Warning,
    info: InfoFilled,
    error: CircleClose
  };

  return iconMap[props.type];
});

// 计算自定义样式
const customStyle = computed(() => {
  return {
    top: `${props.offset}px`,
    zIndex: props.zIndex
  };
});

// 关闭消息
function close() {
  visible.value = false;
}

// 启动自动关闭定时器
function startTimer() {
  if (props.duration > 0) {
    timer = setTimeout(() => {
      close();
    }, props.duration);
  }
}

// 清除定时器
function clearTimer() {
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
}

// 进入动画完成
function onEnter() {
  startTimer();
}

// 离开动画完成
function onLeave() {
  emit("destroy");
  if (props.onClose) {
    props.onClose();
  }
}

// 显示消息
function show() {
  visible.value = true;
}

// 组件挂载时显示
onMounted(() => {
  show();
});

// 组件卸载时清理
onBeforeUnmount(() => {
  clearTimer();
});

// 暴露方法给外部调用
defineExpose({
  close,
  show
});
</script>

<style scoped>
.forbidden-word-message {
  --el-message-bg-color: var(--el-color-info-light-9);
  --el-message-border-color: var(--el-border-color-lighter);
  --el-message-padding: 15px 19px;
  --el-message-close-size: 16px;
  --el-message-close-icon-color: var(--el-text-color-placeholder);
  --el-message-close-hover-color: var(--el-text-color-secondary);

  width: fit-content;
  max-width: calc(100% - 32px);
  box-sizing: border-box;
  border-radius: var(--el-border-radius-base);
  border-width: var(--el-border-width);
  border-style: var(--el-border-style);
  border-color: var(--el-message-border-color);
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--el-message-bg-color);
  transition: opacity var(--el-transition-duration), transform 0.4s, top 0.4s;
  padding: var(--el-message-padding);
  display: flex;
  align-items: center;
}

.forbidden-word-message.is-center {
  justify-content: center;
}

.forbidden-word-message p {
  margin: 0;
}

/* 成功样式 */
.forbidden-word-message--success {
  --el-message-bg-color: var(--el-color-success-light-9);
  --el-message-border-color: var(--el-color-success-light-8);
  --el-message-text-color: var(--el-color-success);
}

.forbidden-word-message--success .forbidden-word-message__content {
  color: var(--el-message-text-color);
  overflow-wrap: anywhere;
}

.forbidden-word-message .forbidden-word-message-icon--success {
  color: var(--el-message-text-color);
}

/* 信息样式 */
.forbidden-word-message--info {
  --el-message-bg-color: var(--el-color-info-light-9);
  --el-message-border-color: var(--el-color-info-light-8);
  --el-message-text-color: var(--el-color-info);
}

.forbidden-word-message--info .forbidden-word-message__content {
  color: var(--el-message-text-color);
  overflow-wrap: anywhere;
}

.forbidden-word-message .forbidden-word-message-icon--info {
  color: var(--el-message-text-color);
}

/* 警告样式 */
.forbidden-word-message--warning {
  --el-message-bg-color: var(--el-color-warning-light-9);
  --el-message-border-color: var(--el-color-warning-light-8);
  --el-message-text-color: var(--el-color-warning);
}

.forbidden-word-message--warning .forbidden-word-message__content {
  color: var(--el-message-text-color);
  overflow-wrap: anywhere;
}

.forbidden-word-message .forbidden-word-message-icon--warning {
  color: var(--el-message-text-color);
}

/* 错误样式 */
.forbidden-word-message--error {
  --el-message-bg-color: var(--el-color-error-light-9);
  --el-message-border-color: var(--el-color-error-light-8);
  --el-message-text-color: var(--el-color-error);
}

.forbidden-word-message--error .forbidden-word-message__content {
  color: var(--el-message-text-color);
  overflow-wrap: anywhere;
}

.forbidden-word-message .forbidden-word-message-icon--error {
  color: var(--el-message-text-color);
}

.forbidden-word-message__icon {
  margin-right: 10px;
}

.forbidden-word-message__content {
  padding: 0;
  font-size: 14px;
  line-height: 1;
}

.forbidden-word-message__content:focus {
  outline-width: 0;
}

/* 动画效果 */
.forbidden-word-message-fade-enter-from,
.forbidden-word-message-fade-leave-to {
  opacity: 0;
  transform: translate(-50%, -100%);
}

.forbidden-word-message-fade-enter-active {
  transition: all 0.3s ease-out;
}

.forbidden-word-message-fade-leave-active {
  transition: all 0.3s ease-in;
}

.forbidden-word-message-fade-enter-to,
.forbidden-word-message-fade-leave-from {
  opacity: 1;
  transform: translateX(-50%);
}
</style>
