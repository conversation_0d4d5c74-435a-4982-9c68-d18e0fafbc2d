# 违禁词提示组件 (ForbiddenWordMessage)

> 一个自定义的违禁词提示组件，仿照Element Plus Message样式但不响应ESC键关闭

## 📖 组件概述

违禁词提示组件是AI质检练习系统中的专用提示组件，用于在检测到违禁词时向用户显示警告信息。该组件采用与Element Plus Message相似的视觉设计，但具有特殊的交互行为，不会被ESC键意外关闭，确保重要的合规提示能够被用户看到。

## ✨ 核心特性

- 🎮 **ESC键免疫**: 不响应ESC键关闭，避免与录音取消功能冲突
- ⚡ **Element Plus风格**: 采用与Element Plus Message一致的视觉设计
- 🖼️ **多种类型**: 支持success、warning、info、error四种提示类型
- 🔧 **自定义样式**: 支持自定义偏移量、层级和样式类
- 🎨 **动画效果**: 流畅的进入和离开动画效果
- 📱 **Teleport渲染**: 使用Teleport渲染到body，避免层级问题

## 🚀 快速开始

### 基本用法

```vue
<template>
  <ForbiddenWordMessage
    :message="errorMessage"
    type="error"
    :duration="5000"
    :offset="calculatedOffset"
    @destroy="handleDestroy"
  />
</template>

<script setup>
import ForbiddenWordMessage from './ForbiddenWordMessage.vue'

const errorMessage = ref('检测到违禁词：敏感内容')
const calculatedOffset = ref(20)

const handleDestroy = () => {
  console.log('违禁词提示已销毁')
}
</script>
```

### Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| message | string | ❌ | '' | 提示消息内容 |
| type | 'success' \| 'warning' \| 'info' \| 'error' | ❌ | 'error' | 提示类型 |
| iconComponent | any | ❌ | - | 自定义图标组件 |
| dangerouslyUseHTMLString | boolean | ❌ | false | 是否将message作为HTML字符串处理 |
| customClass | string | ❌ | '' | 自定义CSS类名 |
| duration | number | ❌ | 0 | 自动关闭时间（毫秒），0表示不自动关闭 |
| center | boolean | ❌ | false | 是否居中显示 |
| onClose | () => void | ❌ | - | 关闭回调函数 |
| offset | number | ❌ | 20 | 距离顶部的偏移量 |
| zIndex | number | ❌ | 1000001 | 层级，默认高于Element Plus Message |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| destroy | - | 组件销毁时触发 |

## 🔧 技术实现

### 核心原理

1. **Teleport渲染**: 使用Vue 3的Teleport将组件渲染到body元素
2. **高层级设计**: 设置比Element Plus Message更高的z-index确保显示优先级
3. **ESC键隔离**: 不监听键盘事件，避免被ESC键意外关闭
4. **动态偏移**: 支持动态计算偏移量，避免与其他消息重叠

### 关键方法

```typescript
// 显示消息
function show(): void

// 关闭消息
function close(): void

// 启动自动关闭定时器
function startTimer(): void

// 清除定时器
function clearTimer(): void
```

### 样式特性

- **Element Plus兼容**: 使用Element Plus的CSS变量和设计规范
- **类型样式**: 不同类型对应不同的颜色主题
- **动画效果**: 淡入淡出和位移动画
- **响应式设计**: 自适应宽度和最大宽度限制

## 🎯 使用场景

- **违禁词检测**: 在AI对话中检测到敏感词汇时显示警告
- **合规提醒**: 重要的合规信息提示，需要确保用户看到
- **录音场景**: 在录音过程中显示提示，不被ESC键取消录音功能干扰
- **错误提示**: 需要持久显示的错误信息

## 📋 更新日志

### v1.0.0 (2025-01-27)

- ✨ 新增违禁词提示组件
- ⚡ 实现ESC键免疫功能，避免与录音取消冲突
- 🔧 采用Element Plus Message样式设计
- 🎨 支持四种提示类型和自定义样式
- 📱 使用Teleport渲染，确保正确的层级显示
- 🔄 实现动态偏移量计算，避免与其他消息重叠
