<!--
 * @Date         : 2025-05-23 16:30:00
 * @Description  : 对话区域组件 - 使用Web Audio API优化版本
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="flex-1 flex flex-col overflow-hidden">
    <!-- 对话内容区域 -->
    <div ref="chatContentRef" class="flex-1 p-20px overflow-auto bg-gray-50">
      <!-- 消息列表 -->
      <div class="space-y-15px">
        <div
          v-for="(message, index) in messages"
          :key="index"
          class="flex flex-col gap-5px"
        >
          <!-- 时间戳显示 -->
          <div
            v-if="message.startTime"
            class="text-center text-gray-400 text-12px"
          >
            {{ formatTimestamp(message.startTime) }}
          </div>

          <!-- 消息内容 -->
          <div
            class="flex"
            :class="message.isBot ? 'justify-start' : 'justify-end'"
          >
            <!-- 机器人消息 -->
            <div
              v-if="message.isBot"
              class="flex items-start gap-10px max-w-80%"
            >
              <!-- 机器人头像 -->
              <div
                class="w-40px h-40px rounded-full bg-blue-500 flex items-center justify-center text-white text-14px font-medium flex-shrink-0"
              >
                {{ roleName.charAt(0) }}
              </div>

              <!-- 消息内容 -->
              <div class="flex flex-col gap-8px">
                <div
                  class="bg-white p-15px rounded-12px shadow-sm border border-gray-200 text-gray-800 leading-relaxed"
                >
                  {{ message.content }}
                </div>

                <!-- 音频播放控制 -->
                <div class="flex items-center gap-8px">
                  <button
                    @click="toggleAudio(message)"
                    class="flex items-center gap-5px px-10px py-5px bg-blue-50 hover:bg-blue-100 rounded-6px text-blue-600 text-12px transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-blue-50"
                    :disabled="
                      !message.content.trim() ||
                      hasAudioError(message.index) ||
                      isPlayButtonDisabled(message)
                    "
                    :title="getPlayButtonTooltip(message)"
                  >
                    <el-icon size="14">
                      <VideoPlay
                        v-if="playingMessage?.index !== message.index"
                      />
                      <VideoPause v-else />
                    </el-icon>
                    <span>
                      {{
                        playingMessage?.index === message.index
                          ? "暂停"
                          : "播放"
                      }}
                    </span>
                  </button>

                  <!-- 音频错误提示 -->
                  <div
                    v-if="hasAudioError(message.index)"
                    class="text-red-500 text-12px"
                    :title="getAudioErrorMessage(message.index)"
                  >
                    音频生成失败
                  </div>

                  <!-- 播放状态指示 -->
                  <div
                    v-if="playingMessage?.index === message.index"
                    class="flex items-center gap-5px text-blue-600 text-12px"
                  >
                    <div class="flex gap-1">
                      <div
                        class="w-2px h-10px bg-blue-600 animate-bounce"
                        style="animation-delay: 0ms"
                      />
                      <div
                        class="w-2px h-10px bg-blue-600 animate-bounce"
                        style="animation-delay: 150ms"
                      />
                      <div
                        class="w-2px h-10px bg-blue-600 animate-bounce"
                        style="animation-delay: 300ms"
                      />
                    </div>
                    <span>播放中</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 用户消息 -->
            <div v-else class="flex items-start gap-10px max-w-80%">
              <!-- 消息内容 -->
              <div
                class="bg-blue-500 text-white p-15px rounded-12px shadow-sm leading-relaxed"
              >
                {{ message.content }}
              </div>

              <!-- 用户头像 -->
              <div
                class="w-40px h-40px rounded-full bg-gray-400 flex items-center justify-center text-white text-14px font-medium flex-shrink-0"
              >
                我
              </div>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="flex justify-start">
          <div class="flex items-start gap-10px max-w-80%">
            <!-- 机器人头像 -->
            <div
              class="w-40px h-40px rounded-full bg-blue-500 flex items-center justify-center text-white text-14px font-medium flex-shrink-0"
            >
              {{ roleName.charAt(0) }}
            </div>

            <!-- 思考动画 -->
            <div
              class="bg-white p-15px rounded-12px shadow-sm border border-gray-200 flex items-center gap-8px"
            >
              <div class="flex gap-2">
                <div
                  class="w-8px h-8px bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: 0ms"
                />
                <div
                  class="w-8px h-8px bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: 150ms"
                />
                <div
                  class="w-8px h-8px bg-gray-400 rounded-full animate-bounce"
                  style="animation-delay: 300ms"
                />
              </div>
              <span class="text-gray-500 text-14px">AI正在思考中...</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 导入基础模块
import { ref, nextTick, watch, onMounted, onBeforeUnmount } from "vue";
import { ElIcon, ElMessage } from "element-plus";
import { VideoPlay, VideoPause } from "@element-plus/icons-vue";
import baseURL from "/@/api/url";

// 导入全局音频播放器
import {
  playerStop,
  isPlaying,
  replay,
  player,
  startStreamPlayer,
  initGlobalAudioContext,
  cleanupGlobalAudio
} from "../utils/audioPlayer";

// 定义props
const props = defineProps<{
  messages: {
    content: string;
    isBot: boolean;
    index: string;
    startTime?: number;
  }[];
  roleName: string;
  audioData?: Map<string, ArrayBuffer>;
  // 新增：聊天状态相关props
  isTtsWaiting?: boolean; // TTS是否正在等待返回
  isMessageStreamPlaying?: (messageId: string) => boolean; // 检查消息是否正在流式播放
  shouldDisablePlayButton?: (
    messageId: string,
    isCurrentPlaying: boolean
  ) => boolean; // 检查是否应该禁用播放按钮
  completeStreamAudioPlayback?: (messageId: string) => void; // 新增：音频播放完全结束回调
}>();

// 音频错误状态管理
const audioErrorMessages = ref<Map<string, string>>(new Map());

// 定义事件
const emit = defineEmits<{
  audioPlayStateChange: [isPlaying: boolean];
}>();

// 组件引用
const chatContentRef = ref<HTMLElement | null>(null);

// 状态变量
const isLoading = ref(false);
const playingMessage = ref<{
  content: string;
  isBot: boolean;
  index: string;
} | null>(null);

// 流式音频播放相关
const audioQueue = ref<AudioBuffer[]>([]);
const isStreamPlaying = ref(false);
const currentStreamMessageId = ref<string | null>(null);

// 错误统计相关变量
const audioChunkErrorStats = ref({
  totalChunks: 0,
  errorChunks: 0,
  lastErrorTime: 0,
  consecutiveErrors: 0
});

/**
 * 格式化时间戳为可读时间
 * @param timestamp 秒级时间戳
 * @returns 格式化的时间字符串
 */
function formatTimestamp(timestamp?: number): string {
  if (!timestamp) return "";

  const date = new Date(timestamp * 1000);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const messageDate = new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate()
  );

  // 判断是否是今天
  if (messageDate.getTime() === today.getTime()) {
    // 今天只显示时间
    return date.toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    });
  } else {
    // 其他日期显示完整时间
    return date.toLocaleString("zh-CN", {
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    });
  }
}

/**
 * 检查播放按钮是否应该被禁用
 * @param message 消息对象
 * @returns 是否应该禁用
 */
function isPlayButtonDisabled(message: {
  content: string;
  isBot: boolean;
  index: string;
}): boolean {
  // 基础检查
  if (!message.content.trim() || hasAudioError(message.index)) {
    return true;
  }

  const isCurrentPlaying = playingMessage.value?.index === message.index;

  // 使用新的状态管理逻辑
  if (props.shouldDisablePlayButton) {
    const shouldDisable = props.shouldDisablePlayButton(
      message.index,
      isCurrentPlaying
    );
    if (shouldDisable) {
      return true;
    }
  }

  // 传统逻辑：其他音频正在播放时禁用
  if (isPlaying() && !isCurrentPlaying) {
    return true;
  }

  return false;
}

/**
 * 获取播放按钮的提示文字
 * @param message 消息对象
 * @returns 提示文字
 */
function getPlayButtonTooltip(message: {
  content: string;
  isBot: boolean;
  index: string;
}): string {
  if (!message.content.trim()) {
    return "消息内容为空，无法播放";
  }

  // 检查是否有音频错误
  if (audioErrorMessages.value.has(message.index)) {
    return "音频生成失败，无法播放";
  }

  const isCurrentPlaying = playingMessage.value?.index === message.index;

  // 检查TTS等待状态
  if (props.isTtsWaiting) {
    return "TTS正在生成中，请等待";
  }

  // 检查是否正在流式播放且无法暂停
  if (isCurrentPlaying && props.isMessageStreamPlaying) {
    const isStreamPlaying = props.isMessageStreamPlaying(message.index);
    if (isStreamPlaying) {
      return "流式播放中，无法暂停";
    }
  }

  if (isCurrentPlaying) {
    return "点击暂停播放";
  }

  if (isPlaying()) {
    return "其他音频正在播放中，请等待播放完成";
  }

  return "点击播放语音";
}

/**
 * 检查消息是否有音频错误
 * @param messageIndex 消息索引
 * @returns 是否有错误
 */
function hasAudioError(messageIndex: string): boolean {
  return audioErrorMessages.value.has(messageIndex);
}

/**
 * 获取音频错误信息
 * @param messageIndex 消息索引
 * @returns 错误信息
 */
function getAudioErrorMessage(messageIndex: string): string {
  return audioErrorMessages.value.get(messageIndex) || "";
}

/**
 * 切换音频播放状态
 * @param message 消息对象
 */
function toggleAudio(message: {
  content: string;
  isBot: boolean;
  index: string;
}) {
  if (playingMessage.value?.index === message.index) {
    // 停止当前播放
    playerStop();
    playingMessage.value = null;
    emit("audioPlayStateChange", false);
  } else {
    // 检查是否有其他音频正在播放
    if (isPlaying()) {
      playerStop();
    }
    playAudio(message);
  }
}

/**
 * 播放指定消息的音频 - 使用全局音频播放器
 * @param message 要播放的消息
 */
async function playAudio(message: {
  content: string;
  isBot: boolean;
  index: string;
}) {
  if (!message.isBot) return;

  // 使用全局播放器停止所有播放
  playerStop();

  // 清理之前的错误状态
  clearAudioError(message.index);

  playingMessage.value = message;
  emit("audioPlayStateChange", true);

  try {
    console.log("播放音频请求:", {
      messageIndex: message.index,
      hasAudioData: props.audioData && props.audioData.has(message.index),
      audioDataSize: props.audioData?.size || 0,
      audioDataKeys: props.audioData ? Array.from(props.audioData.keys()) : []
    });

    // 优先使用本地音频数据
    if (props.audioData && props.audioData.has(message.index)) {
      const audioBuffer = props.audioData.get(message.index);
      if (audioBuffer && audioBuffer.byteLength > 0) {
        console.log(
          "使用本地音频数据播放，大小:",
          audioBuffer.byteLength,
          "bytes"
        );

        // 验证音频数据
        if (audioBuffer.byteLength < 100) {
          console.warn("音频数据过小，可能不完整:", audioBuffer.byteLength);
          handleAudioError(message.index, "音频数据过小，可能不完整");
          return;
        }

        // 分析音频数据格式
        const audioAnalysis = analyzeAudioChunk(audioBuffer);
        console.log("缓存音频数据分析:", audioAnalysis);

        // 使用全局播放器播放
        replay(
          audioBuffer,
          () => {
            // 播放结束回调
            playingMessage.value = null;
            emit("audioPlayStateChange", false);
          },
          async (error: Error) => {
            // 播放错误回调
            console.error("全局播放器播放失败:", error);

            // 尝试HTML Audio降级播放
            if (
              error.name === "EncodingError" ||
              error.message.includes("decode")
            ) {
              console.log("尝试HTML Audio降级播放缓存音频");
              try {
                await playAudioWithHTMLAudio(audioBuffer, message.index);
                return; // 降级播放成功，直接返回
              } catch (htmlError) {
                console.error("HTML Audio降级播放也失败:", htmlError);
              }
            }

            let errorMessage = "音频播放失败";
            if (error.message.includes("decode")) {
              errorMessage = "音频格式不支持或数据损坏";
            } else {
              errorMessage = error.message;
            }
            handleAudioError(message.index, errorMessage);
          }
        );
      } else {
        console.warn("本地音频数据为空或无效");
        handleAudioError(message.index, "本地音频数据无效，无法播放");
        return;
      }
    } else {
      console.warn("没有找到本地音频数据");
      handleAudioError(message.index, "音频数据未找到，请重新生成");
      return;
    }
  } catch (error) {
    console.error("音频播放失败:", error);

    // 分析错误类型
    let errorMessage = "音频播放失败";
    if (error instanceof DOMException && error.name === "EncodingError") {
      errorMessage = "音频格式不支持或数据损坏";
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    handleAudioError(message.index, errorMessage);
  }
}

/**
 * 使用HTML Audio作为降级方案
 * @param audioBuffer 音频数据（可选）
 * @param messageIndex 消息索引
 */
async function playAudioWithHTMLAudio(
  audioBuffer: ArrayBuffer | null,
  messageIndex: string
) {
  console.log("使用HTML Audio降级播放");

  const audio = new Audio();

  if (audioBuffer) {
    // 尝试不同的MIME类型
    const mimeTypes = ["audio/wav", "audio/mpeg", "audio/mp4", "audio/ogg"];

    for (const mimeType of mimeTypes) {
      try {
        const blob = new Blob([audioBuffer], { type: mimeType });
        const audioUrl = URL.createObjectURL(blob);
        audio.src = audioUrl;

        // 测试是否可以播放
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => reject(new Error("加载超时")), 3000);

          audio.oncanplay = () => {
            clearTimeout(timeout);
            resolve(true);
          };

          audio.onerror = () => {
            clearTimeout(timeout);
            URL.revokeObjectURL(audioUrl);
            reject(new Error(`MIME类型 ${mimeType} 不支持`));
          };
        });

        console.log(`成功使用MIME类型: ${mimeType}`);
        break;
      } catch (error) {
        console.warn(`MIME类型 ${mimeType} 失败:`, error);
        continue;
      }
    }
  } else {
    // 使用远程URL
    audio.src = `${baseURL.robot}/admin/audio/tts/${messageIndex}`;
  }

  // 播放音频
  audio.onended = () => {
    if (audio.src.startsWith("blob:")) {
      URL.revokeObjectURL(audio.src);
    }
    handleAudioEnded();
  };

  audio.onerror = event => {
    if (audio.src.startsWith("blob:")) {
      URL.revokeObjectURL(audio.src);
    }

    // 分析错误类型
    let errorMessage = "Failed to load because no supported source was found.";
    if (audioBuffer) {
      errorMessage = "音频数据格式不支持";
    } else {
      errorMessage = "远程音频资源加载失败";
    }

    handleAudioError(messageIndex, errorMessage);
  };

  await audio.play();
  console.log("HTML Audio播放成功");
}

/**
 * 停止音频播放
 */
function stopAudio() {
  console.log("停止音频播放");

  // 使用全局播放器停止
  playerStop();

  // 停止流式播放
  if (isStreamPlaying.value) {
    isStreamPlaying.value = false;
    currentStreamMessageId.value = null;
    audioQueue.value = [];
  }

  // 重置播放状态
  playingMessage.value = null;
  emit("audioPlayStateChange", false);
}

/**
 * 处理音频播放错误
 * @param messageId 消息ID（可选）
 * @param errorMessage 错误信息（可选）
 */
function handleAudioError(messageId?: string, errorMessage?: string) {
  console.error("音频播放出错");

  // 如果有消息ID，记录错误信息
  if (messageId) {
    const errorMsg = errorMessage || "音频播放失败";
    audioErrorMessages.value.set(messageId, errorMsg);

    // 显示错误提示
    ElMessage.error(`音频播放失败: ${errorMsg}`);
  }

  playerStop();
  playingMessage.value = null;
  emit("audioPlayStateChange", false);
}

/**
 * 处理流式音频播放错误
 * @param messageId 消息ID（可选）
 * @param errorMessage 错误信息（可选）
 */
function handleStreamAudioError(messageId?: string, errorMessage?: string) {
  console.error("流式音频播放出错，重置所有状态");

  // 停止全局播放器（这会重置全局播放状态）
  playerStop();

  // 如果有消息ID，记录错误信息
  if (messageId) {
    const errorMsg = errorMessage || "音频解码失败，可能是音频格式不支持";
    audioErrorMessages.value.set(messageId, errorMsg);

    // 显示错误提示
    ElMessage.error(`音频播放失败: ${errorMsg}`);
  }

  // 重置所有本地播放状态
  currentStreamMessageId.value = null;
  isStreamPlaying.value = false;
  audioQueue.value = [];
  playingMessage.value = null;

  // 通知父组件音频播放结束
  emit("audioPlayStateChange", false);

  console.log("流式音频错误处理完成，所有状态已重置");
}

/**
 * 音频播放结束处理
 */
function handleAudioEnded() {
  console.log("音频播放结束");
  playingMessage.value = null;
  emit("audioPlayStateChange", false);
}

/**
 * 开始流式音频播放
 * @param messageId 消息ID
 */
function startStreamAudio(messageId: string) {
  console.log("开始流式音频播放:", messageId);

  // 使用全局播放器停止所有播放
  playerStop();

  // 清理之前的错误状态
  clearAudioError(messageId);

  // 重置错误统计
  resetAudioChunkErrorStats();

  // 设置流式播放状态
  currentStreamMessageId.value = messageId;
  isStreamPlaying.value = true;
  audioQueue.value = [];

  // 开始流式播放器
  startStreamPlayer(() => {
    // 流式播放结束回调
    handleStreamAudioComplete();
  });

  // 设置播放状态
  const message = props.messages.find(msg => msg.index === messageId);
  if (message) {
    playingMessage.value = message;
    emit("audioPlayStateChange", true);
  }

  // 设置流式播放超时检测（15秒）
  setTimeout(() => {
    if (isStreamPlaying.value && currentStreamMessageId.value === messageId) {
      console.warn("流式音频播放超时，自动停止");
      handleStreamAudioComplete();
    }
  }, 15000);
}

/**
 * 处理流式音频数据块
 * @param audioChunk 音频数据块
 */
async function handleStreamAudioChunk(audioChunk: ArrayBuffer) {
  if (!currentStreamMessageId.value || !isStreamPlaying.value) {
    console.warn("没有当前流式消息或流式播放未激活");
    return;
  }

  // 检查音频数据有效性
  if (!audioChunk || audioChunk.byteLength === 0) {
    console.warn("收到空的音频数据块，跳过处理");
    return;
  }

  // 弱网环境优化：增加数据块大小检查
  if (audioChunk.byteLength < 100) {
    console.warn(
      `音频数据块过小 (${audioChunk.byteLength} bytes)，可能是网络传输不完整，跳过处理`
    );
    audioChunkErrorStats.value.totalChunks++;
    audioChunkErrorStats.value.errorChunks++;
    return;
  }

  try {
    console.log("处理流式音频数据块，大小:", audioChunk.byteLength, "bytes");

    // 更新总块数统计
    audioChunkErrorStats.value.totalChunks++;

    // 弱网环境优化：预验证音频数据格式
    const audioAnalysis = analyzeAudioChunk(audioChunk);
    console.log("音频数据块分析:", audioAnalysis);

    // 如果数据格式无法识别且过小，直接跳过
    if (
      !audioAnalysis.isWAV &&
      !audioAnalysis.isMP3 &&
      !audioAnalysis.isOGG &&
      audioChunk.byteLength < 1024
    ) {
      console.warn(
        "音频数据块格式无法识别且过小，可能是网络传输问题，跳过处理"
      );
      audioChunkErrorStats.value.errorChunks++;
      return;
    }

    // 获取全局音频上下文
    const audioContext = initGlobalAudioContext();

    // 弱网环境优化：使用增强的重试机制解码音频数据
    const audioBuffer = await decodeAudioDataWithRetryEnhanced(
      audioContext,
      audioChunk,
      5 // 增加重试次数到5次
    );
    console.log("音频数据块解码成功，时长:", audioBuffer.duration, "秒");

    // 再次检查播放状态（防止在解码过程中被停止）
    if (!isStreamPlaying.value || !isPlaying()) {
      console.log("播放状态已改变，丢弃音频块");
      return;
    }

    // 添加到播放队列
    audioQueue.value.push(audioBuffer);

    // 如果是第一个音频块，立即开始播放
    if (audioQueue.value.length === 1 && isStreamPlaying.value && isPlaying()) {
      player(audioContext, audioQueue.value, 0);
    }
  } catch (error) {
    console.error("解码流式音频数据块失败:", error);

    // 详细的错误分析
    const errorAnalysis = analyzeDecodingError(error, audioChunk);
    console.warn("音频解码错误分析:", errorAnalysis);

    // 记录错误统计
    recordAudioChunkError(error, audioChunk);

    // 弱网环境优化：更智能的降级处理策略
    if (error instanceof DOMException && error.name === "EncodingError") {
      console.warn("音频解码失败，尝试多种降级策略");

      // 策略1：尝试HTML Audio降级播放
      try {
        await playAudioChunkWithHTMLAudioEnhanced(audioChunk);
        console.log("HTML Audio降级播放成功，继续处理后续数据块");
        return; // 降级播放成功，继续处理后续数据
      } catch (htmlError) {
        console.error("HTML Audio降级播放失败:", htmlError);
      }

      // 策略2：尝试数据修复后再次解码
      try {
        const repairedChunk = await repairAudioChunkForWeakNetwork(audioChunk);
        const audioContext = initGlobalAudioContext();
        const audioBuffer = await audioContext.decodeAudioData(repairedChunk);

        // 添加到播放队列
        audioQueue.value.push(audioBuffer);
        console.log("数据修复后解码成功，继续处理");
        return;
      } catch (repairError) {
        console.error("数据修复后解码仍失败:", repairError);
      }
    }

    // 所有解码方式都失败，跳过这个数据块但继续流式播放
    console.warn(
      `跳过无法处理的音频数据块（大小: ${audioChunk.byteLength} bytes），继续处理后续数据`
    );

    // 显示用户友好的警告信息（不中断播放）
    const errorMessage = getAudioChunkErrorMessage(error, audioChunk);
    console.warn(`音频块处理警告: ${errorMessage}`);

    // 弱网环境优化：降低通知频率，避免用户体验受影响
    if (shouldShowChunkErrorNotificationEnhanced()) {
      ElMessage.warning({
        message: `网络不稳定，部分音频块解码失败，已自动跳过`,
        duration: 3000,
        showClose: false
      });
    }

    // 继续流式播放，不中断整个过程
    return;
  }
}

/**
 * 弱网环境增强的音频解码重试函数
 * @param audioContext 音频上下文
 * @param audioChunk 音频数据块
 * @param maxRetries 最大重试次数
 * @returns Promise<AudioBuffer>
 */
async function decodeAudioDataWithRetryEnhanced(
  audioContext: AudioContext,
  audioChunk: ArrayBuffer,
  maxRetries: number = 5
): Promise<AudioBuffer> {
  let lastError: any = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`音频解码尝试 ${attempt}/${maxRetries}`);

      // 尝试不同的数据处理方式
      let dataToProcess: ArrayBuffer;

      if (attempt === 1) {
        // 第一次尝试：直接使用原始数据
        dataToProcess = audioChunk.slice(0);
      } else if (attempt === 2) {
        // 第二次尝试：创建新的ArrayBuffer副本
        const newBuffer = new ArrayBuffer(audioChunk.byteLength);
        const newView = new Uint8Array(newBuffer);
        const originalView = new Uint8Array(audioChunk);
        newView.set(originalView);
        dataToProcess = newBuffer;
      } else if (attempt === 3) {
        // 第三次尝试：检查并修复可能的数据问题
        dataToProcess = await sanitizeAudioData(audioChunk);
      } else if (attempt === 4) {
        // 第四次尝试：弱网环境专用修复
        dataToProcess = await repairAudioChunkForWeakNetwork(audioChunk);
      } else {
        // 第五次尝试：尝试截取有效部分
        dataToProcess = await extractValidAudioPortion(audioChunk);
      }

      // 执行解码
      const audioBuffer = await audioContext.decodeAudioData(dataToProcess);

      if (attempt > 1) {
        console.log(`音频解码在第 ${attempt} 次尝试时成功`);
      }

      return audioBuffer;
    } catch (error) {
      lastError = error;
      console.warn(`音频解码第 ${attempt} 次尝试失败:`, error);

      // 弱网环境优化：增加重试间隔，给网络更多恢复时间
      if (attempt < maxRetries) {
        const delay = Math.min(100 * attempt, 500); // 最大延迟500ms
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  // 所有重试都失败，抛出最后一个错误
  throw lastError;
}

/**
 * 弱网环境专用音频数据修复
 * @param audioChunk 原始音频数据
 * @returns Promise<ArrayBuffer> 修复后的音频数据
 */
async function repairAudioChunkForWeakNetwork(
  audioChunk: ArrayBuffer
): Promise<ArrayBuffer> {
  console.log("弱网环境专用音频数据修复");

  // 检查数据是否为空或过小
  if (audioChunk.byteLength < 100) {
    throw new Error("音频数据过小，无法修复");
  }

  const view = new DataView(audioChunk);
  const uint8View = new Uint8Array(audioChunk);

  // 尝试修复常见的弱网传输问题

  // 1. 检查并修复WAV文件头
  if (audioChunk.byteLength >= 44) {
    const riff = String.fromCharCode(
      view.getUint8(0),
      view.getUint8(1),
      view.getUint8(2),
      view.getUint8(3)
    );

    if (riff === "RIFF" || riff.startsWith("RIF")) {
      // 尝试修复不完整的RIFF头
      const fixedBuffer = new ArrayBuffer(audioChunk.byteLength);
      const fixedView = new DataView(fixedBuffer);
      const fixedUint8 = new Uint8Array(fixedBuffer);

      // 复制原始数据
      fixedUint8.set(uint8View);

      // 修复RIFF标识
      if (riff !== "RIFF") {
        fixedView.setUint8(0, 0x52); // 'R'
        fixedView.setUint8(1, 0x49); // 'I'
        fixedView.setUint8(2, 0x46); // 'F'
        fixedView.setUint8(3, 0x46); // 'F'
        console.log("修复了不完整的RIFF头");
      }

      // 修复文件大小字段
      const actualSize = audioChunk.byteLength - 8;
      fixedView.setUint32(4, actualSize, true);

      // 检查并修复WAVE标识
      if (audioChunk.byteLength >= 12) {
        const wave = String.fromCharCode(
          view.getUint8(8),
          view.getUint8(9),
          view.getUint8(10),
          view.getUint8(11)
        );

        if (wave !== "WAVE") {
          fixedView.setUint8(8, 0x57); // 'W'
          fixedView.setUint8(9, 0x41); // 'A'
          fixedView.setUint8(10, 0x56); // 'V'
          fixedView.setUint8(11, 0x45); // 'E'
          console.log("修复了不完整的WAVE标识");
        }
      }

      return fixedBuffer;
    }
  }

  // 2. 如果不是WAV格式，尝试添加简单的WAV头
  if (audioChunk.byteLength >= 100) {
    console.log("尝试为原始音频数据添加WAV头");

    const sampleRate = 16000; // 假设采样率
    const numChannels = 1; // 假设单声道
    const bitsPerSample = 16; // 假设16位

    const wavHeaderSize = 44;
    const totalSize = wavHeaderSize + audioChunk.byteLength;
    const wavBuffer = new ArrayBuffer(totalSize);
    const wavView = new DataView(wavBuffer);
    const wavUint8 = new Uint8Array(wavBuffer);

    // 写入WAV头
    let offset = 0;

    // RIFF标识
    wavView.setUint8(offset++, 0x52); // 'R'
    wavView.setUint8(offset++, 0x49); // 'I'
    wavView.setUint8(offset++, 0x46); // 'F'
    wavView.setUint8(offset++, 0x46); // 'F'

    // 文件大小
    wavView.setUint32(offset, totalSize - 8, true);
    offset += 4;

    // WAVE标识
    wavView.setUint8(offset++, 0x57); // 'W'
    wavView.setUint8(offset++, 0x41); // 'A'
    wavView.setUint8(offset++, 0x56); // 'V'
    wavView.setUint8(offset++, 0x45); // 'E'

    // fmt 子块
    wavView.setUint8(offset++, 0x66); // 'f'
    wavView.setUint8(offset++, 0x6d); // 'm'
    wavView.setUint8(offset++, 0x74); // 't'
    wavView.setUint8(offset++, 0x20); // ' '

    // fmt子块大小
    wavView.setUint32(offset, 16, true);
    offset += 4;

    // 音频格式 (PCM = 1)
    wavView.setUint16(offset, 1, true);
    offset += 2;

    // 声道数
    wavView.setUint16(offset, numChannels, true);
    offset += 2;

    // 采样率
    wavView.setUint32(offset, sampleRate, true);
    offset += 4;

    // 字节率
    wavView.setUint32(
      offset,
      (sampleRate * numChannels * bitsPerSample) / 8,
      true
    );
    offset += 4;

    // 块对齐
    wavView.setUint16(offset, (numChannels * bitsPerSample) / 8, true);
    offset += 2;

    // 位深度
    wavView.setUint16(offset, bitsPerSample, true);
    offset += 2;

    // data子块
    wavView.setUint8(offset++, 0x64); // 'd'
    wavView.setUint8(offset++, 0x61); // 'a'
    wavView.setUint8(offset++, 0x74); // 't'
    wavView.setUint8(offset++, 0x61); // 'a'

    // data子块大小
    wavView.setUint32(offset, audioChunk.byteLength, true);
    offset += 4;

    // 复制原始音频数据
    wavUint8.set(new Uint8Array(audioChunk), offset);

    return wavBuffer;
  }

  // 如果无法修复，返回原始数据的副本
  return audioChunk.slice(0);
}

/**
 * 提取音频数据的有效部分
 * @param audioChunk 原始音频数据
 * @returns Promise<ArrayBuffer> 有效部分的音频数据
 */
async function extractValidAudioPortion(
  audioChunk: ArrayBuffer
): Promise<ArrayBuffer> {
  console.log("尝试提取音频数据的有效部分");

  // 如果数据太小，无法提取
  if (audioChunk.byteLength < 200) {
    throw new Error("音频数据过小，无法提取有效部分");
  }

  const view = new DataView(audioChunk);

  // 查找可能的音频数据开始位置
  let dataStart = 0;
  let dataEnd = audioChunk.byteLength;

  // 如果是WAV格式，查找data块
  if (audioChunk.byteLength >= 44) {
    const riff = String.fromCharCode(
      view.getUint8(0),
      view.getUint8(1),
      view.getUint8(2),
      view.getUint8(3)
    );

    if (riff === "RIFF") {
      // 查找data块
      for (let i = 12; i < audioChunk.byteLength - 8; i += 4) {
        const chunk = String.fromCharCode(
          view.getUint8(i),
          view.getUint8(i + 1),
          view.getUint8(i + 2),
          view.getUint8(i + 3)
        );

        if (chunk === "data") {
          const dataSize = view.getUint32(i + 4, true);
          dataStart = i + 8;
          dataEnd = Math.min(dataStart + dataSize, audioChunk.byteLength);
          console.log(`找到data块，位置: ${dataStart}-${dataEnd}`);
          break;
        }
      }
    }
  }

  // 如果找到了有效的数据范围，提取该部分
  if (dataEnd > dataStart && dataEnd - dataStart >= 100) {
    const extractedSize = dataEnd - dataStart;
    const extractedBuffer = new ArrayBuffer(extractedSize);
    const extractedView = new Uint8Array(extractedBuffer);
    const originalView = new Uint8Array(audioChunk, dataStart, extractedSize);

    extractedView.set(originalView);

    console.log(`提取了有效音频数据，大小: ${extractedSize} bytes`);
    return extractedBuffer;
  }

  // 如果无法找到有效部分，尝试截取中间部分
  const middleStart = Math.floor(audioChunk.byteLength * 0.1);
  const middleEnd = Math.floor(audioChunk.byteLength * 0.9);
  const middleSize = middleEnd - middleStart;

  if (middleSize >= 100) {
    const middleBuffer = new ArrayBuffer(middleSize);
    const middleView = new Uint8Array(middleBuffer);
    const originalView = new Uint8Array(audioChunk, middleStart, middleSize);

    middleView.set(originalView);

    console.log(`提取了中间部分音频数据，大小: ${middleSize} bytes`);
    return middleBuffer;
  }

  // 如果都无法提取，返回原始数据
  return audioChunk.slice(0);
}

/**
 * 弱网环境增强的HTML Audio降级播放
 * @param audioChunk 音频数据块
 */
async function playAudioChunkWithHTMLAudioEnhanced(
  audioChunk: ArrayBuffer
): Promise<void> {
  return new Promise((resolve, reject) => {
    console.log(
      "弱网环境增强HTML Audio降级播放，数据大小:",
      audioChunk.byteLength
    );

    // 扩展MIME类型列表，增加更多兼容性
    const mimeTypes = [
      "audio/wav",
      "audio/mpeg",
      "audio/mp4",
      "audio/ogg",
      "audio/webm",
      "audio/x-wav",
      "audio/vnd.wav",
      "audio/wave",
      "audio/x-pn-wav",
      "audio/aac",
      "audio/flac"
    ];

    let attemptCount = 0;
    let lastError: any = null;

    const tryNextMimeType = (index: number) => {
      if (index >= mimeTypes.length) {
        reject(
          new Error(
            `所有MIME类型都尝试失败，共尝试了${attemptCount}次。最后错误: ${
              lastError?.message || "未知错误"
            }`
          )
        );
        return;
      }

      const mimeType = mimeTypes[index];
      attemptCount++;

      console.log(
        `尝试MIME类型 ${mimeType} (${attemptCount}/${mimeTypes.length})`
      );

      const blob = new Blob([audioChunk], { type: mimeType });
      const audioUrl = URL.createObjectURL(blob);
      const audio = new Audio(audioUrl);

      const cleanup = () => {
        URL.revokeObjectURL(audioUrl);
      };

      // 弱网环境优化：增加超时时间
      const timeout = setTimeout(() => {
        cleanup();
        console.warn(`MIME类型 ${mimeType} 加载超时`);
        tryNextMimeType(index + 1);
      }, 8000); // 从5秒增加到8秒

      audio.oncanplay = () => {
        clearTimeout(timeout);
        console.log(`HTML Audio成功使用MIME类型: ${mimeType}`);

        audio.onended = () => {
          cleanup();
          console.log(`HTML Audio播放完成，使用的MIME类型: ${mimeType}`);
          resolve();
        };

        audio.onerror = errorEvent => {
          cleanup();
          lastError = errorEvent;
          console.error(`HTML Audio播放失败 (${mimeType}):`, errorEvent);
          reject(new Error(`播放失败: ${mimeType}`));
        };

        // 弱网环境优化：降低音量，减少播放压力
        audio.volume = 0.8;

        // 弱网环境优化：添加播放前的缓冲检查
        if (audio.readyState >= 2) {
          // HAVE_CURRENT_DATA
          audio.play().catch(playError => {
            cleanup();
            lastError = playError;
            console.error(
              `HTML Audio play()调用失败 (${mimeType}):`,
              playError
            );
            reject(playError);
          });
        } else {
          // 等待更多数据加载
          audio.oncanplaythrough = () => {
            audio.play().catch(playError => {
              cleanup();
              lastError = playError;
              console.error(
                `HTML Audio play()调用失败 (${mimeType}):`,
                playError
              );
              reject(playError);
            });
          };
        }
      };

      audio.onerror = errorEvent => {
        clearTimeout(timeout);
        cleanup();
        lastError = errorEvent;
        console.warn(`MIME类型 ${mimeType} 加载失败:`, errorEvent);
        tryNextMimeType(index + 1);
      };

      // 弱网环境优化：添加更多事件监听
      audio.onloadstart = () => {
        console.log(`开始加载MIME类型: ${mimeType}`);
      };

      audio.onprogress = () => {
        console.log(`正在加载MIME类型: ${mimeType}`);
      };

      audio.onstalled = () => {
        console.warn(`MIME类型 ${mimeType} 加载停滞`);
      };

      audio.onsuspend = () => {
        console.warn(`MIME类型 ${mimeType} 加载暂停`);
      };
    };

    tryNextMimeType(0);
  });
}

/**
 * 弱网环境优化的错误通知判断
 * @returns 是否显示通知
 */
function shouldShowChunkErrorNotificationEnhanced(): boolean {
  // 弱网环境优化：降低通知频率，每30秒最多显示一次
  const now = Date.now();
  const lastNotificationTime = audioChunkErrorStats.value.lastErrorTime;
  const timeSinceLastNotification = now - lastNotificationTime;

  // 如果错误率很高，减少通知频率
  const errorRate =
    audioChunkErrorStats.value.totalChunks > 0
      ? (audioChunkErrorStats.value.errorChunks /
          audioChunkErrorStats.value.totalChunks) *
        100
      : 0;

  if (errorRate > 30) {
    // 错误率超过30%时，每60秒最多显示一次
    return timeSinceLastNotification > 60000;
  } else {
    // 正常情况下，每30秒最多显示一次
    return timeSinceLastNotification > 30000;
  }
}

/**
 * 完成流式音频播放
 */
function completeStreamAudio() {
  console.log("流式音频接收完成");
  isStreamPlaying.value = false;

  // 如果队列为空，直接结束播放
  if (audioQueue.value.length === 0) {
    console.warn("流式音频队列为空，可能TTS生成失败");
    handleStreamAudioComplete();
  }
  // 否则等待队列播放完成
}

/**
 * 处理流式音频播放完成
 */
function handleStreamAudioComplete() {
  console.log("流式音频播放完全结束");

  const messageId = currentStreamMessageId.value;

  // 输出最终的错误统计
  if (audioChunkErrorStats.value.totalChunks > 0) {
    const totalChunks = audioChunkErrorStats.value.totalChunks;
    const errorChunks = audioChunkErrorStats.value.errorChunks;
    const successChunks = totalChunks - errorChunks;
    const successRate = ((successChunks / totalChunks) * 100).toFixed(2);
    const errorRate = ((errorChunks / totalChunks) * 100).toFixed(2);

    console.log("流式播放最终统计:", {
      总块数: totalChunks,
      错误块数: errorChunks,
      成功块数: successChunks,
      成功率: `${successRate}%`,
      错误率: `${errorRate}%`
    });
  }

  // 重置本地状态
  currentStreamMessageId.value = null;
  isStreamPlaying.value = false;
  audioQueue.value = [];
  playingMessage.value = null;
  emit("audioPlayStateChange", false);

  // 通知useChat清理TTS等待状态
  if (messageId && props.completeStreamAudioPlayback) {
    props.completeStreamAudioPlayback(messageId);
  }
}

/**
 * 强制停止流式音频播放（用于错误处理）
 */
function forceStopStreamAudio() {
  console.log("强制停止流式音频播放");

  // 停止全局播放器
  playerStop();

  // 重置所有状态
  currentStreamMessageId.value = null;
  isStreamPlaying.value = false;
  audioQueue.value = [];
  playingMessage.value = null;
  emit("audioPlayStateChange", false);
}

/**
 * 滚动到底部
 */
function scrollToBottom() {
  nextTick(() => {
    if (chatContentRef.value) {
      chatContentRef.value.scrollTop = chatContentRef.value.scrollHeight;
    }
  });
}

/**
 * 显示加载状态
 */
function showLoading() {
  isLoading.value = true;
  scrollToBottom();
}

/**
 * 隐藏加载状态
 */
function hideLoading() {
  isLoading.value = false;
}

/**
 * 初始化音频播放器
 */
function initAudioPlayer() {
  // 初始化全局音频上下文
  initGlobalAudioContext();
  console.log("音频播放器初始化完成");
}

/**
 * 清理音频播放器
 */
function cleanupAudioPlayer() {
  // 停止当前播放
  stopAudio();

  // 清理全局音频资源
  cleanupGlobalAudio();

  // 清理错误状态
  audioErrorMessages.value.clear();

  console.log("音频播放器清理完成");
}

/**
 * 清理指定消息的错误状态
 * @param messageId 消息ID
 */
function clearAudioError(messageId: string) {
  audioErrorMessages.value.delete(messageId);
}

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages.length,
  () => {
    scrollToBottom();
  },
  { immediate: true }
);

// 组件挂载时初始化
onMounted(() => {
  initAudioPlayer();
});

// 组件卸载时清理
onBeforeUnmount(() => {
  cleanupAudioPlayer();
});

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  initAudioPlayer,
  cleanupAudioPlayer,
  playAudio,
  stopAudio,
  showLoading,
  hideLoading,
  isAudioPlaying: () => playingMessage.value !== null,
  startStreamAudio,
  handleStreamAudioChunk,
  completeStreamAudio,
  forceStopStreamAudio,
  clearAudioError,
  hasAudioError,
  getAudioErrorMessage
});

/**
 * 记录音频块错误统计
 * @param error 错误对象
 * @param audioChunk 音频数据块
 */
function recordAudioChunkError(error: any, audioChunk: ArrayBuffer) {
  audioChunkErrorStats.value.errorChunks++;

  const now = Date.now();
  if (now - audioChunkErrorStats.value.lastErrorTime < 5000) {
    // 5秒内的连续错误
    audioChunkErrorStats.value.consecutiveErrors++;
  } else {
    audioChunkErrorStats.value.consecutiveErrors = 1;
  }

  audioChunkErrorStats.value.lastErrorTime = now;

  console.log("音频块错误统计:", {
    总块数: audioChunkErrorStats.value.totalChunks,
    错误块数: audioChunkErrorStats.value.errorChunks,
    错误率: `${(
      (audioChunkErrorStats.value.errorChunks /
        audioChunkErrorStats.value.totalChunks) *
      100
    ).toFixed(2)}%`,
    连续错误数: audioChunkErrorStats.value.consecutiveErrors
  });

  // 记录连续错误统计，但不停止播放，继续处理后续音频块
  if (audioChunkErrorStats.value.consecutiveErrors >= 10) {
    console.warn("连续音频块错误较多，但继续尝试处理后续音频块");
  }
}

/**
 * 获取音频块错误的用户友好信息
 * @param error 错误对象
 * @param audioChunk 音频数据块
 * @returns 错误信息
 */
function getAudioChunkErrorMessage(
  error: any,
  audioChunk: ArrayBuffer
): string {
  if (audioChunk.byteLength === 0) {
    return "接收到空的音频数据块";
  } else if (audioChunk.byteLength < 100) {
    return "音频数据块过小，可能不完整";
  } else if (error.name === "EncodingError") {
    return "音频格式不支持或数据损坏";
  } else {
    return "音频数据块处理失败";
  }
}

/**
 * 重置错误统计（在开始新的流式播放时调用）
 */
function resetAudioChunkErrorStats() {
  audioChunkErrorStats.value = {
    totalChunks: 0,
    errorChunks: 0,
    lastErrorTime: 0,
    consecutiveErrors: 0
  };
  console.log("音频块错误统计已重置");
}

/**
 * 分析音频数据块格式
 * @param audioChunk 音频数据块
 * @returns 分析结果
 */
function analyzeAudioChunk(audioChunk: ArrayBuffer): any {
  const view = new DataView(audioChunk);
  const analysis = {
    byteLength: audioChunk.byteLength,
    firstBytes: [] as number[],
    isWAV: false,
    isMP3: false,
    isOGG: false,
    isEmpty: audioChunk.byteLength === 0
  };

  // 读取前16个字节用于格式检测
  for (let i = 0; i < Math.min(16, audioChunk.byteLength); i++) {
    analysis.firstBytes.push(view.getUint8(i));
  }

  // 检查是否为WAV格式 (RIFF...WAVE)
  if (audioChunk.byteLength >= 12) {
    const riff = String.fromCharCode(
      view.getUint8(0),
      view.getUint8(1),
      view.getUint8(2),
      view.getUint8(3)
    );
    const wave = String.fromCharCode(
      view.getUint8(8),
      view.getUint8(9),
      view.getUint8(10),
      view.getUint8(11)
    );
    analysis.isWAV = riff === "RIFF" && wave === "WAVE";
  }

  // 检查是否为MP3格式
  if (audioChunk.byteLength >= 3) {
    const mp3Header =
      view.getUint8(0) === 0xff && (view.getUint8(1) & 0xe0) === 0xe0;
    analysis.isMP3 = mp3Header;
  }

  // 检查是否为OGG格式
  if (audioChunk.byteLength >= 4) {
    const ogg = String.fromCharCode(
      view.getUint8(0),
      view.getUint8(1),
      view.getUint8(2),
      view.getUint8(3)
    );
    analysis.isOGG = ogg === "OggS";
  }

  return analysis;
}

/**
 * 分析音频解码错误
 * @param error 错误对象
 * @param audioChunk 音频数据块
 * @returns 错误分析结果
 */
function analyzeDecodingError(error: any, audioChunk: ArrayBuffer): any {
  return {
    errorType: error.constructor.name,
    errorName: error.name,
    errorMessage: error.message,
    audioSize: audioChunk.byteLength,
    audioAnalysis: analyzeAudioChunk(audioChunk),
    possibleCauses: [
      audioChunk.byteLength === 0 ? "空数据" : null,
      audioChunk.byteLength < 100 ? "数据过小" : null,
      error.name === "EncodingError" ? "格式不支持" : null,
      "网络传输损坏",
      "服务端编码问题"
    ].filter(Boolean)
  };
}

/**
 * 清理和修复音频数据（原始版本）
 * @param audioChunk 原始音频数据
 * @returns Promise<ArrayBuffer> 修复后的音频数据
 */
async function sanitizeAudioData(
  audioChunk: ArrayBuffer
): Promise<ArrayBuffer> {
  console.log("尝试修复音频数据");

  // 检查数据是否为空或过小
  if (audioChunk.byteLength < 44) {
    throw new Error("音频数据过小，无法修复");
  }

  // 创建数据视图
  const view = new DataView(audioChunk);
  const uint8View = new Uint8Array(audioChunk);

  // 检查WAV文件头
  const riff = String.fromCharCode(
    view.getUint8(0),
    view.getUint8(1),
    view.getUint8(2),
    view.getUint8(3)
  );

  if (riff === "RIFF") {
    // 这是一个WAV文件，检查文件大小字段
    const fileSize = view.getUint32(4, true);
    const actualSize = audioChunk.byteLength - 8;

    if (fileSize !== actualSize) {
      console.log(`修复WAV文件大小字段: ${fileSize} -> ${actualSize}`);

      // 创建修复后的数据
      const fixedBuffer = new ArrayBuffer(audioChunk.byteLength);
      const fixedView = new DataView(fixedBuffer);
      const fixedUint8 = new Uint8Array(fixedBuffer);

      // 复制原始数据
      fixedUint8.set(uint8View);

      // 修复文件大小字段
      fixedView.setUint32(4, actualSize, true);

      return fixedBuffer;
    }
  }

  // 如果不需要修复或不是WAV格式，返回原始数据的副本
  return audioChunk.slice(0);
}
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite ease-in-out both;
}
</style>
