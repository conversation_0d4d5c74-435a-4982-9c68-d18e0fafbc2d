<!--
 * @Date         : 2025-05-23 10:20:00
 * @Description  : 课程练习 - 录音控制区域 - 优化版本（支持空格键提示和取消录音）
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <div class="p-15px border-t border-gray-200 input-area">
    <!-- 录音区域 -->
    <div class="flex justify-between items-center py-10px">
      <!-- 左侧放弃练习按钮 -->
      <div class="flex-1 flex justify-start">
        <el-button
          type="danger"
          plain
          @click="$emit('abandon')"
          :disabled="!canAbandonPractice"
          :title="getAbandonButtonTooltip()"
        >
          放弃练习
        </el-button>
      </div>

      <!-- 中间录音按钮区域 -->
      <div class="flex-1 flex justify-center relative">
        <div class="flex items-center gap-10px">
          <!-- 主录音按钮 -->
          <el-button
            :type="getButtonType()"
            circle
            :icon="getButtonIcon()"
            @click="toggleRecording"
            :disabled="isButtonDisabled"
            size="large"
            :class="getButtonClass()"
            :title="getButtonTooltip()"
          />

          <!-- 取消录音按钮 - 仅在录音时显示 -->
          <el-button
            v-if="isRecording"
            type="warning"
            plain
            size="small"
            @click="cancelRecording"
            :title="'取消本次录音'"
            class="cancel-recording-btn"
          >
            取消
          </el-button>
        </div>

        <!-- 状态提示文字 -->
        <div v-if="statusText" class="status-text">
          {{ statusText }}
        </div>
      </div>

      <!-- 右侧完成练习按钮 -->
      <div class="flex-1 flex justify-end">
        <el-button
          type="primary"
          :loading="finishLoading"
          :disabled="!canFinishPractice"
          @click="handleFinishPractice"
          :title="getFinishButtonTooltip()"
        >
          完成练习
        </el-button>
      </div>
    </div>

    <!-- 空格键录音提示 -->
    <div class="space-key-hint">
      <div
        class="flex items-center justify-center gap-8px text-gray-500 text-12px"
      >
        <el-icon size="14"><Microphone /></el-icon>
        <span>长按空格键录音，松开发送</span>
        <div class="space-key-icon">Space</div>
        <span class="mx-8px">|</span>
        <span>按Esc键取消录音</span>
        <div class="space-key-icon">Esc</div>
      </div>
    </div>

    <!-- 录音中状态 - 浮动显示 -->
    <div v-if="isRecording" class="recording-bubble">
      <div class="recording-content">
        <div class="text-center text-white text-18px mb-20px">
          {{ recordingText }}
        </div>

        <div class="recording-wave mb-20px">
          <span v-for="i in 9" :key="i" class="wave-bar" />
        </div>

        <div class="recording-time text-center text-white mb-10px">
          {{ formatRecordingTime(recordingTime) }}
        </div>

        <div class="text-center text-white text-12px opacity-80">
          点击按钮停止录音或点击取消按钮取消录音
        </div>
      </div>
      <!-- 气泡尖角 -->
      <div class="bubble-arrow" />
    </div>

    <!-- AI响应中状态 - 浮动显示 -->
    <div v-if="isAiResponding" class="ai-thinking-bubble">
      <div class="ai-thinking-content">
        <div class="text-center text-white text-16px mb-15px">
          AI正在思考中...
        </div>

        <div class="thinking-dots mb-15px">
          <span v-for="i in 3" :key="i" class="thinking-dot" />
        </div>

        <div class="text-center text-white text-12px opacity-80">
          请稍候，不要录音
        </div>
      </div>
      <!-- 气泡尖角 -->
      <div class="bubble-arrow-ai" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import { Microphone, Close, Loading } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import {
  finishWorkerTrainingTaskCourse,
  WorkerTrainingTaskCourseUpdateRequest,
  WorkerTrainingTaskCourseInstance
} from "/@/api/AIQualityInspection/taskManagement";
import { useUserStoreHook } from "/@/store/modules/user";

// 定义props
const props = defineProps<{
  isRecording: boolean;
  recordingTime: number;
  recordingText: string;
  isAudioPlaying: boolean;
  isAiResponding?: boolean; // AI是否正在响应
  isTtsWaiting?: boolean; // 新增：TTS是否正在等待返回
  isWaitingForCompletion?: boolean; // 新增：是否处于智能等待阶段（等待AI响应和TTS完成）
  courseInfo: WorkerTrainingTaskCourseInstance; // 课程信息
  evaluationLevel?: "A" | "B" | "C" | "D"; // 评估等级
  /** 对话消息列表 */
  messages?: Array<{
    content: string;
    isBot: boolean;
    index: string;
    startTime?: number;
  }>;
  /** 练习开始时间（秒级时间戳） */
  practiceStartTime?: number;
  /** 练习结束时间（秒级时间戳） */
  practiceEndTime?: number;
  /** 练习持续时长（秒） */
  practiceDuration?: number;
}>();

// 定义事件
const emit = defineEmits([
  "start-recording",
  "stop-recording",
  "cancel-recording", // 新增：取消录音事件
  "abandon",
  "finish"
]);

// 状态变量
const finishLoading = ref(false);

/**
 * 计算按钮是否禁用
 */
const isButtonDisabled = computed(() => {
  return props.isAudioPlaying || props.isAiResponding || props.isTtsWaiting;
});

/**
 * 计算是否可以放弃练习
 * @description 在智能等待阶段不允许放弃练习
 */
const canAbandonPractice = computed(() => {
  // 检查是否处于智能等待阶段
  const isWaiting = props.isWaitingForCompletion || false;

  // 不处于智能等待阶段时才能放弃练习
  return !isWaiting;
});

/**
 * 计算是否可以完成练习
 * @description 只有当存在对话消息且不处于智能等待阶段时才允许完成练习
 */
const canFinishPractice = computed(() => {
  const messages = props.messages || [];
  // 检查是否有用户发送的消息（非机器人消息）
  const userMessages = messages.filter(msg => !msg.isBot);
  const hasUserMessages = userMessages.length > 0;

  // 检查是否处于智能等待阶段
  const isWaiting = props.isWaitingForCompletion || false;

  // 只有有用户消息且不处于智能等待阶段时才能完成练习
  return hasUserMessages && !isWaiting;
});

/**
 * 获取按钮类型
 */
function getButtonType() {
  if (props.isRecording) return "danger";
  if (props.isAiResponding || props.isTtsWaiting) return "info";
  return "primary";
}

/**
 * 获取按钮图标
 */
function getButtonIcon() {
  if (props.isRecording) return Close;
  if (props.isAiResponding || props.isTtsWaiting) return Loading;
  return Microphone;
}

/**
 * 获取按钮样式类
 */
function getButtonClass() {
  const baseClass = "recording-btn-center recording-btn-large";
  if (props.isRecording) return `${baseClass} recording-btn`;
  if (props.isAiResponding || props.isTtsWaiting)
    return `${baseClass} ai-thinking-btn`;
  return baseClass;
}

/**
 * 获取按钮提示文字
 */
function getButtonTooltip() {
  if (props.isRecording) return "点击停止录音";
  if (props.isAudioPlaying) return "请等待语音播放完成";
  if (props.isTtsWaiting) return "TTS正在生成中，请稍候";
  if (props.isAiResponding) return "AI正在思考中，请稍候";
  return "点击开始录音";
}

/**
 * 获取状态提示文字
 */
const statusText = computed(() => {
  if (props.isAudioPlaying) return "语音播放中...";
  if (props.isTtsWaiting) return "TTS生成中...";
  if (props.isAiResponding) return "AI思考中...";
  return "";
});

/**
 * 格式化录音时间
 * @param seconds 秒数
 * @returns 格式化的时间字符串
 */
function formatRecordingTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
    .toString()
    .padStart(2, "0")}`;
}

/**
 * 切换录音状态
 */
function toggleRecording() {
  if (isButtonDisabled.value) return;

  if (props.isRecording) {
    emit("stop-recording");
  } else {
    emit("start-recording");
  }
}

/**
 * 取消录音
 */
function cancelRecording() {
  console.log("用户点击取消录音");
  emit("cancel-recording");
}

/**
 * 完成练习 - 根据新的接口要求构建请求参数
 * @description 移除aiAppraise参数，使用秒级时间戳，传递实际对话消息和持续时长
 */
async function handleFinishPractice() {
  // 检查是否可以完成练习
  if (!canFinishPractice.value) {
    ElMessage.warning("请先进行对话后再完成练习");
    return;
  }

  try {
    finishLoading.value = true;

    // 获取用户信息
    const userStore = useUserStoreHook();
    const userMsg = userStore.userMsg;

    // 获取当前时间戳（秒级）
    const currentTimestamp = Math.floor(Date.now() / 1000);

    // 计算练习开始和结束时间
    const practiceStartTime =
      props.practiceStartTime ||
      currentTimestamp - (props.practiceDuration || 0);
    const practiceEndTime = props.practiceEndTime || currentTimestamp;
    const practiceDuration =
      props.practiceDuration || practiceEndTime - practiceStartTime;

    // 转换消息格式，使用正确的时间戳
    const conversationMessages = (props.messages || []).map(msg => ({
      role: msg.isBot ? "bot" : "user", // 角色：机器人或用户
      content: msg.content, // 消息内容
      start: (msg.startTime || practiceStartTime).toString(), // 消息开始时间：用户消息为录音开始时间，机器人消息为TTS开始时间
      key: msg.index // 消息唯一标识
    }));

    // 构建请求参数 - 根据新的接口结构
    const requestData: WorkerTrainingTaskCourseUpdateRequest = {
      workerTrainingTaskId: props.courseInfo.workerTrainingTaskId || "0",
      trainingTaskCourseId: props.courseInfo.trainingTaskCourseId || "0",
      conversation: {
        workerId: userMsg.id || 0, // 工作人员ID
        trainingTaskCourseId: props.courseInfo.trainingTaskCourseId || "0", // 训练任务课程ID
        duration: practiceDuration, // 实际练习时长（秒）
        messages: conversationMessages, // 实际对话消息（不包含createdAt）
        begin: practiceStartTime.toString(), // 开始时间（秒级时间戳）
        end: practiceEndTime.toString() // 结束时间（秒级时间戳）
        // 移除 aiAppraise 参数
      }
    };

    console.log("完成练习请求参数:", requestData);

    // 调用完成练习接口
    await finishWorkerTrainingTaskCourse(requestData);

    ElMessage.success("练习完成成功！");

    // 通知父组件练习完成，传递课程信息以便打开评估抽屉
    emit("finish", {
      requestData,
      courseInfo: props.courseInfo,
      shouldOpenEvaluation: true // 标识需要打开评估抽屉
    });
  } catch (error) {
    console.error("完成练习失败:", error);
    ElMessage.error("完成练习失败，请重试");
  } finally {
    finishLoading.value = false;
  }
}

/**
 * 获取完成练习按钮的提示文字
 */
function getFinishButtonTooltip() {
  const messages = props.messages || [];
  const userMessages = messages.filter(msg => !msg.isBot);

  // 检查是否有用户消息
  if (userMessages.length === 0) {
    return "请先进行对话后再完成练习";
  }

  // 检查是否处于智能等待阶段
  if (props.isWaitingForCompletion) {
    return "正在等待AI回复完成，请稍候...";
  }

  return "完成练习";
}

/**
 * 获取放弃练习按钮的提示文字
 */
function getAbandonButtonTooltip() {
  // 检查是否处于智能等待阶段
  if (props.isWaitingForCompletion) {
    return "正在等待AI回复完成，请稍候...";
  }

  return "放弃练习";
}
</script>

<style scoped>
/* 输入区域 */
.input-area {
  position: relative;
}

/* 取消录音按钮样式 */
.cancel-recording-btn {
  height: 32px !important;
  padding: 0 12px !important;
  font-size: 12px !important;
  border-radius: 16px !important;
  transition: all 0.3s ease;
}

.cancel-recording-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

/* 空格键录音提示 */
.space-key-hint {
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 10px;
}

.space-key-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 11px;
  font-weight: 500;
  color: #666;
  min-width: 40px;
  height: 20px;
}

/* 录音按钮居中 */
.recording-btn-center {
  position: relative;
}

/* 放大录音按钮 */
.recording-btn-large {
  width: 60px !important;
  height: 60px !important;
  font-size: 24px !important;
  transition: all 0.3s ease;
}

.recording-btn-large :deep(.el-icon) {
  font-size: 32px;
  width: 32px;
  height: 32px;
}

/* 状态提示文字 */
.status-text {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

/* 录音气泡 */
.recording-bubble {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  width: 100%;
  max-width: 320px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 25px;
}

.recording-content {
  width: 100%;
  background-color: #1677ff;
  border-radius: 12px;
  padding: 25px 20px;
  box-shadow: 0 8px 24px rgba(22, 119, 255, 0.3);
  position: relative;
}

/* AI思考气泡 */
.ai-thinking-bubble {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  width: 100%;
  max-width: 280px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 25px;
}

.ai-thinking-content {
  width: 100%;
  background-color: #52c41a;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(82, 196, 26, 0.3);
  position: relative;
}

/* 气泡尖角 */
.bubble-arrow {
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-top: 12px solid #1677ff;
  margin-top: -1px;
}

.bubble-arrow-ai {
  width: 0;
  height: 0;
  border-left: 12px solid transparent;
  border-right: 12px solid transparent;
  border-top: 12px solid #52c41a;
  margin-top: -1px;
}

/* 录音波形动画 */
.recording-wave {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3px;
  height: 30px;
}

.wave-bar {
  width: 3px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: wave 1.2s ease-in-out infinite;
}

.wave-bar:nth-child(1) {
  animation-delay: 0s;
}
.wave-bar:nth-child(2) {
  animation-delay: 0.1s;
}
.wave-bar:nth-child(3) {
  animation-delay: 0.2s;
}
.wave-bar:nth-child(4) {
  animation-delay: 0.3s;
}
.wave-bar:nth-child(5) {
  animation-delay: 0.4s;
}
.wave-bar:nth-child(6) {
  animation-delay: 0.3s;
}
.wave-bar:nth-child(7) {
  animation-delay: 0.2s;
}
.wave-bar:nth-child(8) {
  animation-delay: 0.1s;
}
.wave-bar:nth-child(9) {
  animation-delay: 0s;
}

@keyframes wave {
  0%,
  100% {
    height: 8px;
  }
  50% {
    height: 24px;
  }
}

/* 思考点动画 */
.thinking-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.thinking-dot {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: thinking 1.4s ease-in-out infinite;
}

.thinking-dot:nth-child(1) {
  animation-delay: 0s;
}
.thinking-dot:nth-child(2) {
  animation-delay: 0.2s;
}
.thinking-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes thinking {
  0%,
  80%,
  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 录音按钮动画 */
.recording-btn {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 77, 79, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 77, 79, 0);
  }
}

/* AI思考按钮动画 */
.ai-thinking-btn :deep(.el-icon) {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 禁用状态样式 */
.recording-btn-large:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
