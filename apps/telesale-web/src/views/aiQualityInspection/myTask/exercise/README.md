# AI质检练习组件 (Exercise Component)

> 一个功能完整的AI质检练习系统，支持语音识别、实时对话、违禁词检测等核心功能

## 📋 目录

- [业务概述](#-业务概述)
- [技术架构](#️-技术架构)
- [文件结构](#-文件结构)
- [核心功能](#-核心功能)
- [业务流程](#-业务流程)
- [技术实现](#️-技术实现)
- [使用指南](#-使用指南)
    - [使用流程图](#使用流程图)
    - [操作说明](#操作说明)
- [API接口](#-api接口)

## 🎯 业务概述

### 业务背景

AI质检练习组件是电销质检系统的核心模块，为销售人员提供模拟练习环境。通过与AI客户进行实时对话练习，帮助销售人员提升沟通技巧和业务能力。

### 主要功能

- **实时语音对话**: 支持语音识别和TTS语音合成
- **AI智能回复**: 基于角色设定的智能对话系统
- **违禁词检测**: 实时检测敏感词汇并提醒
- **练习计时**: 支持设定练习时长和超时提醒
- **多模式支持**: 语音+文本模式、纯文本模式
- **练习记录**: 完整的对话记录和练习结果

### 应用场景

- 新员工入职培训
- 销售技能提升练习
- 话术标准化训练
- 质检标准学习

## 🏗️ 技术架构

### 架构设计原则

- **组件化**: 功能模块化，职责清晰
- **状态统一**: 使用VueUse创建全局状态管理
- **Hook复用**: 业务逻辑抽象为可复用的Hook
- **类型安全**: 完整的TypeScript类型定义

### 技术栈

- **前端框架**: Vue 3 + TypeScript
- **状态管理**: VueUse createGlobalState
- **UI组件**: Element Plus
- **样式方案**: UnoCSS
- **时间处理**: Day.js
- **音频处理**: Web Audio API
- **实时通信**: WebSocket (语音识别)

## 📁 文件结构

```text
exercise/
├── index.vue                    # 主入口组件 (447行)
├── store.ts                     # 统一状态管理 (1170行)
├── components/                  # UI组件层
│   ├── InfoPanel.vue           # 课程信息面板
│   ├── ChatArea.vue            # 对话区域组件
│   ├── RecordingControl.vue    # 录音控制组件
│   └── ForbiddenWordMessage.vue # 违禁词提示组件
├── hooks/                      # 业务逻辑层
│   ├── index.ts                # Hook统一导出
│   ├── useAudioRecorder.ts     # 音频录音Hook (450行)
│   ├── useTimer.ts             # 计时器Hook (189行)
│   ├── useKeyboardRecording.ts # 键盘录音Hook (180行)
│   ├── useChat.ts              # 聊天功能Hook (720行)
│   ├── useExercise.ts          # 练习状态Hook (242行)
│   └── useForbiddenWordCheck.ts # 违禁词检测Hook (424行)
├── utils/                      # 工具函数层
│   ├── audioPlayer.ts          # 音频播放工具 (350行)
│   └── forbiddenWordMessage.ts # 违禁词提示工具 (170行)
└── README.md                   # 组件文档
```

## ⚡ 核心功能

### 1. 语音识别与录音

- **WebSocket实时识别**: 基于WebSocket的实时语音转文字
- **多种录音方式**: 支持按钮录音、空格键录音
- **录音状态管理**: 开始、暂停、停止、取消录音
- **音频流处理**: 实时音频数据采集和传输

### 2. AI智能对话

- **流式响应**: 支持AI回复的流式输出
- **角色扮演**: 基于课程设定的AI角色对话
- **上下文管理**: 维护完整的对话上下文
- **TTS语音合成**: AI回复的语音播放

### 3. 违禁词检测

- **实时检测**: 用户发言的实时违禁词检测
- **自定义提示**: 不响应ESC键的自定义提示组件
- **异步处理**: 不阻塞主流程的异步检测
- **EventStream接口**: 基于流式接口的检测结果

### 4. 练习管理

- **计时功能**: 支持设定最大练习时长
- **状态控制**: 开始、暂停、停止、完成练习
- **结果保存**: 练习完成后的结果提交
- **进度跟踪**: 实时显示练习进度

### 5. 音频播放

- **Web Audio API**: 基于现代Web Audio API的音频处理
- **流式播放**: 支持音频流的实时播放
- **播放控制**: 播放、暂停、停止音频
- **错误处理**: 完善的音频播放错误处理机制

## 🔄 业务流程

### 练习完整流程

1. **初始化阶段**
   - 加载课程信息和AI角色设定
   - 初始化各个Hook实例
   - 设置回调函数和事件监听

2. **练习进行阶段**
   - 用户通过按钮或空格键开始录音
   - WebSocket实时语音识别
   - 违禁词异步检测
   - AI流式回复和TTS播放

3. **练习结束阶段**
   - 用户主动完成或时间到自动完成
   - 保存练习结果到服务器
   - 清理资源和重置状态

### 状态流转

```text
初始状态 → 练习中 → 录音中 → AI思考中 → AI回复中 → 练习中 → 练习完成
    ↓         ↓        ↓         ↓          ↓         ↓         ↓
  加载课程   开始计时   语音识别   流式响应    TTS播放   继续对话   保存结果
```

### 错误处理流程

- **录音权限错误**: 提示用户授权麦克风权限
- **网络连接错误**: 自动重连WebSocket
- **违禁词检测**: 显示自定义提示，不阻塞流程
- **AI服务异常**: 显示错误信息，允许重试
- **音频播放错误**: 降级到文本模式

### 边界情况处理

#### ⏰ 计时器超时处理

当练习时间达到最大持续时间时，系统会智能处理各种边界情况：

1. **AI回复未完成时**
   - 等待AI流式回复完全结束
   - 不会中断正在进行的AI生成过程
   - 确保用户能看到完整的AI回复内容

2. **TTS语音未完成时**
   - 等待TTS语音生成和播放完成
   - 保证用户能听到完整的AI语音回复
   - 避免音频播放被突然中断

3. **用户正在录音时**
   - 允许用户完成当前录音
   - 等待语音识别结果返回
   - 处理完最后一轮对话后再结束

4. **违禁词检测进行中**
   - 等待违禁词检测完成
   - 如有违禁词提示，正常显示
   - 确保合规检测的完整性

#### 🔄 状态同步机制

```text
// 超时处理伪代码
当计时器达到最大时间 {
  设置超时标记 = true

  如果 (AI正在回复) {
    等待AI回复完成
  }

  如果 (TTS正在播放) {
    等待TTS播放结束
  }

  如果 (用户正在录音) {
    等待录音和识别完成
  }

  如果 (违禁词检测中) {
    等待检测结果
  }

  所有异步操作完成后 {
    自动执行完成练习()
  }
}
```

#### 🛡️ 资源清理保障

- **WebSocket连接**: 确保所有连接正确关闭
- **音频资源**: 释放麦克风和音频上下文
- **定时器**: 清理所有计时器和间隔器
- **内存管理**: 清理消息列表和状态缓存

## 🛠️ 技术实现

### 1. 状态管理架构

使用VueUse的`createGlobalState`创建全局状态管理：

```text
// 状态管理伪代码
创建全局状态 {
  课程信息: courseInfo
  练习状态: isPracticing, isRecording, isAiResponding
  消息列表: messages[]
  Hook实例: audioRecorder, timer, keyboard, chat, exercise

  方法: {
    开始录音(), 停止录音(), 发送消息(), 完成练习()
  }
}
```

### 2. WebSocket语音识别

```text
// 语音识别伪代码
开始录音() {
  1. 获取麦克风权限
  2. 建立WebSocket连接
  3. 创建音频上下文
  4. 实时采集音频数据
  5. 发送音频流到WebSocket
  6. 接收语音识别结果
}
```

### 3. AI流式对话

```text
// AI对话伪代码
发送消息(用户输入) {
  1. 设置AI响应状态
  2. 调用流式AI接口
  3. 实时接收AI回复片段
  4. 逐步更新对话内容
  5. 完成后生成TTS语音
  6. 播放AI语音回复
}
```

### 4. 违禁词检测

```text
// 违禁词检测伪代码
检测违禁词(文本内容) {
  1. 调用EventStream检测接口
  2. 流式接收检测结果
  3. 解析检测数据
  4. 如果发现违禁词 -> 显示自定义提示
  5. 异步处理，不阻塞主流程
}
```

### 5. Web Audio API音频播放

```text
// 音频播放伪代码
播放音频(音频数据) {
  1. 创建音频上下文
  2. 解码音频数据
  3. 创建音频源节点
  4. 连接到音频输出
  5. 开始播放
  6. 监听播放结束事件
}
```

### 6. 超时边界处理

```text
// 超时智能处理伪代码
计时器达到最大时间() {
  设置超时标记 = true
  显示"正在结束练习..."提示

  创建等待队列 = []

  如果 (AI正在回复) {
    等待队列.添加("AI回复完成")
  }

  如果 (TTS正在播放) {
    等待队列.添加("TTS播放完成")
  }

  如果 (用户正在录音) {
    等待队列.添加("录音识别完成")
  }

  如果 (违禁词检测中) {
    等待队列.添加("违禁词检测完成")
  }

  等待所有异步操作完成() {
    自动执行完成练习()
    保存练习结果()
    清理所有资源()
  }
}
```

## 📖 使用指南

### 使用流程图

以下流程图展示了用户使用AI质检练习组件的完整操作流程：

```mermaid
flowchart TD
    A[用户打开练习页面] --> B[选择练习课程]
    B --> C[点击开始练习]
    C --> D[系统初始化]

    D --> E[加载课程信息]
    E --> F[设置AI角色]
    F --> G[显示练习界面]
    G --> H[开始计时]

    H --> I[用户开始对话]
    I --> J{选择输入方式}

    J -->|按钮录音| K[点击录音按钮]
    J -->|空格键录音| L[按住空格键]
    J -->|文本输入| M[直接输入文字]

    K --> N[开始语音识别]
    L --> N
    N --> O[实时显示识别文字]
    O --> P[松开按钮/空格键]
    P --> Q[完成录音]

    M --> R[发送文字消息]
    Q --> R

    R --> S[违禁词检测]
    S --> T{检测结果}
    T -->|有违禁词| U[显示违禁词提示]
    T -->|无违禁词| V[发送给AI]

    U --> I
    V --> W[AI开始思考]
    W --> X[流式返回AI回复]
    X --> Y[逐步显示AI消息]
    Y --> Z[生成TTS语音]
    Z --> AA[播放AI语音]

    AA --> BB{练习是否继续}
    BB -->|继续对话| I
    BB -->|用户完成| DD[点击完成按钮]
    BB -->|用户放弃| EE[点击放弃按钮]
    BB -->|时间到| CC{检查当前状态}

    CC -->|AI回复中| CC1[等待AI回复完成]
    CC -->|TTS播放中| CC2[等待TTS播放结束]
    CC -->|用户录音中| CC3[等待录音识别完成]
    CC -->|违禁词检测中| CC4[等待检测结果]
    CC -->|无异步操作| FF[保存练习记录]

    CC1 --> CC5{TTS是否需要播放}
    CC5 -->|是| CC2
    CC5 -->|否| FF
    CC2 --> FF
    CC3 --> CC6{是否需要AI回复}
    CC6 -->|是| CC1
    CC6 -->|否| FF
    CC4 --> CC7{是否有违禁词}
    CC7 -->|有| UU[显示违禁词提示后结束]
    CC7 -->|无| FF
    UU --> FF

    DD --> FF
    EE --> GG[确认放弃]
    GG --> HH[清理资源]

    FF --> II[显示练习结果]
    II --> JJ[返回课程列表]
    HH --> JJ

    style A fill:#e3f2fd
    style JJ fill:#f3e5f5
    style U fill:#ffebee
    style UU fill:#ffebee
    style FF fill:#e8f5e8
    style CC fill:#fff3e0
    style CC1 fill:#fff8e1
    style CC2 fill:#fff8e1
    style CC3 fill:#fff8e1
    style CC4 fill:#fff8e1
    style DD fill:#e8f5e8
    style EE fill:#ffebee
```

### 操作说明

#### 🎯 练习准备

1. **选择课程**: 从课程列表中选择要练习的课程
2. **开始练习**: 点击"开始练习"按钮进入练习界面
3. **界面初始化**: 系统自动加载课程信息和AI角色设定

#### 🎤 录音操作

- **按钮录音**: 点击录音按钮开始，再次点击结束
- **空格键录音**: 按住空格键录音，松开结束（推荐方式）
- **文本输入**: 直接在输入框中输入文字

#### 🤖 AI交互

1. **发送消息**: 录音或输入完成后自动发送给AI
2. **违禁词检测**: 系统自动检测敏感词汇
3. **AI回复**: AI流式返回回复内容
4. **语音播放**: 自动播放AI的语音回复

#### ⏰ 练习结束

- **自动结束**: 达到设定时间自动结束
- **手动完成**: 点击"完成练习"按钮
- **放弃练习**: 点击"放弃练习"按钮并确认

#### 🔍 边界情况说明

**时间到达最大持续时间时的智能处理：**

1. **AI回复进行中**
   - 系统会等待AI流式回复完全结束
   - 确保用户能看到完整的AI回答
   - 如需TTS播放，会继续等待语音播放完成

2. **TTS语音播放中**
   - 等待当前语音播放完毕
   - 不会突然中断音频播放
   - 保证用户体验的连贯性

3. **用户正在录音**
   - 允许用户完成当前录音操作
   - 等待语音识别结果返回
   - 处理完最后一轮对话后再结束练习

4. **违禁词检测中**
   - 等待违禁词检测完成
   - 如检测到违禁词，正常显示提示
   - 确保合规检测的完整性

**注意事项：**

- 超时后的等待时间通常不会很长（几秒到十几秒）
- 系统会显示"正在结束练习..."的提示
- 所有异步操作完成后会自动保存练习结果

### 1. 基本使用

```vue
<template>
  <ExerciseComponent
    v-model:visible="exerciseVisible"
    @success="handleExerciseSuccess"
    @finish="handleExerciseFinish"
  />
</template>

<script setup>
// 导入组件，设置响应式状态
// 定义事件处理函数
</script>
```

### 2. 状态管理使用

```text
// 状态管理使用伪代码
导入 { useExerciseStore }
创建 store实例 = useExerciseStore()

访问状态: store.isRecording, store.messages, store.courseInfo
调用方法: store.startRecording(), store.sendMessage(), store.finishPractice()
```

### 3. Hook独立使用

```text
// Hook使用伪代码
导入 { useAudioRecorder }
创建 audioRecorder实例 = useAudioRecorder()

设置回调函数: {
  onTextRecognized: 处理识别文本
  onRecordingStateChange: 处理录音状态变化
}

调用方法: audioRecorder.startRecording()
```

## 🔌 API接口

### 1. 主要接口

| 接口名称 | 类型 | 描述 |
|---------|------|------|
| `workerTrainingTaskCourseChatStream` | POST | AI对话流式接口 |
| `finishWorkerTrainingTaskCourse` | POST | 完成练习接口 |
| `getWorkerTrainingTaskCourseInfo` | GET | 获取课程信息 |
| `forbiddenWordCheck` | POST | 违禁词检测接口 |

### 2. WebSocket接口

```text
// WebSocket连接伪代码
创建WebSocket连接 -> wss://api.example.com/asr
发送音频数据 -> ws.send(audioBuffer)
接收识别结果 -> ws.onmessage(识别文本)
```

### 3. 数据结构

```text
// 主要数据结构
课程信息: {
  trainingTaskCourseId: 课程ID
  course: { 课程名称, 最大时长, 角色信息 }
}

消息结构: {
  content: 消息内容
  isBot: 是否AI消息
  index: 消息ID
  startTime: 开始时间
}

练习结果: {
  success: 是否成功
  data: 结果数据
  message: 结果消息
}
```

## 📊 性能优化

### 1. 状态管理优化

- 使用VueUse的`createGlobalState`避免重复创建状态
- 按需更新状态，减少不必要的响应式更新
- 合理使用`computed`缓存计算结果

### 2. 音频处理优化

- 使用Web Audio API的原生缓冲区处理
- 实现音频数据的分块传输
- 添加音频解码重试机制

### 3. 网络请求优化

- WebSocket连接复用和自动重连
- 流式接口的分块处理
- 请求去重和防抖处理

## 🔧 开发调试

### 1. 调试工具

组件提供了丰富的调试功能：

```text
// 浏览器控制台调试命令
window.exerciseDebug.testRecording()     // 测试录音功能
window.exerciseDebug.testAiWaiting()     // 测试AI等待逻辑
window.exerciseDebug.testTimeoutWithAi() // 测试超时处理
window.exerciseDebug.testBoundaryCase()  // 测试边界情况处理
window.exerciseDebug.simulateTimeout()   // 模拟超时场景
```

### 2. 日志系统

所有关键操作都有详细的控制台日志：

- 🎤 录音相关日志
- 🤖 AI对话日志
- ⏰ 计时器日志（包含超时处理）
- 🚫 违禁词检测日志
- 🔊 音频播放日志
- 🔄 边界情况处理日志
- 📊 异步操作等待状态日志

### 3. 错误监控

完善的错误处理和上报机制：

- 录音权限错误
- 网络连接错误
- 音频播放错误
- API调用错误

## 📝 总结

AI质检练习组件是一个功能完整、架构清晰的复杂业务组件。通过合理的分层设计和状态管理，实现了：

- **高内聚低耦合**: 各模块职责明确，依赖关系清晰
- **可维护性强**: 代码结构清晰，易于理解和修改
- **可扩展性好**: 基于Hook的设计便于功能扩展
- **用户体验佳**: 流畅的交互和完善的错误处理
- **性能优异**: 优化的状态管理和音频处理

该组件为电销质检系统提供了强大的练习功能支持，帮助销售人员提升业务能力和沟通技巧。

---

*本文档详细介绍了AI质检练习组件的业务功能、技术架构和使用方法。如有疑问，请联系开发团队。*
