# 课程练习模块重构说明

## 📁 文件结构

```
exercise/
├── index.vue                    # 主入口组件（378行，原976行）
├── components/                  # UI组件
│   ├── InfoPanel.vue           # 信息面板组件
│   ├── ChatArea.vue            # 聊天区域组件
│   └── RecordingControl.vue    # 录音控制组件
├── hooks/                      # 业务逻辑Hooks
│   ├── index.ts                # Hooks统一导出
│   ├── useAudioRecorder.ts     # 音频录音Hook
│   ├── useTimer.ts             # 计时器Hook
│   ├── useKeyboardRecording.ts # 键盘录音Hook
│   ├── useChat.ts              # 聊天功能Hook
│   └── useExercise.ts          # 练习状态管理Hook
├── utils/                      # 工具函数
│   ├── audioPlayer.ts          # 音频播放工具
│   ├── ttsDebugger.ts          # TTS调试工具
│   └── streamTtsTest.ts        # 流式TTS测试工具
└── README.md                   # 本文档
```

## 🔧 重构内容

### 1. 组件拆分
- **AudioRecorder.vue** → **useAudioRecorder.ts** (Hook)
  - 原因：该组件没有UI，纯逻辑组件，更适合作为Hook使用
  - 优势：更好的复用性，减少组件层级

### 2. 逻辑拆分为Hooks

#### useAudioRecorder.ts
- **职责**：音频录音功能
- **功能**：
  - WebSocket连接管理
  - 音频流处理
  - 语音识别
  - 录音状态管理

#### useTimer.ts
- **职责**：练习计时功能
- **功能**：
  - 计时器管理
  - 时间格式化
  - 超时处理
  - 剩余时间计算

#### useKeyboardRecording.ts
- **职责**：键盘录音功能
- **功能**：
  - 空格键录音
  - 键盘事件监听
  - 输入框焦点检测

#### useChat.ts
- **职责**：聊天功能
- **功能**：
  - 消息管理
  - AI对话处理
  - 流式响应处理
  - TTS语音生成

#### useExercise.ts
- **职责**：练习状态管理
- **功能**：
  - 练习生命周期管理
  - 状态验证
  - 角色信息管理
  - 练习结果处理

## 📊 重构效果

### 代码量对比
- **主文件**：976行 → 378行（减少61%）
- **总代码量**：基本保持不变，但结构更清晰

### 优势
1. **可维护性**：逻辑分离，职责明确
2. **可复用性**：Hooks可在其他组件中复用
3. **可测试性**：每个Hook可独立测试
4. **可读性**：主文件逻辑简化，更易理解

### 类型安全
- 所有Hooks都有完整的TypeScript类型定义
- 接口定义清晰，减少类型错误

## 🚀 使用方式

```typescript
// 在组件中使用
import {
  useAudioRecorder,
  useTimer,
  useKeyboardRecording,
  useChat,
  useExercise
} from "./hooks";

// 初始化hooks
const exerciseState = useExercise(courseInfo);
const timerState = useTimer(exerciseState.getMaxDuration());
const audioRecorderState = useAudioRecorder();
const keyboardRecordingState = useKeyboardRecording();
const chatState = useChat();
```

## 🔄 Hook间通信

通过回调函数实现Hook间的通信：

```typescript
// 设置录音回调
audioRecorderState.setCallbacks({
  onTextRecognized: sendMessage,
  onRecordingStateChange: (state) => {
    // 处理录音状态变化
  }
});

// 设置聊天区域回调
chatState.setChatAreaCallbacks({
  onShowLoading: () => chatAreaRef.value?.showLoading(),
  onHideLoading: () => chatAreaRef.value?.hideLoading(),
  // ... 其他回调
});
```

## 📝 注意事项

1. **响应式数据访问**：在模板中使用Hook的响应式数据时需要添加`.value`
2. **生命周期管理**：每个Hook都有自己的清理逻辑，在组件卸载时会自动清理
3. **错误处理**：每个Hook都有完善的错误处理机制
4. **性能优化**：避免不必要的重复计算和资源浪费

## 🔮 未来扩展

1. **更多Hook**：可以继续拆分更细粒度的功能Hook
2. **全局状态**：可以考虑使用Pinia进行全局状态管理
3. **单元测试**：为每个Hook编写单元测试
4. **文档完善**：为每个Hook编写详细的API文档 