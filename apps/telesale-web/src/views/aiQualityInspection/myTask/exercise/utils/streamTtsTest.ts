/**
 * TTS流式播放测试工具
 * 用于测试和验证流式TTS功能
 */

import { sendTtsStream } from "/@/api/AIQualityInspection/taskManagement";

/**
 * 测试流式TTS功能
 * @param text 测试文本
 * @param role 角色
 * @returns Promise<boolean> 是否测试成功
 */
export async function testStreamTTS(
  text: string = "你好，这是一个流式TTS测试",
  role: string = "father"
): Promise<boolean> {
  try {
    console.log("=== 流式TTS测试开始 ===");
    console.log("测试参数:", { text, role });

    const msgid = crypto.randomUUID();
    const audioChunks: ArrayBuffer[] = [];
    let chunkCount = 0;

    // 记录开始时间
    const startTime = Date.now();

    const audioBuffer = await sendTtsStream(
      { text, role, msgid },
      // 音频数据块回调
      (audioChunk: ArrayBuffer) => {
        chunkCount++;
        audioChunks.push(audioChunk);

        const currentTime = Date.now();
        const elapsedTime = currentTime - startTime;

        console.log(`接收到第${chunkCount}个音频块:`, {
          chunkSize: audioChunk.byteLength,
          totalChunks: chunkCount,
          elapsedTime: `${elapsedTime}ms`,
          totalSize: audioChunks.reduce(
            (sum, chunk) => sum + chunk.byteLength,
            0
          )
        });

        // 如果是第一个音频块，记录首次响应时间
        if (chunkCount === 1) {
          console.log(`🎉 首个音频块响应时间: ${elapsedTime}ms`);
        }
      }
    );

    const totalTime = Date.now() - startTime;

    console.log("流式TTS测试结果:", {
      success: true,
      totalChunks: chunkCount,
      totalTime: `${totalTime}ms`,
      finalAudioBuffer: audioBuffer,
      isArrayBuffer: audioBuffer instanceof ArrayBuffer,
      finalSize:
        audioBuffer instanceof ArrayBuffer ? audioBuffer.byteLength : "N/A",
      chunksSize: audioChunks.reduce((sum, chunk) => sum + chunk.byteLength, 0)
    });

    // 验证数据完整性
    const chunksSize = audioChunks.reduce(
      (sum, chunk) => sum + chunk.byteLength,
      0
    );
    const finalSize =
      audioBuffer instanceof ArrayBuffer ? audioBuffer.byteLength : 0;

    if (chunksSize === finalSize) {
      console.log("✅ 数据完整性验证通过");
    } else {
      console.warn("⚠️ 数据完整性验证失败:", { chunksSize, finalSize });
    }

    return audioBuffer instanceof ArrayBuffer && audioBuffer.byteLength > 0;
  } catch (error) {
    console.error("流式TTS测试失败:", error);
    return false;
  }
}

/**
 * 测试音频播放性能
 * @param audioBuffer 音频数据
 * @returns Promise<boolean> 是否播放成功
 */
export async function testAudioPlayback(
  audioBuffer: ArrayBuffer
): Promise<boolean> {
  try {
    console.log("=== 音频播放测试开始 ===");

    // 创建AudioContext
    const audioContext = new AudioContext();

    // 解码音频数据
    const decodedAudioBuffer = await audioContext.decodeAudioData(
      audioBuffer.slice(0)
    );
    console.log("音频解码成功:", {
      duration: decodedAudioBuffer.duration,
      sampleRate: decodedAudioBuffer.sampleRate,
      numberOfChannels: decodedAudioBuffer.numberOfChannels
    });

    // 创建音频源节点
    const audioSource = audioContext.createBufferSource();
    audioSource.buffer = decodedAudioBuffer;
    audioSource.connect(audioContext.destination);

    // 播放音频
    return new Promise(resolve => {
      audioSource.onended = () => {
        console.log("✅ 音频播放完成");
        audioContext.close();
        resolve(true);
      };

      try {
        audioSource.start();
        console.log("🎵 音频开始播放");
      } catch (error) {
        console.error("❌ 音频播放失败:", error);
        audioContext.close();
        resolve(false);
      }
    });
  } catch (error) {
    console.error("音频播放测试失败:", error);
    return false;
  }
}

/**
 * 完整的流式TTS测试套件
 * @param text 测试文本
 * @param role 角色
 * @returns Promise<void>
 */
export async function runStreamTTSTestSuite(
  text: string = "这是一个完整的流式TTS测试，包含多个句子。我们将测试音频的流式接收和播放功能。",
  role: string = "father"
): Promise<void> {
  console.log("🚀 开始流式TTS完整测试套件");

  try {
    // 1. 测试流式TTS接收
    console.log("\n📡 步骤1: 测试流式TTS接收");
    const streamSuccess = await testStreamTTS(text, role);

    if (!streamSuccess) {
      console.error("❌ 流式TTS接收测试失败");
      return;
    }

    // 2. 重新获取音频数据用于播放测试
    console.log("\n🎵 步骤2: 测试音频播放");
    const msgid = crypto.randomUUID();
    const audioBuffer = await sendTtsStream({ text, role, msgid });

    if (audioBuffer instanceof ArrayBuffer && audioBuffer.byteLength > 0) {
      const playbackSuccess = await testAudioPlayback(audioBuffer);

      if (playbackSuccess) {
        console.log("\n🎉 流式TTS完整测试套件通过!");
      } else {
        console.error("\n❌ 音频播放测试失败");
      }
    } else {
      console.error("\n❌ 无效的音频数据");
    }
  } catch (error) {
    console.error("\n❌ 测试套件执行失败:", error);
  }
}

/**
 * 在控制台中运行流式TTS测试
 * 可以在浏览器控制台中调用: window.testStreamTTSDebug()
 */
export function setupStreamTTSDebugger() {
  // @ts-ignore
  window.testStreamTTSDebug = testStreamTTS;
  // @ts-ignore
  window.testAudioPlayback = testAudioPlayback;
  // @ts-ignore
  window.runStreamTTSTestSuite = runStreamTTSTestSuite;

  console.log("流式TTS调试工具已加载，可以使用以下命令:");
  console.log(
    "- window.testStreamTTSDebug('测试文本', '角色') - 测试流式TTS功能"
  );
  console.log("- window.testAudioPlayback(audioBuffer) - 测试音频播放");
  console.log(
    "- window.runStreamTTSTestSuite('测试文本', '角色') - 运行完整测试套件"
  );
}
