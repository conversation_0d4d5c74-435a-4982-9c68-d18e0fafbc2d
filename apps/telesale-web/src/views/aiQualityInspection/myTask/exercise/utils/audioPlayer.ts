/**
 * 全局音频播放器管理器
 * 参考chat.tsx的实现，确保同一时间只有一个音频在播放
 */

let globalAudioContext: AudioContext | null = null;
let currentPlayingSource: AudioBufferSourceNode | null = null;
let isGlobalPlayerActive = false;
let currentPlayCallback: (() => void) | null = null;

/**
 * 初始化全局音频上下文
 */
export function initGlobalAudioContext(): AudioContext {
  if (!globalAudioContext) {
    globalAudioContext = new AudioContext();
  }
  return globalAudioContext;
}

/**
 * 停止当前播放的音频
 */
export function playerStop(): void {
  console.log("全局播放器停止");

  if (currentPlayingSource) {
    try {
      currentPlayingSource.stop();
      currentPlayingSource.disconnect();
    } catch (error) {
      console.warn("停止音频源时出错:", error);
    }
    currentPlayingSource = null;
  }

  isGlobalPlayerActive = false;
  currentPlayCallback = null;
}

/**
 * 检查是否正在播放
 */
export function isPlaying(): boolean {
  return isGlobalPlayerActive;
}

/**
 * 播放音频缓冲区
 * @param audioBuffer 音频数据
 * @param onEnded 播放结束回调
 * @param onError 播放错误回调
 */
export function replay(
  audioBuffer: ArrayBuffer,
  onEnded?: () => void,
  onError?: (error: Error) => void
): void {
  // 停止当前播放
  playerStop();

  const audioContext = initGlobalAudioContext();

  console.log("开始解码音频数据，大小:", audioBuffer.byteLength);

  // 使用重试机制解码并播放音频
  decodeAudioDataWithRetry(audioContext, audioBuffer, 3)
    .then(decodedBuffer => {
      // 检查播放器是否已被停止（防止在解码过程中被停止）
      if (isGlobalPlayerActive) {
        console.log("播放器已被停止，取消播放");
        return;
      }

      // 设置播放状态
      isGlobalPlayerActive = true;
      currentPlayCallback = onEnded || null;

      const source = audioContext.createBufferSource();
      source.buffer = decodedBuffer;
      source.connect(audioContext.destination);

      source.onended = () => {
        console.log("全局播放器音频播放结束");
        // 重置播放状态
        isGlobalPlayerActive = false;
        currentPlayingSource = null;

        // 调用结束回调
        if (currentPlayCallback) {
          const callback = currentPlayCallback;
          currentPlayCallback = null;
          callback();
        }
      };

      currentPlayingSource = source;
      source.start();
      console.log(
        "全局播放器开始播放音频，时长:",
        decodedBuffer.duration,
        "秒"
      );
    })
    .catch(error => {
      console.error("音频解码失败:", error);
      // 确保状态被重置
      isGlobalPlayerActive = false;
      currentPlayingSource = null;
      currentPlayCallback = null;

      // 调用错误回调
      if (onError) {
        onError(error);
      } else if (onEnded) {
        // 如果没有错误回调但有结束回调，也调用结束回调
        onEnded();
      }
    });
}

/**
 * 带重试机制的音频解码函数
 * @param audioContext 音频上下文
 * @param audioBuffer 音频数据
 * @param maxRetries 最大重试次数
 * @returns Promise<AudioBuffer>
 */
async function decodeAudioDataWithRetry(
  audioContext: AudioContext,
  audioBuffer: ArrayBuffer,
  maxRetries: number = 3
): Promise<AudioBuffer> {
  let lastError: any = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`音频解码尝试 ${attempt}/${maxRetries}`);

      // 尝试不同的数据处理方式
      let dataToProcess: ArrayBuffer;

      if (attempt === 1) {
        // 第一次尝试：直接使用原始数据
        dataToProcess = audioBuffer.slice(0);
      } else if (attempt === 2) {
        // 第二次尝试：创建新的ArrayBuffer副本
        const newBuffer = new ArrayBuffer(audioBuffer.byteLength);
        const newView = new Uint8Array(newBuffer);
        const originalView = new Uint8Array(audioBuffer);
        newView.set(originalView);
        dataToProcess = newBuffer;
      } else {
        // 第三次尝试：检查并修复可能的数据问题
        dataToProcess = await sanitizeAudioData(audioBuffer);
      }

      // 执行解码
      const decodedBuffer = await audioContext.decodeAudioData(dataToProcess);

      if (attempt > 1) {
        console.log(`音频解码在第 ${attempt} 次尝试时成功`);
      }

      return decodedBuffer;
    } catch (error) {
      lastError = error;
      console.warn(`音频解码第 ${attempt} 次尝试失败:`, error);

      // 如果不是最后一次尝试，等待一小段时间再重试
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 50 * attempt));
      }
    }
  }

  // 所有重试都失败，抛出最后一个错误
  throw lastError;
}

/**
 * 清理和修复音频数据
 * @param audioBuffer 原始音频数据
 * @returns Promise<ArrayBuffer> 修复后的音频数据
 */
async function sanitizeAudioData(
  audioBuffer: ArrayBuffer
): Promise<ArrayBuffer> {
  console.log("尝试修复音频数据");

  // 检查数据是否为空或过小
  if (audioBuffer.byteLength < 44) {
    throw new Error("音频数据过小，无法修复");
  }

  // 创建数据视图
  const view = new DataView(audioBuffer);
  const uint8View = new Uint8Array(audioBuffer);

  // 检查WAV文件头
  const riff = String.fromCharCode(
    view.getUint8(0),
    view.getUint8(1),
    view.getUint8(2),
    view.getUint8(3)
  );

  if (riff === "RIFF") {
    // 这是一个WAV文件，检查文件大小字段
    const fileSize = view.getUint32(4, true);
    const actualSize = audioBuffer.byteLength - 8;

    if (fileSize !== actualSize) {
      console.log(`修复WAV文件大小字段: ${fileSize} -> ${actualSize}`);

      // 创建修复后的数据
      const fixedBuffer = new ArrayBuffer(audioBuffer.byteLength);
      const fixedView = new DataView(fixedBuffer);
      const fixedUint8 = new Uint8Array(fixedBuffer);

      // 复制原始数据
      fixedUint8.set(uint8View);

      // 修复文件大小字段
      fixedView.setUint32(4, actualSize, true);

      return fixedBuffer;
    }
  }

  // 如果不需要修复或不是WAV格式，返回原始数据的副本
  return audioBuffer.slice(0);
}

/**
 * 流式音频播放器
 * 参考chat.tsx中的player函数实现
 */
export function player(
  audioContext: AudioContext,
  playBuffer: AudioBuffer[],
  index: number
): void {
  // 检查播放器状态
  if (!isGlobalPlayerActive) {
    console.log("播放器已停止，终止流式播放");
    return;
  }

  if (index >= playBuffer.length) {
    console.log("流式播放完成，重置全局播放状态");
    isGlobalPlayerActive = false;
    currentPlayingSource = null;
    if (currentPlayCallback) {
      const callback = currentPlayCallback;
      currentPlayCallback = null;
      callback();
    }
    return;
  }

  try {
    const source = audioContext.createBufferSource();
    source.buffer = playBuffer[index];
    source.connect(audioContext.destination);

    source.onended = () => {
      console.log(`流式音频块 ${index} 播放完成`);
      // 检查是否还应该继续播放
      if (isGlobalPlayerActive && index + 1 < playBuffer.length) {
        // 播放下一个音频块
        setTimeout(() => {
          player(audioContext, playBuffer, index + 1);
        }, 50); // 50ms延迟确保连续播放
      } else if (index + 1 >= playBuffer.length) {
        // 所有音频块播放完成
        console.log("所有流式音频块播放完成");
        isGlobalPlayerActive = false;
        currentPlayingSource = null;
        if (currentPlayCallback) {
          const callback = currentPlayCallback;
          currentPlayCallback = null;
          callback();
        }
      }
    };

    currentPlayingSource = source;
    source.start();
    console.log(
      `开始播放流式音频块 ${index}，时长:`,
      playBuffer[index].duration,
      "秒"
    );
  } catch (error) {
    console.error(`播放流式音频块 ${index} 失败:`, error);
    // 出错时重置状态
    isGlobalPlayerActive = false;
    currentPlayingSource = null;
    if (currentPlayCallback) {
      const callback = currentPlayCallback;
      currentPlayCallback = null;
      callback();
    }
  }
}

/**
 * 开始流式播放
 * @param onEnded 播放结束回调
 */
export function startStreamPlayer(onEnded?: () => void): void {
  // 停止当前播放
  playerStop();

  isGlobalPlayerActive = true;
  currentPlayCallback = onEnded || null;
  console.log("开始流式播放器");
}

/**
 * 清理全局音频资源
 */
export function cleanupGlobalAudio(): void {
  playerStop();

  if (globalAudioContext) {
    globalAudioContext.close();
    globalAudioContext = null;
  }

  console.log("全局音频资源清理完成");
}
