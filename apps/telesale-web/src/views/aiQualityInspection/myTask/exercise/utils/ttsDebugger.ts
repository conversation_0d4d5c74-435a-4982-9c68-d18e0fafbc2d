/**
 * TTS调试工具
 * 用于测试和诊断TTS功能问题
 */

import { sendTts } from "/@/api/AIQualityInspection/taskManagement";

/**
 * 测试TTS功能
 * @param text 测试文本
 * @param role 角色
 * @returns Promise<boolean> 是否测试成功
 */
export async function testTTS(
  text: string = "你好，这是一个测试",
  role: string = "father"
): Promise<boolean> {
  try {
    console.log("=== TTS测试开始 ===");
    console.log("测试参数:", { text, role });

    const msgid = crypto.randomUUID();
    const audioBuffer = await sendTts({ text, role, msgid });

    console.log("TTS测试结果:", {
      success: true,
      audioBuffer,
      isArrayBuffer: audioBuffer instanceof ArrayBuffer,
      byteLength:
        audioBuffer instanceof ArrayBuffer ? audioBuffer.byteLength : "N/A"
    });

    if (audioBuffer instanceof ArrayBuffer && audioBuffer.byteLength > 0) {
      // 尝试播放音频
      const blob = new Blob([audioBuffer], { type: "audio/wav" });
      const audioUrl = URL.createObjectURL(blob);
      const audio = new Audio(audioUrl);

      return new Promise(resolve => {
        audio.oncanplay = () => {
          console.log("音频可以播放，时长:", audio.duration);
          audio
            .play()
            .then(() => {
              console.log("音频播放成功");
              resolve(true);
            })
            .catch(error => {
              console.error("音频播放失败:", error);
              resolve(false);
            });
        };

        audio.onerror = error => {
          console.error("音频加载失败:", error);
          resolve(false);
        };

        // 5秒超时
        setTimeout(() => {
          console.warn("音频测试超时");
          resolve(false);
        }, 5000);
      });
    } else {
      console.error("TTS返回无效的音频数据");
      return false;
    }
  } catch (error) {
    console.error("TTS测试失败:", error);
    return false;
  }
}

/**
 * 检查音频格式兼容性
 * @param audioBuffer 音频数据
 * @returns 兼容的音频格式列表
 */
export function checkAudioFormats(audioBuffer: ArrayBuffer): string[] {
  const supportedFormats: string[] = [];
  const audio = new Audio();

  const formats = [
    { type: "audio/wav", ext: "wav" },
    { type: "audio/mpeg", ext: "mp3" },
    { type: "audio/mp4", ext: "mp4" },
    { type: "audio/ogg", ext: "ogg" },
    { type: "audio/webm", ext: "webm" }
  ];

  formats.forEach(format => {
    if (audio.canPlayType(format.type)) {
      supportedFormats.push(format.type);
    }
  });

  console.log("浏览器支持的音频格式:", supportedFormats);
  return supportedFormats;
}

/**
 * 分析音频数据
 * @param audioBuffer 音频数据
 * @returns 音频数据分析结果
 */
export function analyzeAudioData(audioBuffer: ArrayBuffer): any {
  const view = new DataView(audioBuffer);
  const analysis = {
    byteLength: audioBuffer.byteLength,
    firstBytes: [],
    lastBytes: [],
    isWAV: false,
    isMP3: false,
    isEmpty: audioBuffer.byteLength === 0
  };

  // 读取前16个字节
  for (let i = 0; i < Math.min(16, audioBuffer.byteLength); i++) {
    analysis.firstBytes.push(view.getUint8(i));
  }

  // 读取后16个字节
  for (
    let i = Math.max(0, audioBuffer.byteLength - 16);
    i < audioBuffer.byteLength;
    i++
  ) {
    analysis.lastBytes.push(view.getUint8(i));
  }

  // 检查是否为WAV格式 (RIFF...WAVE)
  if (audioBuffer.byteLength >= 12) {
    const riff = String.fromCharCode(
      view.getUint8(0),
      view.getUint8(1),
      view.getUint8(2),
      view.getUint8(3)
    );
    const wave = String.fromCharCode(
      view.getUint8(8),
      view.getUint8(9),
      view.getUint8(10),
      view.getUint8(11)
    );
    analysis.isWAV = riff === "RIFF" && wave === "WAVE";
  }

  // 检查是否为MP3格式
  if (audioBuffer.byteLength >= 3) {
    const mp3Header =
      view.getUint8(0) === 0xff && (view.getUint8(1) & 0xe0) === 0xe0;
    analysis.isMP3 = mp3Header;
  }

  console.log("音频数据分析:", analysis);
  return analysis;
}

/**
 * 在控制台中运行TTS测试
 * 可以在浏览器控制台中调用: window.testTTSDebug()
 */
export function setupTTSDebugger() {
  // @ts-ignore
  window.testTTSDebug = testTTS;
  // @ts-ignore
  window.checkAudioFormats = checkAudioFormats;
  // @ts-ignore
  window.analyzeAudioData = analyzeAudioData;

  console.log("TTS调试工具已加载，可以使用以下命令:");
  console.log("- window.testTTSDebug('测试文本', '角色') - 测试TTS功能");
  console.log("- window.checkAudioFormats() - 检查音频格式支持");
  console.log("- window.analyzeAudioData(audioBuffer) - 分析音频数据");
}
