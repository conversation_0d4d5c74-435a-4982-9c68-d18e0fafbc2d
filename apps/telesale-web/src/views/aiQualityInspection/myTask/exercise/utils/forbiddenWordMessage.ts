/**
 * 自定义违禁词提示工具
 * 仿照Element Plus Message API，但不响应ESC键
 */

import { createApp, App } from "vue";
import { ElMessage } from "element-plus";
import ForbiddenWordMessage from "../components/ForbiddenWordMessage.vue";

export interface ForbiddenWordMessageOptions {
  id?: string;
  message?: string;
  type?: "success" | "warning" | "info" | "error";
  iconComponent?: any;
  dangerouslyUseHTMLString?: boolean;
  customClass?: string;
  duration?: number;
  center?: boolean;
  onClose?: () => void;
  offset?: number;
  zIndex?: number;
}

export interface ForbiddenWordMessageHandler {
  close: () => void;
}

// 存储当前显示的消息实例
let currentMessageInstance: {
  app: App;
  container: HTMLElement;
  close: () => void;
} | null = null;

// 原始的 ElMessage 方法
let originalElMessage: any = null;
let isElMessageIntercepted = false;

/**
 * 拦截 Element Plus Message，为其添加额外的 offset
 */
function interceptElMessage() {
  if (isElMessageIntercepted || typeof window === "undefined") {
    return;
  }

  // 保存原始的 ElMessage 方法
  originalElMessage = {
    main: ElMessage,
    success: ElMessage.success,
    warning: ElMessage.warning,
    info: ElMessage.info,
    error: ElMessage.error
  };
  isElMessageIntercepted = true;

  // 拦截各种 message 方法
  const methods = ["success", "warning", "info", "error"];

  methods.forEach(method => {
    const originalMethod = originalElMessage[method];
    ElMessage[method] = function (options: any) {
      // 如果有违禁词提示正在显示，增加 offset
      if (currentMessageInstance) {
        if (typeof options === "string") {
          options = { message: options };
        }
        // 增加 offset，避免与违禁词提示重叠
        options.offset = (options.offset || 20) + 60; // 违禁词提示高度约40px + 20px间距
        console.log(`拦截到 ElMessage.${method}，调整 offset:`, options.offset);
      }
      return originalMethod.call(this, options);
    };
  });

  console.log("ElMessage 拦截已启用");
}

/**
 * 恢复 Element Plus Message 的原始行为
 */
function restoreElMessage() {
  if (!isElMessageIntercepted || !originalElMessage) {
    return;
  }

  // 恢复原始方法
  ElMessage.success = originalElMessage.success;
  ElMessage.warning = originalElMessage.warning;
  ElMessage.info = originalElMessage.info;
  ElMessage.error = originalElMessage.error;

  isElMessageIntercepted = false;
  originalElMessage = null;

  console.log("ElMessage 拦截已恢复");
}

/**
 * 显示违禁词提示
 * @param options 配置选项
 * @returns 消息处理器
 */
export function showForbiddenWordMessage(
  options: ForbiddenWordMessageOptions | string = {}
): ForbiddenWordMessageHandler {
  // 如果已经有消息在显示，先关闭它
  if (currentMessageInstance) {
    currentMessageInstance.close();
  }

  // 启用 ElMessage 拦截
  interceptElMessage();

  // 处理参数
  const opts: ForbiddenWordMessageOptions =
    typeof options === "string" ? { message: options } : options;

  // 设置默认值
  const finalOptions: ForbiddenWordMessageOptions = {
    message: "监测到当前发言涉及侮辱、政治等敏感信息，请回归业务场景沟通~",
    type: "error",
    duration: 0, // 不自动关闭
    center: false,
    offset: 20, // 使用默认offset，显示在顶部中央
    zIndex: 1000001, // 设置比Element Plus message更高的z-index确保在最顶层（Element Plus error message z-index: 1000000）
    ...opts
  };

  return createForbiddenWordMessage(finalOptions);
}

/**
 * 创建违禁词提示组件的内部方法
 */
function createForbiddenWordMessage(
  finalOptions: ForbiddenWordMessageOptions
): ForbiddenWordMessageHandler {
  // 创建容器元素
  const container = document.createElement("div");
  container.className = "forbidden-word-message-container";

  // 创建Vue应用实例
  const app = createApp(ForbiddenWordMessage, {
    ...finalOptions,
    onClose: () => {
      // 清理当前实例
      if (currentMessageInstance) {
        currentMessageInstance = null;
      }
      // 调用用户传入的onClose回调
      if (finalOptions.onClose) {
        finalOptions.onClose();
      }
    }
  });

  // 挂载到容器
  const vm = app.mount(container);

  // 将容器添加到body
  document.body.appendChild(container);

  // 创建关闭函数
  const close = () => {
    if (vm && typeof (vm as any).close === "function") {
      (vm as any).close();
    }

    // 延迟清理DOM，等待动画完成
    setTimeout(() => {
      try {
        if (container && container.parentNode) {
          container.parentNode.removeChild(container);
        }
        app.unmount();
      } catch (error) {
        console.warn("清理违禁词提示DOM失败:", error);
      }
    }, 400); // 等待动画完成

    // 清理当前实例引用
    if (currentMessageInstance) {
      currentMessageInstance = null;
    }

    // 恢复 ElMessage 的原始行为
    restoreElMessage();
  };

  // 保存当前实例
  currentMessageInstance = {
    app,
    container,
    close
  };

  // 返回处理器
  return {
    close
  };
}

/**
 * 关闭当前显示的违禁词提示
 */
export function closeForbiddenWordMessage(): void {
  if (currentMessageInstance) {
    currentMessageInstance.close();
  }
  // 确保恢复 ElMessage 的原始行为
  restoreElMessage();
}

/**
 * 检查是否有违禁词提示正在显示
 */
export function isForbiddenWordMessageShowing(): boolean {
  return currentMessageInstance !== null;
}

/**
 * 错误类型的违禁词提示（快捷方法）
 */
export function showForbiddenWordError(
  message?: string,
  options?: Omit<ForbiddenWordMessageOptions, "message" | "type">
): ForbiddenWordMessageHandler {
  return showForbiddenWordMessage({
    message:
      message || "监测到当前发言涉及侮辱、政治等敏感信息，请回归业务场景沟通~",
    type: "error",
    ...options
  });
}

/**
 * 警告类型的违禁词提示（快捷方法）
 */
export function showForbiddenWordWarning(
  message?: string,
  options?: Omit<ForbiddenWordMessageOptions, "message" | "type">
): ForbiddenWordMessageHandler {
  return showForbiddenWordMessage({
    message:
      message || "监测到当前发言涉及侮辱、政治等敏感信息，请回归业务场景沟通~",
    type: "warning",
    ...options
  });
}

// 默认导出
export default {
  show: showForbiddenWordMessage,
  close: closeForbiddenWordMessage,
  isShowing: isForbiddenWordMessageShowing,
  error: showForbiddenWordError,
  warning: showForbiddenWordWarning
};
