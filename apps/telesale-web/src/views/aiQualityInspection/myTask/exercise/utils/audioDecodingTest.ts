/**
 * 音频解码测试工具
 * 用于验证TTS流式播放解码错误修复效果
 */

/**
 * 测试音频解码重试机制
 * @param audioData 音频数据
 * @returns 测试结果
 */
export async function testAudioDecodingRetry(audioData: ArrayBuffer): Promise<{
  success: boolean;
  attempts: number;
  error?: any;
  duration?: number;
}> {
  const startTime = performance.now();
  let attempts = 0;
  let lastError: any = null;

  const audioContext = new AudioContext();

  try {
    for (let attempt = 1; attempt <= 3; attempt++) {
      attempts = attempt;
      try {
        console.log(`🧪 测试解码尝试 ${attempt}/3`);

        let dataToProcess: ArrayBuffer;

        if (attempt === 1) {
          // 第一次尝试：直接使用原始数据
          dataToProcess = audioData.slice(0);
        } else if (attempt === 2) {
          // 第二次尝试：创建新的ArrayBuffer副本
          const newBuffer = new ArrayBuffer(audioData.byteLength);
          const newView = new Uint8Array(newBuffer);
          const originalView = new Uint8Array(audioData);
          newView.set(originalView);
          dataToProcess = newBuffer;
        } else {
          // 第三次尝试：检查并修复可能的数据问题
          dataToProcess = await sanitizeAudioDataForTest(audioData);
        }

        // 执行解码
        const audioBuffer = await audioContext.decodeAudioData(dataToProcess);

        const endTime = performance.now();
        const duration = endTime - startTime;

        console.log(
          `✅ 音频解码成功！尝试次数: ${attempt}, 耗时: ${duration.toFixed(
            2
          )}ms`
        );

        await audioContext.close();

        return {
          success: true,
          attempts: attempt,
          duration
        };
      } catch (error) {
        lastError = error;
        console.warn(`❌ 第 ${attempt} 次尝试失败:`, error);

        // 如果不是最后一次尝试，等待一小段时间再重试
        if (attempt < 3) {
          await new Promise(resolve => setTimeout(resolve, 50 * attempt));
        }
      }
    }

    // 所有重试都失败
    await audioContext.close();

    return {
      success: false,
      attempts,
      error: lastError
    };
  } catch (error) {
    await audioContext.close();

    return {
      success: false,
      attempts,
      error
    };
  }
}

/**
 * 测试用的音频数据修复函数
 * @param audioData 原始音频数据
 * @returns 修复后的音频数据
 */
async function sanitizeAudioDataForTest(
  audioData: ArrayBuffer
): Promise<ArrayBuffer> {
  console.log("🔧 尝试修复音频数据");

  // 检查数据是否为空或过小
  if (audioData.byteLength < 44) {
    throw new Error("音频数据过小，无法修复");
  }

  // 创建数据视图
  const view = new DataView(audioData);
  const uint8View = new Uint8Array(audioData);

  // 检查WAV文件头
  const riff = String.fromCharCode(
    view.getUint8(0),
    view.getUint8(1),
    view.getUint8(2),
    view.getUint8(3)
  );

  if (riff === "RIFF") {
    // 这是一个WAV文件，检查文件大小字段
    const fileSize = view.getUint32(4, true);
    const actualSize = audioData.byteLength - 8;

    if (fileSize !== actualSize) {
      console.log(`🔧 修复WAV文件大小字段: ${fileSize} -> ${actualSize}`);

      // 创建修复后的数据
      const fixedBuffer = new ArrayBuffer(audioData.byteLength);
      const fixedView = new DataView(fixedBuffer);
      const fixedUint8 = new Uint8Array(fixedBuffer);

      // 复制原始数据
      fixedUint8.set(uint8View);

      // 修复文件大小字段
      fixedView.setUint32(4, actualSize, true);

      return fixedBuffer;
    }
  }

  // 如果不需要修复或不是WAV格式，返回原始数据的副本
  return audioData.slice(0);
}

/**
 * 批量测试音频解码
 * @param audioDataList 音频数据列表
 * @returns 批量测试结果
 */
export async function batchTestAudioDecoding(
  audioDataList: ArrayBuffer[]
): Promise<{
  totalTests: number;
  successCount: number;
  failureCount: number;
  successRate: number;
  averageAttempts: number;
  averageDuration: number;
  results: any[];
}> {
  console.log(`🧪 开始批量测试音频解码，共 ${audioDataList.length} 个音频文件`);

  const results = [];
  let successCount = 0;
  let totalAttempts = 0;
  let totalDuration = 0;

  for (let i = 0; i < audioDataList.length; i++) {
    console.log(`\n📁 测试音频文件 ${i + 1}/${audioDataList.length}`);

    const result = await testAudioDecodingRetry(audioDataList[i]);
    results.push({
      index: i,
      ...result
    });

    if (result.success) {
      successCount++;
      totalDuration += result.duration || 0;
    }

    totalAttempts += result.attempts;

    // 避免过快的连续测试
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  const successRate = (successCount / audioDataList.length) * 100;
  const averageAttempts = totalAttempts / audioDataList.length;
  const averageDuration = successCount > 0 ? totalDuration / successCount : 0;

  console.log(`\n📊 批量测试结果:`);
  console.log(`总测试数: ${audioDataList.length}`);
  console.log(`成功数: ${successCount}`);
  console.log(`失败数: ${audioDataList.length - successCount}`);
  console.log(`成功率: ${successRate.toFixed(2)}%`);
  console.log(`平均尝试次数: ${averageAttempts.toFixed(2)}`);
  console.log(`平均解码时间: ${averageDuration.toFixed(2)}ms`);

  return {
    totalTests: audioDataList.length,
    successCount,
    failureCount: audioDataList.length - successCount,
    successRate,
    averageAttempts,
    averageDuration,
    results
  };
}

/**
 * 分析音频数据格式
 * @param audioData 音频数据
 * @returns 格式分析结果
 */
export function analyzeAudioFormat(audioData: ArrayBuffer): {
  size: number;
  format: string;
  isValid: boolean;
  details: any;
} {
  const view = new DataView(audioData);
  const analysis = {
    size: audioData.byteLength,
    format: "unknown",
    isValid: false,
    details: {
      firstBytes: [] as number[],
      header: "",
      subformat: ""
    }
  };

  // 读取前16个字节
  for (let i = 0; i < Math.min(16, audioData.byteLength); i++) {
    analysis.details.firstBytes.push(view.getUint8(i));
  }

  if (audioData.byteLength >= 4) {
    analysis.details.header = String.fromCharCode(
      view.getUint8(0),
      view.getUint8(1),
      view.getUint8(2),
      view.getUint8(3)
    );
  }

  // WAV格式检测
  if (audioData.byteLength >= 12) {
    const riff = String.fromCharCode(
      view.getUint8(0),
      view.getUint8(1),
      view.getUint8(2),
      view.getUint8(3)
    );
    const wave = String.fromCharCode(
      view.getUint8(8),
      view.getUint8(9),
      view.getUint8(10),
      view.getUint8(11)
    );

    if (riff === "RIFF" && wave === "WAVE") {
      analysis.format = "WAV";
      analysis.isValid = true;
      analysis.details.subformat = "RIFF-WAVE";
    }
  }

  // MP3格式检测
  if (audioData.byteLength >= 3) {
    const mp3Header =
      view.getUint8(0) === 0xff && (view.getUint8(1) & 0xe0) === 0xe0;
    if (mp3Header) {
      analysis.format = "MP3";
      analysis.isValid = true;
      analysis.details.subformat = "MPEG";
    }
  }

  return analysis;
}

/**
 * 在开发环境下设置音频解码测试工具
 */
export function setupAudioDecodingTestTool() {
  if (import.meta.env.DEV) {
    // 将测试函数挂载到全局对象上，方便在控制台调用
    (window as any).audioDecodingTest = {
      testSingle: testAudioDecodingRetry,
      testBatch: batchTestAudioDecoding,
      analyzeFormat: analyzeAudioFormat
    };

    console.log("🧪 音频解码测试工具已加载");
    console.log("使用方法:");
    console.log(
      "- window.audioDecodingTest.testSingle(audioData) - 测试单个音频"
    );
    console.log(
      "- window.audioDecodingTest.testBatch([audioData1, audioData2]) - 批量测试"
    );
    console.log(
      "- window.audioDecodingTest.analyzeFormat(audioData) - 分析音频格式"
    );
  }
}
