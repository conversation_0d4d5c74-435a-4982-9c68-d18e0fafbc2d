/**
 * 音频调试工具
 * 用于诊断和解决音频播放问题
 */

import {
  sendTts,
  sendTtsStream
} from "/@/api/AIQualityInspection/taskManagement";

/**
 * 音频格式检测器
 */
export class AudioFormatDetector {
  /**
   * 检测音频数据格式
   * @param audioBuffer 音频数据
   * @returns 格式检测结果
   */
  static detect(audioBuffer: ArrayBuffer): {
    format: string;
    isValid: boolean;
    details: any;
  } {
    const view = new DataView(audioBuffer);
    const result = {
      format: "unknown",
      isValid: false,
      details: {
        size: audioBuffer.byteLength,
        firstBytes: [] as number[],
        header: "",
        subformat: ""
      }
    };

    // 读取前16个字节
    for (let i = 0; i < Math.min(16, audioBuffer.byteLength); i++) {
      result.details.firstBytes.push(view.getUint8(i));
    }

    if (audioBuffer.byteLength >= 4) {
      result.details.header = String.fromCharCode(
        view.getUint8(0),
        view.getUint8(1),
        view.getUint8(2),
        view.getUint8(3)
      );
    }

    // WAV格式检测
    if (audioBuffer.byteLength >= 12) {
      const riff = String.fromCharCode(
        view.getUint8(0),
        view.getUint8(1),
        view.getUint8(2),
        view.getUint8(3)
      );
      const wave = String.fromCharCode(
        view.getUint8(8),
        view.getUint8(9),
        view.getUint8(10),
        view.getUint8(11)
      );

      if (riff === "RIFF" && wave === "WAVE") {
        result.format = "WAV";
        result.isValid = true;
        result.details.subformat = "RIFF-WAVE";
      }
    }

    // MP3格式检测
    if (audioBuffer.byteLength >= 3) {
      const mp3Header =
        view.getUint8(0) === 0xff && (view.getUint8(1) & 0xe0) === 0xe0;
      if (mp3Header) {
        result.format = "MP3";
        result.isValid = true;
        result.details.subformat = "MPEG";
      }
    }

    // OGG格式检测
    if (audioBuffer.byteLength >= 4) {
      const ogg = String.fromCharCode(
        view.getUint8(0),
        view.getUint8(1),
        view.getUint8(2),
        view.getUint8(3)
      );
      if (ogg === "OggS") {
        result.format = "OGG";
        result.isValid = true;
        result.details.subformat = "Ogg Vorbis";
      }
    }

    return result;
  }
}

/**
 * 音频解码测试器
 */
export class AudioDecodingTester {
  /**
   * 测试音频解码能力
   * @param audioBuffer 音频数据
   * @returns 解码测试结果
   */
  static async testDecoding(audioBuffer: ArrayBuffer): Promise<{
    success: boolean;
    error?: any;
    audioBuffer?: AudioBuffer;
    details: any;
  }> {
    const result: {
      success: boolean;
      error?: any;
      audioBuffer?: AudioBuffer;
      details: any;
    } = {
      success: false,
      details: {
        inputSize: audioBuffer.byteLength,
        format: AudioFormatDetector.detect(audioBuffer),
        decodingTime: 0,
        outputDetails: null as any
      }
    };

    try {
      const startTime = performance.now();
      const audioContext = new AudioContext();

      const decodedBuffer = await audioContext.decodeAudioData(
        audioBuffer.slice(0)
      );

      const endTime = performance.now();
      result.details.decodingTime = endTime - startTime;

      result.success = true;
      result.audioBuffer = decodedBuffer;
      result.details.outputDetails = {
        duration: decodedBuffer.duration,
        sampleRate: decodedBuffer.sampleRate,
        numberOfChannels: decodedBuffer.numberOfChannels,
        length: decodedBuffer.length
      };

      await audioContext.close();
    } catch (error) {
      result.success = false;
      result.error = {
        name: error.name,
        message: error.message,
        type: error.constructor.name
      };
    }

    return result;
  }
}

/**
 * TTS接口测试器
 */
export class TTSInterfaceTester {
  /**
   * 测试传统TTS接口
   * @param text 测试文本
   * @param role 角色
   * @returns 测试结果
   */
  static async testTraditionalTTS(
    text: string = "你好，这是一个测试",
    role: string = "father"
  ): Promise<{
    success: boolean;
    audioBuffer?: ArrayBuffer;
    error?: any;
    details: any;
  }> {
    const result: {
      success: boolean;
      audioBuffer?: ArrayBuffer;
      error?: any;
      details: any;
    } = {
      success: false,
      details: {
        requestTime: 0,
        responseSize: 0,
        format: null as any,
        decodingTest: null as any
      }
    };

    try {
      console.log("🧪 开始测试传统TTS接口");
      const startTime = performance.now();

      const msgid = crypto.randomUUID();
      const audioBuffer = await sendTts({ text, role, msgid });

      const endTime = performance.now();
      result.details.requestTime = endTime - startTime;

      if (audioBuffer instanceof ArrayBuffer) {
        result.success = true;
        result.audioBuffer = audioBuffer;
        result.details.responseSize = audioBuffer.byteLength;
        result.details.format = AudioFormatDetector.detect(audioBuffer);
        result.details.decodingTest = await AudioDecodingTester.testDecoding(
          audioBuffer
        );

        console.log("✅ 传统TTS测试成功:", result.details);
      } else {
        result.success = false;
        result.error = { message: "返回的不是ArrayBuffer" };
      }
    } catch (error) {
      result.success = false;
      result.error = {
        name: error.name,
        message: error.message,
        type: error.constructor.name
      };
      console.error("❌ 传统TTS测试失败:", error);
    }

    return result;
  }

  /**
   * 测试流式TTS接口
   * @param text 测试文本
   * @param role 角色
   * @returns 测试结果
   */
  static async testStreamTTS(
    text: string = "你好，这是一个流式TTS测试",
    role: string = "father"
  ): Promise<{
    success: boolean;
    chunks: any[];
    finalBuffer?: ArrayBuffer;
    error?: any;
    details: any;
  }> {
    const result: {
      success: boolean;
      chunks: any[];
      finalBuffer?: ArrayBuffer;
      error?: any;
      details: any;
    } = {
      success: false,
      chunks: [] as any[],
      details: {
        requestTime: 0,
        totalChunks: 0,
        totalSize: 0,
        firstChunkTime: 0,
        chunkAnalysis: [] as any[],
        finalFormat: null as any,
        finalDecodingTest: null as any
      }
    };

    try {
      console.log("🧪 开始测试流式TTS接口");
      const startTime = performance.now();
      let firstChunkReceived = false;

      const msgid = crypto.randomUUID();

      const finalBuffer = await sendTtsStream(
        { text, role, msgid },
        (audioChunk: ArrayBuffer) => {
          const chunkTime = performance.now();

          if (!firstChunkReceived) {
            result.details.firstChunkTime = chunkTime - startTime;
            firstChunkReceived = true;
          }

          const chunkAnalysis = {
            index: result.chunks.length,
            size: audioChunk.byteLength,
            format: AudioFormatDetector.detect(audioChunk),
            timestamp: chunkTime - startTime
          };

          result.chunks.push({
            buffer: audioChunk,
            analysis: chunkAnalysis
          });

          result.details.chunkAnalysis.push(chunkAnalysis);
          result.details.totalSize += audioChunk.byteLength;

          console.log(
            `📦 接收到第${result.chunks.length}个音频块:`,
            chunkAnalysis
          );
        }
      );

      const endTime = performance.now();
      result.details.requestTime = endTime - startTime;
      result.details.totalChunks = result.chunks.length;

      if (finalBuffer instanceof ArrayBuffer) {
        result.success = true;
        result.finalBuffer = finalBuffer;
        result.details.finalFormat = AudioFormatDetector.detect(finalBuffer);
        result.details.finalDecodingTest =
          await AudioDecodingTester.testDecoding(finalBuffer);

        console.log("✅ 流式TTS测试成功:", result.details);
      } else {
        result.success = false;
        result.error = { message: "最终返回的不是ArrayBuffer" };
      }
    } catch (error) {
      result.success = false;
      result.error = {
        name: error.name,
        message: error.message,
        type: error.constructor.name
      };
      console.error("❌ 流式TTS测试失败:", error);
    }

    return result;
  }
}

/**
 * 综合音频诊断器
 */
export class AudioDiagnostics {
  /**
   * 运行完整的音频诊断
   * @param text 测试文本
   * @param role 角色
   * @returns 诊断结果
   */
  static async runFullDiagnostics(
    text: string = "这是一个完整的音频诊断测试，包含多种场景的验证。",
    role: string = "father"
  ): Promise<{
    traditionalTTS: any;
    streamTTS: any;
    recommendations: string[];
    summary: any;
  }> {
    console.log("🔍 开始完整音频诊断...");

    const traditionalResult = await TTSInterfaceTester.testTraditionalTTS(
      text,
      role
    );
    const streamResult = await TTSInterfaceTester.testStreamTTS(text, role);

    const recommendations: string[] = [];

    // 分析传统TTS结果
    if (!traditionalResult.success) {
      recommendations.push("传统TTS接口存在问题，请检查服务端配置");
    } else if (!traditionalResult.details.decodingTest?.success) {
      recommendations.push("传统TTS返回的音频格式无法解码，请检查音频编码设置");
    }

    // 分析流式TTS结果
    if (!streamResult.success) {
      recommendations.push("流式TTS接口存在问题，请检查服务端流式响应实现");
    } else {
      const failedChunks = streamResult.details.chunkAnalysis.filter(
        chunk => !chunk.format.isValid
      );

      if (failedChunks.length > 0) {
        recommendations.push(
          `${failedChunks.length}个音频块格式无效，请检查流式音频编码`
        );
      }

      if (!streamResult.details.finalDecodingTest?.success) {
        recommendations.push(
          "流式TTS最终合并的音频无法解码，请检查音频拼接逻辑"
        );
      }
    }

    // 浏览器兼容性检查
    if (typeof AudioContext === "undefined") {
      recommendations.push("浏览器不支持Web Audio API，请升级浏览器");
    }

    const summary = {
      traditionalTTSWorking: traditionalResult.success,
      streamTTSWorking: streamResult.success,
      audioDecodingWorking:
        traditionalResult.details.decodingTest?.success ||
        streamResult.details.finalDecodingTest?.success,
      totalIssues: recommendations.length,
      overallHealth:
        recommendations.length === 0
          ? "良好"
          : recommendations.length <= 2
          ? "一般"
          : "严重"
    };

    console.log("🎯 音频诊断完成:", summary);

    return {
      traditionalTTS: traditionalResult,
      streamTTS: streamResult,
      recommendations,
      summary
    };
  }
}

/**
 * 设置全局调试工具
 */
export function setupAudioDebugger(): void {
  if (typeof window !== "undefined") {
    // 将调试工具挂载到全局对象
    (window as any).audioDebugger = {
      formatDetector: AudioFormatDetector,
      decodingTester: AudioDecodingTester,
      ttsInterfaceTester: TTSInterfaceTester,
      diagnostics: AudioDiagnostics,

      // 快捷方法
      async testAll() {
        return await AudioDiagnostics.runFullDiagnostics();
      },

      async testTraditionalTTS() {
        return await TTSInterfaceTester.testTraditionalTTS();
      },

      async testStreamTTS() {
        return await TTSInterfaceTester.testStreamTTS();
      },

      detectFormat(audioBuffer: ArrayBuffer) {
        return AudioFormatDetector.detect(audioBuffer);
      },

      async testDecoding(audioBuffer: ArrayBuffer) {
        return await AudioDecodingTester.testDecoding(audioBuffer);
      }
    };

    console.log("🛠️ 音频调试工具已加载到 window.audioDebugger");
    console.log("使用方法:");
    console.log("- window.audioDebugger.testAll() - 运行完整诊断");
    console.log("- window.audioDebugger.testTraditionalTTS() - 测试传统TTS");
    console.log("- window.audioDebugger.testStreamTTS() - 测试流式TTS");
    console.log("- window.audioDebugger.detectFormat(buffer) - 检测音频格式");
    console.log("- window.audioDebugger.testDecoding(buffer) - 测试音频解码");
  }
}
