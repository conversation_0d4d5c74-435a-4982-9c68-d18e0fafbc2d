<!--
 * @Date         : 2025-01-27 16:30:00
 * @Description  : 课程练习抽屉 - 重构版本（使用hooks）
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-drawer
    v-model="drawerVisible"
    title="课程练习"
    :close-on-press-escape="false"
    :show-close="false"
    :close-on-click-modal="false"
    size="90%"
    destroy-on-close
    direction="rtl"
    :before-close="handleClose"
    @open="handleDrawerOpen"
  >
    <div class="exercise-drawer" v-loading="exerciseStateRef?.loading?.value">
      <!-- 课程信息为空时的提示 -->
      <div
        v-if="!courseInfoRef"
        class="flex items-center justify-center h-full"
      >
        <el-empty description="课程信息加载中..." />
      </div>

      <!-- 练习内容区域 -->
      <div v-else class="flex gap-20px h-full relative">
        <!-- 左侧信息区域 -->
        <InfoPanel :course-info="courseInfoRef" />

        <!-- 右侧对话区域 -->
        <div
          class="flex-1 flex flex-col bg-white rounded-8px shadow-sm overflow-hidden"
        >
          <!-- 对话区域顶部 - 计时器 -->
          <div class="p-15px border-b border-gray-200 flex justify-end">
            <div
              class="timer-box flex items-center gap-10px bg-blue-50 px-15px py-5px rounded-20px"
            >
              <el-icon><Timer /></el-icon>
              <span>{{ timerStateRef?.formattedTime() || "00:00:00" }}</span>
            </div>
          </div>

          <!-- 对话内容区域 -->
          <ChatArea
            ref="chatAreaRef"
            :messages="chatState.messages.value"
            :role-name="roleName"
            :audio-data="chatState.audioData.value"
            :is-tts-waiting="chatState.isTtsWaiting.value"
            :is-message-stream-playing="chatState.isMessageStreamPlaying"
            :should-disable-play-button="chatState.shouldDisablePlayButton"
            :complete-stream-audio-playback="
              chatState.completeStreamAudioPlayback
            "
            @audio-play-state-change="
              exerciseStateRef?.handleAudioPlayStateChange
            "
          />

          <!-- 录音控制区域 -->
          <RecordingControl
            :is-recording="audioRecorderState.isRecording.value"
            :recording-time="audioRecorderState.recordingTime.value"
            :recording-text="audioRecorderState.recordingText.value"
            :is-audio-playing="exerciseStateRef?.isAudioPlaying.value || false"
            :is-ai-responding="chatState.isAiResponding.value"
            :is-tts-waiting="chatState.isTtsWaiting.value"
            :course-info="courseInfoRef"
            :evaluation-level="'B'"
            :messages="chatState.messages.value"
            :practice-start-time="practiceStartTime"
            :practice-duration="timerStateRef?.timer.value || 0"
            :is-waiting-for-completion="isWaitingForCompletion"
            @start-recording="handleStartRecording"
            @stop-recording="audioRecorderState.stopRecording"
            @cancel-recording="audioRecorderState.cancelRecording"
            @abandon="handleAbandonPractice"
            @finish="handleFinishPractice"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import {
  ref,
  defineModel,
  onMounted,
  onBeforeUnmount,
  withDefaults,
  nextTick,
  toRef,
  watch,
  computed
} from "vue";
import { Timer } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import {
  WorkerTrainingTaskCourseInstance,
  WorkerTrainingTaskCourseUpdateRequest,
  finishWorkerTrainingTaskCourse
} from "/@/api/AIQualityInspection/taskManagement";
import { useUserStoreHook } from "/@/store/modules/user";

// 导入拆分的组件
import InfoPanel from "./components/InfoPanel.vue";
import ChatArea from "./components/ChatArea.vue";
import RecordingControl from "./components/RecordingControl.vue";

// 导入hooks
import {
  useAudioRecorder,
  useTimer,
  useKeyboardRecording,
  useChat,
  useExercise
} from "./hooks";

// 开发环境下引入TTS调试工具
if (import.meta.env.DEV) {
  import("./utils/ttsDebugger").then(({ setupTTSDebugger }) => {
    setupTTSDebugger();
  });

  // 引入流式TTS测试工具
  import("./utils/streamTtsTest").then(({ setupStreamTTSDebugger }) => {
    setupStreamTTSDebugger();
  });

  // 引入音频调试工具
  import("./utils/audioDebugger").then(({ setupAudioDebugger }) => {
    setupAudioDebugger();
  });

  // 引入音频解码测试工具
  import("./utils/audioDecodingTest").then(({ setupAudioDecodingTestTool }) => {
    setupAudioDecodingTestTool();
  });
}

// 使用defineModel实现双向绑定
const drawerVisible = defineModel<boolean>("visible", { default: false });

// 定义props
const props = withDefaults(
  defineProps<{
    courseInfo?: WorkerTrainingTaskCourseInstance;
  }>(),
  {
    courseInfo: undefined
  }
);

// 定义事件
const emit = defineEmits(["success", "finish"]);

// 组件引用
const chatAreaRef = ref<InstanceType<typeof ChatArea> | null>(null);

// 练习开始时间（秒级时间戳）
const practiceStartTime = ref<number>(0);

// 智能等待状态（等待AI响应和TTS完成）
const isWaitingForCompletion = ref<boolean>(false);

// 将 courseInfo 转换为响应式引用
const courseInfoRef = toRef(props, "courseInfo");

// 使用响应式状态管理hooks
let exerciseState: any = null;
let timerState: any = null;

// 创建响应式引用以便模板访问
const exerciseStateRef = ref<any>(null);
const timerStateRef = ref<any>(null);

const audioRecorderState = useAudioRecorder();
const keyboardRecordingState = useKeyboardRecording();
const chatState = useChat();

/**
 * 计算角色名称 - 用于调试和确保正确传递
 */
const roleName = computed(() => {
  const roleInfo = exerciseStateRef.value?.roleInfo?.value;
  const courseBotName = courseInfoRef.value?.course?.course?.botName;

  console.log("角色名称计算:", {
    hasExerciseStateRef: !!exerciseStateRef.value,
    hasRoleInfo: !!roleInfo,
    roleInfoRoleName: roleInfo?.roleName,
    courseBotName: courseBotName,
    finalRoleName: roleInfo?.roleName || courseBotName || "客户"
  });

  return roleInfo?.roleName || courseBotName || "客户";
});

/**
 * 初始化或重新初始化练习相关状态
 * @param courseInfo 课程信息
 */
function initializeExerciseStates(
  courseInfo: WorkerTrainingTaskCourseInstance | undefined
) {
  console.log("初始化练习状态", {
    hasCourseInfo: !!courseInfo,
    courseId: courseInfo?.trainingTaskCourseId,
    maxDuration: courseInfo?.course?.course?.maxDuration
  });

  // 清理旧的状态
  if (timerState) {
    timerState.stopTimer();
  }

  // 重置状态
  exerciseState = null;
  timerState = null;
  exerciseStateRef.value = null;
  timerStateRef.value = null;

  // 如果有课程信息，创建新的状态
  if (courseInfo) {
    try {
      exerciseState = useExercise(courseInfoRef);
      const maxDuration = exerciseState.getMaxDuration();
      timerState = useTimer(maxDuration);

      // 设置计时器回调 - 确保计时器到达上限时执行自动完成逻辑
      timerState.setTimeUpCallback(handleAutoFinishPractice);

      // 验证回调是否设置成功
      console.log("计时器回调设置验证", {
        hasCallback: typeof timerState.setTimeUpCallback === "function",
        maxDurationSeconds: timerState.maxDurationSeconds,
        callbackFunction: handleAutoFinishPractice.name
      });

      // 更新响应式引用
      exerciseStateRef.value = exerciseState;
      timerStateRef.value = timerState;

      // 添加角色信息调试
      console.log("练习状态初始化完成 - 角色信息调试:", {
        maxDuration,
        hasExerciseState: !!exerciseState,
        hasTimerState: !!timerState,
        hasTimeUpCallback: true,
        roleInfo: exerciseState.roleInfo?.value,
        courseBotName: courseInfo?.course?.course?.botName,
        courseBotRole: courseInfo?.course?.course?.botRole
      });
    } catch (error) {
      console.error("初始化练习状态失败:", error);
      exerciseState = null;
      timerState = null;
      exerciseStateRef.value = null;
      timerStateRef.value = null;
    }
  }
}

// 初始化状态
initializeExerciseStates(courseInfoRef.value);

// 监听 courseInfo 变化，重新初始化相关状态
watch(
  courseInfoRef,
  (newCourseInfo, oldCourseInfo) => {
    console.log("courseInfo 发生变化:", {
      oldCourseId: oldCourseInfo?.trainingTaskCourseId,
      newCourseId: newCourseInfo?.trainingTaskCourseId,
      oldMaxDuration: oldCourseInfo?.course?.course?.maxDuration,
      newMaxDuration: newCourseInfo?.course?.course?.maxDuration,
      hasChange:
        oldCourseInfo?.trainingTaskCourseId !==
        newCourseInfo?.trainingTaskCourseId
    });

    // 如果课程ID发生变化，清理当前状态并重新初始化
    if (
      oldCourseInfo?.trainingTaskCourseId !==
      newCourseInfo?.trainingTaskCourseId
    ) {
      console.log("课程ID发生变化，清理状态并重新初始化");

      // 清理聊天数据（包括TTS状态）
      chatState.clearChat();

      // 清理录音状态
      audioRecorderState.cleanup();

      // 清理音频播放资源
      if (chatAreaRef.value) {
        chatAreaRef.value.cleanupAudioPlayer();
      }

      // 重置练习开始时间
      practiceStartTime.value = 0;

      // 重新初始化练习状态（包括计时器）
      initializeExerciseStates(newCourseInfo);

      console.log("课程切换状态清理和重新初始化完成");
    }
  },
  { deep: true }
);

// 监听角色名称变化
watch(
  roleName,
  (newRoleName, oldRoleName) => {
    console.log("角色名称发生变化:", {
      oldRoleName,
      newRoleName,
      timestamp: new Date().toISOString()
    });
  },
  { immediate: true }
);

/**
 * 处理开始录音
 */
function handleStartRecording() {
  if (!exerciseState) return;

  if (
    !exerciseState.canStartRecording(
      audioRecorderState.isRecording.value,
      chatState.isAiResponding.value,
      chatState.isTtsWaiting.value
    )
  ) {
    return;
  }
  audioRecorderState.startRecording();
}

/**
 * 处理放弃练习
 */
async function handleAbandonPractice() {
  if (!exerciseState) return;

  const confirmed = await exerciseState.handleAbandonPractice();
  if (confirmed) {
    // 停止计时器
    if (timerState) {
      timerState.stopTimer();
    }

    // 清理录音资源
    audioRecorderState.cleanup();

    // 清理音频播放资源
    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }

    // 清理聊天数据
    chatState.clearChat();

    // 重置练习开始时间
    practiceStartTime.value = 0;

    // 关闭抽屉
    drawerVisible.value = false;
  }
}

/**
 * 处理结束练习 - 从RecordingControl组件触发
 */
async function handleFinishPractice(data?: any) {
  console.log("练习完成，接收到的数据:", data);

  // 停止计时器
  if (timerState) {
    timerState.stopTimer();
  }

  // 清理资源
  audioRecorderState.cleanup();

  if (chatAreaRef.value) {
    chatAreaRef.value.cleanupAudioPlayer();
  }

  // 关闭抽屉并通知父组件
  drawerVisible.value = false;

  // 传递完整的数据给父组件，包括课程信息和评估抽屉标识
  emit("finish", data);
}

/**
 * 等待AI响应和TTS完成
 * @description 在自动完成练习前，等待正在进行的AI响应和TTS生成完成，确保消息内容完整
 */
async function waitForAiAndTtsCompletion(): Promise<void> {
  const maxWaitTime = 30000; // 最大等待时间30秒
  const checkInterval = 500; // 检查间隔500ms
  let waitedTime = 0;

  // 设置智能等待状态
  isWaitingForCompletion.value = true;

  console.log("开始等待AI响应和TTS完成", {
    isAiResponding: chatState.isAiResponding.value,
    isTtsWaiting: chatState.isTtsWaiting.value,
    maxWaitTime,
    checkInterval
  });

  return new Promise(resolve => {
    const checkCompletion = () => {
      // 检查是否都已完成
      const isAiCompleted = !chatState.isAiResponding.value;
      const isTtsCompleted = !chatState.isTtsWaiting.value;

      if (isAiCompleted && isTtsCompleted) {
        console.log("AI响应和TTS已完成，继续自动完成练习", {
          waitedTime,
          finalMessageCount: chatState.messages.value.length
        });
        // 清理智能等待状态
        isWaitingForCompletion.value = false;
        resolve();
        return;
      }

      // 检查是否超时
      if (waitedTime >= maxWaitTime) {
        console.warn("等待AI响应和TTS完成超时，强制继续完成练习", {
          waitedTime,
          isAiResponding: chatState.isAiResponding.value,
          isTtsWaiting: chatState.isTtsWaiting.value,
          messageCount: chatState.messages.value.length
        });
        // 清理智能等待状态
        isWaitingForCompletion.value = false;
        resolve();
        return;
      }

      // 继续等待
      waitedTime += checkInterval;
      setTimeout(checkCompletion, checkInterval);
    };

    // 开始检查
    checkCompletion();
  });
}

/**
 * 处理自动结束练习（时间到）
 * @description 当时间到达上限时，检查是否有对话，如果没有对话则走放弃练习逻辑
 */
async function handleAutoFinishPractice() {
  if (!exerciseState || !timerState || !courseInfoRef.value) return;

  console.log("练习时间到达上限，检查是否有对话", {
    duration: timerState.timer.value,
    messageCount: chatState.messages.value.length,
    userMessageCount: chatState.messages.value.filter(msg => !msg.isBot).length,
    isAiResponding: chatState.isAiResponding.value,
    isTtsWaiting: chatState.isTtsWaiting.value
  });

  // 检查是否有用户发送的消息
  const userMessages = chatState.messages.value.filter(msg => !msg.isBot);
  const hasUserMessages = userMessages.length > 0;

  if (!hasUserMessages) {
    // 没有对话，走放弃练习逻辑
    console.log("练习时间到达上限且无对话，自动放弃练习");

    ElMessage({
      message: `练习时间已达到上限（${exerciseState.getMaxDuration()}分钟），且未进行对话，系统自动放弃练习`,
      type: "warning",
      duration: 4000
    });

    // 停止计时器
    timerState.stopTimer();

    // 清理资源
    audioRecorderState.cleanup();

    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }

    // 清理聊天数据
    chatState.clearChat();

    // 重置练习开始时间
    practiceStartTime.value = 0;

    // 关闭抽屉（不传递任何数据，相当于放弃练习）
    drawerVisible.value = false;

    console.log("自动放弃练习完成");
    return;
  }

  // 有对话，检查是否需要等待AI响应或TTS完成
  if (chatState.isAiResponding.value || chatState.isTtsWaiting.value) {
    console.log("练习时间到达上限，但AI正在响应或TTS正在生成，等待完成", {
      isAiResponding: chatState.isAiResponding.value,
      isTtsWaiting: chatState.isTtsWaiting.value
    });

    ElMessage({
      message: `练习时间已达到上限，正在等待AI回复完成...`,
      type: "info",
      duration: 3000
    });

    // 等待AI响应和TTS完成
    await waitForAiAndTtsCompletion();
  }

  // 正常完成练习
  console.log("练习时间到达上限但有对话，自动完成练习");

  ElMessage({
    message: `练习时间已达到上限（${exerciseState.getMaxDuration()}分钟），系统自动完成练习`,
    type: "info",
    duration: 3000
  });

  try {
    // 调用完成练习接口 - 与手动完成练习保持一致
    await callFinishPracticeAPI();

    // 停止计时器
    timerState.stopTimer();

    // 清理资源
    audioRecorderState.cleanup();

    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }

    // 构造完成数据，包含评估抽屉标识
    const finishData = {
      shouldOpenEvaluation: true, // 自动打开评估抽屉
      courseInfo: courseInfoRef.value, // 传递课程信息
      messages: chatState.messages.value,
      duration: timerState.timer.value,
      practiceStartTime: practiceStartTime.value,
      isAutoFinish: true, // 标识为自动结束
      roleInfo: exerciseState.roleInfo.value
    };

    // 关闭抽屉并通知父组件
    drawerVisible.value = false;

    // 传递完整的数据给父组件，包括课程信息和评估抽屉标识
    emit("finish", finishData);

    console.log("自动完成练习成功，数据已传递给父组件", finishData);
  } catch (error) {
    console.error("自动完成练习失败:", error);
    ElMessage.error("自动完成练习失败，请重试");

    // 即使接口调用失败，也要停止计时器和清理资源
    timerState.stopTimer();
    audioRecorderState.cleanup();

    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }
  }
}

/**
 * 调用完成练习接口
 * @description 与RecordingControl组件中的逻辑保持一致
 */
async function callFinishPracticeAPI() {
  if (!exerciseState || !timerState || !courseInfoRef.value) {
    throw new Error("练习状态或课程信息不完整");
  }

  // 获取用户信息
  const userStore = useUserStoreHook();
  const userMsg = userStore.userMsg;

  // 获取当前时间戳（秒级）
  const currentTimestamp = Math.floor(Date.now() / 1000);

  // 计算练习开始和结束时间
  const practiceStartTimeValue =
    practiceStartTime.value || currentTimestamp - timerState.timer.value;
  const practiceEndTime = currentTimestamp;
  const practiceDuration = timerState.timer.value;

  // 转换消息格式，使用正确的时间戳
  const conversationMessages = chatState.messages.value.map(msg => ({
    role: msg.isBot ? "bot" : "user", // 角色：机器人或用户
    content: msg.content, // 消息内容
    start: (msg.startTime || practiceStartTimeValue).toString(), // 消息开始时间：用户消息为录音开始时间，机器人消息为TTS开始时间
    key: msg.index // 消息唯一标识
  }));

  // 构建请求参数 - 根据新的接口结构
  const requestData: WorkerTrainingTaskCourseUpdateRequest = {
    workerTrainingTaskId: courseInfoRef.value.workerTrainingTaskId || "0",
    trainingTaskCourseId: courseInfoRef.value.trainingTaskCourseId || "0",
    conversation: {
      workerId: userMsg.id || 0, // 工作人员ID
      trainingTaskCourseId: courseInfoRef.value.trainingTaskCourseId || "0", // 训练任务课程ID
      duration: practiceDuration, // 实际练习时长（秒）
      messages: conversationMessages, // 实际对话消息（不包含createdAt）
      begin: practiceStartTimeValue.toString(), // 开始时间（秒级时间戳）
      end: practiceEndTime.toString() // 结束时间（秒级时间戳）
    }
  };

  console.log("自动完成练习请求参数:", requestData);

  // 调用完成练习接口
  await finishWorkerTrainingTaskCourse(requestData);
}

/**
 * 发送消息
 * @param content 消息内容
 * @param startTime 消息开始时间（录音开始时间，秒级时间戳）
 * @param msgId 消息ID（来自录音时的msgid，确保与WebSocket ASR的msgid一致）
 */
async function sendMessage(
  content: string,
  startTime?: number,
  msgId?: string
) {
  if (!exerciseState) return;

  if (!exerciseState.canSendMessage(content, chatState.isAiResponding.value)) {
    return;
  }

  await chatState.sendMessage(
    content,
    exerciseState.getWorkerTrainingTaskId(),
    exerciseState.getTrainingTaskCourseId(),
    exerciseState.roleInfo.value,
    startTime,
    msgId
  );
}

/**
 * 抽屉打开事件处理 - 重置所有状态
 */
function handleDrawerOpen() {
  console.log("练习抽屉打开，重置所有状态", {
    hasCourseInfo: !!courseInfoRef.value,
    hasExerciseState: !!exerciseState,
    hasTimerState: !!timerState,
    currentPracticeStartTime: practiceStartTime.value
  });

  // 使用 nextTick 确保组件完全渲染后再进行初始化
  nextTick(() => {
    // 检查必要的状态是否已准备好
    if (!exerciseState || !timerState) {
      console.warn("练习状态或计时器状态未准备好");
      return;
    }

    // 清理聊天数据（包括TTS状态）
    chatState.clearChat();

    // 清理录音状态
    audioRecorderState.cleanup();

    // 清理音频播放资源
    if (chatAreaRef.value) {
      chatAreaRef.value.cleanupAudioPlayer();
    }

    // 重置计时器
    timerState.resetTimer();

    // 重置练习状态
    exerciseState.startPractice();

    // 重新设置练习开始时间（秒级时间戳）
    practiceStartTime.value = Math.floor(Date.now() / 1000);

    // 重新启动计时器
    timerState.startTimer();

    console.log("练习抽屉状态重置完成，计时器已启动", {
      practiceStartTime: practiceStartTime.value,
      timerValue: timerState.timer.value,
      isRunning: timerState.isRunning.value
    });
  });
}

/**
 * 关闭抽屉前的确认
 */
function handleClose() {
  if (
    exerciseState &&
    exerciseState.isPracticing.value &&
    chatState.messages.value.length > 0
  ) {
    exerciseState.handleAbandonPractice().then(confirmed => {
      if (confirmed) {
        // 停止计时器
        if (timerState) {
          timerState.stopTimer();
        }

        // 清理资源
        audioRecorderState.cleanup();

        if (chatAreaRef.value) {
          chatAreaRef.value.cleanupAudioPlayer();
        }

        // 清理聊天数据
        chatState.clearChat();

        // 重置练习开始时间
        practiceStartTime.value = 0;

        drawerVisible.value = false;
      }
    });
  } else {
    drawerVisible.value = false;
  }
}

// 组件挂载时初始化
onMounted(() => {
  console.log("练习组件挂载", {
    hasCourseInfo: !!courseInfoRef.value,
    hasExerciseState: !!exerciseState,
    hasTimerState: !!timerState
  });

  // 如果没有课程信息，直接返回
  if (!courseInfoRef.value || !exerciseState || !timerState) {
    console.warn("练习组件挂载失败：缺少必要状态");
    return;
  }

  // 初始化聊天区域
  if (chatAreaRef.value) {
    chatAreaRef.value.initAudioPlayer();
  }

  // 设置hooks回调
  exerciseState.setCallbacks({
    onFinish: result => {
      emit("finish", result);
    },
    onSuccess: () => {
      emit("success");
    }
  });

  // 设置录音回调
  audioRecorderState.setCallbacks({
    onTextRecognized: (text: string, startTime: number, msgId: string) => {
      sendMessage(text, startTime, msgId);
    },
    onRecordingStateChange: _state => {
      // 录音状态变化处理
    }
  });

  // 设置键盘录音回调
  keyboardRecordingState.setCallbacks({
    onStartRecording: () => {
      if (
        exerciseState &&
        exerciseState.canStartRecording(
          audioRecorderState.isRecording.value,
          chatState.isAiResponding.value,
          chatState.isTtsWaiting.value
        )
      ) {
        audioRecorderState.startRecording();
      }
    },
    onStopRecording: () => {
      audioRecorderState.stopRecording();
    },
    onCancelRecording: () => {
      audioRecorderState.cancelRecording();
    }
  });

  // 设置聊天区域回调
  chatState.setChatAreaCallbacks({
    onShowLoading: () => {
      if (chatAreaRef.value) {
        chatAreaRef.value.showLoading();
      }
    },
    onHideLoading: () => {
      if (chatAreaRef.value) {
        chatAreaRef.value.hideLoading();
      }
    },
    onScrollToBottom: () => {
      if (chatAreaRef.value) {
        chatAreaRef.value.scrollToBottom();
      }
    },
    onStartStreamAudio: (msgId: string) => {
      if (chatAreaRef.value) {
        chatAreaRef.value.startStreamAudio(msgId);
      }
    },
    onHandleStreamAudioChunk: (audioChunk: ArrayBuffer) => {
      if (chatAreaRef.value) {
        chatAreaRef.value.handleStreamAudioChunk(audioChunk);
      }
    },
    onCompleteStreamAudio: () => {
      if (chatAreaRef.value) {
        chatAreaRef.value.completeStreamAudio();
      }
    },
    onCompleteStreamAudioPlayback: (msgId: string) => {
      // 调用useChat的完成流式音频播放方法
      chatState.completeStreamAudioPlayback(msgId);
    },
    onForceStopStreamAudio: () => {
      if (chatAreaRef.value) {
        chatAreaRef.value.forceStopStreamAudio();
      }
    },
    onPlayAudio: message => {
      if (chatAreaRef.value) {
        chatAreaRef.value.playAudio(message);
      }
    },
    onStopAudio: () => {
      if (chatAreaRef.value) {
        chatAreaRef.value.stopAudio();
      }
    }
  });

  // 延迟初始化练习状态，确保组件完全准备好
  setTimeout(() => {
    console.log("备用初始化检查", {
      hasTimerState: !!timerState,
      isTimerRunning: timerState.isRunning.value,
      practiceStartTime: practiceStartTime.value,
      needsBackupInit:
        timerState &&
        !timerState.isRunning.value &&
        practiceStartTime.value === 0
    });

    // 如果计时器还没有启动（可能 handleDrawerOpen 没有执行或失败），则在这里启动
    if (
      timerState &&
      !timerState.isRunning.value &&
      practiceStartTime.value === 0
    ) {
      console.log("备用初始化：启动计时器");
      practiceStartTime.value = Math.floor(Date.now() / 1000);
      chatState.clearChat();
      timerState.startTimer();
      console.log("备用初始化完成", {
        practiceStartTime: practiceStartTime.value,
        isTimerRunning: timerState.isRunning.value
      });
    }
  }, 100); // 100ms 延迟，确保 handleDrawerOpen 有机会执行
});

// 组件卸载时清理资源
onBeforeUnmount(() => {
  // 清理计时器
  if (timerState) {
    timerState.stopTimer();
  }

  // 清理聊天区域资源
  if (chatAreaRef.value) {
    chatAreaRef.value.cleanupAudioPlayer();
  }

  // 清理录音资源
  audioRecorderState.cleanup();

  // 清理音频数据
  chatState.clearChat();
});
</script>

<style scoped>
.exercise-drawer {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.timer-box {
  color: #1890ff;
}
</style>
