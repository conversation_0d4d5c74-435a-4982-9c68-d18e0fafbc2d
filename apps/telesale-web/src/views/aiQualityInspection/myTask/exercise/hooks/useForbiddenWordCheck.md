# 违禁词检测Hook (useForbiddenWordCheck)

> 一个用于处理违禁词检测的Hook，支持EventStream接口和自定义提示组件

## 📖 Hook概述

违禁词检测Hook是AI质检练习系统中的核心业务逻辑Hook，负责实时检测用户发言中的敏感词汇。通过EventStream接口进行异步检测，并使用自定义提示组件显示检测结果，确保合规性检查不会干扰正常的练习流程。

## ✨ 核心特性

- 🎮 **EventStream支持**: 基于Server-Sent Events的流式违禁词检测
- ⚡ **异步处理**: 不阻塞主流程的异步检测机制
- 🖼️ **智能提示**: 自定义违禁词提示组件，避免ESC键冲突
- 🔧 **状态管理**: 完整的检测状态跟踪和管理
- 🎨 **错误处理**: 健壮的异常处理和降级机制

## 🚀 快速开始

### 基本用法

```typescript
import { useForbiddenWordCheck } from './hooks/useForbiddenWordCheck'

// 在组件中使用
const {
  checkState,
  checkForbiddenWords,
  checkForbiddenWordsAsync,
  resetCheckState,
  clearForbiddenWordMessage,
  isShowingForbiddenWordMessage
} = useForbiddenWordCheck()

// 检测违禁词
const hasForbiddenWords = await checkForbiddenWords('用户输入的内容')

// 异步检测（不阻塞）
checkForbiddenWordsAsync('用户输入的内容')

// 清理状态
resetCheckState()
```

### 返回值

| 属性/方法 | 类型 | 说明 |
|----------|------|------|
| checkState | Ref\<ForbiddenWordCheckState\> | 检测状态响应式对象 |
| checkForbiddenWords | (content: string) => Promise\<boolean\> | 同步检测违禁词 |
| checkForbiddenWordsAsync | (content: string) => Promise\<void\> | 异步检测违禁词 |
| getCheckState | () => object | 获取当前检测状态 |
| resetCheckState | () => void | 重置检测状态 |
| clearForbiddenWordMessage | () => void | 清除违禁词提示 |
| showForbiddenWordMessage | () => void | 显示违禁词提示 |
| isShowingForbiddenWordMessage | () => boolean | 检查是否正在显示提示 |

### 状态接口

```typescript
interface ForbiddenWordCheckState {
  isChecking: boolean                    // 是否正在检测
  lastCheckTime: number                  // 最后检测时间
  checkCount: number                     // 检测次数
  hasForbiddenWordMessage: boolean       // 是否已显示提示
  forbiddenWordMessageInstance: any      // 提示实例
  isShowingForbiddenWordMessage: boolean // 是否正在显示提示
}
```

## 🔧 技术实现

### 核心原理

1. **EventStream处理**: 使用Fetch API处理Server-Sent Events流式响应
2. **状态管理**: 响应式状态跟踪检测进度和结果
3. **提示控制**: 智能控制违禁词提示的显示和隐藏
4. **异常处理**: 完善的错误处理和降级机制

### 关键方法

```typescript
// 检测违禁词
async function checkForbiddenWords(content: string): Promise<boolean>

// 处理EventStream数据块
function processEventStreamChunk(chunk: string): {
  result: string | null
  finished: boolean
}

// 显示违禁词提示
function showForbiddenWordMessage(): void

// 重置检测状态
function resetCheckState(): void
```

### EventStream数据处理

- **SSE格式**: 处理`data: `前缀的Server-Sent Events格式
- **JSON解析**: 智能解析JSON数据中的message、result、content字段
- **结束标记**: 识别`[DONE]`、`EOF`等流结束标记
- **错误容错**: 对非标准格式数据的容错处理

## 🎯 使用场景

- **实时检测**: 在用户发言后立即进行违禁词检测
- **合规监控**: 确保AI对话练习符合内容合规要求
- **异步处理**: 不影响正常对话流程的后台检测
- **错误提示**: 向用户友好地提示违禁词检测结果

## 📋 更新日志

### v1.0.0 (2025-01-27)

- ✨ 新增违禁词检测Hook
- ⚡ 实现EventStream接口支持
- 🔧 添加自定义违禁词提示组件集成
- 🎨 实现智能状态管理和错误处理
- 📱 支持异步检测，不阻塞主流程
