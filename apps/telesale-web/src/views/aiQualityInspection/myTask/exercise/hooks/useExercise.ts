/**
 * @Date         : 2025-01-27 16:30:00
 * @Description  : 练习状态管理Hook
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { computed, toRef, Ref } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { WorkerTrainingTaskCourseInstance } from "/@/api/AIQualityInspection/taskManagement";

// 注意：ExerciseResult 接口已移除，现在统一使用 store 中的数据结构

/**
 * 练习状态管理Hook
 * @param courseInfo 课程信息（支持响应式）
 * @returns 练习状态管理相关的状态和方法
 */
export function useExercise(
  courseInfo:
    | WorkerTrainingTaskCourseInstance
    | Ref<WorkerTrainingTaskCourseInstance>
) {
  // 将 courseInfo 转换为响应式引用
  const courseInfoRef =
    typeof courseInfo === "object" && "value" in courseInfo
      ? courseInfo
      : toRef(() => courseInfo);

  // 添加courseInfo的空值检查（但不立即抛出错误，而是在使用时检查）
  console.log("useExercise 初始化:", {
    hasCourseInfo: !!courseInfoRef.value,
    courseInfoType: typeof courseInfo,
    isRef: "value" in courseInfo
  });

  // 注意：状态变量已移除，现在统一使用 store 中的状态管理
  // 如果需要访问这些状态，请使用 useExerciseStore()

  // 注意：事件回调已移除，现在统一使用 store 中的回调机制

  // 角色信息 - 从课程信息中获取（响应式）
  const roleInfo = computed(() => {
    const course = courseInfoRef.value?.course?.course;

    console.log("useExercise - roleInfo 计算:", {
      hasCourseInfoRef: !!courseInfoRef.value,
      hasCourse: !!course,
      botName: course?.botName,
      botRole: course?.botRole,
      target: course?.target,
      backgroundDesc: course?.backgroundDesc,
      conversationReq: course?.conversationReq
    });

    return {
      role: course?.botRole || "customer", // 从课程信息中获取机器人角色
      roleName: course?.botName || "客户", // 从课程信息中获取机器人名称
      roleIntroduction:
        course?.target || course?.backgroundDesc || "练习对话角色",
      personality: course?.conversationReq || "按照课程要求进行对话" // 使用对话要求作为性格描述
    };
  });

  // 注意：setCallbacks 方法已移除，现在统一使用 store 中的回调设置

  /**
   * 处理音频播放状态变化
   * @param isPlaying 是否正在播放
   */
  function handleAudioPlayStateChange(isPlaying: boolean) {
    // 注意：状态变化现在由 store 统一管理
    console.log("useExercise.handleAudioPlayStateChange:", isPlaying);
  }

  /**
   * 放弃练习
   */
  async function handleAbandonPractice() {
    try {
      await ElMessageBox.confirm(
        "放弃任务或者离开当前页面不会保存本次练习进度，请确认是否离开本页面?",
        "放弃练习",
        {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        }
      );

      return true; // 确认放弃
    } catch (error) {
      return false; // 用户取消操作
    }
  }

  // 注意：finishPractice 方法已移除，现在统一使用 store.finishPractice()
  // 如果需要完成练习，请调用 store.finishPractice() 方法

  /**
   * 检查是否可以开始录音
   * @param isRecording 当前是否正在录音
   * @param isAiResponding 是否AI正在响应
   * @param isTtsWaiting 是否TTS正在等待返回（可选）
   * @returns 是否可以开始录音
   */
  function canStartRecording(
    isRecording: boolean,
    isAiResponding: boolean,
    isTtsWaiting?: boolean
  ): boolean {
    // 注意：isPracticing 状态现在由 store 管理，这里假设练习正在进行
    // 实际的状态检查应该在调用方（store）进行

    if (isRecording) {
      return false;
    }

    // 注意：isAudioPlaying 状态检查现在由 store 统一处理

    if (isAiResponding) {
      ElMessage.warning("AI正在思考中，请稍候");
      return false;
    }

    if (isTtsWaiting) {
      ElMessage.warning("TTS正在生成中，请稍候");
      return false;
    }

    return true;
  }

  /**
   * 检查是否可以发送消息
   * @param content 消息内容
   * @param isAiResponding 是否AI正在响应
   * @returns 是否可以发送消息
   */
  function canSendMessage(content: string, isAiResponding: boolean): boolean {
    if (!content.trim()) {
      return false;
    }

    // 注意：isPracticing 状态现在由 store 统一管理
    // 这里假设练习正在进行，实际的状态检查应该在调用方（store）进行
    // if (!isPracticing.value) {
    //   return false;
    // }

    if (isAiResponding) {
      return false;
    }

    return true;
  }

  /**
   * 设置加载状态
   * @param isLoading 是否加载中
   */
  function setLoading(isLoading: boolean) {
    // 注意：loading 状态现在由 store 统一管理
    console.log("useExercise.setLoading:", isLoading);
  }

  /**
   * 停止练习
   */
  function stopPractice() {
    // 注意：isPracticing 状态现在由 store 统一管理
    console.log("useExercise.stopPractice 被调用");
  }

  /**
   * 开始练习
   */
  function startPractice() {
    // 注意：isPracticing 状态现在由 store 统一管理
    console.log("useExercise.startPractice 被调用");
  }

  /**
   * 获取课程最大持续时间（分钟）
   */
  function getMaxDuration(): number {
    return courseInfoRef.value?.course?.course?.maxDuration || 60;
  }

  /**
   * 获取工作任务ID
   */
  function getWorkerTrainingTaskId(): number {
    console.log("courseInfo", courseInfoRef.value);
    const taskId = courseInfoRef.value?.workerTrainingTaskId;
    if (!taskId) {
      console.error(
        "workerTrainingTaskId 为空，courseInfo:",
        courseInfoRef.value
      );
      // 返回默认值而不是抛出错误
      return 0;
    }
    return Number(taskId);
  }

  /**
   * 获取课程ID
   */
  function getTrainingTaskCourseId(): number {
    const courseId = courseInfoRef.value?.trainingTaskCourseId;
    if (!courseId) {
      console.error(
        "trainingTaskCourseId 为空，courseInfo:",
        courseInfoRef.value
      );
      // 返回默认值而不是抛出错误
      return 0;
    }
    return Number(courseId);
  }

  return {
    // 状态（基础状态已移至 store 统一管理）
    // loading, isPracticing, isAudioPlaying 请使用 useExerciseStore()
    roleInfo,

    // 方法
    // setCallbacks 已移除，请使用 store 中的回调设置
    handleAudioPlayStateChange,
    handleAbandonPractice,
    // finishPractice 已移除，请使用 store.finishPractice()
    canStartRecording,
    canSendMessage,
    setLoading,
    stopPractice,
    startPractice,
    getMaxDuration,
    getWorkerTrainingTaskId,
    getTrainingTaskCourseId
  };
}
