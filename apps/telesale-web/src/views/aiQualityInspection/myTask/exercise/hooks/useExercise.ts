/**
 * @Date         : 2025-01-27 16:30:00
 * @Description  : 练习状态管理Hook
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref, computed, toRef, Ref } from "vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { WorkerTrainingTaskCourseInstance } from "/@/api/AIQualityInspection/taskManagement";

/**
 * 练习结果接口
 */
interface ExerciseResult {
  courseId: number;
  messages: Array<{ content: string; isBot: boolean; index: string }>;
  duration: number;
  roleInfo: {
    role: string;
    roleName: string;
    roleIntroduction: string;
    personality: string;
  };
}

/**
 * 练习状态管理Hook
 * @param courseInfo 课程信息（支持响应式）
 * @returns 练习状态管理相关的状态和方法
 */
export function useExercise(
  courseInfo:
    | WorkerTrainingTaskCourseInstance
    | Ref<WorkerTrainingTaskCourseInstance>
) {
  // 将 courseInfo 转换为响应式引用
  const courseInfoRef =
    typeof courseInfo === "object" && "value" in courseInfo
      ? courseInfo
      : toRef(() => courseInfo);

  // 添加courseInfo的空值检查
  if (!courseInfoRef.value) {
    console.error("useExercise: courseInfo 不能为空");
    throw new Error("courseInfo 不能为空");
  }

  // 状态变量
  const loading = ref(false);
  const isPracticing = ref(true); // 默认进入就开始练习
  const isAudioPlaying = ref(false);

  // 事件回调
  const onFinish = ref<(result: ExerciseResult) => void>(() => {});
  const onSuccess = ref<() => void>(() => {});

  // 角色信息 - 从课程信息中获取（响应式）
  const roleInfo = computed(() => {
    const course = courseInfoRef.value?.course?.course;

    console.log("useExercise - roleInfo 计算:", {
      hasCourseInfoRef: !!courseInfoRef.value,
      hasCourse: !!course,
      botName: course?.botName,
      botRole: course?.botRole,
      target: course?.target,
      backgroundDesc: course?.backgroundDesc,
      conversationReq: course?.conversationReq
    });

    return {
      role: course?.botRole || "customer", // 从课程信息中获取机器人角色
      roleName: course?.botName || "客户", // 从课程信息中获取机器人名称
      roleIntroduction:
        course?.target || course?.backgroundDesc || "练习对话角色",
      personality: course?.conversationReq || "按照课程要求进行对话" // 使用对话要求作为性格描述
    };
  });

  /**
   * 设置事件回调
   * @param callbacks 回调函数对象
   */
  function setCallbacks(callbacks: {
    onFinish?: (result: ExerciseResult) => void;
    onSuccess?: () => void;
  }) {
    if (callbacks.onFinish) {
      onFinish.value = callbacks.onFinish;
    }
    if (callbacks.onSuccess) {
      onSuccess.value = callbacks.onSuccess;
    }
  }

  /**
   * 处理音频播放状态变化
   * @param isPlaying 是否正在播放
   */
  function handleAudioPlayStateChange(isPlaying: boolean) {
    isAudioPlaying.value = isPlaying;
  }

  /**
   * 放弃练习
   */
  async function handleAbandonPractice() {
    try {
      await ElMessageBox.confirm(
        "放弃任务或者离开当前页面不会保存本次练习进度，请确认是否离开本页面?",
        "放弃练习",
        {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning"
        }
      );

      return true; // 确认放弃
    } catch (error) {
      return false; // 用户取消操作
    }
  }

  /**
   * 结束练习
   * @param autoFinish 是否为自动结束
   * @param messages 消息列表
   * @param duration 练习时长
   */
  async function finishPractice(
    autoFinish = false,
    messages: Array<{ content: string; isBot: boolean; index: string }> = [],
    duration: number = 0
  ) {
    try {
      // 如果自动结束，则显示提示
      if (autoFinish) {
        // 自动结束时显示提示
        ElMessage({
          message: "练习时间已达上限，系统自动结束练习",
          type: "info",
          duration: 3000
        });
      }

      // 通知父组件练习完成
      onFinish.value({
        courseId: Number(courseInfoRef.value.trainingTaskCourseId),
        messages: messages,
        duration: duration,
        roleInfo: roleInfo.value
      });

      return true; // 确认结束
    } catch (error) {
      return false; // 用户取消操作
    }
  }

  /**
   * 检查是否可以开始录音
   * @param isRecording 当前是否正在录音
   * @param isAiResponding 是否AI正在响应
   * @param isTtsWaiting 是否TTS正在等待返回（可选）
   * @returns 是否可以开始录音
   */
  function canStartRecording(
    isRecording: boolean,
    isAiResponding: boolean,
    isTtsWaiting?: boolean
  ): boolean {
    if (!isPracticing.value) {
      return false;
    }

    if (isRecording) {
      return false;
    }

    if (isAudioPlaying.value) {
      ElMessage.warning("请等待语音播放完成后再录音");
      return false;
    }

    if (isAiResponding) {
      ElMessage.warning("AI正在思考中，请稍候");
      return false;
    }

    if (isTtsWaiting) {
      ElMessage.warning("TTS正在生成中，请稍候");
      return false;
    }

    return true;
  }

  /**
   * 检查是否可以发送消息
   * @param content 消息内容
   * @param isAiResponding 是否AI正在响应
   * @returns 是否可以发送消息
   */
  function canSendMessage(content: string, isAiResponding: boolean): boolean {
    if (!content.trim()) {
      return false;
    }

    if (!isPracticing.value) {
      return false;
    }

    if (isAiResponding) {
      return false;
    }

    return true;
  }

  /**
   * 设置加载状态
   * @param isLoading 是否加载中
   */
  function setLoading(isLoading: boolean) {
    loading.value = isLoading;
  }

  /**
   * 停止练习
   */
  function stopPractice() {
    isPracticing.value = false;
  }

  /**
   * 开始练习
   */
  function startPractice() {
    isPracticing.value = true;
  }

  /**
   * 获取课程最大持续时间（分钟）
   */
  function getMaxDuration(): number {
    return courseInfoRef.value?.course?.course?.maxDuration || 60;
  }

  /**
   * 获取工作任务ID
   */
  function getWorkerTrainingTaskId(): number {
    console.log("courseInfo", courseInfoRef.value);
    const taskId = courseInfoRef.value?.workerTrainingTaskId;
    if (!taskId) {
      console.error(
        "workerTrainingTaskId 为空，courseInfo:",
        courseInfoRef.value
      );
      throw new Error("workerTrainingTaskId 不能为空");
    }
    return Number(taskId);
  }

  /**
   * 获取课程ID
   */
  function getTrainingTaskCourseId(): number {
    const courseId = courseInfoRef.value?.trainingTaskCourseId;
    if (!courseId) {
      console.error(
        "trainingTaskCourseId 为空，courseInfo:",
        courseInfoRef.value
      );
      throw new Error("trainingTaskCourseId 不能为空");
    }
    return Number(courseId);
  }

  return {
    // 状态
    loading,
    isPracticing,
    isAudioPlaying,
    roleInfo,

    // 方法
    setCallbacks,
    handleAudioPlayStateChange,
    handleAbandonPractice,
    finishPractice,
    canStartRecording,
    canSendMessage,
    setLoading,
    stopPractice,
    startPractice,
    getMaxDuration,
    getWorkerTrainingTaskId,
    getTrainingTaskCourseId
  };
}
