/**
 * @Date         : 2025-01-27 16:30:00
 * @Description  : 练习计时器Hook
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref, onBeforeUnmount } from "vue";

/**
 * 计时器Hook
 * @param maxDurationMinutes 最大持续时间（分钟）
 * @returns 计时器相关的状态和方法
 */
export function useTimer(maxDurationMinutes: number = 60) {
  // 状态变量
  const timer = ref(0);
  const timerInterval = ref<number | null>(null);
  const isRunning = ref(false);

  // 最大时间（秒）
  const maxDurationSeconds = maxDurationMinutes * 60;

  // 事件回调
  const onTimeUp = ref<() => void>(() => {});

  /**
   * 设置时间到达回调
   * @param callback 时间到达时的回调函数
   */
  function setTimeUpCallback(callback: () => void) {
    onTimeUp.value = callback;
  }

  /**
   * 格式化时间显示
   * @param seconds 秒数
   * @returns 格式化的时间字符串 HH:mm:ss
   */
  function formatTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    return [
      hours.toString().padStart(2, "0"),
      minutes.toString().padStart(2, "0"),
      secs.toString().padStart(2, "0")
    ].join(":");
  }

  /**
   * 开始计时
   */
  function startTimer() {
    if (isRunning.value) {
      console.warn("计时器已在运行中");
      return;
    }

    console.log("开始计时...");
    isRunning.value = true;

    timerInterval.value = window.setInterval(() => {
      timer.value++;

      // 检查是否达到最大时间
      if (timer.value >= maxDurationSeconds) {
        console.log(`达到最大时间(${maxDurationMinutes}分钟)，自动停止计时`);
        stopTimer();
        onTimeUp.value();
      }
    }, 1000);
  }

  /**
   * 停止计时
   * @param clearCallback 是否清理回调函数，默认为false
   */
  function stopTimer(clearCallback: boolean = false) {
    if (timerInterval.value) {
      clearInterval(timerInterval.value);
      timerInterval.value = null;
    }
    isRunning.value = false;

    // 只有在明确要求时才清理回调函数
    if (clearCallback) {
      onTimeUp.value = () => {};
      console.log("计时器已停止，回调已清理");
    } else {
      console.log("计时器已停止，回调保留");
    }
  }

  /**
   * 重置计时器
   */
  function resetTimer() {
    stopTimer();
    timer.value = 0;
    console.log("计时器已重置");
  }

  /**
   * 暂停计时
   */
  function pauseTimer() {
    if (timerInterval.value) {
      clearInterval(timerInterval.value);
      timerInterval.value = null;
    }
    isRunning.value = false;
    console.log("计时器已暂停");
  }

  /**
   * 恢复计时
   */
  function resumeTimer() {
    if (isRunning.value) {
      console.warn("计时器已在运行中");
      return;
    }

    console.log("恢复计时...");
    isRunning.value = true;

    timerInterval.value = window.setInterval(() => {
      timer.value++;

      // 检查是否达到最大时间
      if (timer.value >= maxDurationSeconds) {
        console.log(`达到最大时间(${maxDurationMinutes}分钟)，自动停止计时`);
        stopTimer();
        onTimeUp.value();
      }
    }, 1000);
  }

  /**
   * 获取剩余时间（秒）
   */
  function getRemainingTime(): number {
    return Math.max(0, maxDurationSeconds - timer.value);
  }

  /**
   * 获取剩余时间百分比
   */
  function getRemainingPercentage(): number {
    return (getRemainingTime() / maxDurationSeconds) * 100;
  }

  /**
   * 检查是否即将超时（剩余时间少于指定分钟数）
   * @param warningMinutes 警告时间（分钟）
   */
  function isNearTimeout(warningMinutes: number = 5): boolean {
    return getRemainingTime() <= warningMinutes * 60;
  }

  // 组件卸载时清理计时器
  onBeforeUnmount(() => {
    stopTimer();
  });

  return {
    // 状态
    timer,
    isRunning,
    maxDurationSeconds,

    // 计算属性
    formattedTime: () => formatTime(timer.value),
    remainingTime: getRemainingTime,
    remainingPercentage: getRemainingPercentage,

    // 方法
    startTimer,
    stopTimer,
    resetTimer,
    pauseTimer,
    resumeTimer,
    formatTime,
    isNearTimeout,
    setTimeUpCallback
  };
}
