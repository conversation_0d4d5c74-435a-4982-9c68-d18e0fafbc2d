/**
 * @Date         : 2025-01-27 16:30:00
 * @Description  : 音频录音Hook - WebSocket实时语音识别版本（支持取消录音）
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref, onBeforeUnmount } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import baseURL from "/@/api/url";

/**
 * 录音状态接口
 */
interface RecordingState {
  isRecording: boolean;
  recordingTime: number;
  recordingText: string;
}

/**
 * 音频录音Hook - 使用WebSocket实时语音识别
 * @returns 音频录音相关的状态和方法
 */
export function useAudioRecorder() {
  // 状态变量
  const isRecording = ref(false);
  const recordingTime = ref(0);
  const recordingText = ref("请开始发言");
  const recordingInterval = ref<number | null>(null);
  const recordingStartTime = ref<number>(0); // 录音开始时间（秒级时间戳）

  // WebSocket和音频处理相关变量
  const wsRef = ref<WebSocket | null>(null);
  const streamRef = ref<MediaStream | null>(null);
  const audioContextRef = ref<AudioContext | null>(null);
  const currentMsgIdRef = ref<string | null>(null);

  // 事件回调
  const onTextRecognized = ref<
    (text: string, startTime: number, msgId: string) => void
  >(() => {});
  const onRecordingStateChange = ref<(state: RecordingState) => void>(() => {});

  /**
   * 设置回调函数
   * @param callbacks 回调函数对象
   */
  function setCallbacks(callbacks: {
    onTextRecognized?: (text: string, startTime: number, msgId: string) => void;
    onRecordingStateChange?: (state: RecordingState) => void;
  }) {
    if (callbacks.onTextRecognized) {
      onTextRecognized.value = callbacks.onTextRecognized;
    }
    if (callbacks.onRecordingStateChange) {
      onRecordingStateChange.value = callbacks.onRecordingStateChange;
    }
  }

  /**
   * 通知状态变化
   */
  function notifyStateChange() {
    const state: RecordingState = {
      isRecording: isRecording.value,
      recordingTime: recordingTime.value,
      recordingText: recordingText.value
    };
    onRecordingStateChange.value(state);
  }

  /**
   * 开始录音
   */
  async function startRecording() {
    if (isRecording.value) {
      console.warn("已在录音中");
      return;
    }

    console.log("开始录音...");

    isRecording.value = true;
    recordingTime.value = 0;
    recordingText.value = "正在录音...";
    recordingStartTime.value = Math.floor(Date.now() / 1000); // 记录录音开始时间（秒级时间戳）

    // 通知状态变化
    notifyStateChange();

    // 启动录音计时器
    recordingInterval.value = window.setInterval(() => {
      recordingTime.value++;

      // 通知状态变化
      notifyStateChange();
    }, 1000);

    // 生成唯一消息ID
    const currentMsgId = crypto.randomUUID();
    currentMsgIdRef.value = currentMsgId;

    try {
      // 创建WebSocket连接
      const wsUrl = baseURL.robot.replace("https://", "wss://");
      const websocketUrl = `${wsUrl}/admin/audio/asr?msgid=${currentMsgId}`;
      console.log("WebSocket连接URL:", websocketUrl);

      wsRef.value = new WebSocket(websocketUrl);

      // WebSocket连接成功
      wsRef.value.onopen = () => {
        console.log("WebSocket连接成功");
      };

      // 接收WebSocket消息
      wsRef.value.onmessage = event => {
        const dataRaw = event.data.trim();
        try {
          // 解析返回的JSON数据
          const data = JSON.parse(dataRaw);

          // 提取识别的文本内容
          let content = "";
          if (Array.isArray(data)) {
            data.forEach(item => {
              if (item.content) content += item.content;
            });
          } else if (data.content) {
            content = data.content;
          }

          // 更新录音文本显示
          if (content.length > 0) {
            recordingText.value = content;
            console.log("语音识别实时结果:", content);

            // 通知状态变化
            notifyStateChange();
          }
        } catch (err) {
          console.error("解析WebSocket数据失败:", err);
        }
      };

      // WebSocket连接关闭
      wsRef.value.onclose = () => {
        console.log("WebSocket连接已关闭");
      };

      // WebSocket连接错误
      wsRef.value.onerror = error => {
        console.error("WebSocket连接错误:", error);
        ElMessageBox.alert("语音识别服务连接失败，请检查网络或重试", "错误");
        stopRecording();
      };

      // 获取麦克风权限并开始录音
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 16000,
          channelCount: 1
        }
      });

      // 保存音频流引用
      streamRef.value = stream;

      // 创建音频上下文
      const audioCtx = new AudioContext({
        sampleRate: 16000
      });

      // 保存音频上下文引用
      audioContextRef.value = audioCtx;

      // 创建音频处理节点
      const scriptProcessorNode = audioCtx.createScriptProcessor(1024, 1, 1);

      // 处理音频数据
      scriptProcessorNode.onaudioprocess = event => {
        // 获取音频输入数据
        const float32Data = event.inputBuffer.getChannelData(0);

        // 将Float32数据转换为Int16数据
        const int16Data = new Int16Array(float32Data.length);
        for (let i = 0; i < float32Data.length; i++) {
          int16Data[i] = Math.min(1, float32Data[i]) * 0x7fff;
        }

        // 发送数据到WebSocket
        if (wsRef.value && wsRef.value.readyState === WebSocket.OPEN) {
          wsRef.value.send(int16Data);
        }
      };

      // 连接音频节点
      const sourceNode = audioCtx.createMediaStreamSource(stream);
      sourceNode.connect(scriptProcessorNode);
      scriptProcessorNode.connect(audioCtx.destination);

      console.log("录音开始成功");
    } catch (error) {
      console.error("录音初始化失败:", error);
      ElMessage.error("无法访问麦克风，请检查权限设置");
      resetRecordingState();
    }
  }

  /**
   * 停止录音
   */
  async function stopRecording() {
    if (!isRecording.value) {
      console.warn("当前未在录音");
      return;
    }

    console.log(`录音结束，时长: ${recordingTime.value}秒`);

    // 停止录音计时器
    if (recordingInterval.value) {
      clearInterval(recordingInterval.value);
      recordingInterval.value = null;
    }

    isRecording.value = false;

    // 通知状态变化
    notifyStateChange();

    // 停止音频流
    if (streamRef.value) {
      streamRef.value.getTracks().forEach(track => track.stop());
      streamRef.value = null;
    }

    // 关闭音频上下文
    if (audioContextRef.value) {
      try {
        await audioContextRef.value.close();
      } catch (err) {
        console.error("关闭音频上下文失败:", err);
      }
      audioContextRef.value = null;
    }

    // 延迟处理WebSocket和文本识别结果
    setTimeout(() => {
      // 关闭WebSocket连接
      if (wsRef.value) {
        wsRef.value.close();
        wsRef.value = null;
      }

      // 处理识别结果
      const recognizedText = recordingText.value;
      if (
        recognizedText &&
        recognizedText !== "请开始发言" &&
        recognizedText !== "正在录音..."
      ) {
        console.log("语音识别最终结果:", recognizedText);

        // 发送识别到的文本，包含录音开始时间和消息ID
        const msgId = currentMsgIdRef.value || crypto.randomUUID();
        onTextRecognized.value(recognizedText, recordingStartTime.value, msgId);

        // 重置录音文本
        recordingText.value = "请开始发言";
        notifyStateChange();
      } else if (recordingTime.value < 1) {
        // 录音时间太短，可能没有识别到语音
        ElMessageBox.alert("未识别到语音，请检查麦克风或再次发言", "提示");
        recordingText.value = "请开始发言";
        notifyStateChange();
      } else {
        // 没有识别到文本但录音时间足够
        ElMessageBox.alert("未能识别您的语音，请重新尝试", "提示");
        recordingText.value = "请开始发言";
        notifyStateChange();
      }

      // 清除当前消息ID
      currentMsgIdRef.value = null;
    }, 500); // 延迟处理，确保WebSocket有时间处理最后的数据

    console.log("录音停止成功");
  }

  /**
   * 取消录音 - 不发送识别结果，直接重置状态
   */
  async function cancelRecording() {
    if (!isRecording.value) {
      console.warn("当前未在录音");
      return;
    }

    console.log(`取消录音，时长: ${recordingTime.value}秒`);

    // 停止录音计时器
    if (recordingInterval.value) {
      clearInterval(recordingInterval.value);
      recordingInterval.value = null;
    }

    isRecording.value = false;

    // 停止音频流
    if (streamRef.value) {
      streamRef.value.getTracks().forEach(track => track.stop());
      streamRef.value = null;
    }

    // 关闭音频上下文
    if (audioContextRef.value) {
      try {
        await audioContextRef.value.close();
      } catch (err) {
        console.error("关闭音频上下文失败:", err);
      }
      audioContextRef.value = null;
    }

    // 关闭WebSocket连接
    if (wsRef.value) {
      wsRef.value.close();
      wsRef.value = null;
    }

    // 清除当前消息ID
    currentMsgIdRef.value = null;

    // 重置录音文本（不发送识别结果）
    recordingText.value = "请开始发言";

    // 通知状态变化
    notifyStateChange();

    // 显示取消提示
    ElMessage.info("已取消本次录音");

    console.log("录音取消成功");
  }

  /**
   * 重置录音状态
   */
  function resetRecordingState() {
    isRecording.value = false;
    recordingTime.value = 0;
    recordingText.value = "请开始发言";

    // 停止计时器
    if (recordingInterval.value) {
      clearInterval(recordingInterval.value);
      recordingInterval.value = null;
    }

    // 通知状态变化
    notifyStateChange();
  }

  /**
   * 清理资源
   */
  function cleanup() {
    console.log("清理录音资源...");

    // 停止录音计时器
    if (recordingInterval.value) {
      clearInterval(recordingInterval.value);
      recordingInterval.value = null;
    }

    // 如果正在录音，先停止
    if (isRecording.value) {
      // 停止音频流
      if (streamRef.value) {
        streamRef.value.getTracks().forEach(track => track.stop());
        streamRef.value = null;
      }

      // 关闭音频上下文
      if (audioContextRef.value) {
        audioContextRef.value.close().catch(err => {
          console.error("关闭音频上下文失败:", err);
        });
        audioContextRef.value = null;
      }

      // 关闭WebSocket连接
      if (wsRef.value) {
        wsRef.value.close();
        wsRef.value = null;
      }

      isRecording.value = false;
    }

    // 清除消息ID
    currentMsgIdRef.value = null;

    // 重置状态
    resetRecordingState();

    console.log("录音资源清理完成");
  }

  /**
   * 检查录音权限
   */
  async function checkRecordingPermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error("录音权限检查失败:", error);
      return false;
    }
  }

  // 组件卸载时清理资源
  onBeforeUnmount(() => {
    cleanup();
  });

  return {
    // 状态
    isRecording,
    recordingTime,
    recordingText,
    recordingStartTime,

    // 方法
    startRecording,
    stopRecording,
    cancelRecording,
    cleanup,
    setCallbacks,
    checkRecordingPermission,
    resetRecordingState
  };
}
