# 违禁词检测功能

## 📖 功能概述

违禁词检测功能是AI质检练习模块中的核心安全功能，通过EventStream接口实时检测用户输入内容是否包含违禁词汇，确保练习过程中的内容合规性。该功能支持实时检测、智能提示和调试工具。

## ✨ 核心特性

- 🔍 **实时检测**: 基于EventStream的流式违禁词检测
- ⚡ **智能提示**: 检测到违禁词时显示友好的用户提示
- 🛠️ **调试工具**: 完整的调试工具集，支持多种测试场景
- 🔄 **状态管理**: 完善的检测状态跟踪和管理
- 📊 **性能优化**: 避免重复检测，优化用户体验
- 🎯 **错误处理**: 完善的异常处理和降级策略

## 🚀 快速开始

### 基本用法

```vue
<template>
  <div>
    <!-- 在练习组件中使用 -->
    <input 
      v-model="userInput" 
      @blur="handleInputBlur"
      placeholder="请输入内容..."
    />
  </div>
</template>

<script setup>
import { useForbiddenWordCheck } from './hooks/useForbiddenWordCheck'

const { 
  checkForbiddenWordsAsync, 
  resetCheckState,
  getCheckState 
} = useForbiddenWordCheck()

const userInput = ref('')

// 输入失焦时检测违禁词
async function handleInputBlur() {
  if (userInput.value.trim()) {
    await checkForbiddenWordsAsync(userInput.value)
  }
}

// 重置检测状态（如开始新对话时）
function startNewConversation() {
  resetCheckState()
}
</script>
```

### Hook API

| 方法 | 类型 | 说明 |
|------|------|------|
| checkForbiddenWords | `(content: string) => Promise<boolean>` | 同步检测违禁词，返回是否包含违禁词 |
| checkForbiddenWordsAsync | `(content: string) => Promise<void>` | 异步检测违禁词，不阻塞主流程 |
| getCheckState | `() => CheckState` | 获取当前检测状态 |
| resetCheckState | `() => void` | 重置检测状态和清理提示 |
| clearForbiddenWordMessage | `() => void` | 手动清除违禁词提示 |

### 状态接口

```typescript
interface ForbiddenWordCheckState {
  isChecking: boolean;           // 是否正在检测
  lastCheckTime: number;         // 最后检测时间
  checkCount: number;            // 检测次数
  hasForbiddenWordMessage: boolean; // 是否已显示违禁词提示
}
```

## 🔧 技术实现

### 核心原理

1. **EventStream处理**: 使用Fetch API处理Server-Sent Events格式的流式响应
2. **智能解析**: 支持多种数据格式（JSON、纯文本、SSE格式）
3. **状态管理**: 维护检测状态，避免重复提示和资源泄漏
4. **错误降级**: 多层错误处理，确保功能稳定性

### 关键方法

```typescript
// 核心检测方法
async function checkForbiddenWords(content: string): Promise<boolean>

// EventStream数据处理
function processEventStreamChunk(chunk: string): {
  result: string | null;
  finished: boolean;
}

// 违禁词提示显示
function showForbiddenWordMessage(): void
```

### 检测流程

```mermaid
graph TD
    A[用户输入内容] --> B[调用checkForbiddenWords]
    B --> C[发送EventStream请求]
    C --> D[处理流式响应]
    D --> E{检测结果}
    E -->|通过| F[返回false]
    E -->|违禁词| G[显示提示]
    G --> H[返回true]
    D --> I{流结束?}
    I -->|否| D
    I -->|是| J[完成检测]
```

## 🛠️ 调试工具

### 调试工具使用

项目集成了完整的违禁词检测调试工具，在开发环境中可通过浏览器控制台使用：

```javascript
// 查看帮助信息
forbiddenWordDebugger.help()

// 测试正常内容
await forbiddenWordDebugger.testNormalContent()

// 测试违禁词内容
await forbiddenWordDebugger.testForbiddenContent()

// 测试自定义内容
await forbiddenWordDebugger.testCustomContent("你的测试内容")

// 运行批量测试
await forbiddenWordDebugger.runBatchTest()

// 获取检测状态
forbiddenWordDebugger.getCheckState()

// 重置状态
forbiddenWordDebugger.resetState()
```

### 调试功能特性

- **多场景测试**: 支持正常内容、违禁词内容、空内容等多种测试场景
- **批量测试**: 一键运行多个测试用例，生成详细报告
- **性能监控**: 记录每次检测的耗时和状态
- **状态管理**: 实时查看和重置检测状态

## 🎯 使用场景

- **练习对话检测**: 在AI质检练习过程中检测用户输入
- **内容合规审查**: 确保用户生成内容符合平台规范
- **实时内容过滤**: 在聊天、评论等场景中实时过滤违禁内容
- **开发调试**: 使用调试工具验证检测功能的准确性

## 📋 更新日志

### v1.0.0 (2025-01-27)

- ✨ 新增基于EventStream的违禁词检测功能
- ✨ 新增智能违禁词提示机制
- ✨ 新增完整的调试工具集
- ⚡ 优化检测性能和用户体验
- 🔧 完善错误处理和状态管理
- 🛠️ 集成到AI质检练习模块

## ⚠️ 注意事项

### 使用建议

1. **异步检测优先**: 推荐使用`checkForbiddenWordsAsync`避免阻塞用户操作
2. **状态重置**: 在开始新对话或重置练习时调用`resetCheckState`
3. **错误处理**: 检测失败时不应影响主要功能流程
4. **性能考虑**: 避免对相同内容重复检测

### 技术限制

- 依赖后端EventStream接口的稳定性
- 网络异常时可能影响检测准确性
- 检测结果依赖于后端违禁词库的完整性

### 安全考虑

- 违禁词提示不会泄露具体的违禁词内容
- 检测过程中的敏感信息不会在前端缓存
- 支持认证头部确保接口安全性
