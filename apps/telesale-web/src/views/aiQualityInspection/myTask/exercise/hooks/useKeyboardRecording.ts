/**
 * @Date         : 2025-01-27 16:30:00
 * @Description  : 键盘录音功能Hook（支持取消录音）
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref, onMounted, onBeforeUnmount } from "vue";

/**
 * 键盘录音Hook
 * @returns 键盘录音相关的状态和方法
 */
export function useKeyboardRecording() {
  // 状态变量
  const isSpacePressed = ref(false);
  const spaceKeyRecording = ref(false);
  const isEnabled = ref(true);

  // 事件回调
  const onStartRecording = ref<() => void>(() => {});
  const onStopRecording = ref<() => void>(() => {});
  const onCancelRecording = ref<() => void>(() => {});

  /**
   * 设置录音回调函数
   * @param callbacks 回调函数对象
   */
  function setCallbacks(callbacks: {
    onStartRecording?: () => void;
    onStopRecording?: () => void;
    onCancelRecording?: () => void;
  }) {
    if (callbacks.onStartRecording) {
      onStartRecording.value = callbacks.onStartRecording;
    }
    if (callbacks.onStopRecording) {
      onStopRecording.value = callbacks.onStopRecording;
    }
    if (callbacks.onCancelRecording) {
      onCancelRecording.value = callbacks.onCancelRecording;
    }
  }

  /**
   * 检查当前焦点是否在输入框中
   * @param target 事件目标
   * @returns 是否在输入框中
   */
  function isInputFocused(target: EventTarget | null): boolean {
    if (!target) return false;

    const element = target as HTMLElement;
    const tagName = element.tagName.toLowerCase();

    return (
      tagName === "input" ||
      tagName === "textarea" ||
      element.contentEditable === "true" ||
      element.getAttribute("contenteditable") === "true"
    );
  }

  /**
   * 处理键盘按下事件
   * @param event 键盘事件
   */
  function handleKeyDown(event: KeyboardEvent) {
    // 检查功能是否启用
    if (!isEnabled.value) return;

    // 处理空格键录音
    if (event.code === "Space" && !isInputFocused(event.target)) {
      event.preventDefault();

      // 避免重复触发
      if (isSpacePressed.value) return;

      isSpacePressed.value = true;

      // 开始录音
      if (!spaceKeyRecording.value) {
        spaceKeyRecording.value = true;
        console.log("空格键按下，开始录音");
        onStartRecording.value();
      }
    }

    // 处理Escape键取消录音
    if (event.code === "Escape" && !isInputFocused(event.target)) {
      event.preventDefault();

      // 如果正在通过空格键录音，则取消录音
      if (spaceKeyRecording.value) {
        spaceKeyRecording.value = false;
        isSpacePressed.value = false;
        console.log("Escape键按下，取消录音");
        onCancelRecording.value();
      }
    }
  }

  /**
   * 处理键盘松开事件
   * @param event 键盘事件
   */
  function handleKeyUp(event: KeyboardEvent) {
    if (event.code === "Space") {
      event.preventDefault();
      isSpacePressed.value = false;

      // 如果是通过空格键开始的录音，则停止录音
      if (spaceKeyRecording.value) {
        spaceKeyRecording.value = false;
        console.log("空格键松开，停止录音");
        onStopRecording.value();
      }
    }
  }

  /**
   * 启用键盘录音功能
   */
  function enable() {
    isEnabled.value = true;
    console.log("键盘录音功能已启用");
  }

  /**
   * 禁用键盘录音功能
   */
  function disable() {
    isEnabled.value = false;
    // 重置状态
    isSpacePressed.value = false;
    spaceKeyRecording.value = false;
    console.log("键盘录音功能已禁用");
  }

  /**
   * 强制停止空格键录音
   */
  function forceStopSpaceRecording() {
    if (spaceKeyRecording.value) {
      spaceKeyRecording.value = false;
      isSpacePressed.value = false;
      console.log("强制停止空格键录音");
      onStopRecording.value();
    }
  }

  /**
   * 强制取消空格键录音
   */
  function forceCancelSpaceRecording() {
    if (spaceKeyRecording.value) {
      spaceKeyRecording.value = false;
      isSpacePressed.value = false;
      console.log("强制取消空格键录音");
      onCancelRecording.value();
    }
  }

  /**
   * 初始化键盘事件监听器
   */
  function initEventListeners() {
    document.addEventListener("keydown", handleKeyDown);
    document.addEventListener("keyup", handleKeyUp);
    console.log("键盘事件监听器已初始化");
  }

  /**
   * 移除键盘事件监听器
   */
  function removeEventListeners() {
    document.removeEventListener("keydown", handleKeyDown);
    document.removeEventListener("keyup", handleKeyUp);
    console.log("键盘事件监听器已移除");
  }

  // 自动初始化和清理
  onMounted(() => {
    initEventListeners();
  });

  onBeforeUnmount(() => {
    removeEventListeners();
  });

  return {
    // 状态
    isSpacePressed,
    spaceKeyRecording,
    isEnabled,

    // 方法
    setCallbacks,
    enable,
    disable,
    forceStopSpaceRecording,
    forceCancelSpaceRecording,
    initEventListeners,
    removeEventListeners
  };
}
