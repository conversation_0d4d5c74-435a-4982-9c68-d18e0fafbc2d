/**
 * @Date         : 2025-01-27 20:30:00
 * @Description  : 违禁词检测Hook - 处理EventStream接口
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
 */

import { ref } from "vue";
import baseURL from "/@/api/url";
import {
  showForbiddenWordError,
  closeForbiddenWordMessage,
  isForbiddenWordMessageShowing
} from "../utils/forbiddenWordMessage";
import type { ForbiddenWordMessageHandler } from "../utils/forbiddenWordMessage";

/**
 * 违禁词检测结果枚举
 */
export enum ForbiddenWordResult {
  PASS = "通过",
  FORBIDDEN = "违禁词"
}

/**
 * 违禁词检测状态
 */
interface ForbiddenWordCheckState {
  isChecking: boolean;
  lastCheckTime: number;
  checkCount: number;
  hasForbiddenWordMessage: boolean; // 是否已显示违禁词提示
  forbiddenWordMessageInstance: ForbiddenWordMessageHandler | null; // 违禁词提示实例
  isShowingForbiddenWordMessage: boolean; // 是否正在显示违禁词提示（用于ESC键冲突处理）
}

/**
 * 违禁词检测Hook
 * @returns 违禁词检测相关的状态和方法
 */
export function useForbiddenWordCheck() {
  // 状态变量
  const checkState = ref<ForbiddenWordCheckState>({
    isChecking: false,
    lastCheckTime: 0,
    checkCount: 0,
    hasForbiddenWordMessage: false,
    forbiddenWordMessageInstance: null,
    isShowingForbiddenWordMessage: false
  });

  /**
   * 显示违禁词提示 - 确保在本次对话中只显示一次
   */
  function showForbiddenWordMessage() {
    // 如果已经显示了违禁词提示，不重复显示
    if (checkState.value.hasForbiddenWordMessage) {
      console.log("违禁词提示已存在，跳过重复显示");
      return;
    }

    console.log("显示自定义违禁词提示");

    // 使用自定义违禁词提示组件
    const messageInstance = showForbiddenWordError(
      "监测到当前发言涉及侮辱、政治等敏感信息，请回归业务场景沟通~",
      {
        onClose: () => {
          // 当提示被关闭时，重置状态
          checkState.value.hasForbiddenWordMessage = false;
          checkState.value.forbiddenWordMessageInstance = null;
          checkState.value.isShowingForbiddenWordMessage = false;
          console.log("违禁词提示已关闭");
        }
      }
    );

    // 保存提示实例和状态
    checkState.value.hasForbiddenWordMessage = true;
    checkState.value.forbiddenWordMessageInstance = messageInstance;
    checkState.value.isShowingForbiddenWordMessage = true;
  }

  /**
   * 处理EventStream数据块
   * @param chunk 数据块
   * @returns 处理结果 { result: 检测结果, finished: 是否完成 }
   */
  function processEventStreamChunk(chunk: string): {
    result: string | null;
    finished: boolean;
  } {
    try {
      console.log("处理违禁词检测数据块:", chunk);

      // 处理Server-Sent Events格式
      if (chunk.startsWith("data: ")) {
        const dataStr = chunk.substring(6).trim();

        // 检查是否为结束标记
        if (dataStr === "[DONE]" || dataStr === "EOF" || dataStr === "") {
          console.log("检测到流结束标记:", dataStr);
          return { result: null, finished: true };
        }

        // 尝试解析JSON数据
        try {
          const data = JSON.parse(dataStr);
          console.log("解析的JSON数据:", data);

          // 处理包含message字段的数据
          if (data.message) {
            console.log("检测到message字段:", data.message);
            return { result: data.message, finished: false };
          }

          // 处理其他可能的字段
          if (data.result || data.content) {
            const result = data.result || data.content;
            console.log("检测到result/content字段:", result);
            return { result: result, finished: false };
          }
        } catch (jsonError) {
          // 如果不是JSON格式，直接作为文本内容处理
          if (dataStr && dataStr !== "EOF") {
            console.log("直接使用文本内容:", dataStr);
            return { result: dataStr, finished: false };
          }
        }
      } else {
        // 处理非SSE格式的数据
        if (chunk.trim() === "EOF") {
          console.log("检测到EOF结束标记");
          return { result: null, finished: true };
        }

        // 尝试解析为JSON
        try {
          const data = JSON.parse(chunk);
          console.log("解析的非SSE JSON数据:", data);

          // 处理包含message字段的数据
          if (data.message) {
            console.log("检测到message字段:", data.message);
            return { result: data.message, finished: false };
          }

          // 处理其他可能的字段
          if (data.result || data.content) {
            const result = data.result || data.content;
            console.log("检测到result/content字段:", result);
            return { result: result, finished: false };
          }
        } catch (jsonError) {
          // 如果不是JSON格式，直接作为文本内容处理
          if (chunk.trim() && chunk.trim() !== "EOF") {
            console.log("直接使用非SSE文本内容:", chunk.trim());
            return { result: chunk.trim(), finished: false };
          }
        }
      }
    } catch (error) {
      console.warn("解析违禁词检测数据块失败:", chunk, error);
    }

    return { result: null, finished: false };
  }

  /**
   * 检查违禁词 - 处理EventStream响应
   * @param content 需要检查的内容
   * @returns Promise<boolean> 是否检测到违禁词
   */
  async function checkForbiddenWords(content: string): Promise<boolean> {
    if (!content.trim()) {
      return false;
    }

    // 更新检测状态
    checkState.value.isChecking = true;
    checkState.value.lastCheckTime = Date.now();
    checkState.value.checkCount++;

    console.log("开始违禁词检测:", {
      contentLength: content.length,
      checkCount: checkState.value.checkCount
    });

    try {
      const detectionResults: string[] = []; // 收集所有检测结果
      let isStreamFinished = false;
      let hasForbiddenWords = false;

      console.log("准备调用违禁词检测接口:", {
        url: `${baseURL.robot}/admin/workerTrainingTaskCourse/check`,
        content: content.substring(0, 100) + (content.length > 100 ? "..." : "")
      });

      // 调用EventStream接口
      await new Promise<void>((resolve, reject) => {
        // 获取认证头部
        const ShadowAuthorization = localStorage.getItem(
          "whcrmShadowAuthorization"
        );
        const headers: Record<string, string> = {
          "Content-Type": "application/json",
          Accept: "text/event-stream",
          "Cache-Control": "no-cache"
        };

        if (ShadowAuthorization) {
          headers["ShadowAuthorization"] = ShadowAuthorization;
        }

        console.log("请求头部:", headers);

        // 创建请求
        fetch(`${baseURL.robot}/admin/workerTrainingTaskCourse/check`, {
          method: "POST",
          headers,
          body: JSON.stringify({ question: content })
        })
          .then(response => {
            console.log(
              "违禁词检测响应状态:",
              response.status,
              response.statusText
            );
            console.log(
              "响应Content-Type:",
              response.headers.get("content-type")
            );

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const reader = response.body?.getReader();
            if (!reader) {
              throw new Error("Failed to get response reader");
            }

            const decoder = new TextDecoder();

            function readStream(): Promise<void> {
              return reader.read().then(({ done, value }) => {
                if (done) {
                  isStreamFinished = true;
                  resolve();
                  return;
                }

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split("\n");

                for (const line of lines) {
                  if (line.trim()) {
                    const result = processEventStreamChunk(line);
                    if (result.result) {
                      detectionResults.push(result.result);
                      console.log("违禁词检测实时结果:", result.result);

                      // 检查是否为违禁词
                      if (result.result === ForbiddenWordResult.FORBIDDEN) {
                        hasForbiddenWords = true;
                        console.log("检测到违禁词!");
                      } else if (result.result === ForbiddenWordResult.PASS) {
                        console.log("检测通过");
                      } else {
                        // 处理其他可能的返回值，如"违禁"、"通过"等
                        console.log("检测结果:", result.result);
                        if (
                          result.result.includes("违禁") ||
                          result.result === "违禁词"
                        ) {
                          hasForbiddenWords = true;
                          console.log("检测到违禁词(模糊匹配)!");
                        }
                      }
                    }
                    if (result.finished) {
                      isStreamFinished = true;
                      resolve();
                      return;
                    }
                  }
                }

                return readStream();
              });
            }

            return readStream();
          })
          .catch(error => {
            console.error("违禁词检测请求失败:", error);
            reject(error);
          });
      });

      console.log("违禁词检测完成:", {
        results: detectionResults,
        hasForbiddenWords,
        isStreamFinished,
        totalResults: detectionResults.length
      });

      // 如果检测到违禁词，显示提示
      if (hasForbiddenWords) {
        showForbiddenWordMessage();
      }

      return hasForbiddenWords;
    } catch (error) {
      console.warn("违禁词检测异常:", error);

      // 检查是否是因为违禁词导致的异常
      const errorResponse = error as any;

      // 如果是HTTP错误且状态码不是网络错误，可能是违禁词检测
      if (
        errorResponse?.response?.status &&
        errorResponse.response.status < 500
      ) {
        showForbiddenWordMessage();
        return true;
      }

      // 其他错误（如网络错误、服务器错误）不显示违禁词提示
      console.warn("违禁词检测服务异常，跳过提示");
      return false;
    } finally {
      // 重置检测状态
      checkState.value.isChecking = false;
    }
  }

  /**
   * 异步检查违禁词 - 不阻塞主流程
   * @param content 需要检查的内容
   */
  async function checkForbiddenWordsAsync(content: string): Promise<void> {
    try {
      await checkForbiddenWords(content);
    } catch (error) {
      console.warn("违禁词异步检测失败:", error);
    }
  }

  /**
   * 获取检测状态
   */
  function getCheckState() {
    return {
      isChecking: checkState.value.isChecking,
      lastCheckTime: checkState.value.lastCheckTime,
      checkCount: checkState.value.checkCount
    };
  }

  /**
   * 重置检测状态
   */
  function resetCheckState() {
    // 如果有违禁词提示实例，先关闭它
    if (checkState.value.forbiddenWordMessageInstance) {
      checkState.value.forbiddenWordMessageInstance.close();
    }

    checkState.value = {
      isChecking: false,
      lastCheckTime: 0,
      checkCount: 0,
      hasForbiddenWordMessage: false,
      forbiddenWordMessageInstance: null,
      isShowingForbiddenWordMessage: false
    };
  }

  /**
   * 清除违禁词提示
   */
  function clearForbiddenWordMessage() {
    if (checkState.value.forbiddenWordMessageInstance) {
      checkState.value.forbiddenWordMessageInstance.close();
      checkState.value.hasForbiddenWordMessage = false;
      checkState.value.forbiddenWordMessageInstance = null;
      checkState.value.isShowingForbiddenWordMessage = false;
      console.log("手动清除违禁词提示");
    } else {
      // 如果没有实例引用，尝试使用全局方法关闭
      closeForbiddenWordMessage();
      checkState.value.hasForbiddenWordMessage = false;
      checkState.value.forbiddenWordMessageInstance = null;
      checkState.value.isShowingForbiddenWordMessage = false;
      console.log("通过全局方法清除违禁词提示");
    }
  }

  /**
   * 检查是否正在显示违禁词提示
   */
  function isShowingForbiddenWordMessage() {
    // 优先使用本地状态，如果本地状态为false，再检查全局状态
    return (
      checkState.value.isShowingForbiddenWordMessage ||
      isForbiddenWordMessageShowing()
    );
  }

  return {
    // 状态
    checkState,

    // 方法
    checkForbiddenWords,
    checkForbiddenWordsAsync,
    getCheckState,
    resetCheckState,
    clearForbiddenWordMessage,
    showForbiddenWordMessage,
    isShowingForbiddenWordMessage
  };
}
