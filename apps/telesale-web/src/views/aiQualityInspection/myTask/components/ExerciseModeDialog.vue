<!--
 * @Date         : 2025-01-27 18:00:00
 * @Description  : AI练习模式选择弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="dialogVisible"
    title="选择AI练习模式"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    @close="handleClose"
  >
    <div class="mode-selection-container">
      <div class="mode-description mb-20px">
        <p class="text-gray-600 text-14px leading-relaxed">
          请选择您希望使用的AI对练模式，不同模式将提供不同的交互体验。
        </p>
      </div>

      <div class="mode-options">
        <!-- 语音+文本对练模式 -->
        <div
          class="mode-option"
          :class="{ active: selectedMode === ExerciseMode.VOICE_TEXT }"
          @click="selectMode(ExerciseMode.VOICE_TEXT)"
        >
          <div class="mode-icon">
            <el-icon size="24" color="#1890ff">
              <Microphone />
            </el-icon>
          </div>
          <div class="mode-content">
            <h3 class="mode-title">语音+文本对练</h3>
            <p class="mode-desc">该模式进行对练时，支持语音合成和文本显示</p>
            <div class="mode-features">
              <span class="feature-tag">语音合成</span>
              <span class="feature-tag">文本显示</span>
              <span class="feature-tag">完整体验</span>
            </div>
          </div>
          <div class="mode-radio">
            <div
              class="custom-radio"
              :class="{ checked: selectedMode === ExerciseMode.VOICE_TEXT }"
            >
              <div class="radio-inner" />
            </div>
          </div>
        </div>

        <!-- 纯文本对练模式 -->
        <div
          class="mode-option"
          :class="{ active: selectedMode === ExerciseMode.TEXT_ONLY }"
          @click="selectMode(ExerciseMode.TEXT_ONLY)"
        >
          <div class="mode-icon">
            <el-icon size="24" color="#52c41a">
              <ChatLineRound />
            </el-icon>
          </div>
          <div class="mode-content">
            <h3 class="mode-title">纯文本对练</h3>
            <p class="mode-desc">
              使用该模式进行对练时，仅返回纯文本内容，提高对练效率
            </p>
            <div class="mode-features">
              <span class="feature-tag">纯文本</span>
              <span class="feature-tag">快速响应</span>
              <span class="feature-tag">高效练习</span>
            </div>
          </div>
          <div class="mode-radio">
            <div
              class="custom-radio"
              :class="{ checked: selectedMode === ExerciseMode.TEXT_ONLY }"
            >
              <div class="radio-inner" />
            </div>
          </div>
        </div>
      </div>

      <div class="mode-note mt-20px">
        <div class="note-item">
          <el-icon class="text-orange-500 mr-5px">
            <InfoFilled />
          </el-icon>
          <span class="text-12px text-gray-500">
            注意：销售侧在进行对练时，仍需要通过发送语音进行对练
          </span>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :disabled="!selectedMode"
        >
          开始练习
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineModel, defineEmits } from "vue";
import { Microphone, ChatLineRound, InfoFilled } from "@element-plus/icons-vue";
import { ExerciseMode } from "../exercise/store";

// 使用defineModel实现双向绑定
const dialogVisible = defineModel<boolean>("visible", { default: false });

// 定义事件
const emit = defineEmits<{
  confirm: [mode: ExerciseMode];
  close: [];
}>();

// 选中的模式
const selectedMode = ref<ExerciseMode>(ExerciseMode.VOICE_TEXT);

/**
 * 选择模式
 */
const selectMode = (mode: ExerciseMode) => {
  selectedMode.value = mode;
};

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedMode.value) {
    emit("confirm", selectedMode.value);
    dialogVisible.value = false;
  }
};

/**
 * 关闭弹窗
 */
const handleClose = () => {
  emit("close");
  dialogVisible.value = false;
};
</script>

<style scoped>
.mode-selection-container {
  padding: 10px 0;
}

.mode-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mode-option {
  display: flex;
  align-items: flex-start;
  padding: 20px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.mode-option:hover {
  border-color: #1890ff;
  background: #f0f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
}

.mode-option.active {
  border-color: #1890ff;
  background: #f0f9ff;
  box-shadow: 0 0 0 1px rgba(24, 144, 255, 0.2);
}

.mode-icon {
  margin-right: 16px;
  margin-top: 2px;
}

.mode-content {
  flex: 1;
}

.mode-title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.mode-desc {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.mode-features {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.feature-tag {
  display: inline-block;
  padding: 4px 8px;
  background: #e1f5fe;
  color: #0277bd;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid #b3e5fc;
}

.mode-radio {
  margin-left: 16px;
  margin-top: 2px;
}

.custom-radio {
  width: 20px;
  height: 20px;
  border: 2px solid #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.custom-radio:hover {
  border-color: #1890ff;
}

.custom-radio.checked {
  border-color: #1890ff;
  background: #1890ff;
}

.radio-inner {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.custom-radio.checked .radio-inner {
  opacity: 1;
}

.mode-note {
  padding: 12px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 8px;
}

.note-item {
  display: flex;
  align-items: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 使用unocss实现的样式 */
.mb-20px {
  margin-bottom: 20px;
}

.mt-20px {
  margin-top: 20px;
}

.mr-5px {
  margin-right: 5px;
}

.text-gray-600 {
  color: #6b7280;
}

.text-gray-500 {
  color: #9ca3af;
}

.text-orange-500 {
  color: #f97316;
}

.text-14px {
  font-size: 14px;
}

.text-12px {
  font-size: 12px;
}

.leading-relaxed {
  line-height: 1.625;
}
</style>
