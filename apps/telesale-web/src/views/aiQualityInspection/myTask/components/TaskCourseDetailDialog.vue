<!--
 * @Date         : 2025-05-21 15:00:00
 * @Description  : 任务课程详情弹窗
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-dialog
    v-model="visible"
    title="任务详情"
    width="800px"
    destroy-on-close
    @open="fetchTaskCourses"
  >
    <div class="task-course-detail">
      <!-- 课程卡片列表 -->
      <div v-loading="loading" class="course-cards flex flex-wrap gap-20px">
        <el-card
          v-for="course in courseList"
          :key="course.trainingTaskCourseId"
          class="course-card w-full"
          shadow="hover"
        >
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <!-- 课程标题 -->
              <div class="text-16px font-bold mb-10px flex items-center">
                <span class="mr-10px">
                  {{ course.course?.course?.name || "未命名课程" }}
                </span>

                <!-- 已完成标志 -->
                <template v-if="course.finished">
                  <el-tag type="success" class="mr-10px">已完成</el-tag>
                  <!-- 评级结果 -->
                  <span v-if="course.score" class="text-14px">
                    评级
                    <span class="text-blue-500 font-bold">
                      {{ course.score }}
                    </span>
                    级
                  </span>
                </template>
              </div>

              <!-- 练习说明 -->
              <div class="text-14px text-gray-500">
                <el-tooltip
                  v-if="course.course?.course?.target"
                  :content="course.course?.course.target"
                  placement="bottom"
                  popper-class="task-course-tooltip-max-width"
                >
                  <div class="truncate max-w-500px inline-block">
                    练习说明：{{ course.course?.course?.target || "无" }}
                  </div>
                </el-tooltip>
                <div v-else class="truncate max-w-500px">练习说明：无</div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex flex-col gap-10px">
              <!-- 管理员视角：显示不同的按钮逻辑 -->
              <template v-if="isAdminView">
                <!-- 已完成的课程显示详情按钮 -->
                <el-button
                  v-if="course.finished"
                  type="primary"
                  @click="handleViewCourseDetail(course)"
                >
                  详情
                </el-button>
                <!-- 未完成的课程显示红色未完成文字 -->
                <span v-else class="text-red-500 font-bold text-14px">
                  未完成
                </span>
              </template>
              <!-- 员工视角：原有逻辑 -->
              <template v-else>
                <!-- 任务已结束：只有已完成的课程显示详情按钮，未完成的不显示按钮 -->
                <template v-if="taskStatus === TASK_STATUS.END">
                  <el-button
                    v-if="course.finished"
                    type="primary"
                    @click="handleViewCourseDetail(course)"
                  >
                    详情
                  </el-button>
                </template>
                <!-- 任务进行中：按原有逻辑显示按钮 -->
                <template v-else>
                  <el-button
                    v-if="course.finished"
                    type="primary"
                    @click="handleViewCourseDetail(course)"
                  >
                    详情
                  </el-button>
                  <el-button
                    v-else
                    type="primary"
                    @click="handlePracticeCourse(course)"
                  >
                    练习
                  </el-button>
                </template>
              </template>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </el-dialog>

  <!-- 练习抽屉 -->
  <ExerciseDrawer
    v-model:visible="exerciseDrawerVisible"
    @finish="handleExerciseFinish"
  />

  <!-- 评估抽屉 -->
  <EvaluationDrawer
    v-if="currentCourse"
    v-model:visible="evaluationDrawerVisible"
    :course-info="currentCourse"
  />

  <!-- 练习模式选择弹窗 -->
  <ExerciseModeDialog
    v-model:visible="exerciseModeDialogVisible"
    @confirm="handleModeConfirm"
    @close="handleModeDialogClose"
  />
</template>

<script setup lang="ts">
import { ref, defineModel, defineEmits, nextTick } from "vue";
import { ElMessage } from "element-plus";
import {
  getWorkerTrainingTaskInfo,
  WorkerTrainingTaskCourseInstance,
  TASK_STATUS
} from "/@/api/AIQualityInspection/taskManagement";
import ExerciseDrawer from "../exercise/index.vue";
import EvaluationDrawer from "../evaluation/index.vue";
import ExerciseModeDialog from "./ExerciseModeDialog.vue";
import { useExerciseStore, ExerciseMode } from "../exercise/store";

// 使用defineModel实现双向绑定
const visible = defineModel<boolean>("visible");

// 定义props
const props = defineProps({
  taskId: {
    type: Number,
    default: undefined
  },
  /** 是否为管理员视角 */
  isAdminView: {
    type: Boolean,
    default: false
  }
});

// 任务名称和状态
const taskName = ref("");
const taskStatus = ref("");

// 课程列表
const courseList = ref<WorkerTrainingTaskCourseInstance[]>([]);
const loading = ref(false);

// 抽屉控制
const exerciseDrawerVisible = ref(false);
const evaluationDrawerVisible = ref(false);
const currentCourse = ref<WorkerTrainingTaskCourseInstance | null>(null);

// 模式选择弹窗控制
const exerciseModeDialogVisible = ref(false);
const pendingCourse = ref<WorkerTrainingTaskCourseInstance | null>(null);

// 使用练习状态管理
const exerciseStore = useExerciseStore();

// 定义事件
const emit = defineEmits(["close", "refresh"]);

// 获取任务课程列表
async function fetchTaskCourses() {
  if (!props.taskId) {
    ElMessage.error("任务ID不能为空");
    visible.value = false;
    return;
  }

  try {
    loading.value = true;
    const res = await getWorkerTrainingTaskInfo(props.taskId);

    // 处理API返回的数据
    const data = res.data;
    const taskInfo = data.item;

    if (!taskInfo) {
      ElMessage.error("获取任务详情失败");
      return;
    }

    // 设置任务名称和状态
    taskName.value = taskInfo.task?.name || "未命名任务";
    taskStatus.value = taskInfo.task?.status || "";

    // 设置课程列表
    courseList.value = taskInfo.courses || [];
  } catch (error) {
    console.error("获取任务课程列表失败:", error);
    ElMessage.error("获取任务课程列表失败");
  } finally {
    loading.value = false;
  }
}

// 查看课程详情
function handleViewCourseDetail(course: WorkerTrainingTaskCourseInstance) {
  if (!course) {
    ElMessage.error("课程信息不能为空");
    return;
  }

  if (!course.workerTrainingTaskId || !course.trainingTaskCourseId) {
    ElMessage.error("课程信息不完整，缺少必要的ID信息");
    console.error("课程信息不完整:", course);
    return;
  }

  console.log("查看课程详情:", course);

  // 确保练习抽屉已关闭，避免计时器冲突
  exerciseDrawerVisible.value = false;

  // 设置当前课程信息
  currentCourse.value = course;

  // 使用nextTick确保数据设置完成后再打开抽屉
  nextTick(() => {
    evaluationDrawerVisible.value = true;
  });
}

// 练习课程
function handlePracticeCourse(course: WorkerTrainingTaskCourseInstance) {
  if (!course) {
    ElMessage.error("课程信息不能为空");
    return;
  }

  if (!course.workerTrainingTaskId || !course.trainingTaskCourseId) {
    ElMessage.error("课程信息不完整，缺少必要的ID信息");
    console.error("课程信息不完整:", course);
    return;
  }

  console.log("点击练习课程，显示模式选择弹窗:", course);

  // 保存待练习的课程信息
  pendingCourse.value = course;

  // 确保评估抽屉已关闭
  evaluationDrawerVisible.value = false;

  // 显示模式选择弹窗
  exerciseModeDialogVisible.value = true;
}

/**
 * 处理模式选择确认
 */
function handleModeConfirm(mode: ExerciseMode) {
  if (!pendingCourse.value) {
    ElMessage.error("课程信息丢失，请重新选择");
    return;
  }

  console.log("确认练习模式:", mode, "课程:", pendingCourse.value);

  // 设置练习模式
  exerciseStore.setExerciseMode(mode);

  // 设置store中的课程信息
  exerciseStore.setCourseInfo(pendingCourse.value);

  // 确保loading状态被清除
  exerciseStore.loading.value = false;

  // 保留currentCourse用于评估抽屉
  currentCourse.value = pendingCourse.value;

  // 清空待练习课程
  pendingCourse.value = null;

  // 打开练习抽屉
  exerciseDrawerVisible.value = true;
}

/**
 * 处理模式选择弹窗关闭
 */
function handleModeDialogClose() {
  // 清空待练习课程
  pendingCourse.value = null;
  console.log("取消练习模式选择");
}

// 处理练习完成
async function handleExerciseFinish(data: any) {
  console.log("练习完成，接收到的数据:", data);

  // 关闭练习抽屉
  exerciseDrawerVisible.value = false;

  // 刷新任务课程数据
  await fetchTaskCourses();

  // 通知父组件刷新数据
  emit("refresh");

  ElMessage.success("课程练习已完成！");

  // 检查是否需要打开评估抽屉
  if (data?.shouldOpenEvaluation && data?.courseInfo) {
    console.log("自动打开评估抽屉，课程信息:", data.courseInfo);

    // 设置当前课程信息
    currentCourse.value = data.courseInfo;

    // 延迟一下再打开评估抽屉，确保练习抽屉已经完全关闭
    setTimeout(() => {
      evaluationDrawerVisible.value = true;
    }, 300);
  }
}
</script>

<style scoped>
.course-card {
  transition: all 0.3s;
  margin-bottom: 16px;
  border-radius: 8px;
}

.course-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
}

/* 使用unocss实现的样式 */
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: flex-start;
}
.justify-between {
  justify-content: space-between;
}
.gap-20px {
  gap: 20px;
}
.m-0 {
  margin: 0;
}
.mb-10px {
  margin-bottom: 10px;
}
.mb-20px {
  margin-bottom: 20px;
}
.mr-10px {
  margin-right: 10px;
}
.text-14px {
  font-size: 14px;
}
.text-16px {
  font-size: 16px;
}
.text-18px {
  font-size: 18px;
}
.font-bold {
  font-weight: bold;
}
.text-gray-500 {
  color: #6b7280;
}
.text-blue-500 {
  color: #3b82f6;
}
.text-red-500 {
  color: #ef4444;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.max-w-500px {
  max-width: 500px;
}
.w-full {
  width: 100%;
}
.gap-10px {
  gap: 10px;
}
.flex-col {
  flex-direction: column;
}
.mt-20px {
  margin-top: 20px;
}
</style>

<!-- 全局样式 -->
<style>
.task-course-tooltip-max-width {
  max-width: 500px !important;
}
</style>
