# AI练习模式选择弹窗组件 (ExerciseModeDialog)

> 一个用于选择AI对练模式的弹窗组件，支持语音+文本和纯文本两种练习模式

## 📖 组件概述

AI练习模式选择弹窗组件是AI质检练习系统中的模式选择界面，为用户提供直观的练习模式选择体验。通过可视化的模式卡片和详细的功能说明，帮助用户根据需求选择合适的练习模式。

## ✨ 核心特性

- 🎮 **双模式支持**: 支持语音+文本和纯文本两种练习模式
- ⚡ **直观选择**: 卡片式设计，功能特性一目了然
- 🖼️ **视觉反馈**: 丰富的交互动画和状态提示
- 🔧 **类型安全**: 完整的TypeScript类型定义
- 🎨 **响应式设计**: 适配不同屏幕尺寸

## 🚀 快速开始

### 基本用法

```vue
<template>
  <ExerciseModeDialog
    v-model:visible="dialogVisible"
    @confirm="handleModeConfirm"
    @close="handleDialogClose"
  />
</template>

<script setup>
import ExerciseModeDialog from './components/ExerciseModeDialog.vue'
import { ExerciseMode } from './exercise/store'

const dialogVisible = ref(false)

const handleModeConfirm = (mode: ExerciseMode) => {
  console.log('选择的模式:', mode)
  // 处理模式选择逻辑
}

const handleDialogClose = () => {
  console.log('弹窗关闭')
}
</script>
```

### Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| visible | boolean | ✅ | false | 弹窗显示状态（支持v-model） |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| confirm | mode: ExerciseMode | 用户确认选择模式时触发 |
| close | - | 弹窗关闭时触发 |

### 练习模式类型

```typescript
enum ExerciseMode {
  VOICE_TEXT = 'voice_text',  // 语音+文本模式
  TEXT_ONLY = 'text_only'     // 纯文本模式
}
```

## 🔧 技术实现

### 核心原理

1. **模式管理**: 使用响应式状态管理选中的练习模式
2. **双向绑定**: 通过defineModel实现弹窗显示状态的双向绑定
3. **事件通信**: 通过defineEmits定义组件事件，实现与父组件的通信
4. **视觉设计**: 卡片式布局配合动画效果，提供良好的用户体验

### 关键方法

```typescript
// 选择模式
function selectMode(mode: ExerciseMode): void

// 确认选择
function handleConfirm(): void

// 关闭弹窗
function handleClose(): void
```

### 模式特性对比

| 特性 | 语音+文本模式 | 纯文本模式 |
|------|---------------|------------|
| 语音合成 | ✅ | ❌ |
| 文本显示 | ✅ | ✅ |
| 响应速度 | 中等 | 快速 |
| 体验完整性 | 完整 | 简化 |
| 适用场景 | 完整练习 | 快速练习 |

## 🎯 使用场景

- **新员工培训**: 选择语音+文本模式进行完整的对话练习
- **快速练习**: 选择纯文本模式提高练习效率
- **网络环境差**: 选择纯文本模式避免语音加载问题
- **批量练习**: 选择纯文本模式快速完成多个课程

## 📋 更新日志

### v1.0.0 (2025-01-27)

- ✨ 新增AI练习模式选择弹窗组件
- ⚡ 支持语音+文本和纯文本两种练习模式
- 🔧 实现模式选择的视觉反馈和交互动画
- 🎨 采用卡片式设计，提供直观的模式选择体验
- 📱 支持响应式设计，适配不同屏幕尺寸
