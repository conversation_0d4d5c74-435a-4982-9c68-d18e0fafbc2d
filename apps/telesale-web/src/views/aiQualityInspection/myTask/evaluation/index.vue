<!--
 * @Date         : 2025-05-21 16:45:00
 * @Description  : 课程评估抽屉
 * @Autor        : Qzr(<EMAIL>)
 * @LastEditors  : Qzr(<EMAIL>)
-->

<template>
  <el-drawer
    v-model="drawerVisible"
    :title="courseData?.course?.course?.name || '课程评估'"
    size="90%"
    destroy-on-close
    direction="rtl"
    :before-close="handleClose"
    @open="handleDrawerOpen"
  >
    <div class="evaluation-drawer" v-loading="loading">
      <!-- 评估内容区域 -->
      <div class="flex gap-20px h-full">
        <!-- 左侧对话记录区域 -->
        <div
          class="w-70% flex flex-col bg-white rounded-8px shadow-sm overflow-hidden"
        >
          <div class="p-15px border-b border-gray-200">
            <h3 class="text-16px font-bold text-blue-500">通话记录</h3>
          </div>

          <!-- 音频播放器区域 - 仅在非纯文本模式下显示 -->
          <div v-if="!isTextOnlyMode" class="p-15px border-b border-gray-200">
            <!-- 检查是否有合并音频URL -->
            <div v-if="conversationData?.mergeAudioURL">
              <!-- 音频检查中 -->
              <div
                v-if="audioCheckLoading"
                class="flex items-center justify-center p-20px bg-gray-50 rounded-8px"
              >
                <div class="text-center text-gray-500">
                  <el-icon size="24" class="mb-10px animate-spin">
                    <Timer />
                  </el-icon>
                  <div class="text-14px">正在检查音频状态...</div>
                </div>
              </div>

              <!-- 音频可播放 -->
              <div v-else-if="audioPlayable === true">
                <Audio
                  :audioSrc="conversationData.mergeAudioURL"
                  :actionId="conversationData?.id || ''"
                  :createdAt="conversationData?.begin"
                  :useDirectDownload="true"
                />
              </div>

              <!-- 音频不可播放 -->
              <div v-else-if="audioPlayable === false" class="relative">
                <!-- 置灰的音频播放器 -->
                <div class="opacity-30 pointer-events-none">
                  <Audio
                    :audioSrc="conversationData.mergeAudioURL"
                    :actionId="conversationData?.id || ''"
                    :createdAt="conversationData?.begin"
                    :useDirectDownload="true"
                  />
                </div>

                <!-- 覆盖层提示 -->
                <div
                  class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 rounded-8px"
                >
                  <div class="text-center">
                    <el-icon size="24" class="mb-5px text-orange-500">
                      <Timer />
                    </el-icon>
                    <div
                      class="text-red-500 text-12px font-medium synthesis-hint"
                    >
                      语音合成中～
                    </div>
                  </div>
                </div>
              </div>

              <!-- 未检查状态（初始状态） -->
              <div v-else>
                <Audio
                  :audioSrc="conversationData.mergeAudioURL"
                  :actionId="conversationData?.id || ''"
                  :createdAt="conversationData?.begin"
                  :useDirectDownload="true"
                />
              </div>
            </div>

            <!-- 当没有合并音频时显示提示信息 -->
            <div
              v-else
              class="flex items-center justify-center p-20px bg-gray-50 rounded-8px"
            >
              <div class="text-center text-gray-500">
                <el-icon size="24" class="mb-10px">
                  <Timer />
                </el-icon>
                <div class="text-14px">合并音频正在生成中，请稍后刷新查看</div>
                <div class="text-12px text-gray-400 mt-5px">
                  音频合并通常需要几分钟时间
                </div>
                <el-button
                  type="primary"
                  size="small"
                  class="mt-15px"
                  :loading="loading"
                  @click="refreshAudioData"
                >
                  刷新音频状态
                </el-button>
              </div>
            </div>
          </div>

          <!-- 对话内容区域 -->
          <div
            class="flex-1 p-20px overflow-auto bg-gray-50"
            ref="chatContentRef"
          >
            <!-- 消息列表 -->
            <div class="space-y-15px">
              <div
                v-for="(message, index) in conversationData?.messages"
                :key="index"
                class="flex flex-col gap-5px"
              >
                <!-- 时间戳显示 -->
                <div
                  v-if="message.start"
                  class="text-center text-gray-400 text-12px"
                >
                  {{ formatTimestamp(message.start) }}
                </div>

                <!-- 消息内容 -->
                <div
                  class="flex"
                  :class="
                    message.role === 'bot' ? 'justify-start' : 'justify-end'
                  "
                >
                  <!-- 机器人消息 -->
                  <div
                    v-if="message.role === 'bot'"
                    class="flex items-start gap-10px max-w-80%"
                  >
                    <!-- 机器人头像 -->
                    <div
                      class="w-40px h-40px rounded-full bg-blue-500 flex items-center justify-center text-white text-14px font-medium flex-shrink-0"
                    >
                      {{
                        courseData?.course?.course?.botName?.charAt(0) || "机"
                      }}
                    </div>

                    <!-- 消息内容 -->
                    <div class="flex flex-col gap-8px">
                      <div
                        class="bg-white p-15px rounded-12px shadow-sm border border-gray-200 text-gray-800 leading-relaxed"
                      >
                        {{ message.content }}
                      </div>

                      <!-- 音频播放控制 - 仅在非纯文本模式下显示 -->
                      <div
                        v-if="!isTextOnlyMode && message.key"
                        class="flex items-center gap-8px"
                      >
                        <button
                          @click="playAudio(message)"
                          class="flex items-center gap-5px px-10px py-5px bg-blue-50 hover:bg-blue-100 rounded-6px text-blue-600 text-12px transition-colors"
                        >
                          <el-icon size="14">
                            <VideoPlay v-if="playingMessage !== message" />
                            <VideoPause v-else />
                          </el-icon>
                          <span>
                            {{ playingMessage === message ? "暂停" : "播放" }}
                          </span>
                        </button>

                        <!-- 播放状态指示 -->
                        <div
                          v-if="playingMessage === message"
                          class="flex items-center gap-5px text-blue-600 text-12px"
                        >
                          <div class="flex gap-1">
                            <div
                              class="w-2px h-10px bg-blue-600 animate-bounce"
                              style="animation-delay: 0ms"
                            />
                            <div
                              class="w-2px h-10px bg-blue-600 animate-bounce"
                              style="animation-delay: 150ms"
                            />
                            <div
                              class="w-2px h-10px bg-blue-600 animate-bounce"
                              style="animation-delay: 300ms"
                            />
                          </div>
                          <span>播放中</span>
                        </div>
                      </div>

                      <!-- 无音频提示 - 仅在非纯文本模式下显示 -->
                      <div
                        v-if="!isTextOnlyMode && !message.key"
                        class="text-gray-400 text-12px"
                      >
                        暂无音频
                      </div>
                    </div>
                  </div>

                  <!-- 用户消息 -->
                  <div v-else class="flex items-start gap-10px max-w-80%">
                    <!-- 消息内容和音频控制 -->
                    <div class="flex flex-col gap-8px">
                      <!-- 消息内容 -->
                      <div
                        class="bg-blue-500 text-white p-15px rounded-12px shadow-sm leading-relaxed"
                      >
                        {{ message.content }}
                      </div>

                      <!-- 音频播放控制 - 仅在非纯文本模式下显示 -->
                      <div
                        v-if="!isTextOnlyMode && message.key"
                        class="flex items-center justify-end gap-8px"
                      >
                        <button
                          v-if="message.role === 'bot'"
                          @click="playAudio(message)"
                          class="flex items-center gap-5px px-10px py-5px bg-blue-50 hover:bg-blue-100 rounded-6px text-blue-600 text-12px transition-colors"
                        >
                          <el-icon size="14">
                            <VideoPlay v-if="playingMessage !== message" />
                            <VideoPause v-else />
                          </el-icon>
                          <span>
                            {{ playingMessage === message ? "暂停" : "播放" }}
                          </span>
                        </button>

                        <!-- 播放状态指示 -->
                        <div
                          v-if="playingMessage === message"
                          class="flex items-center gap-5px text-blue-600 text-12px"
                        >
                          <div class="flex gap-1">
                            <div
                              class="w-2px h-10px bg-blue-600 animate-bounce"
                              style="animation-delay: 0ms"
                            />
                            <div
                              class="w-2px h-10px bg-blue-600 animate-bounce"
                              style="animation-delay: 150ms"
                            />
                            <div
                              class="w-2px h-10px bg-blue-600 animate-bounce"
                              style="animation-delay: 300ms"
                            />
                          </div>
                          <span>播放中</span>
                        </div>
                      </div>

                      <!-- 无音频提示 - 仅在非纯文本模式下显示 -->
                      <div
                        v-if="!isTextOnlyMode && !message.key"
                        class="text-gray-400 text-12px text-right"
                      >
                        暂无音频
                      </div>
                    </div>

                    <!-- 用户头像 -->
                    <div
                      class="w-40px h-40px rounded-full bg-gray-400 flex items-center justify-center text-white text-14px font-medium flex-shrink-0"
                    >
                      我
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧评估结果区域 -->
        <div class="w-30% flex flex-col h-full">
          <!-- 右侧内容滚动容器 -->
          <div class="flex-1 overflow-auto pr-5px">
            <!-- 对话评估报告 -->
            <el-card class="mb-20px">
              <template #header>
                <div
                  class="flex justify-between items-center cursor-pointer"
                  @click="toggleEvaluationCollapse"
                >
                  <span class="text-16px font-bold">对话评估报告</span>
                  <el-icon
                    :class="{ 'transform rotate-180': !evaluationCollapsed }"
                  >
                    <ArrowDown />
                  </el-icon>
                </div>
              </template>

              <div v-show="!evaluationCollapsed" class="p-10px">
                <div class="text-center mb-20px">
                  <div
                    class="text-24px font-bold mb-10px"
                    :class="{
                      'text-green-500': evaluationLevel === 'A',
                      'text-yellow-500': evaluationLevel === 'B',
                      'text-orange-500': evaluationLevel === 'C',
                      'text-red-500': evaluationLevel === 'D'
                    }"
                  >
                    任务得分：{{ evaluationLevel || "评估中..." }}
                  </div>
                </div>

                <!-- 评估结果 -->
                <div v-if="evaluationResult" class="mb-15px">
                  <div class="text-16px font-bold mb-10px">评估结果：</div>
                  <div
                    class="text-14px text-gray-700 leading-relaxed max-h-200px overflow-auto"
                    v-html="evaluationResult"
                  />
                </div>

                <!-- 评估原因 -->
                <div v-if="evaluationReason" class="mb-15px">
                  <div class="text-16px font-bold mb-10px">评估原因：</div>
                  <div
                    class="text-14px text-gray-700 leading-relaxed max-h-200px overflow-auto"
                    v-html="evaluationReason"
                  />
                </div>

                <!-- 优势点 -->
                <div v-if="evaluationStrengths.length > 0" class="mb-15px">
                  <div class="text-16px font-bold mb-10px">优势点：</div>
                  <ul class="pl-20px max-h-150px overflow-auto">
                    <li
                      v-for="(item, index) in evaluationStrengths"
                      :key="index"
                      class="text-14px mb-5px"
                    >
                      {{ item }}
                    </li>
                  </ul>
                </div>

                <!-- 改进建议 -->
                <div v-if="evaluationImprovements.length > 0">
                  <div class="text-16px font-bold mb-10px">改进建议：</div>
                  <ul class="pl-20px max-h-150px overflow-auto">
                    <li
                      v-for="(item, index) in evaluationImprovements"
                      :key="index"
                      class="text-14px mb-5px"
                    >
                      {{ item }}
                    </li>
                  </ul>
                </div>
              </div>
            </el-card>

            <!-- 任务信息卡片 -->
            <el-card class="mb-20px">
              <template #header>
                <div
                  class="flex justify-between items-center cursor-pointer"
                  @click="toggleTaskInfoCollapse"
                >
                  <span class="text-16px font-bold">任务信息</span>
                  <el-icon
                    :class="{ 'transform rotate-180': !taskInfoCollapsed }"
                  >
                    <ArrowDown />
                  </el-icon>
                </div>
              </template>

              <div v-show="!taskInfoCollapsed" class="p-10px">
                <el-descriptions :column="1">
                  <el-descriptions-item label="开始时间">
                    {{
                      conversationData?.begin
                        ? dayjs
                            .unix(Number(conversationData.begin))
                            .format("YYYY-MM-DD HH:mm:ss")
                        : "未知"
                    }}
                  </el-descriptions-item>

                  <el-descriptions-item label="结束时间">
                    {{
                      conversationData?.end
                        ? dayjs
                            .unix(Number(conversationData.end))
                            .format("YYYY-MM-DD HH:mm:ss")
                        : "未知"
                    }}
                  </el-descriptions-item>

                  <el-descriptions-item label="任务持续时长">
                    {{ formatTime(conversationData?.duration || 0) }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>

            <!-- 课程信息卡片 -->
            <el-card class="mb-20px">
              <template #header>
                <div
                  class="flex justify-between items-center cursor-pointer"
                  @click="toggleCourseInfoCollapse"
                >
                  <span class="text-16px font-bold">课程信息</span>
                  <el-icon
                    :class="{ 'transform rotate-180': !courseInfoCollapsed }"
                  >
                    <ArrowDown />
                  </el-icon>
                </div>
              </template>

              <div v-show="!courseInfoCollapsed" class="p-10px">
                <el-descriptions :column="1">
                  <el-descriptions-item label="课程名称">
                    {{ courseData?.course?.course?.name || "未命名课程" }}
                  </el-descriptions-item>

                  <el-descriptions-item label="最大持续时间">
                    {{ courseData?.course?.course?.maxDuration || 60 }} 分钟
                  </el-descriptions-item>

                  <el-descriptions-item label="机器人名称">
                    {{ courseData?.course?.course?.botName || "未设置" }}
                  </el-descriptions-item>

                  <el-descriptions-item label="机器人角色">
                    {{ courseData?.course?.course?.botRole || "未设置" }}
                  </el-descriptions-item>

                  <el-descriptions-item label="背景描述">
                    <div
                      class="whitespace-pre-wrap break-words text-12px max-h-100px overflow-auto"
                    >
                      {{
                        courseData?.course?.course?.backgroundDesc ||
                        "无背景描述"
                      }}
                    </div>
                  </el-descriptions-item>

                  <el-descriptions-item label="对话要求">
                    <div
                      class="whitespace-pre-wrap break-words text-12px max-h-100px overflow-auto"
                    >
                      {{
                        courseData?.course?.course?.conversationReq ||
                        "无对话要求"
                      }}
                    </div>
                  </el-descriptions-item>

                  <el-descriptions-item label="练习目标">
                    <div
                      class="whitespace-pre-wrap break-words text-12px max-h-100px overflow-auto"
                    >
                      {{ courseData?.course?.course?.target || "无练习目标" }}
                    </div>
                  </el-descriptions-item>

                  <el-descriptions-item label="创建时间">
                    {{
                      courseData?.course?.course?.createdAt
                        ? new Date(
                            courseData.course.course.createdAt
                          ).toLocaleString()
                        : "未知"
                    }}
                  </el-descriptions-item>

                  <el-descriptions-item label="用户标签">
                    <div
                      v-loading="tagsLoading"
                      class="whitespace-pre-wrap break-words"
                    >
                      <el-tag
                        v-for="tag in userTags"
                        :key="tag.id"
                        effect="plain"
                        class="mr-5px mb-5px"
                      >
                        {{ tag.name }}
                      </el-tag>
                      <span
                        v-if="userTags.length === 0 && !tagsLoading"
                        class="text-gray-400"
                      >
                        暂无标签
                      </span>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>

    <!-- 隐藏的音频播放器 - 仅在非纯文本模式下渲染 -->
    <audio
      v-if="!isTextOnlyMode"
      ref="audioPlayer"
      style="display: none"
      @ended="handleAudioEnded"
    />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineModel, defineProps, nextTick, computed } from "vue";
import { ElMessage } from "element-plus";
import {
  Timer,
  VideoPlay,
  VideoPause,
  ArrowDown
} from "@element-plus/icons-vue";
import {
  WorkerTrainingTaskCourseInstance,
  getWorkerTrainingTaskCourseInfo,
  WorkerTrainingConversationInstance,
  WorkerTrainingTaskMessage
} from "/@/api/AIQualityInspection/taskManagement";
import { getMget } from "/@/api/AISupport/Library";
import { TagInstance } from "/@/api/AIQualityInspection/excellentCases";
import libraryInfo from "/@/views/aiQualityInspection/const/Library";
import dayjs from "dayjs";
import Audio from "/@/views/AISupport/Quality/DetailDrawer/children/Audio.vue";

// 使用defineModel实现双向绑定
const drawerVisible = defineModel<boolean>("visible", { default: false });

// 定义props
const props = defineProps<{
  courseInfo: WorkerTrainingTaskCourseInstance | null;
}>();

// 定义事件
const emit = defineEmits(["close"]);

// 状态变量
const loading = ref(false);
const playingMessage = ref<WorkerTrainingTaskMessage | null>(null);
const audioPlayer = ref<HTMLAudioElement | null>(null);
const chatContentRef = ref<HTMLElement | null>(null);

// 音频播放状态
const audioCheckLoading = ref(false);
const audioPlayable = ref<boolean | null>(null); // null: 未检查, true: 可播放, false: 不可播放

// 数据状态
const courseData = ref<WorkerTrainingTaskCourseInstance | null>(null);
const conversationData = ref<WorkerTrainingConversationInstance | null>(null);
const evaluationLevel = ref<"A" | "B" | "C" | "D" | null>(null);
const evaluationResult = ref<string>("");
const evaluationReason = ref<string>("");
const evaluationStrengths = ref<string[]>([]);
const evaluationImprovements = ref<string[]>([]);

// 折叠状态
const evaluationCollapsed = ref(false);
const taskInfoCollapsed = ref(false);
const courseInfoCollapsed = ref(false);

/**
 * 用户标签数据
 */
const userTags = ref<TagInstance[]>([]);

/**
 * 标签加载状态
 */
const tagsLoading = ref(false);

/**
 * 是否为纯文本模式
 * 根据 conversationData.trainingMode 判断
 */
const isTextOnlyMode = computed(() => {
  return conversationData.value?.trainingMode === "text";
});

/**
 * 格式化时间显示
 * @param seconds 秒数
 * @returns 格式化的时间字符串 HH:mm:ss
 */
function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  return [
    hours.toString().padStart(2, "0"),
    minutes.toString().padStart(2, "0"),
    secs.toString().padStart(2, "0")
  ].join(":");
}

/**
 * 格式化时间戳为可读时间
 * @param timestamp 秒级时间戳字符串
 * @returns 格式化的时间字符串 YYYY-MM-DD HH:mm:ss
 */
function formatTimestamp(timestamp?: string): string {
  if (!timestamp) return "";

  const date = new Date(Number(timestamp) * 1000);

  // 使用dayjs格式化为YYYY-MM-DD HH:mm:ss格式
  return dayjs(date).format("YYYY-MM-DD HH:mm:ss");
}

/**
 * 播放音频
 * @param message 消息对象
 */
function playAudio(message: WorkerTrainingTaskMessage) {
  // 在纯文本模式下禁用音频播放
  if (isTextOnlyMode.value) {
    ElMessage.warning("当前为纯文本模式，无法播放音频");
    return;
  }

  if (playingMessage.value === message) {
    // 如果当前正在播放，则停止
    if (audioPlayer.value) {
      audioPlayer.value.pause();
      audioPlayer.value.currentTime = 0;
    }
    playingMessage.value = null;
  } else {
    // 开始播放新的音频
    playingMessage.value = message;

    // 设置真实的音频URL
    if (audioPlayer.value && message.key) {
      // 根据消息角色选择音频格式：bot使用mp3，user使用wav
      const audioFormat = message.role === "bot" ? "mp3" : "wav";
      audioPlayer.value.src = `https://wuhan-file.tos-cn-beijing.volces.com/telesalerobot/${message.key}.${audioFormat}`;
      audioPlayer.value.play().catch(error => {
        console.error("播放音频失败:", error);
        ElMessage.error("音频播放失败");
        playingMessage.value = null;
      });
    } else {
      console.warn("消息缺少音频key，无法播放音频");
      ElMessage.warning("该消息没有音频资源");
    }
  }
}

/**
 * 音频播放结束处理
 */
function handleAudioEnded() {
  playingMessage.value = null;
}

/**
 * 切换评估报告折叠状态
 */
function toggleEvaluationCollapse() {
  evaluationCollapsed.value = !evaluationCollapsed.value;
}

/**
 * 切换任务信息折叠状态
 */
function toggleTaskInfoCollapse() {
  taskInfoCollapsed.value = !taskInfoCollapsed.value;
}

/**
 * 切换课程信息折叠状态
 */
function toggleCourseInfoCollapse() {
  courseInfoCollapsed.value = !courseInfoCollapsed.value;
}

/**
 * 获取用户标签数据
 */
async function fetchUserTags() {
  if (
    !courseData.value?.course?.course?.referenceQas ||
    courseData.value.course.course.referenceQas.length === 0
  ) {
    userTags.value = [];
    return;
  }

  // 提取所有referenceQaId
  const referenceQaIds = courseData.value.course.course.referenceQas
    .map(qa => qa.referenceQaId)
    .filter(id => id); // 过滤掉空值

  if (referenceQaIds.length === 0) {
    userTags.value = [];
    return;
  }

  tagsLoading.value = true;
  try {
    const response = await getMget({
      libraryUUID: libraryInfo.libraryUUID,
      ids: referenceQaIds
    });

    // 定义返回数据类型
    interface TagGroupInstance {
      id: number;
      name: string;
      key: string;
      tags: TagInstance[];
    }

    interface KnowledgeItem {
      id: string;
      tags?: TagGroupInstance[];
    }

    interface ResponseData {
      list: KnowledgeItem[];
    }

    // 提取所有标签数据
    const allTags: TagInstance[] = [];
    const responseData = response.data as ResponseData;
    if (responseData?.list) {
      responseData.list.forEach((item: KnowledgeItem) => {
        if (item.tags && Array.isArray(item.tags)) {
          // 从标签组中提取实际的标签
          item.tags.forEach((tagGroup: TagGroupInstance) => {
            if (tagGroup.tags && Array.isArray(tagGroup.tags)) {
              allTags.push(...tagGroup.tags);
            }
          });
        }
      });
    }

    // 去重标签（根据key去重）
    const uniqueTags = allTags.filter(
      (tag, index, self) => index === self.findIndex(t => t.key === tag.key)
    );

    userTags.value = uniqueTags;
  } catch (error) {
    console.error("获取用户标签失败:", error);
    ElMessage.error("获取用户标签失败");
    userTags.value = [];
  } finally {
    tagsLoading.value = false;
  }
}

/**
 * 关闭抽屉
 */
function handleClose() {
  drawerVisible.value = false;
  emit("close");
}

/**
 * 检查音频URL是否可播放
 * @param audioUrl 音频URL
 * @returns Promise<boolean> 是否可播放
 */
async function checkAudioPlayable(audioUrl: string): Promise<boolean> {
  return new Promise(resolve => {
    const audio = document.createElement("audio") as HTMLAudioElement;

    // 设置超时时间（5秒）
    const timeout = setTimeout(() => {
      console.warn("音频检查超时:", audioUrl);
      resolve(false);
    }, 5000);

    // 音频可以播放时触发
    audio.addEventListener("canplay", () => {
      clearTimeout(timeout);
      console.log("音频检查成功，可以播放:", audioUrl);
      resolve(true);
    });

    // 音频加载错误时触发
    audio.addEventListener("error", (e: Event) => {
      clearTimeout(timeout);
      console.warn("音频检查失败，无法播放:", audioUrl, e);
      resolve(false);
    });

    // 开始加载音频
    audio.src = audioUrl;
    audio.load();
  });
}

/**
 * 检查合并音频的可播放性
 */
async function checkMergeAudioPlayable() {
  // 在纯文本模式下跳过音频检查
  if (isTextOnlyMode.value) {
    console.log("纯文本模式，跳过音频可播放性检查");
    audioPlayable.value = null;
    return;
  }

  if (!conversationData.value?.mergeAudioURL) {
    audioPlayable.value = null;
    return;
  }

  audioCheckLoading.value = true;
  audioPlayable.value = null;

  try {
    console.log("开始检查音频可播放性:", conversationData.value.mergeAudioURL);
    const isPlayable = await checkAudioPlayable(
      conversationData.value.mergeAudioURL
    );
    audioPlayable.value = isPlayable;

    console.log("✅ 合并音频可播放性检查完成:", {
      url: conversationData.value.mergeAudioURL,
      playable: isPlayable,
      timestamp: new Date().toISOString()
    });

    // 根据检查结果显示不同的日志
    if (isPlayable) {
      console.log("🎵 音频可以正常播放");
    } else {
      console.warn("⚠️ 音频无法播放，可能正在合成中");
    }
  } catch (error) {
    console.error("❌ 检查音频可播放性失败:", error);
    audioPlayable.value = false;
  } finally {
    audioCheckLoading.value = false;
  }
}

/**
 * 刷新音频数据
 */
async function refreshAudioData() {
  if (
    !props.courseInfo ||
    !props.courseInfo.workerTrainingTaskId ||
    !props.courseInfo.trainingTaskCourseId
  ) {
    ElMessage.error("缺少必要的课程信息");
    return;
  }

  loading.value = true;

  try {
    // 重新调用接口获取最新的课程详情
    const response = await getWorkerTrainingTaskCourseInfo({
      workerTrainingTaskId: props.courseInfo.workerTrainingTaskId,
      trainingTaskCourseId: props.courseInfo.trainingTaskCourseId
    });

    if (response?.data?.conversation) {
      // 更新会话数据
      conversationData.value = response.data.conversation;

      // 检查音频可播放性
      await checkMergeAudioPlayable();

      // 根据检查结果显示消息
      if (conversationData.value.mergeAudioURL) {
        if (audioPlayable.value) {
          ElMessage.success("音频已生成，可以播放了！");
        } else {
          ElMessage.info("音频仍在合成中，请稍后刷新查看");
        }
      } else {
        ElMessage.info("音频仍在生成中，请稍后再试");
      }
    } else {
      ElMessage.error("未找到课程对话记录");
    }
  } catch (error) {
    console.error("刷新音频数据失败:", error);
    ElMessage.error("刷新音频数据失败");
  } finally {
    loading.value = false;
  }
}

/**
 * 抽屉打开事件处理
 */
async function handleDrawerOpen() {
  if (
    !props.courseInfo ||
    !props.courseInfo.workerTrainingTaskId ||
    !props.courseInfo.trainingTaskCourseId
  ) {
    ElMessage.error("缺少必要的课程信息");
    return;
  }

  loading.value = true;

  try {
    // 调用接口获取课程详情
    const response = await getWorkerTrainingTaskCourseInfo({
      workerTrainingTaskId: props.courseInfo.workerTrainingTaskId,
      trainingTaskCourseId: props.courseInfo.trainingTaskCourseId
    });

    if (response?.data?.conversation) {
      conversationData.value = response.data.conversation;
      courseData.value = props.courseInfo;

      // 调试信息：检查 mergeAudioURL 状态和训练模式
      console.log("课程对话数据:", {
        conversationId: response.data.conversation.id,
        mergeAudioURL: response.data.conversation.mergeAudioURL,
        hasAudio: !!response.data.conversation.mergeAudioURL,
        messagesCount: response.data.conversation.messages?.length || 0,
        begin: response.data.conversation.begin,
        end: response.data.conversation.end,
        duration: response.data.conversation.duration,
        trainingMode: response.data.conversation.trainingMode,
        isTextOnlyMode: response.data.conversation.trainingMode === "text"
      });

      // 检查音频可播放性
      if (response.data.conversation.mergeAudioURL) {
        console.log("检测到音频URL，开始检查可播放性...");
        await checkMergeAudioPlayable();
      } else {
        console.warn("⚠️ mergeAudioURL 为空，可能的原因：", [
          "1. 后端尚未实现音频合并功能",
          "2. 音频正在异步生成中",
          "3. 练习时间太短，没有足够的音频内容",
          "4. 前后端接口定义不一致"
        ]);
        audioPlayable.value = null;
      }

      // 从AI评估结果中获取评级和建议
      if (response.data.conversation.aiAppraise) {
        const appraise = response.data.conversation.aiAppraise;
        evaluationLevel.value =
          (appraise.score as "A" | "B" | "C" | "D") || null;
        evaluationResult.value = appraise.result || "";
        evaluationReason.value = appraise.reason || "";

        // 清空默认的优势和改进建议，使用AI评估的结果和原因
        evaluationStrengths.value = [];
        evaluationImprovements.value = [];
      } else {
        // 如果没有AI评估结果，使用课程的评分
        evaluationLevel.value =
          (props.courseInfo!.score as "A" | "B" | "C" | "D") || null;
        evaluationResult.value = "";
        evaluationReason.value = "";
        evaluationStrengths.value = [
          "话术逻辑清晰，层次分明",
          "主动引导对话，把握节奏",
          "能够针对客户疑虑给出有效回应"
        ];
        evaluationImprovements.value = [
          "产品细节描述可以更加具体",
          "应对客户异议时可以更加灵活",
          "可以更多使用数据支持论点"
        ];
      }

      // 获取用户标签
      await fetchUserTags();

      // 滚动到对话开始位置
      nextTick(() => {
        if (chatContentRef.value) {
          chatContentRef.value.scrollTop = 0;
        }
      });
    } else {
      ElMessage.error("未找到课程对话记录");
    }
  } catch (error) {
    console.error("获取课程详情失败:", error);
    ElMessage.error("获取课程详情失败");
  } finally {
    loading.value = false;
  }
}
</script>

<style scoped>
.evaluation-drawer {
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

/* 自定义滚动条样式 */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
@keyframes bounce {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite ease-in-out both;
}

/* 间距样式 */
.space-y-15px > * + * {
  margin-top: 15px;
}

/* 最大宽度样式 */
.max-w-80\% {
  max-width: 80%;
}

/* 间距样式 */
.gap-8px {
  gap: 8px;
}

.gap-5px {
  gap: 5px;
}

.gap-1 {
  gap: 0.25rem;
}

/* 内边距样式 */
.px-10px {
  padding-left: 10px;
  padding-right: 10px;
}

.py-5px {
  padding-top: 5px;
  padding-bottom: 5px;
}

/* 圆角样式 */
.rounded-6px {
  border-radius: 6px;
}

/* 字体大小样式 */
.text-12px {
  font-size: 12px;
}

/* 字体粗细样式 */
.font-medium {
  font-weight: 500;
}

/* 行高样式 */
.leading-relaxed {
  line-height: 1.625;
}

/* 弹性布局样式 */
.flex-shrink-0 {
  flex-shrink: 0;
}

/* 过渡效果 */
.transition-colors {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* 阴影样式 */
.shadow-sm {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

/* 边框样式 */
.border-gray-200 {
  border-color: #e5e7eb;
}

/* 背景色样式 */
.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.bg-blue-100 {
  background-color: #dbeafe;
}

/* 悬停效果 */
.hover\:bg-blue-100:hover {
  background-color: #dbeafe;
}

/* 文字颜色样式 */
.text-blue-600 {
  color: #2563eb;
}

.text-gray-700 {
  color: #374151;
}

/* 文本换行样式 */
.whitespace-pre-wrap {
  white-space: pre-wrap;
}

.break-words {
  word-break: break-word;
}

/* 高度限制样式 */
.max-h-100px {
  max-height: 100px;
}

.max-h-150px {
  max-height: 150px;
}

.max-h-200px {
  max-height: 200px;
}

/* 右侧内边距 */
.pr-5px {
  padding-right: 5px;
}

/* 折叠动画样式 */
.transform {
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}

/* 音频检查动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* 语音合成提示动画 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.synthesis-hint {
  animation: pulse 2s ease-in-out infinite;
}
</style>
