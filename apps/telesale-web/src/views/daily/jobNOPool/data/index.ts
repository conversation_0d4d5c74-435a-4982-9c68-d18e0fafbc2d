/*
 * @Date         : 2024-07-23 11:59:35
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { ref } from "vue";
import { TableColumns } from "/@/components/ReTable/types";
import { useUserStore } from "/@/store/modules/user";
import { storeToRefs } from "pinia";
import { getLabel } from "/@/utils/common";

const { channelList } = storeToRefs(useUserStore());
export const listHeader = ref<TableColumns[]>([
  {
    field: "channelId",
    desc: "渠道",
    customRender: ({ text }) => {
      return getLabel(text, channelList.value, "text");
    }
  },
  {
    field: "agentNo",
    desc: "外呼工号"
  },
  {
    field: "operatorName",
    desc: "操作人"
  },
  {
    field: "operatorAt",
    desc: "操作时间",
    timeChange: 2
  }
]);
