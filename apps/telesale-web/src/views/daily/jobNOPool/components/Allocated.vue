<!--
 * @Date         : 2024-07-23 11:07:06
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { ElMessage } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";

import RePagination from "/@/components/RePagination/index.vue";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";

import SetJobNO from "../dialog/SetJobNO.vue";
import UploadFileSelect from "../dialog/UploadFileSelect.vue";

import channelMath from "/@/utils/asyn/findChannel";
import findSiteMath from "/@/utils/asyn/findSite";
import findAllAgentMath from "/@/utils/asyn/findAllAgent";
import paramsHandle from "/@/utils/handle/paramsHandle";
import { getListNO, delNO } from "/@/api/daily";

let device = useAppStoreHook().device;
let pureUser = useUserStoreHook();
let isSet =
  pureUser.authorizationMap.indexOf("telesale_admin_agent_number_set") > -1;
let isUpload =
  pureUser.authorizationMap.indexOf("telesale_admin_agent_number_import") > -1;

const listHeader = [
  {
    field: "siteName",
    desc: "公司主体",
    filters: row =>
      siteList.value.find(item => row.siteId === item.id)?.name || ""
  },
  {
    field: "workerId",
    desc: "坐席",
    filters: row => allSiteAgentList.value[row.workerId]?.name
  },
  { field: "channelName", desc: "渠道" },

  { field: "agentNO", desc: "外呼工号" },
  {
    field: "isUsing",
    desc: "当前状态",
    filters: row => (row.isUsing ? "使用中" : "未使用")
  },
  { field: "operatorName", desc: "操作人" },
  { field: "operatorAt", desc: "操作时间", timeChange: 3 }
];

const operation = [
  // { event: "edit", text: "编辑", isShow: row => !row.isUsing },
  {
    event: "del",
    text: "删除",
    isShow: row => !row.isUsing,
    popconfirm: "确定删除此工号吗？"
  }
];
function parantMath({ key, params }) {
  switch (key) {
    case "edit":
      setNO(params);
      break;
    case "del":
      del(params);
      break;
  }
}

function del(row) {
  loading.value = true;
  delNO({ id: row.id })
    .then(() => {
      ElMessage.success("删除成功");
      getList();
    })
    .catch(() => {
      loading.value = false;
    });
}

const isModel = ref(false);
const isModelUpload = ref(false);
const dataMemory = ref();

function setNO(row) {
  dataMemory.value = row;
  isModel.value = true;
}

//form查询
const form = reactive({
  agentNO: "",
  workerId: undefined,
  channelId: "",
  siteId: ""
});

let loading = ref(true);
let dataList = ref([]);
let total = ref(0);

//分页
const rePaginationRefs = ref();
function onSearch() {
  rePaginationRefs.value.onSearch();
}

//form查询
function getList() {
  loading.value = true;
  getListNO(
    paramsHandle(form, {
      zero: ["agentNO", "workerId", "channelId", "siteId"],
      pageIndex: rePaginationRefs.value.pageIndex,
      pageSize: rePaginationRefs.value.pageSize
    })
  )
    .then(({ data }: { data: any }) => {
      dataList.value = data.list;
      total.value = data.total;
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      total.value = 0;
      loading.value = false;
    });
}

let siteList = ref([]);
let allSiteAgentList = ref({});
let siteAgentList = ref([]);

async function changeSite(val) {
  form.workerId = undefined;
  let allList = await findAllAgentMath({ siteId: val || -1 });
  siteAgentList.value = allList.map(item => {
    item.status === 2 && (item.name += "（已离职）");
    item.label = item.name;
    item.value = item.id;
    return item;
  });
}

onMounted(async () => {
  if (!pureUser.channelList.length) {
    pureUser.setChannelList(await channelMath());
  }
  siteList.value = await findSiteMath();
  allSiteAgentList.value = {};
  let allList = await findAllAgentMath({ siteId: -1 });
  siteAgentList.value = allList.map(item => {
    item.status === 2 && (item.name += "（已离职）");
    item.label = item.name;
    item.value = item.id;
    allSiteAgentList.value[item.id] = item;
    return item;
  });
  onSearch();
});
</script>

<template>
  <div v-loading="loading">
    <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
      <el-form-item prop="siteId">
        <el-select
          v-model="form.siteId"
          placeholder="请选择公司主体"
          clearable
          filterable
          @change="changeSite"
          @keyup.enter="onSearch"
        >
          <el-option
            v-for="item in siteList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="workerId">
        <el-select-v2
          v-model="form.workerId"
          filterable
          clearable
          :options="siteAgentList"
          placeholder="请选择坐席"
        />
      </el-form-item>
      <el-form-item prop="channelId">
        <el-select
          v-model="form.channelId"
          placeholder="请选择渠道"
          clearable
          filterable
          @keyup.enter="onSearch"
        >
          <el-option
            v-for="item in pureUser.channelList"
            :key="item.value"
            :label="item.text"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item prop="agentNO">
        <el-input
          v-model="form.agentNO"
          placeholder="请输入外呼工号"
          clearable
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('search')"
          @click="onSearch"
        >
          搜索
        </el-button>
      </el-form-item>
      <el-form-item class="g-set-button">
        <el-button type="primary" v-if="isUpload" @click="isModelUpload = true">
          导入
        </el-button>
        <el-button type="primary" @click="setNO('')" v-if="isSet">
          新增
        </el-button>
      </el-form-item>
    </el-form>
    <div class="g-table-box">
      <ReTable
        v-if="device !== 'mobile'"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="isSet ? operation : []"
        @parantMath="parantMath"
      />
      <ReCardList
        v-else
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="isSet ? operation : []"
        @parantMath="parantMath"
      />
    </div>
    <RePagination ref="rePaginationRefs" :total="total" @getList="getList" />
    <SetJobNO
      v-if="isModel"
      v-model:value="isModel"
      @onSearch="getList"
      :dataMemory="dataMemory"
      :siteList="siteList"
      :allSiteAgentList="allSiteAgentList"
    />
    <UploadFileSelect
      v-if="isModelUpload"
      v-model:value="isModelUpload"
      @onSearch="onSearch"
    />
  </div>
</template>
