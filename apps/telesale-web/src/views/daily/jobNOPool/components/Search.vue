<!--
 * @Date         : 2024-07-23 12:18:51
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts">
import { computed, ref } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import UploadFileSelect from "../dialog/UploadFileSelect.vue";
import ImportResult from "../dialog/ImportResult.vue";
import { exportAgentNoApi } from "/@/api/daily/jobNoPool";
import downloadFile from "/@/utils/handle/downloadFile";

interface Emits {
  (e: "onSearch"): void;
  (e: "update:form", val: any): void;
}

const props = defineProps<{
  form: any;
}>();

const emit = defineEmits<Emits>();

const { channelList } = storeToRefs(useUserStore());
const isModalResult = ref<boolean>(false);
const dataList = ref([]);

function onSearch() {
  emit("onSearch");
}

const form = computed({
  get() {
    return props.form;
  },
  set(val: any) {
    emit("update:form", val);
  }
});

const isModal = ref<boolean>(false);

const onUpload = data => {
  dataList.value = data;
  isModalResult.value = true;
  onSearch();
};

const exportExcel = () => {
  exportAgentNoApi(form.value).then(res => {
    downloadFile(res);
  });
};
</script>
<template>
  <el-form ref="formRef" :inline="true" :model="form" class="clearfix">
    <el-form-item prop="channelId">
      <el-select
        v-model="form.channelId"
        placeholder="请选择渠道"
        clearable
        filterable
        @keyup.enter="onSearch"
      >
        <el-option
          v-for="item in channelList"
          :key="item.value"
          :label="item.text"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item prop="agentNo">
      <el-input
        v-model="form.agentNo"
        placeholder="请输入外呼工号"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
    </el-form-item>
    <el-form-item class="g-set-button">
      <el-button
        type="primary"
        v-auth="'telesale_admin_agent_number_import'"
        @click="isModal = true"
      >
        导入工号
      </el-button>
      <el-button
        type="primary"
        v-auth="'telesale_admin_agent_number_import'"
        @click="exportExcel"
      >
        导出工号
      </el-button>
    </el-form-item>
  </el-form>
  <UploadFileSelect
    v-if="isModal"
    v-model:value="isModal"
    type="not"
    @upload="onUpload"
  />
  <ImportResult
    v-if="isModalResult"
    v-model:value="isModalResult"
    :dataList="dataList"
    @onSearch="onSearch"
  />
</template>
