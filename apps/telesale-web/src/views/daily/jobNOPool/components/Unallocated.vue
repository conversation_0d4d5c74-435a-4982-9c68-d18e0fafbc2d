<!--
 * @Date         : 2024-07-23 11:07:26
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script setup lang="ts" name="userList">
import { onMounted } from "vue";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStore } from "/@/store/modules/user";
import { listHeader } from "../data//index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { useTable } from "/@/hooks/useTable";
import { ElMessage, ElMessageBox } from "element-plus";
import { OperationObj } from "/@/components/ReTable/types";
import {
  deleteAgentNoPollApi,
  getAgentNoPollApi
} from "/@/api/daily/jobNoPool";
import Search from "../components/Search.vue";
import { getAuth } from "/@/utils/auth";

const { device } = useAppStoreHook();

const { dataList, Pagination, onSearch, searchForm, loading, handlerQuery } =
  useTable({
    api: getAgentNoPollApi
  });

const operation: OperationObj[] = [
  {
    text: "删除",
    isShow: getAuth("telesale_admin_agent_number_set"),
    eventFn: row => {
      ElMessageBox.confirm("确认删除该工号吗？", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        loading.value = true;
        deleteAgentNoPollApi({ id: row.id })
          .then(() => {
            handlerQuery();
            ElMessage.success("删除成功");
          })
          .finally(() => {
            loading.value = false;
          });
      });
    }
  }
];
</script>

<template>
  <div v-loading="loading">
    <Search ref="formRefs" v-model:form="searchForm" @onSearch="onSearch" />
    <div class="g-table-box">
      <ReTable
        v-if="device !== 'mobile'"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
      />
      <ReCardList
        v-else
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
      />
    </div>
    <Pagination />
  </div>
</template>
