<!--
 * @Date         : 2024-07-23 13:42:12
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="tsx" setup>
import { computed } from "vue";
import { TableColumns } from "/@/components/ReTable/types";
import ReTable from "/@/components/ReTable/index.vue";

interface Emits {
  (e: "update:value", val: boolean): void;
}
const props = defineProps<{
  value: boolean;
  dataList: any[];
}>();
const emit = defineEmits<Emits>();

const listHeader: TableColumns[] = [
  {
    field: "channel",
    desc: "渠道",
    customRender: ({ text }) => {
      return text?.cn;
    }
  },
  {
    field: "agentNo",
    desc: "外呼工号"
  },
  {
    field: "errMsg",
    desc: "导入结果",
    customRender: ({ text }) => {
      return text ? <span class="c-red">{text}</span> : "导入成功";
    }
  }
];
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}
</script>

<template>
  <div>
    <el-dialog title="导入工号" v-model="isModel" :before-close="handleClose">
      <ReTable
        ref="tableRefs"
        :dataList="props.dataList"
        :listHeader="listHeader"
      />
      <template #footer>
        <el-button @click="handleClose">关闭</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped></style>
