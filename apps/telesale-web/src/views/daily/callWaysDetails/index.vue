<script setup lang="ts" name="callWaysDetails">
import { ref, reactive } from "vue";
import { useRoute } from "vue-router";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "/@/store/modules/user";
import { closePageBox } from "/@/utils/handle/closePage";
import { detailCall, addCall, editCall } from "/@/api/daily";
import rulesProps from "./utils/rules";
import SetValue from "./dialog/SetValue.vue";
import channelMath from "/@/utils/asyn/findChannel";

const { closePage } = closePageBox();
let device = useAppStoreHook().device;
const loading = ref<boolean>(false);

const route = useRoute();
const id = (route.query?.id as string) || "";
const type = (route.query?.type as string) || "";

const isModel = ref(false);
let positeIndex = ref();
function setValueMath(index) {
  positeIndex.value = index;
  isModel.value = true;
}

function getInitData() {
  const form = {
    cn: "",
    en: "",
    host: "",
    accessId: "",
    secret: "",
    config: "",
    expend: [],
    state: 2,
    audioChannel: 1,
    domain: "",
    password: ""
  };
  return JSON.parse(JSON.stringify(form));
}
//form查询
const form: any = reactive(getInitData());

const formRef = ref<FormInstance>();
const rules = reactive<FormRules>(rulesProps);

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      let params: any = { ...form };
      params.extra = JSON.stringify({
        form: params.expend,
        config: params.config
      });
      delete params.expend;
      delete params.config;
      let method = type === "add" ? addCall : editCall;
      method(params)
        .then(async () => {
          useUserStoreHook().setChannelList(await channelMath());
          loading.value = false;
          ElMessage.success("操作成功");
          closePage(id, "callWays");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};

function getDetails() {
  loading.value = true;
  detailCall({ id })
    .then(({ data }: { data: any }) => {
      let extra = data.channel.extra ? JSON.parse(data.channel.extra) : {};
      data.channel.expend = extra.form || [];
      data.channel.config = extra.config || "";
      for (let key in form) {
        form[key] = data.channel[key];
      }
      form.id = data.channel.id;
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

if (type !== "add") {
  getDetails();
} else {
  let initData = getInitData();
  delete form.id;
  for (let key in initData) {
    form[key] = initData[key];
  }
}
</script>
<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <el-form
        :model="form"
        :class="device"
        label-suffix="："
        :label-width="device !== 'mobile' ? '140px' : ''"
        ref="formRef"
        :rules="rules"
        v-loading="loading"
      >
        <el-row class="g-width-all">
          <el-col :lg="16">
            <el-form-item label="外呼名称" prop="cn">
              <el-input
                v-model.trim="form.cn"
                clearable
                maxlength="32"
                show-word-limit
                :disabled="type === 'detail'"
              />
            </el-form-item>
            <el-form-item label="英文名称" prop="en">
              <el-input
                v-model.trim="form.en"
                clearable
                maxlength="32"
                show-word-limit
                :disabled="type === 'detail'"
              />
            </el-form-item>
            <el-form-item label="接入地址" prop="host">
              <el-input
                v-model.trim="form.host"
                clearable
                maxlength="256"
                show-word-limit
                :disabled="type === 'detail'"
              />
            </el-form-item>
            <el-form-item label="账号" prop="accessId">
              <el-input
                v-model.trim="form.accessId"
                clearable
                maxlength="256"
                show-word-limit
                :disabled="type === 'detail'"
              />
            </el-form-item>
            <el-form-item label="接入密钥" prop="secret">
              <el-input
                v-model.trim="form.secret"
                clearable
                maxlength="256"
                show-word-limit
                :disabled="type === 'detail'"
              />
            </el-form-item>
            <el-form-item label="是否启用" prop="state">
              <el-switch
                v-model="form.state"
                :active-value="2"
                :inactive-value="1"
                :disabled="type === 'detail'"
              />
            </el-form-item>
            <el-form-item label="扩展字段" prop="config">
              <el-input
                clearable
                :disabled="type === 'detail'"
                type="textarea"
                :autosize="{ minRows: 3, maxRows: 6 }"
                v-model.trim="form.config"
              />
            </el-form-item>
            <el-form-item label="表单配置" prop="expend">
              <el-table
                :data="form.expend"
                :border="true"
                highlight-current-row
              >
                <el-table-column property="label" label="label" />
                <el-table-column property="key" label="key" />
                <el-table-column property="type" label="type" />
                <el-table-column property="list" label="list">
                  <template #default="scope">
                    <el-select placeholder="请选择" v-if="scope.row.list">
                      <el-option
                        v-for="item in scope.row.list"
                        :key="item.value"
                        :label="item.label + ' - ' + item.value"
                        :value="item.value"
                        disabled
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column fixed="right" width="100">
                  <template #header>
                    <el-button
                      type="primary"
                      link
                      :icon="useRenderIcon('plus')"
                      @click="setValueMath(-1)"
                      :disabled="type === 'detail'"
                    >
                      添加
                    </el-button>
                  </template>
                  <template #default="scope">
                    <el-button
                      type="primary"
                      :icon="useRenderIcon('edits')"
                      circle
                      :disabled="type === 'detail'"
                      @click="setValueMath(scope.$index)"
                    />
                    <el-button
                      :icon="useRenderIcon('minus')"
                      circle
                      :disabled="type === 'detail'"
                      @click="form.expend.splice(scope.$index, 1)"
                    />
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-form-item label="左右声道" prop="audioChannel">
              <el-radio-group
                v-model="form.audioChannel"
                :disabled="type === 'detail'"
              >
                <el-radio :label="1">左声道：客户——右声道：坐席</el-radio>
                <el-radio :label="2">左声道：坐席——右声道：客户</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="Domain" prop="domain">
              <el-input
                v-model="form.domain"
                placeholder="请输入内容"
                :disabled="type === 'detail'"
              />
            </el-form-item>
            <el-form-item label="密码管理" prop="password">
              <el-input
                v-model="form.password"
                placeholder="请输入内容"
                :disabled="type === 'detail'"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item>
          <el-button @click="closePage(id, 'callWays')">返回</el-button>
          <el-button
            type="primary"
            @click="submitForm(formRef)"
            v-if="type !== 'detail'"
          >
            确定
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <SetValue
      v-if="isModel"
      v-model:value="isModel"
      v-model:expend="form.expend"
      :positeIndex="positeIndex"
    />
  </div>
</template>
<style scoped lang="scss">
:deep(.el-table__empty-block) {
  height: 100px !important;
}
</style>
