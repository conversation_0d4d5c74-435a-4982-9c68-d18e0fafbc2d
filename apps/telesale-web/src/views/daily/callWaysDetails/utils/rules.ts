const rulesProps = {
  cn: [
    {
      required: true,
      message: "请输入外呼名称",
      trigger: "blur"
    }
  ],
  en: [
    {
      required: true,
      message: "请输入英文名称",
      trigger: "blur"
    }
  ],
  host: [
    {
      required: true,
      message: "请输入账号",
      trigger: "blur"
    }
  ],
  accessId: [
    {
      required: true,
      message: "请输入账号",
      trigger: "blur"
    }
  ],
  secret: [
    {
      required: true,
      message: "请输入接入密钥",
      trigger: "blur"
    }
  ],
  state: [
    {
      required: true,
      message: "请选择是否开启",
      trigger: "change"
    }
  ],
  domain: [
    {
      required: true,
      message: "请配置Domain",
      trigger: "blur"
    }
  ],
  password: [
    {
      required: true,
      message: "请配置密码",
      trigger: "blur"
    }
  ]
};

export default rulesProps;
