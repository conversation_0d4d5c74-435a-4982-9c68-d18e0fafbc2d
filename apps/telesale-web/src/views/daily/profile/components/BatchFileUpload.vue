<script lang="ts" setup>
import { ref, watch, nextTick, computed } from "vue";
import {
  ElMessage,
  UploadFile,
  UploadInstance,
  UploadRequestOptions
} from "element-plus";
import { Delete, Upload } from "@element-plus/icons-vue";
import { uploadPermanentFileApi } from "/@/api/common";

const props = defineProps<{
  fileList: any[];
  loading: boolean;
}>();
const emit = defineEmits<{
  (e: "update:fileList", val: UploadFile[]): void;
  (e: "update:loading", val: boolean): void;
}>();

const fileList = computed({
  get() {
    return props.fileList;
  },
  set(val) {
    emit("update:fileList", val);
  }
});

const loading = computed({
  get() {
    return props.loading;
  },
  set(val) {
    emit("update:loading", val);
  }
});

// 跟踪正在上传的文件数量
const uploadingCount = ref(0);
const uploadRef = ref<UploadInstance>();
const onExceed = () => {
  ElMessage.warning("最多上传50个文件");
};

const uploadFile = (file: UploadRequestOptions) => {
  const myFile = file.file;
  const formData = new FormData();
  formData.append("file", myFile);

  // 增加上传中的文件计数
  uploadingCount.value++;
  loading.value = true;

  uploadPermanentFileApi(formData)
    .then(res => {
      fileList.value.push({
        url: res.data,
        name: myFile.name,
        materialName: myFile.name.replace(".pdf", "").slice(0, 20)
      });
    })
    .catch(error => {
      console.error("文件上传失败:", error);
      ElMessage.error("文件上传失败");
    })
    .finally(() => {
      // 减少上传中的文件计数
      uploadingCount.value--;

      // 只有当所有文件都上传完成后才关闭loading状态
      if (uploadingCount.value === 0) {
        loading.value = false;
      }
    });
};

const removeFile = (index: number) => {
  fileList.value.splice(index, 1);
  // 清除组件内部的文件列表
  uploadRef.value.handleRemove(fileList.value[index]);
};
</script>

<template>
  <div class="w-100%">
    <el-upload
      ref="uploadRef"
      :file-list="fileList"
      action="#"
      :http-request="uploadFile"
      accept=".pdf"
      :limit="50"
      :on-exceed="onExceed"
      multiple
      style="width: 100%"
      :show-file-list="false"
    >
      <div>
        <el-button :icon="Upload" type="primary">上传文件</el-button>
        <div class="c-red">说明：一次最多上传50个文件</div>
      </div>
    </el-upload>
    <div v-if="fileList.length > 0">
      <el-table :data="fileList" border stripe>
        <el-table-column prop="name" label="文件" width="200">
          <template #default="{ row }">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="资料名称">
          <template #default="{ row }">
            <el-input
              v-model="row.materialName"
              style="width: 230px"
              maxlength="20"
              show-word-limit
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="60">
          <template #default="{ $index }">
            <el-button
              type="danger"
              circle
              @click="removeFile($index)"
              :icon="Delete"
            />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
