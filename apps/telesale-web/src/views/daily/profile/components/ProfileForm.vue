<!--
 * @Date         : 2024-05-29 15:30:30
 * @Description  : 上传资料
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { ref, computed, nextTick } from "vue";
import { uploadPermanentFileApi } from "/@/api/common";
import { storeToRefs } from "pinia";
import { useAppStore } from "/@/store/modules/app";
import {
  ElMessage,
  FormInstance,
  FormRules,
  UploadFile,
  UploadRequestOptions
} from "element-plus";
import { Upload } from "@element-plus/icons-vue";
import {
  addProfileApi,
  getProfileInfoApi,
  updateProfileApi
} from "/@/api/daily/profile";
import {
  goodTypeOptions,
  stageList,
  subjectList,
  subjectMap
} from "../data/index";
import BatchFileUpload from "./BatchFileUpload.vue";
import { cloneDeep } from "lodash";

interface Props {
  id?: number;
  multiple?: boolean;
  loading: boolean;
}

interface Emits {
  (e: "success"): void;
  (e: "close"): void;
  (e: "update:loading", val: boolean): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const { device } = storeToRefs(useAppStore());

const loading = computed({
  get() {
    return props.loading;
  },
  set(val: boolean) {
    emit("update:loading", val);
  }
});

const ruleFormRef = ref<FormInstance>();
const form = ref({
  materialName: "",
  fileList: [],
  materialPath: "",
  materialFile: "",
  belongGood: [],
  stage: [],
  subject: [],
  materialFileBatch: [],
  materialPathBatch: [],
  materialNameBatch: []
});

const rules: FormRules = {
  materialName: [
    {
      required: true,
      message: "请输入输入场景名称",
      trigger: "change"
    }
  ],
  fileList: [
    { required: true, message: "请上传文件", trigger: "change" },
    {
      validator: (_rule, value, callback) => {
        if (value && value.length > 50) {
          callback(new Error("最多上传50个文件"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  belongGood: [
    { required: true, message: "请选择所属商品", trigger: "change" }
  ],
  stage: [{ required: true, message: "请选择学段", trigger: "change" }],
  subject: [{ required: true, message: "请选择学科", trigger: "change" }]
};

const onExceed = () => {
  ElMessage.warning("只能上传一个文件");
};

const uploadFile = (file: UploadRequestOptions) => {
  const myFile = file.file;
  form.value.materialFile = myFile.name;
  const formData = new FormData();
  formData.append("file", myFile);
  loading.value = true;
  uploadPermanentFileApi(formData)
    .then(res => {
      form.value.materialPath = res.data;
      // 获取文件名,去掉.pdf后缀
      form.value.materialName = myFile.name.replace(".pdf", "").slice(0, 20);
      form.value.fileList = [
        {
          url: res.data,
          name: myFile.name
        }
      ];
      nextTick(() => {
        ruleFormRef.value.clearValidate("fileList");
      });
    })
    .catch(() => {
      if (form.value.fileList.length === 0) {
        form.value.fileList = [];
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const submitForm = () => {
  // 在提交前检查批量上传的资料名称
  if (props.multiple && form.value.fileList.length > 0) {
    // 检查是否有空的资料名称
    const emptyNames = form.value.fileList.filter(
      item => !item.materialName || item.materialName.trim() === ""
    );
    if (emptyNames.length > 0) {
      ElMessage.error("请填写所有资料名称");
      return;
    }

    // 检查资料名称是否重复
    const names = form.value.fileList.map(item => item.materialName);
    const uniqueNames = new Set(names);
    if (names.length !== uniqueNames.size) {
      // 找出重复的资料名称
      const duplicateNames = names.filter(
        (name, index) => names.indexOf(name) !== index
      );
      const uniqueDuplicates = [...new Set(duplicateNames)];
      ElMessage.error(
        `资料名称不能重复，重复的名称: ${uniqueDuplicates.join("、")}`
      );
      return;
    }
  }

  ruleFormRef.value?.validate(valid => {
    if (valid) {
      const params = cloneDeep(form.value);
      if (!props.id) {
        if (!props.multiple) {
          params.materialNameBatch = [params.materialName];
          params.materialFileBatch = [params.materialFile];
          params.materialPathBatch = [params.materialPath];
        } else {
          params.fileList?.forEach(item => {
            params.materialNameBatch.push(item.materialName);
            params.materialFileBatch.push(item.name);
            params.materialPathBatch.push(item.url);
          });
        }
      }
      loading.value = true;
      const fn = props.id ? updateProfileApi : addProfileApi;
      fn(params)
        .then(res => {
          if (res.data.errMaterialName?.length) {
            ElMessage.error(
              `资料名称已存在: ${res.data.errMaterialName.join("、")}`
            );
          } else {
            ElMessage.success("操作成功");
          }
          emit("success");
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
};

const getInfo = () => {
  loading.value = true;
  getProfileInfoApi(props.id)
    .then(res => {
      form.value = {
        fileList: [
          {
            name: res.data.materialFile,
            url: res.data.materialPath
          }
        ],
        ...res.data
      };
    })
    .finally(() => {
      loading.value = false;
    });
};

function handleMutuallyExclusive(arr: number[], value: number) {
  if (value === 1) {
    return arr.length > 1 ? [1] : arr;
  } else {
    return arr.filter(v => v !== 1);
  }
}

function handleGoodTypeChange(val: number[]) {
  form.value.belongGood = handleMutuallyExclusive(val, val[val.length - 1]);
}

function handleStageChange(val: number[]) {
  form.value.stage = handleMutuallyExclusive(val, val[val.length - 1]);
  form.value.subject = [];
}

function handleSubjectChange(val: number[]) {
  form.value.subject = handleMutuallyExclusive(val, val[val.length - 1]);
}

const subjectOptions = computed(() => {
  if (form.value.stage?.length === 0 || form.value.stage?.includes(1))
    return [];
  // 合并所有学段的学科 value
  const allSubjectValues = Array.from(
    new Set(form.value.stage?.flatMap(stage => subjectMap[stage] || []))
  );
  // 过滤 subjectList
  return subjectList.filter(item =>
    allSubjectValues.includes(Number(item.value))
  );
});

const closeModal = () => {
  emit("close");
};

defineExpose({
  submitForm,
  closeModal
});

props.id && getInfo();
</script>

<template>
  <div>
    <el-form
      :model="form"
      label-suffix="："
      :label-width="device !== 'mobile' ? '140px' : ''"
      ref="ruleFormRef"
      v-loading="loading"
      :rules="rules"
    >
      <template v-if="!props.multiple">
        <el-form-item label="上传文件" prop="fileList">
          <el-upload
            :file-list="form.fileList"
            action="#"
            :http-request="uploadFile"
            accept=".pdf"
            :limit="1"
            :on-exceed="onExceed"
            style="width: 100%"
          >
            <el-button :icon="Upload" type="primary">上传文件</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="资料名称" prop="materialName">
          <el-input
            v-model.trim="form.materialName"
            clearable
            placeholder="请输入资料名称"
            :maxlength="20"
            show-word-limit
          />
        </el-form-item>
      </template>
      <el-form-item label="所属商品" prop="belongGood">
        <el-checkbox-group
          v-model="form.belongGood"
          @change="handleGoodTypeChange"
        >
          <el-checkbox
            v-for="item in goodTypeOptions"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="学段" prop="stage">
        <el-checkbox-group v-model="form.stage" @change="handleStageChange">
          <el-checkbox
            v-for="item in stageList"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item v-if="subjectOptions.length" label="学科" prop="subject">
        <el-checkbox-group v-model="form.subject" @change="handleSubjectChange">
          <el-checkbox
            v-for="item in subjectOptions"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="批量上传" prop="fileList" v-if="props.multiple">
        <BatchFileUpload
          v-model:file-list="form.fileList"
          v-model:loading="loading"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item .el-input) {
  width: 300px;
}
</style>
