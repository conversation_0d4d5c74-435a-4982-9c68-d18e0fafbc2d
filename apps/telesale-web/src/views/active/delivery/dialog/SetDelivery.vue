<script lang="ts" setup>
import { computed, ref, reactive } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { logisticsCompanyList } from "/@/utils/data/common";
import { setDeliveryInfoApi } from "/@/api/active/delivery";

interface Props {
  value: boolean;
  dataMemory: any;
}

interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

let loading = ref<boolean>(false);
let device = useAppStoreHook().device;
const ruleFormRef = ref<FormInstance>();
const form = ref({
  trackingNum: props.dataMemory?.trackingNum || "",
  expressName: props.dataMemory?.expressName || "",
  logisticsId: props.dataMemory?.logisticsId
});
const rules = reactive<FormRules>({
  expressName: [
    {
      required: true,
      message: "请选择物流公司",
      trigger: "change"
    }
  ],
  trackingNum: [
    {
      required: true,
      message: "请输入物流单号",
      trigger: "blur"
    },
    {
      message: "物流单号格式有误",
      pattern: /^[a-zA-Z0-9]{1,50}$/,
      trigger: "blur"
    }
  ]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      setDeliveryInfoApi(form.value)
        .then(() => {
          ElMessage.success("操作成功");
          handleClose();
          emit("onSearch");
        })
        .finally(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};
</script>

<template>
  <el-dialog title="设置物流" v-model="isModel" :before-close="handleClose">
    <el-form
      :model="form"
      label-suffix="："
      :label-width="device !== 'mobile' ? '140px' : ''"
      ref="ruleFormRef"
      :class="{ mobile: device === 'mobile' }"
      :rules="rules"
      v-loading="loading"
    >
      <el-row>
        <el-col :lg="4" />
        <el-col :lg="16">
          <el-form-item prop="expressName" label="物流公司">
            <el-select
              v-model="form.expressName"
              placeholder="请选择物流公司"
              clearable
              filterable
            >
              <el-option
                v-for="item in logisticsCompanyList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="物流单号" prop="trackingNum">
            <el-input
              v-model.trim="form.trackingNum"
              placeholder="请输入物流单号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :lg="4" />
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" @click="submitForm(ruleFormRef)">
        确定
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
