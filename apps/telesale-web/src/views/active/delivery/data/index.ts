/*
 * @Date         : 2024-07-17 17:32:50
 * @Description  : 商品相关数据
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { ref } from "vue";
import { TableColumns } from "/@/components/ReTable/types";
import { getLabel } from "@telesale/shared";

const deliveryType = [
  {
    label: "待发货",
    value: 2
  },
  {
    label: "发货成功",
    value: 3
  }
];

export const listHeader = ref<TableColumns[]>([
  {
    field: "mobile",
    desc: "客户手机号",
    isCopy: true
  },
  {
    field: "rewardName",
    desc: "奖品名称"
  },
  {
    field: "deliverGoodsStatus",
    desc: "物流状态",
    filterOptions: {
      columns: deliveryType
    },
    customRender: ({ text }) => {
      return getLabel(text, deliveryType);
    }
  },
  {
    field: "rewardAt",
    desc: "中奖时间",
    timeChange: 2
  },
  {
    field: "consignee",
    desc: "收货人"
  },
  {
    field: "receiverMobile",
    desc: "收货手机号"
  },
  {
    field: "address",
    desc: "收货地址"
  }
]);
