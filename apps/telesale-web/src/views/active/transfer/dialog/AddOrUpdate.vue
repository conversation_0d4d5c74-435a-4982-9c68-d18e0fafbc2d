<!--
 * @Date         : 2024-07-18 16:37:46
 * @Description  : 新增编辑视频
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<script lang="ts" setup>
import { computed, ref } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import {
  addPlatformApi,
  getPlatformApi,
  PlatformForm,
  updatePlatformApi
} from "@telesale/server/src/api/active/transfer";

interface Props {
  value: boolean;
  id?: number;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});

const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

const loading = ref<boolean>(false);
const ruleFormRef = ref<FormInstance | null>();
const form = ref<PlatformForm>({
  id: undefined,
  name: undefined
});
const rules: FormRules = {
  name: [
    {
      required: true,
      message: "请输入平台名称",
      trigger: "change"
    }
  ]
};
function handleClose() {
  isModel.value = false;
}

const getInfo = () => {
  loading.value = true;
  getPlatformApi({ id: props.id })
    .then(res => {
      form.value = res.data.item;
    })
    .finally(() => {
      loading.value = false;
    });
};

const submit = async () => {
  await ruleFormRef.value?.validate(valid => {
    if (valid) {
      loading.value = true;
      const fn = form.value.id ? updatePlatformApi : addPlatformApi;
      fn(form.value)
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          handleClose();
          emit("onSearch");
        })
        .catch(err => {
          loading.value = false;
        });
    }
  });
};

props.id && getInfo();
</script>

<template>
  <el-dialog
    :title="props.id ? '编辑平台' : '新增平台'"
    v-model="isModel"
    :before-close="handleClose"
    @submit.prevent="submit"
  >
    <div v-loading="loading" class="flex justify-center">
      <el-form
        :model="form"
        label-suffix="："
        ref="ruleFormRef"
        v-loading="loading"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item label="平台名称" prop="name">
          <el-input
            v-model.trim="form.name"
            placeholder="请输入平台名称"
            :maxlength="5"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button type="primary" @click="submit" :disabled="loading">
        确认
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
</template>
