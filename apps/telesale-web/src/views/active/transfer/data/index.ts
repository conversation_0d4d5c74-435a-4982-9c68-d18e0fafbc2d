/*
 * @Date         : 2024-07-17 17:32:50
 * @Description  : 商品相关数据
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { ref } from "vue";
import { TableColumns } from "/@/components/ReTable/types";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";

const { allAgentObj } = storeToRefs(useUserStore());

export const listHeader = ref<TableColumns[]>([
  {
    field: "id",
    desc: "平台ID"
  },
  {
    field: "name",
    desc: "平台"
  },
  {
    field: "operatorId",
    desc: "创建人",
    customRender: ({ text }) => {
      return allAgentObj.value[text]?.name;
    }
  },
  {
    field: "createdAt",
    desc: "创建时间",
    timeChange: 3
  }
]);
