<script setup lang="ts" name="transfer">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { useUserStoreHook } from "/@/store/modules/user";
import { getListTransfer, updateStatus } from "/@/api/active";
import paramsHandle from "/@/utils/handle/paramsHandle";
import filterDataChange from "/@/utils/handle/filterDataChange";
import filterChanges from "/@/utils/handle/filterChanges";
import RePagination from "/@/components/RePagination/index.vue";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import { useDetail } from "../utils/toDetails";

const { toDetail } = useDetail();

let device = useAppStoreHook().device;

let statusList = [
  {
    id: "UP",
    name: "上架"
  },
  {
    id: "DOWN",
    name: "下架"
  }
];

const listHeader: any = [
  { field: "name", desc: "活动主题", minWidth: 150 },
  {
    field: "status",
    desc: "状态",
    minWidth: 180,
    typeChange: statusList,
    filtersList: filterDataChange(statusList, "name", "id")
  },
  {
    field: "operatorId",
    desc: "创建人",
    filters: row => useUserStoreHook().allAgentObj[row.operatorId]?.name,
    minWidth: 80
  },
  { field: "createdAt", desc: "创建时间", timeChange: 3, minWidth: 120 }
];

let operation = [
  {
    event: "up",
    text: "上架",
    isShow: row => row.status === "DOWN",
    popconfirm: "确定上架吗？"
  },
  {
    event: "down",
    text: "下架",
    isShow: row => row.status === "UP",
    popconfirm: "确定下架吗？"
  },
  { event: "edit", text: "编辑" },
  { event: "details", text: "查看详情" }
];

useUserStoreHook().authorizationMap.indexOf(
  "telesale_admin_transferActive_set"
) === -1 && operation.splice(0, 3);

//带分页列表数据必备
const loading = ref(false);
const dataList = ref([]);
const total = ref(0);

//分页
const rePaginationRefs = ref();
function onSearch() {
  rePaginationRefs.value.onSearch();
}

//表头筛选
function filterChange(row) {
  filterChanges(row, [{ name: "status", val: undefined }], form);
  onSearch();
}

//form查询
const form = reactive({
  name: "",
  status: undefined
});

function getList() {
  loading.value = true;
  getListTransfer(
    paramsHandle(form, {
      zero: ["status"],
      pageIndex: rePaginationRefs.value.pageIndex,
      pageSize: rePaginationRefs.value.pageSize
    })
  )
    .then(({ data }: { data: any }) => {
      dataList.value = data.list;
      total.value = data.total;
      loading.value = false;
    })
    .catch(() => {
      dataList.value = [];
      total.value = 0;
      loading.value = false;
    });
}

function parantMath({ key, params }) {
  switch (key) {
    case "edit":
      toDetail({ type: "edit", id: params.id + "" });
      break;
    case "details":
      toDetail({ type: "detail", id: params.id + "" });
      break;
    case "up":
      updateStatusMath(params, "UP");
      break;
    case "down":
      updateStatusMath(params, "DOWN");
      break;
  }
}

function add() {
  toDetail({ type: "add" });
}

function updateStatusMath(row, status) {
  loading.value = true;
  updateStatus({ id: row.id, status: status })
    .then(() => {
      ElMessage.success("操作成功");
      getList();
    })
    .catch(() => {
      loading.value = false;
    });
}

onMounted(() => {
  getList();
});
</script>

<template>
  <div v-loading="loading">
    <el-form
      ref="formRef"
      :inline="true"
      :model="form"
      class="clearfix"
      @submit.prevent
    >
      <el-form-item prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入活动主题"
          clearable
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item class="g-set-button">
        <el-button
          type="primary"
          @click="add"
          v-if="
            useUserStoreHook().authorizationMap.indexOf(
              'telesale_admin_transferActive_set'
            ) > -1
          "
        >
          新增
        </el-button>
      </el-form-item>
    </el-form>
    <div class="g-table-box">
      <ReTable
        v-if="device !== 'mobile'"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
        @parantMath="parantMath"
        :widthOperation="180"
        :filterChange="filterChange"
      />
      <ReCardList
        v-else
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
        @parantMath="parantMath"
      />
    </div>
    <RePagination ref="rePaginationRefs" :total="total" @getList="getList" />
  </div>
</template>
