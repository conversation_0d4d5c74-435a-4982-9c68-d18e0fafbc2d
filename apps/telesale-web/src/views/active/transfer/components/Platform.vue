<!--
 * @Date         : 2024-07-17 17:23:22
 * @Description  : 测试环境工具，新增商品
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { ref } from "vue";
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { listHeader } from "../data";
import { OperationObj } from "/@/components/ReTable/types";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import AddOrUpdate from "../dialog/AddOrUpdate.vue";
import { getPlatformListApi } from "@telesale/server/src/api/active/transfer";

const rowId = ref<number>();
const isModal = ref<boolean>(false);
const { dataList, onSearch, searchForm, loading } = useTable({
  api: getPlatformListApi,
  isPages: false,
  dataCallback(res) {
    res.data = res.data.list;
  }
});

const operation: OperationObj[] = [
  {
    text: "编辑",
    eventFn: row => {
      isModal.value = true;
      rowId.value = row.id;
    }
  }
];

const filterHeadData = (data: any) => {
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      searchForm.value[key] = data[key];
    }
  }
  onSearch();
};

const addGoods = () => {
  isModal.value = true;
  rowId.value = undefined;
};
</script>

<template>
  <div v-loading="loading">
    <div class="flex justify-end mb-10px">
      <el-button type="primary" @click="addGoods()">新增</el-button>
    </div>
    <div class="g-table-box">
      <ReTable
        v-if="!deviceDetection()"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
        :width-operation="130"
        @filter-head-data="filterHeadData"
      />
      <ReCardList
        v-else
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="listHeader"
        :operation="operation"
      />
    </div>
    <AddOrUpdate
      v-if="isModal"
      v-model:value="isModal"
      :id="rowId"
      @onSearch="onSearch"
    />
  </div>
</template>

<style lang="scss" scoped></style>
