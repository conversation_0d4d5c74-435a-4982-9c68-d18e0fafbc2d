/*
 * @Date         : 2024-07-19 10:00:00
 * @Description  : 裂变活动中奖明细相关数据
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { ref } from "vue";
import { TableColumns } from "/@/components/ReTable/types";
import { getLabel } from "/@/utils/common";
import { prizeStatusOptions } from "/@/api/active/fission";

// 表格列定义
export const listHeader = ref<TableColumns[]>([
  {
    field: "index",
    desc: "序号"
  },
  {
    field: "onionId",
    desc: "洋葱ID"
  },
  {
    field: "phone",
    desc: "手机号"
  },
  {
    field: "prizeName",
    desc: "奖品名称"
  },
  {
    field: "prizeTime",
    desc: "中奖时间"
  },
  {
    field: "prizeStatus",
    desc: "奖品状态",
    filterOptions: {
      columns: prizeStatusOptions
    },
    customRender: ({ text }) => {
      return getLabel(text, prizeStatusOptions);
    }
  },
  {
    field: "receiveTime",
    desc: "领取时间",
    customRender: ({ text }) => {
      return text || "--";
    }
  },
  {
    field: "activityName",
    desc: "活动名称"
  },
  {
    field: "activityTime",
    desc: "活动时间"
  }
]);
