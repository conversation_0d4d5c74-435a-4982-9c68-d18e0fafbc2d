<!--
 * @Date         : 2025-06-09 11:34:50
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import Delivery from "./components/delivery/index.vue";
import Search from "./components/Search.vue";
import Winning from "./components/winning/index.vue";
const active = ref("1");

const winningRef = ref();
const deliveryRef = ref();
const searchForm = ref({});

const onSearch = () => {
  winningRef.value?.onSearch();
  deliveryRef.value?.onSearch();
};

const reset = () => {
  nextTick(() => {
    winningRef.value?.reset();
    deliveryRef.value?.reset();
  });
};
</script>

<template>
  <div class="g-margin-20">
    <el-card>
      <Search
        ref="formRefs"
        v-model:form="searchForm"
        @onSearch="onSearch"
        @reset="reset"
      />
      <el-tabs v-model="active">
        <el-tab-pane label="中奖明细" name="1">
          <Winning ref="winningRef" :form="searchForm" />
        </el-tab-pane>
        <el-tab-pane label="发货管理" name="2">
          <Delivery ref="deliveryRef" :form="searchForm" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
