/*
 * @Date         : 2025-02-27 11:33:03
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { storeToRefs } from "pinia";
import { TableColumns } from "/@/components/ReTable/types";
import { useUserStore } from "/@/store/modules/user";
import { getLabel } from "@telesale/shared";
import { goodsTypeList } from "/@/utils/data/goodsTypeList";
import dayjs from "dayjs";
import { deliveryType } from "../../delivery/data";

const { allAgentObj } = storeToRefs(useUserStore());

export const deliveryStatus = [
  {
    label: "未发放",
    value: 2
  },
  {
    label: "已发放",
    value: 3
  }
];

export const columns: TableColumns[] = [
  {
    field: "onionId",
    desc: "洋葱ID",
    isCopy: true,
    fixed: "left",
    minWidth: 120
  },
  {
    field: "mobile",
    desc: "手机号",
    isCopy: true,
    fixed: "left",
    minWidth: 130
  },
  {
    field: "rewardType",
    desc: "奖品类型",
    filterOptions: {
      columns: goodsTypeList
    },
    customRender: ({ text }) => {
      return getLabel(text, goodsTypeList);
    },
    minWidth: 120
  },
  {
    field: "rewardName",
    desc: "奖品名称",
    minWidth: 120
  },
  {
    field: "rewardAt",
    desc: "中奖时间",
    timeChange: 2,
    minWidth: 160
  },
  {
    field: "deliverGoodsStatus",
    desc: "发放状态",
    filterOptions: {
      columns: [
        {
          label: "未发放/待发货",
          value: 2
        },
        {
          label: "已发放/发货成功",
          value: 3
        }
      ]
    },
    customRender: ({ row, text }) => {
      if (row.rewardType === 1) {
        return getLabel(text, deliveryStatus);
      } else {
        return getLabel(text, deliveryType);
      }
    },
    minWidth: 120
  },
  {
    field: "activityName",
    desc: "活动名称",
    minWidth: 120
  },
  {
    field: "activityTime",
    desc: "活动时间",
    customRender: ({ text }) => {
      if (!text) return "";

      const list = text?.map(item => {
        return dayjs(item * 1000).format("YYYY-MM-DD HH:mm:ss");
      });
      return list.join("至");
    },
    minWidth: 150
  }
];
