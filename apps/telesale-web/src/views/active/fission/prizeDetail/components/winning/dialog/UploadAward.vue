<!--
 * @Date         : 2025-03-03 16:19:41
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { computed, ref } from "vue";
import { genFileId } from "element-plus";
import type { UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import UploadData from "../components/UploadData.vue";
import { parseWinningRecordExcelApi } from "@telesale/server/src/api/active/fission/prizeDetail";

interface Props {
  value: boolean;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    return props.value;
  },
  set(val: boolean) {
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

function onSearch() {
  handleClose();
  emit("onSearch");
}

const loading = ref<boolean>(false);
const fileData = ref();
const isModal = ref<boolean>(false);
const dataUpload = ref();

const uploadRef = ref<UploadInstance>();

const handleExceed: UploadProps["onExceed"] = files => {
  uploadRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  uploadRef.value!.handleStart(file);
};

function removeFile() {
  fileData.value = "";
}

function uploadFile(file) {
  fileData.value = file.raw;
}

function openUpload(file) {
  let formData = new FormData();
  formData.append("file", fileData.value);
  loading.value = true;
  parseWinningRecordExcelApi(formData)
    .then(({ data }: { data }) => {
      dataUpload.value = data;
      isModal.value = true;
    })
    .finally(() => {
      loading.value = false;
    });
}

function jumpLink() {
  window.open(
    "https://guanghe.feishu.cn/sheets/Tr0LsgTZahT8RStbKukcktFEnje?from=from_copylink"
  );
}
</script>

<template>
  <el-dialog title="上传中奖明细" v-model="isModel" :before-close="handleClose">
    <div v-loading="loading">
      <p>
        1、点击下方按钮打开在线模板，根据模板提示填写信息，并将其导出为Excel文件。
      </p>
      <el-button class="g-margin-20" type="primary" @click="jumpLink">
        查看模板
      </el-button>
      <p>2、上传Excel文件</p>
      <div class="g-margin-20">
        <el-upload
          ref="uploadRef"
          action="#"
          :limit="1"
          :on-exceed="handleExceed"
          :on-remove="removeFile"
          :on-change="uploadFile"
          :auto-upload="false"
          accept=".xlsx"
        >
          <el-button>
            <IconifyIconOffline icon="upload-filled" style="font-size: 18px" />
            &nbsp;选择文件
          </el-button>
        </el-upload>
        <div
          v-show="!fileData"
          style="color: #f56c6c; font-size: 12px; padding-left: 10px"
        >
          未选择任何文件
        </div>
      </div>
    </div>
    <template #footer>
      <el-button
        type="primary"
        @click="openUpload"
        :disabled="!fileData || loading"
      >
        确定
      </el-button>
      <el-button @click="handleClose">取消</el-button>
    </template>
  </el-dialog>
  <UploadData
    v-if="isModal"
    v-model:value="isModal"
    :list="dataUpload"
    @onSearch="onSearch"
  />
</template>
