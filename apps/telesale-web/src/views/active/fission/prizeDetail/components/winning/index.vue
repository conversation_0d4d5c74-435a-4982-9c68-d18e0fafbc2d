<!--
 * @Date         : 2025-02-27 11:31:38
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->
<!--
 * @Date         : 2024-07-17 17:23:22
 * @Description  : 测试环境工具，新增商品
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup name="jdCard">
import { ref } from "vue";
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { columns } from "./data/index";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import UploadAward from "./dialog/UploadAward.vue";
import {
  delWinningRecordApi,
  exportWinningRecordApi,
  listRewardsRecordAllApi,
  virtualRewardsDeliveryApi
} from "@telesale/server/src/api/active/fission/prizeDetail";
import Search from "../delivery/components/Search.vue";
import { cloneDeep } from "lodash-es";
import { formatTime } from "/@/utils/common";
import { downloadFile } from "@telesale/shared";
import { OperationObj } from "/@/components/ReTable/types";

const props = defineProps<{
  form: any;
}>();

const emits = defineEmits(["update:form"]);

const searchForm = computed({
  get() {
    return props.form;
  },
  set(val) {
    emits("update:form", val);
  }
});

const tableRefs = ref<InstanceType<typeof ReTable>>();
const isModal = ref<boolean>(false);
const { dataList, Pagination, onSearch, loading } = useTable({
  api: listRewardsRecordAllApi,
  beforeRequest: data => {
    const res = cloneDeep({
      ...data,
      ...(props.form || {})
    });
    formatTime(res, "rewardAtStart", "rewardAtEnd", "rewardTime");
    return res;
  }
});

const operation: OperationObj[] = [
  {
    text: "删除",
    disabled: row => row.source !== 2 || row.deliverGoodsStatus !== 2,
    eventFn: row => {
      // 二次弹窗确认删除
      ElMessageBox.confirm("是否确认删除该奖品", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        loading.value = true;
        delWinningRecordApi({ id: row.id })
          .then(res => {
            ElMessage.success("操作成功");
            onSearch();
          })
          .finally(() => {
            loading.value = false;
          });
      });
    }
  }
];

const reset = () => {
  tableRefs.value?.clearSort();
  tableRefs.value?.resetFilter();
  onSearch();
};

const exportData = () => {
  const data = cloneDeep(searchForm.value);
  formatTime(data, "rewardAtStart", "rewardAtEnd", "rewardTime");
  loading.value = true;
  exportWinningRecordApi(data)
    .then(res => {
      downloadFile(res);
    })
    .finally(() => {
      loading.value = false;
    });
};

// 表格筛选处理
const filterHeadData = (data: any) => {
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      searchForm.value[key] = data[key];
    }
  }
  onSearch();
};

const sendGood = () => {
  // 弹窗二次确认，弹窗文案【是否确认发放虚拟奖品】
  ElMessageBox.confirm("是否确认发放虚拟奖品", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    loading.value = true;
    virtualRewardsDeliveryApi()
      .then(res => {
        ElMessage.success("操作成功");
        onSearch();
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

defineExpose({
  onSearch,
  reset
});
</script>

<template>
  <div v-loading="loading">
    <div class="flex justify-end mb-10px">
      <el-button type="primary" plain @click="sendGood">虚拟奖品发放</el-button>
      <el-button type="primary" @click="isModal = true">上传</el-button>
      <el-button type="primary" @click="exportData">导出</el-button>
    </div>
    <div class="g-table-box">
      <ReTable
        v-if="!deviceDetection()"
        ref="tableRefs"
        :dataList="dataList"
        :listHeader="columns"
        indexCode
        @filter-head-data="filterHeadData"
        :operation="operation"
      />
      <ReCardList
        v-else
        ref="cardRefs"
        :dataList="dataList"
        :listHeader="columns"
      />
    </div>
    <Pagination />
    <UploadAward v-if="isModal" v-model:value="isModal" @onSearch="onSearch" />
  </div>
</template>

<style lang="scss" scoped>
.sum-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--el-color-primary);
  color: #fff;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 10px;
  font-size: 16px;
}
.sum-item-title {
  font-size: 20px;
}
</style>
