<!--
 * @Date         : 2025-02-27 11:44:57
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup>
import { computed, ref, onUnmounted } from "vue";
import ReTable from "/@/components/ReTable/index.vue";
import { setBatchDeliveryInfoApi } from "/@/api/active/delivery";

interface Props {
  value: boolean;
  list: any;
}
interface Emits {
  (e: "update:value", val: boolean): void;
  (e: "onSearch"): void;
}

const emit = defineEmits<Emits>();
const props = withDefaults(defineProps<Props>(), {});
const isModel = computed({
  get() {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    props.value;
    return props.value;
  },
  set(val: boolean) {
    isImport.value = false;
    emit("update:value", val);
  }
});

function handleClose() {
  isModel.value = false;
}

const loading = ref<boolean>(false);
const isImport = ref<boolean>(props.list.some(item => item.msg));
const tableData = ref(props.list);
const listHeader = ref([
  { field: "logisticsId", desc: "订单号", minWidth: 150 },
  { field: "expressName", desc: "物流公司", minWidth: 130 },
  { field: "trackingNum", desc: "物流单号" },
  { field: "msg", desc: "异常说明", minWidth: 150, htmlChange: true }
]);

function query() {
  loading.value = true;
  isImport.value = true;
  setBatchDeliveryInfoApi({
    records: props.list.map(item => {
      return {
        logisticsId: item.logisticsId,
        expressName: item.expressName,
        trackingNum: item.trackingNum
      };
    })
  })
    .then(() => {
      ElMessage.success(`已成功导入${props.list.length}条`);
      isModel.value = false;
      emit("onSearch");
    })
    .catch(() => {})
    .finally(() => {
      isImport.value = false;
      loading.value = false;
    });
}
</script>

<template>
  <el-dialog
    title="导入数据展示"
    v-model="isModel"
    :before-close="handleClose"
    fullscreen
  >
    <div v-loading="loading">
      <div class="d-head">
        <el-popconfirm title="确定导入这些数据吗？" @confirm="query">
          <template #reference>
            <el-button type="primary" :disabled="isImport">数据导入</el-button>
          </template>
        </el-popconfirm>
      </div>
      <ReTable :dataList="tableData" :listHeader="listHeader" indexCode />
    </div>
  </el-dialog>
</template>

<style scoped>
.d-head {
  position: fixed;
  left: 48%;
  top: 11px;
}
</style>
