/*
 * @Date         : 2024-07-19 10:00:00
 * @Description  : 裂变活动用户参与明细相关数据
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { ref } from "vue";
import { TableColumns } from "/@/components/ReTable/types";
import { getLabel } from "/@/utils/common";
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import dayjs from "dayjs";

const { allAgentObj } = storeToRefs(useUserStore());

// 是否购课选项
export const purchaseOptions = [
  {
    label: "是",
    value: 1
  },
  {
    label: "否",
    value: 2
  }
];

// 主表格列定义
export const mainListHeader = ref<TableColumns[]>([
  {
    field: "onionId",
    desc: "邀请人洋葱ID",
    minWidth: 120,
    isCopy: true,
    fixed: "left"
  },
  {
    field: "inviteeOnionId",
    desc: "被邀请人洋葱ID",
    isCopy: true,
    minWidth: 130,
    fixed: "left"
  },
  {
    field: "inviteePhone",
    desc: "被邀请人手机号",
    isCopy: true,
    minWidth: 130
  },
  {
    field: "bindTime",
    desc: "绑定时间",
    minWidth: 160,
    timeChange: 2
  },
  {
    field: "wechatAddTime",
    desc: "成功添加企微时间",
    minWidth: 160,
    customRender: ({ text }) => {
      if (!text) return "-";

      return dayjs(text * 1000).format("YYYY-MM-DD HH:mm:ss");
    }
  },
  {
    field: "hasPurchased",
    desc: "是否购课",
    customRender: ({ text }) => {
      return text ? "是" : "否";
    },
    minWidth: 100
  },
  {
    field: "historyAmount",
    desc: "历史付费总金额",
    minWidth: 130
  },
  {
    field: "activityName",
    desc: "活动名称",
    minWidth: 120
  },
  {
    field: "activityTime",
    desc: "活动时间",
    customRender: ({ text }) => {
      if (!text) return "-";

      const list = text?.map(item => {
        return dayjs(item * 1000).format("YYYY-MM-DD HH:mm:ss");
      });
      return list.join("至");
    },
    minWidth: 180
  }
]);

// 子表格列定义
export const subListHeader = ref<TableColumns[]>([
  {
    field: "orderId",
    desc: "订单号"
  },
  {
    field: "goodName",
    desc: "订单名称"
  },
  {
    field: "paidTime",
    desc: "支付成功时间",
    timeChange: 2
  },
  {
    field: "amount",
    desc: "实付"
  },
  {
    field: "workerId",
    desc: "坐席",
    customRender: ({ text }) => {
      return allAgentObj.value?.[text]?.name || "-";
    }
  },
  {
    field: "groupName",
    desc: "所属小组"
  }
]);
