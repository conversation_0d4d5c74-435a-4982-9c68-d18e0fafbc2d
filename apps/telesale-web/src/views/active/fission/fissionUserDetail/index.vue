<!--
 * @Date         : 2024-07-19 10:00:00
 * @Description  : 裂变活动用户参与明细
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup name="FissionUserDetail">
import { ref, reactive } from "vue";
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { mainListHeader, subListHeader } from "./data";
import { OperationObj } from "/@/components/ReTable/types";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import Search from "./components/Search.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  exportInviteRecordApi,
  listInviteRecordApi,
  type InviteRecord
} from "@telesale/server/src/api/active/fission/fissionUserDetail";
import { formatTime } from "/@/utils/common";
import { cloneDeep } from "lodash-es";
import { downloadFile } from "@telesale/shared";

// 展开行的ID
const expandedRowIds = ref<number[]>([]);

// 使用useTable钩子获取表格数据
const { dataList, Pagination, onSearch, searchForm, loading } = useTable({
  api: listInviteRecordApi,
  beforeRequest: data => {
    return handleData(data);
  }
});

// 表格操作列定义
const operation: OperationObj[] = [
  {
    text: "查看订单明细",
    isShow: (row: InviteRecord) => !expandedRowIds.value.includes(row.id),
    eventFn: (row: InviteRecord) => {
      expandRow(row);
    }
  },
  {
    text: "收起订单明细",
    isShow: (row: InviteRecord) => expandedRowIds.value.includes(row.id),
    eventFn: (row: InviteRecord) => {
      collapseRow(row);
    }
  }
];

// 展开行，获取订单明细
const expandRow = async (row: InviteRecord) => {
  if (!expandedRowIds.value.includes(row.id)) {
    expandedRowIds.value.push(row.id);
  }
};

// 收起行
const collapseRow = (row: InviteRecord) => {
  const index = expandedRowIds.value.findIndex(id => id === row.id);
  if (index !== -1) {
    expandedRowIds.value.splice(index, 1);
  }
};

function handleData(form) {
  const data = cloneDeep(form);
  formatTime(data, "bindTimeStart", "bindTimeEnd", "bindTime");
  return data;
}

// 导出数据
const exportData = () => {
  ElMessageBox.confirm("确定要导出数据吗?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const data = handleData(searchForm.value);
    loading.value = true;
    exportInviteRecordApi(data)
      .then(res => {
        downloadFile(res);
      })
      .finally(() => {
        loading.value = false;
      });
  });
};

// 表格筛选处理
const filterHeadData = (data: any) => {
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      searchForm.value[key] = data[key];
    }
  }
  onSearch();
};
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <Search ref="formRefs" v-model:form="searchForm" @onSearch="onSearch" />
      <div class="flex justify-end mb-10px">
        <el-button type="primary" @click="exportData">导出</el-button>
      </div>
      <div class="g-table-box">
        <ReTable
          v-if="!deviceDetection()"
          ref="tableRefs"
          :dataList="dataList"
          row-key="id"
          :listHeader="mainListHeader"
          :operation="operation"
          :width-operation="130"
          @filter-head-data="filterHeadData"
          :defaultExpandRows="expandedRowIds"
          index-code
        >
          <!-- 展开行插槽 -->
          <template #expand="{ row }">
            <div
              v-if="expandedRowIds.includes(row.id)"
              class="flex justify-center py-10px"
            >
              <div v-if="row.orderDetails?.length" class="w-95%">
                <ReTable
                  :dataList="row.orderDetails"
                  :listHeader="subListHeader"
                />
              </div>
              <div v-else class="w-full text-center text-16px">暂无数据</div>
            </div>
          </template>
        </ReTable>
        <ReCardList
          v-else
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="mainListHeader"
          :operation="operation"
        />
      </div>
      <Pagination />
    </el-card>
  </div>
</template>

<style lang="scss" scoped>
.sub-table-container {
  padding: 16px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin: 8px 0;
}

.sub-table {
  width: 100%;
  min-height: 100px;
}

.sub-table-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}
</style>
