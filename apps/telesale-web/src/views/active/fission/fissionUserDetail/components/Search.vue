<!--
 * @Date         : 2024-07-19 10:00:00
 * @Description  : 裂变活动用户参与明细查询组件
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import { computed, ref } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";
import { purchaseOptions } from "../data";
import { FormInstance } from "element-plus";

interface Emits {
  (e: "onSearch"): void;
  (e: "update:form", val: any): void;
}

const props = defineProps<{
  form: any;
}>();

const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();

function onSearch() {
  emit("onSearch");
}

const form = computed({
  get() {
    return props.form;
  },
  set(val: any) {
    emit("update:form", val);
  }
});

const reset = () => {
  formRef.value?.resetFields();
  onSearch();
};

const clearValue = (key: string) => {
  form.value[key] = undefined;
};
</script>

<template>
  <el-form
    ref="formRef"
    :inline="true"
    :model="form"
    class="clearfix"
    @submit.prevent
  >
    <el-form-item prop="onionId">
      <el-input
        v-model="form.onionId"
        placeholder="邀请人洋葱ID"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="inviteeOnionId">
      <el-input
        v-model="form.inviteeOnionId"
        placeholder="被邀请人洋葱ID"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="inviteePhone">
      <el-input
        v-model="form.inviteePhone"
        placeholder="被邀请人手机号"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item prop="bindTime">
      <SyncDatePicker
        v-model:value="form.bindTime"
        type="datetimerange"
        value-format="x"
        range-separator="至"
        start-placeholder="绑定时间-开始"
        end-placeholder="绑定时间-结束"
        :default-time="[
          new Date(2000, 1, 1, 0, 0, 0),
          new Date(2000, 2, 1, 23, 59, 59)
        ]"
      />
    </el-form-item>
    <el-form-item prop="hasPurchased">
      <el-select
        v-model="form.hasPurchased"
        placeholder="是否购课"
        clearable
        @clear="clearValue('hasPurchased')"
      >
        <el-option
          v-for="item in purchaseOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
      <el-button :icon="useRenderIcon('refresh')" @click="reset">
        重置
      </el-button>
    </el-form-item>
  </el-form>
</template>
