<script setup lang="ts">
import { computed } from "vue";
import erCode from "/@/assets/erCode.png";

interface Props {
  picUrl: any;
  bottomColor: string;
  navigationFontColor: string;
  type: string;
}
const props = withDefaults(defineProps<Props>(), {
  bottomColor: "#ffffff",
  navigationFontColor: "#000000",
  type: "default"
});

let bg = computed(() => {
  if (
    props.bottomColor.length === 6 &&
    /^[0-9a-fA-F]{6}$/.test(props.bottomColor)
  ) {
    return "#" + props.bottomColor;
  } else {
    return "#ffffff";
  }
});
</script>

<template>
  <div
    class="d-box"
    :style="{
      background: bg,
      transformOrigin: '0 0',
      transform: type !== 'default' ? 'scale(0.5)' : ''
    }"
  >
    <div class="d-nav" :style="'color:' + navigationFontColor">
      <span class="d-left" />
      顶部导航栏
      <span class="d-rit" />
    </div>
    <div class="d-body">
      预览区
      <img class="d-bg" v-show="picUrl.imageUrl" :src="picUrl.imageUrl" />
      <img class="d-btn" v-show="picUrl.button" :src="picUrl.button" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.d-box {
  width: 377px;
  height: 719px;
  border: 1px solid #ccc;
  border-radius: 4px;
  text-align: center;

  .d-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 375px;
    height: 50px;
    padding: 10px 10px 0;
    .d-left {
      display: inline-block;
      height: 25px;
      width: 25px;
      border-radius: 50%;
      margin-right: 55px;
      background-color: #eee;
    }
    .d-rit {
      display: inline-block;
      height: 25px;
      width: 80px;
      border-radius: 25px;
      background-color: #eee;
    }
  }
  .d-body {
    writing-mode: tb;
    font-size: 50px;
    line-height: 375px;
    height: 667px;
    position: relative;
    overflow: hidden;
    overflow-y: auto;
    .d-bg {
      width: 375px;
      position: absolute;
      top: 0;
      left: 0;
    }
    .d-btn {
      position: sticky;
      left: 8px;
      top: 590px;
      width: 360px;
      height: 74px;
      animation: big_in_small 1s ease-in-out 0.1s infinite forwards;
    }
    .d-code {
      width: 170px;
      height: 170px;
      position: absolute;
      top: 255px;
      left: 102.5px;
    }
  }
}

@keyframes big_in_small {
  0% {
    transform: scale(1);
  }

  25% {
    transform: scale(0.8);
  }

  50% {
    transform: scale(1);
  }

  75% {
    transform: scale(0.8);
  }
}
</style>
