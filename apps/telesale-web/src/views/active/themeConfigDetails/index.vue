<script setup lang="ts" name="uiConfigDetails">
import { ref, reactive } from "vue";
import { useRoute } from "vue-router";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useAppStoreHook } from "/@/store/modules/app";
import { closePageBox } from "/@/utils/handle/closePage";
import { detailConfig, createConfig, updateConfig } from "/@/api/active";
import { upload, getPic } from "/@/api/order";
import UITemplate from "./components/UITemplate.vue";
import {
  createExternalConfigApi,
  getExternalConfigInfoApi,
  updateExternalConfigApi
} from "/@/api/active/themeConfig";

const { closePage } = closePageBox();
let device = useAppStoreHook().device;
const loading = ref<boolean>(false);

const route = useRoute();
const id = (route.query?.id as string) || "";
const type = (route.query?.type as string) || "";

const picUrl = reactive({
  imageUrl: "",
  button: ""
});
//获取图片
function getPicMath(val, noSkip, key) {
  loading.value = true;
  getPic(val)
    .then(({ data }: { data: any }) => {
      picUrl[key] = data;
      noSkip && (form[key] = val);
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
//图片上传
function uploadPicBackground(file) {
  uploadPic(file, "imageUrl");
}
function uploadPicButton(file) {
  uploadPic(file, "button");
}
function uploadPic(file, key) {
  if (file.file.size / 1024 / 1024 > 1) {
    ElMessage.warning("图片大小不能超过1M");
    return;
  }
  loading.value = true;
  let formData = new FormData();
  formData.append("file", file.file);
  upload(formData)
    .then(({ data }: { data: any }) => {
      getPicMath(data, true, key);
    })
    .catch(() => {
      loading.value = false;
    });
}

function clearBtn() {
  form.button = "";
  picUrl.button = "";
}

//form查询
const form: any = reactive({
  name: undefined,
  qrCodeId: undefined,
  imageUrl: "",
  button: "",
  bottomColor: "" //4cb4fd
});

const formRef = ref<FormInstance>();
const rules = reactive<FormRules>({
  name: [
    {
      required: true,
      message: "请输入引流页主题",
      trigger: "blur"
    }
  ],
  qrCodeId: [
    {
      required: true,
      message: "请输入渠道活码ID",
      trigger: "blur"
    }
  ],
  imageUrl: [
    {
      required: true,
      message: "请上传图片",
      trigger: "change"
    }
  ],
  bottomColor: [
    {
      required: true,
      message: "底色色值格式有误",
      pattern: /^[0-9a-fA-F]{6}$/,
      trigger: "blur"
    }
  ],
  navigationFontColor: [
    {
      required: true,
      message: "请选择导航栏文字色值",
      trigger: "change"
    }
  ]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate(valid => {
    if (valid) {
      loading.value = true;
      let method =
        type === "add" ? createExternalConfigApi : updateExternalConfigApi;
      method({ ...form })
        .then(() => {
          loading.value = false;
          ElMessage.success("操作成功");
          closePage(id, "ThemeConfig");
        })
        .catch(() => {
          loading.value = false;
        });
    } else {
      return false;
    }
  });
};

function getDetails() {
  loading.value = true;
  getExternalConfigInfoApi({ id: Number(id) })
    .then(({ data }: { data: any }) => {
      form.name = data.name;
      form.imageUrl = data.imageUrl;
      form.button = data.button;
      form.qrCodeId = data.qrCodeId;
      form.id = id;
      getPicMath(form.imageUrl, false, "imageUrl");
      form.button && getPicMath(form.button, false, "button");
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}

if (type !== "add") {
  getDetails();
} else {
  delete form.id;
  form.name = "";
  form.imageUrl = "";
  form.button = "";
}
</script>
<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <el-form
        :model="form"
        :class="device"
        label-suffix="："
        :label-width="device !== 'mobile' ? '140px' : ''"
        ref="formRef"
        :rules="rules"
      >
        <el-row :gutter="22">
          <el-col :md="12">
            <el-form-item label="引流页主题" prop="name" class="g-width-all">
              <el-input
                v-model.trim="form.name"
                clearable
                maxlength="20"
                show-word-limit
                :disabled="type === 'detail'"
              />
            </el-form-item>
            <el-form-item
              label="对应渠道活码ID"
              prop="qrCodeId"
              class="g-width-all"
            >
              <el-input
                v-model.trim="form.qrCodeId"
                clearable
                :disabled="type === 'detail' || type === 'edit'"
              />
            </el-form-item>
            <el-form-item label="上传图片" prop="imageUrl">
              <el-upload
                action="#"
                :http-request="uploadPicBackground"
                :show-file-list="false"
                class="d-upload"
                accept=".jpg,.jpeg,.png,.JPG,.JPEG,.webp"
                :disabled="type === 'detail'"
                :style="{ borderWidth: picUrl.imageUrl ? '0' : '1px' }"
              >
                <div v-if="!picUrl.imageUrl" class="d-text">
                  <IconifyIconOffline
                    icon="upload-filled"
                    class="el-icon-upload"
                  />
                  <p>请上传750*1334px的png、jpg格式的图片，且大小不超过1M</p>
                  <span>背景图片</span>
                </div>
                <img
                  v-else
                  :src="picUrl.imageUrl"
                  class="d-pic"
                  alt="背景图片"
                />
              </el-upload>
            </el-form-item>
            <el-form-item label="上传配置按钮" prop="button">
              <el-upload
                action="#"
                :http-request="uploadPicButton"
                :show-file-list="false"
                class="d-upload d-upload-button"
                accept=".jpg,.jpeg,.png,.JPG,.JPEG,.webp"
                :disabled="type === 'detail'"
                :style="{ borderWidth: picUrl.button ? '0' : '1px' }"
              >
                <div v-if="!picUrl.button" class="d-text">
                  <p>点击上传</p>
                </div>
                <img
                  v-else
                  :src="picUrl.button"
                  class="d-pic-btn"
                  alt="按钮图片"
                />
              </el-upload>
              <IconifyIconOffline
                class="d-close"
                icon="circle-close"
                @click="clearBtn"
                v-if="type !== 'detail'"
                v-show="!!form.button"
              />
              <p style="color: #999">
                请上传240*64px的png、jpg格式的图片，且大小不超过1M
              </p>
            </el-form-item>
          </el-col>
          <el-col :md="12">
            <UITemplate
              ref="UITemplateRef"
              :picUrl="picUrl"
              :bottomColor="form.bottomColor"
              :navigationFontColor="form.navigationFontColor"
              type="default"
            />
          </el-col>
        </el-row>
        <el-form-item>
          <el-button @click="closePage(id, 'ThemeConfig')">返回</el-button>
          <el-button
            type="primary"
            @click="submitForm(formRef)"
            v-if="type !== 'detail'"
          >
            确定
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>
<style scoped lang="scss">
@mixin wh() {
  width: 187.5px;
  height: 333.5px;
}
@mixin whBtn() {
  width: 120px;
  height: 32px;
}
.d-pic {
  @include wh;
  display: block;
}
.d-pic-btn {
  @include whBtn;
  display: block;
}
.d-upload {
  @include wh;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  text-align: center;
  &.d-upload-button {
    @include whBtn;

    .d-text {
      @include whBtn;
    }
  }
  :deep(.el-upload:focus) {
    color: #2c3e50;
  }
  .d-text {
    @include wh;
    .el-icon-upload {
      width: 67px;
      height: 120px;
      margin-top: 30px;
      color: #c0c4cc;
      line-height: 180px;
    }
    p {
      line-height: 1.5;
      padding: 0 20px;
      margin-bottom: 20px;
    }
    span {
      font-weight: 600;
      font-size: 16px;
    }
  }
}
.d-close {
  cursor: pointer;
  opacity: 0.5;
  font-size: 28px;
}
</style>
