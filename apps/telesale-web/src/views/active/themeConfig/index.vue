<!--
 * @Date         : 2024-07-17 17:23:22
 * @Description  : 引流主题配置
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script lang="ts" setup name="ThemeConfig">
import { ref } from "vue";
import { useTable } from "/@/hooks/useTable";
import { deviceDetection } from "/@/utils/deviceDetection";
import { listHeader } from "./data/index";
import { OperationObj } from "/@/components/ReTable/types";
import ReTable from "/@/components/ReTable/index.vue";
import ReCardList from "/@/components/ReCardList/index.vue";
import Search from "./components/Search.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import {
  deleteExternalConfigApi,
  getExternalConfigListApi
} from "/@/api/active/themeConfig";
import { useMultiTagsStoreHook } from "/@/store/modules/multiTags";
import { title } from "process";

// 假设这些是API函数，实际使用时需要替换为真实的API
const router = useRouter();

const rowId = ref<number>();
const isModal = ref<boolean>(false);
const { dataList, Pagination, onSearch, searchForm, loading } = useTable({
  api: getExternalConfigListApi,
  dataCallback(res) {
    res.data.list = res.data.list || [];
  }
});

const operation: OperationObj[] = [
  {
    text: "查看详情",
    eventFn: row => {
      goDetailPage("detail", row.id);
    }
  },
  {
    text: "编辑",
    eventFn: row => {
      goDetailPage("edit", row.id);
    }
  },
  {
    text: "删除",
    eventFn: row => {
      ElMessageBox.confirm("确定是否要删除此主题?", "提示", {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        loading.value = true;
        deleteExternalConfigApi({ id: row.id })
          .then(() => {
            onSearch();
            ElMessage.success("删除成功");
          })
          .finally(() => {
            loading.value = false;
          });
      });
    }
  }
];

const filterHeadData = (data: any) => {
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      searchForm.value[key] = data[key];
    }
  }
  onSearch();
};

const goDetailPage = (type: string, id?: number) => {
  const titleObj = {
    add: "引流页主题配置新增",
    edit: "引流页主题配置编辑",
    detail: "引流页主题配置详情"
  };
  const params = {
    id,
    time: String(new Date().getTime()),
    type
  };
  useMultiTagsStoreHook().handleTags("push", {
    path: "/active/themeConfigDetails",
    name: "ThemeConfigDetails",
    query: params,
    meta: {
      title: titleObj[type],
      dynamicLevel: 1,
      keepAlive: true
    }
  });
  router.push({ name: "ThemeConfigDetails", query: params });
};
</script>

<template>
  <div class="g-margin-20" v-loading="loading">
    <el-card>
      <div class="flex justify-between">
        <Search ref="formRefs" v-model:form="searchForm" @onSearch="onSearch" />
        <el-button type="primary" @click="goDetailPage('add')">新增</el-button>
      </div>
      <div class="g-table-box">
        <ReTable
          v-if="!deviceDetection()"
          ref="tableRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
          :width-operation="180"
          @filter-head-data="filterHeadData"
        />
        <ReCardList
          v-else
          ref="cardRefs"
          :dataList="dataList"
          :listHeader="listHeader"
          :operation="operation"
        />
      </div>
      <Pagination />
    </el-card>
  </div>
</template>

<style lang="scss" scoped></style>
