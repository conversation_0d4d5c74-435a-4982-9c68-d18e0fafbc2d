import { storeToRefs } from "pinia";
import { TableColumns } from "/@/components/ReTable/types";
import { useUserStore } from "/@/store/modules/user";

const { allAgentObj } = storeToRefs(useUserStore());

export const listHeader = ref<TableColumns[]>([
  {
    field: "name",
    desc: "活动主题"
  },
  {
    field: "qrCodeId",
    desc: "渠道活码ID"
  },
  {
    field: "createdBy",
    desc: "创建人",
    customRender: ({ text }) => {
      return allAgentObj.value[text]?.name || "";
    }
  },
  {
    field: "createdAt",
    desc: "创建时间",
    timeChange: 3
  }
]);
