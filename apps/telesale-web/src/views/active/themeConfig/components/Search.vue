<!--
 * @Date         : 2024-07-17 17:29:45
 * @Description  : 查询组件
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
-->

<script setup lang="ts">
import { computed, ref } from "vue";
import { useRenderIcon } from "/@/components/ReIcon/src/hooks";

interface ThemeReq {
  name?: string;
}

interface Emits {
  (e: "onSearch"): void;
  (e: "update:form", val: ThemeReq): void;
}

const props = defineProps<{
  form: ThemeReq;
}>();

const emit = defineEmits<Emits>();

function onSearch() {
  emit("onSearch");
}

const form = computed({
  get() {
    return props.form;
  },
  set(val: ThemeReq) {
    emit("update:form", val);
  }
});
</script>
<template>
  <el-form
    ref="formRef"
    :inline="true"
    :model="form"
    class="clearfix"
    @submit.prevent
  >
    <el-form-item prop="name">
      <el-input
        v-model="form.name"
        placeholder="请输入活动主题"
        clearable
        @keyup.enter="onSearch"
      />
    </el-form-item>
    <el-form-item>
      <el-button
        type="primary"
        :icon="useRenderIcon('search')"
        @click="onSearch"
      >
        搜索
      </el-button>
    </el-form-item>
  </el-form>
</template>
