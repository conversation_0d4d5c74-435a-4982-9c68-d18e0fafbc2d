/*
 * @Date         : 2024-12-18 15:34:33
 * @Description  : 客户管理常量
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { transformMap } from "../common";

export const studyInfoBindList = [
  {
    label: "未绑定",
    value: "noBind"
  },
  {
    label: "已绑定待确认",
    value: "bindUnconfirmed"
  },
  {
    label: "已确认家长绑定",
    value: "parentalBindConfirmed"
  }
];
export const studyInfoBindListMap = transformMap(studyInfoBindList);

// export const familyCategoryList = [
//   { value: "uncertainSingleStudent", label: "疑似单独学生" },
//   { value: "singleStudent", label: "单独学生" },
//   { value: "uncertainAssociatedFamily", label: "疑似建联家庭" },
//   { value: "associatedFamily", label: "建联家庭" },
//   {
//     value: "uncertainNotAssociatedParentalFamily",
//     label: "疑似未建联亲子家庭"
//   },
//   { value: "notAssociatedParentalFamily", label: "未建联亲子家庭" },
//   { value: "uncertainAssociatedParentalFamily", label: "疑似建联亲子家庭" },
//   { value: "associatedParentalFamily", label: "建联亲子家庭" },
//   { value: "noPhoneUncertainSolo", label: "无手机疑似Solo" },
//   { value: "noPhoneUncertainParental", label: "无手机疑似亲子" }
// ];

export const familyCategoryList = [
  {
    value: "uncertainAssociatedParentalFamily,associatedParentalFamily",
    label: "有家长手机且绑定学情"
  },
  {
    value: "uncertainAssociatedFamily,associatedFamily",
    label: "有家长手机未绑定学情"
  },
  {
    value: "uncertainNotAssociatedParentalFamily,notAssociatedParentalFamily",
    label: "无家长手机且绑定学情"
  },
  {
    value: "uncertainSingleStudent,singleStudent",
    label: "无家长手机未绑定学情"
  },
  {
    value: "noPhoneUncertainSolo,noPhoneUncertainParental",
    label: "无手机"
  }
];

export const familyCategoryListMap = {
  uncertainAssociatedParentalFamily: "有家长手机且绑定学情",
  associatedParentalFamily: "有家长手机且绑定学情",
  uncertainAssociatedFamily: "有家长手机未绑定学情",
  associatedFamily: "有家长手机未绑定学情",
  uncertainNotAssociatedParentalFamily: "无家长手机且绑定学情",
  notAssociatedParentalFamily: "无家长手机且绑定学情",
  uncertainSingleStudent: "无家长手机未绑定学情",
  singleStudent: "无家长手机未绑定学情",
  noPhoneUncertainSolo: "无手机",
  noPhoneUncertainParental: "无手机"
};

export const phoneTypeList = [
  { value: "certainParentalPhone", label: "确定家长手机号" },
  { value: "uncertainParentalPhone", label: "疑似家长手机号" },
  { value: "certainStudentPhone", label: "确定学生手机号" },
  { value: "uncertainStudentPhone", label: "疑似学生手机号" }
];

export const phoneTypeMap = transformMap(phoneTypeList);

export const holdStatusList = [
  { value: "", label: "无法合并" },
  { value: "unregistered", label: "无需合并" },
  { value: "holding", label: "占领中" },
  { value: "merged", label: "已合并" },
  { value: "holdFail", label: "占领失败" }
];

export const holdStatusMap = transformMap(holdStatusList);

export const holdVerifyStatusList = [
  { value: "remainVerify", label: "待审核" },
  { value: "verifying", label: "审核中" },
  { value: "verifyPass", label: "审核通过" },
  { value: "verifyFail", label: "审核不通过" }
];

export const holdVerifyStatusMap = transformMap(holdVerifyStatusList);

export const contactStateList = [
  { value: "noCall", label: "无通话" },
  { value: "noValidCall", label: "联系无有效通话" },
  { value: "validCall", label: "有效通话" }
];

export const contactStateMap = transformMap(contactStateList);
