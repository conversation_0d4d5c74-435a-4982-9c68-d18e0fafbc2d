/*
 * @Date         : 2024-09-11 17:23:48
 * @Description  : 外呼相关的公用函数
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { ElMessage } from "element-plus";
import { noticeChangeChannelApi } from "/@/api/customer/call";

/**
 * @description: 转换渠道通知软电话app
 * @param {*}
 * @return {*}
 */
export const changeChannelToApp = async () => {
  try {
    await noticeChangeChannelApi();
  } catch {
    ElMessage.error("新版软电话的外呼渠道切换失败，请先安装新版软电话");
  }
};
