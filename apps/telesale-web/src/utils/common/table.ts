/*
 * @Date         : 2025-03-17 18:11:37
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import { isEqual } from "lodash";

export const tableSelectFn = (columns, tableSelect) => {
  columns.forEach(item => {
    if (item.filterOptions) {
      const value = tableSelect?.[item.field];
      const defaultValue = item.filterOptions.defaultFilterValue;
      if (!isEqual(value, defaultValue)) {
        item.filterOptions.defaultFilterValue = tableSelect?.[item.field];
      }
    }

    if (item.filterCascaderOptions) {
      const value = tableSelect?.[item.field];
      const defaultValue = item.filterCascaderOptions.defaultFilterValue;
      if (!isEqual(value, defaultValue)) {
        item.filterCascaderOptions.defaultFilterValue =
          tableSelect?.[item.field];
      }
    }
  });
};
