/*
 * @Date         : 2024-08-01 18:15:05
 * @Description  : 趋势
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */
import { Top, Bottom } from "@element-plus/icons-vue";

export function toFixedNumber(val, num) {
  return Number(val ? Number(val).toFixed(num) : 0);
}

export const getIconRender = (row, column, sumData, unit = 2) => {
  const value = row[column["field"]];

  if (toFixedNumber(value, unit) > toFixedNumber(sumData, unit)) {
    return (
      <el-icon color="red" size="14" class="inline-block mt-6px">
        <Top />
      </el-icon>
    );
  } else if (toFixedNumber(value, unit) < toFixedNumber(sumData, unit)) {
    return (
      <el-icon color="#67C23A" size="14" class="inline-block mt-6px">
        <Bottom />
      </el-icon>
    );
  } else {
    return;
  }
};
