const Layout = () => import("/@/layout/index.vue");

const aiQualityInspectionRouter = {
  path: "/aiQualityInspection",
  component: Layout,
  redirect: "/aiQualityInspection/excellentCases/index",
  meta: {
    icon: "cpu",
    title: "AI陪练台",
    rank: 0 // 根据需要调整菜单排序
  },
  children: [
    {
      path: "/aiQualityInspection/excellentCases/index",
      name: "ExcellentCases",
      component: () =>
        import("/@/views/aiQualityInspection/excellentCases/index.vue"),
      meta: {
        title: "优秀案例库",
        keepAlive: false // 根据需要调整缓存
      }
    },
    {
      path: "/aiQualityInspection/excellentQA/index",
      name: "ExcellentQA",
      component: () =>
        import("/@/views/aiQualityInspection/excellentQA/index.vue"),
      meta: {
        title: "优秀问答库",
        keepAlive: false // 根据需要调整缓存
      }
    },
    {
      path: "/aiQualityInspection/myTask/index",
      name: "MyTask",
      component: () => import("/@/views/aiQualityInspection/myTask/index.vue"),
      meta: {
        title: "我的任务",
        keepAlive: false // 根据需要调整缓存
      }
    },
    {
      path: "/aiQualityInspection/courseManagement/index",
      name: "CourseManagement",
      component: () =>
        import("/@/views/aiQualityInspection/courseManagement/index.vue"),
      meta: {
        title: "课程管理",
        keepAlive: false // 根据需要调整缓存
      }
    },
    {
      path: "/aiQualityInspection/taskManagement/index",
      name: "TaskManagement",
      component: () =>
        import("/@/views/aiQualityInspection/taskManagement/index.vue"),
      meta: {
        title: "任务管理",
        keepAlive: false // 根据需要调整缓存
      }
    }
  ]
};

export default aiQualityInspectionRouter;
