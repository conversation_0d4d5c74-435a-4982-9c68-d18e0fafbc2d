const Layout = () => import("/@/layout/index.vue");

const systemRouter = {
  path: "/system",
  component: Layout,
  redirect: "noRedirect",
  meta: {
    title: "系统管理"
  },
  children: [
    {
      path: "/system/distribute/index",
      name: "distribute",
      component: () => import("/@/views/system/distribute/index.vue"),
      meta: {
        title: "线索分配设置",
        keepAlive: true
      }
    },
    {
      path: "/system/mvpSet/index",
      name: "mvpSet",
      component: () => import("/@/views/system/mvpSet/index.vue"),
      meta: {
        title: "私域分配设置"
      },
      children: [
        {
          path: "/system/mvpSetDetails/index",
          name: "mvpSetDetails",
          component: () => import("/@/views/system/mvpSetDetails/index.vue"),
          meta: {
            title: "私域城市详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/system/orientation/index",
      name: "orientation",
      component: () => import("/@/views/system/orientation/index.vue"),
      meta: {
        title: "线索定向分配"
      }
    },
    {
      path: "/system/cueModel/index",
      name: "cueModel",
      component: () => import("/@/views/system/cueModel/index.vue"),
      meta: {
        title: "线索模型设置",
        keepAlive: true
      },
      children: [
        {
          path: "/system/cueModelDetail/index",
          name: "cueModelDetail",
          component: () => import("/@/views/system/cueModelDetail/index.vue"),
          meta: {
            title: "客户详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    // {
    //   path: "/system/weight/index",
    //   name: "weight",
    //   component: () => import("/@/views/system/weight/index.vue"),
    //   meta: {
    //     title: "线索权重设置"
    //   }
    // },
    {
      path: "/system/shield/index",
      name: "shield",
      component: () => import("/@/views/system/shield/index.vue"),
      meta: {
        title: "屏蔽线索设置"
      }
    },
    {
      path: "/system/poolShield/index",
      name: "poolShield",
      component: () => import("/@/views/system/poolShield/index.vue"),
      meta: {
        title: "公海池屏蔽规则设置"
      }
    },
    {
      path: "/system/selectAgent/index",
      name: "selectAgent",
      component: () => import("/@/views/system/selectAgent/index.vue"),
      meta: {
        title: "坐席选择设置"
      }
    },
    {
      path: "/system/qualityTesting/index",
      name: "select",
      component: () => import("/@/views/system/qualityTesting/index.vue"),
      meta: {
        title: "质检坐席设置",
        keepAlive: true
      }
    },
    {
      path: "/system/qualityCheckDurationSet/index",
      name: "qualityCheckDurationSet",
      component: () =>
        import("/@/views/system/qualityCheckDurationSet/index.vue"),
      meta: {
        title: "质检时长设置"
      }
    },
    {
      path: "/system/feishuPush/index",
      name: "feishuPush",
      component: () => import("/@/views/system/feishuPush/index.vue"),
      meta: {
        title: "飞书推送设置"
      }
    },
    {
      path: "/system/abTest/index",
      name: "abTest",
      component: () => import("/@/views/system/abTest/index.vue"),
      meta: {
        title: "AB测比例设置"
      }
    },
    {
      path: "/system/tag/index",
      name: "tag",
      component: () => import("/@/views/system/tag/index.vue"),
      meta: {
        title: "线索标签设置"
      }
    },
    {
      path: "/system/cardTemplate/index",
      name: "cardTemplate",
      component: () => import("/@/views/system/cardTemplate/index.vue"),
      meta: {
        title: "卡片模板设置"
      },
      children: [
        {
          path: "/system/cardTemplateDetails/index",
          name: "cardTemplateDetails",
          component: () =>
            import("/@/views/system/cardTemplateDetails/index.vue"),
          meta: {
            title: "卡片模板详情",
            dynamicLevel: 1,
            keepAlive: true
          }
        }
      ]
    },
    {
      path: "/system/explosiveMonogroup/index",
      name: "explosiveMonogroup",
      component: () => import("/@/views/system/explosiveMonogroup/index.vue"),
      meta: {
        title: "爆单群设置"
      }
    },
    {
      path: "/system/salarySet/index",
      name: "salarySet",
      component: () => import("/@/views/system/salarySet/index.vue"),
      meta: {
        title: "坐席薪酬设置"
      }
    },
    {
      path: "/system/giftTimeSet/index",
      name: "giftTimeSet",
      component: () => import("/@/views/system/giftTimeSet/index.vue"),
      meta: {
        title: "活动时间设置"
      }
    },
    {
      path: "/system/callInterval/index",
      name: "callInterval",
      component: () => import("/@/views/system/callInterval/index.vue"),
      meta: {
        title: "外呼间隔时长设置"
      }
    },
    {
      path: "/system/smsTemplate/index",
      name: "smsTemplate",
      component: () => import("/@/views/system/smsTemplate/index.vue"),
      meta: {
        title: "短信模板设置"
      }
    },
    {
      path: "/system/cueSetting/index",
      name: "cueSetting",
      component: () => import("/@/views/system/cueSetting/index.vue"),
      meta: {
        title: "线索操作设置"
      }
    },
    {
      path: "/system/callWarning/index",
      name: "CallWarning",
      component: () => import("/@/views/system/callWarning/index.vue"),
      meta: {
        title: "外呼行为预警提醒"
      }
    }
  ]
};

export default systemRouter;
