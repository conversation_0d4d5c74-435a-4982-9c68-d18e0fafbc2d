<!--
 * @Date         : 2024-12-13 17:51:50
 * @Description  : 坐席选择器
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { storeToRefs } from "pinia";
import { useUserStore } from "/@/store/modules/user";
import findOrganizationMath from "/@/utils/asyn/findOrganization";

const props = withDefaults(
  defineProps<{
    modelValue: number | undefined;
    clearable?: boolean;
    isAll?: boolean;
    placeholder?: string;
  }>(),
  {
    isAll: false,
    clearable: true,
    placeholder: "请选择小组"
  }
);

const emit = defineEmits<{
  (e: "update:modelValue", value: number | undefined): void;
}>();

const orgId = computed({
  get() {
    return props.modelValue;
  },
  set(value) {
    emit("update:modelValue", value);
  }
});

const { orgData } = storeToRefs(useUserStore());

const data = ref([]);
const getData = async () => {
  data.value = props.isAll ? orgData.value : await findOrganizationMath();
};

const propsParams = {
  label: "name",
  value: "id",
  checkStrictly: true,
  emitPath: false
};

getData();
</script>

<template>
  <el-cascader
    v-model="orgId"
    :options="data"
    :props="propsParams"
    filterable
    clearable
    :show-all-levels="false"
    :placeholder="props.placeholder"
    ref="cascaderRefs"
    collapse-tags
    collapse-tags-tooltip
    :max-collapse-tags="1"
  />
</template>
