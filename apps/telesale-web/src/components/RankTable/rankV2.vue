<!--
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-24 12:24:08
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-28 17:15:16
 * @FilePath: /telesale-web_v2/apps/telesale-web/src/components/RankTable/rankV2.vue
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
-->
<script setup lang="ts">
import { storeToRefs } from "pinia";
import { useAppStoreHook } from "/@/store/modules/app";
import { computed, ref, onMounted, onUnmounted, nextTick } from "vue";
import { useUserStore } from "/@/store/modules/user";

interface Props {
  tableData: any[];
  tableHeader: any[];
  name?: string;
  limit?: number;
  isRank?: boolean;
  fixedTopData?: any; // 添加固定在顶部的数据
  revenueField: string; // 收入字段名,当收入为0时不显示样式
}

const props = defineProps<Props>();
const tableHeight = ref<string>("auto"); // 动态计算的表格高度
const rankTableRef = ref();
const { userMsg } = storeToRefs(useUserStore());

// 计算表格数据，如果有固定顶部数据则添加到数组前面
const computedTableData = computed(() => {
  if (props.fixedTopData && !userMsg.value.leafNode) {
    return [props.fixedTopData, ...props.tableData];
  }
  return props.tableData;
});

// 计算表格到页面底部的距离，设置为表格高度
const calculateTableHeight = () => {
  // 获取表格容器元素
  const tableContainer = rankTableRef.value?.$el;
  if (tableContainer) {
    // 获取元素到视口顶部的距离
    const rect = tableContainer.getBoundingClientRect();
    // 计算元素到视口底部的距离
    const distanceToBottom = window.innerHeight - rect.top;
    // 设置表格高度，减去一些边距

    tableHeight.value = `${distanceToBottom - 30}px`;
  }
};

// 监听窗口大小变化
let resizeObserver: ResizeObserver | null = null;

onMounted(() => {
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    // 初始计算高度
    calculateTableHeight();

    // 创建ResizeObserver监听窗口大小变化
    resizeObserver = new ResizeObserver(() => {
      calculateTableHeight();
    });

    // 监听窗口大小变化
    window.addEventListener("resize", calculateTableHeight);

    // 监听表格容器大小变化
    const tableContainer = rankTableRef.value?.$el;
    if (tableContainer && resizeObserver) {
      resizeObserver.observe(tableContainer);
    }
  });
});

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener("resize", calculateTableHeight);

  // 停止观察
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
});

function isShow(scope) {
  let index = props.isRank ? scope.row.rank : scope.$index;
  return !props.limit || index < props.limit;
}

function tableRowClassName({ row, rowIndex }) {
  if (!row[props.revenueField]) {
    return "";
  }
  let index = props.isRank
    ? row.rank
    : props.fixedTopData && !userMsg.value.leafNode
    ? rowIndex - 1
    : rowIndex;

  if (props.fixedTopData && !userMsg.value.leafNode && rowIndex === 0) {
    return "";
  }

  if (index === 0) {
    return "topOne-v2";
  } else if (index === 1) {
    return "topTwo-v2";
  } else if (index === 2) {
    return "topThree-v2";
  } else if (index > 2 && index < 10) {
    return "topTen-v2";
  } else if (index > 9 && index < 20) {
    return "topTwenty-v2";
  } else {
    return "";
  }
}

const getSrc = (rank, index: number) => {
  if (props.fixedTopData && !userMsg.value.leafNode && index === 0) {
    return "https://fp.yangcong345.com/middle/1.0.0/telesale-web-v2/rank-user.png";
  }

  return `https://fp.yangcong345.com/middle/1.0.0/telesale-web-v2/rank-${
    rank + 1
  }.png`;
};
</script>

<template>
  <el-table
    :data="computedTableData"
    highlight-current-row
    :border="true"
    :row-class-name="tableRowClassName"
    :height="tableHeight"
    :class="{
      'rank-table-container': props.fixedTopData && !userMsg.leafNode
    }"
    ref="rankTableRef"
  >
    <el-table-column type="index" label="排名" width="100">
      <template #default="scope">
        <span v-if="isShow(scope)">
          NO.{{ (isRank ? scope.row.rank : scope.$index) + 1 }}
        </span>
        <div v-else>NO. {{ (isRank ? scope.row.rank : scope.$index) + 1 }}</div>
      </template>
    </el-table-column>
    <el-table-column v-if="name" :label="name">
      <el-table-column
        v-for="item in tableHeader"
        :key="item.field"
        :label="item.desc"
        :prop="item.field"
      >
        <template #default="{ $index, row, column }">
          <div
            v-if="
              row[props.revenueField] &&
              column.property === props.tableHeader?.[0]?.field &&
              row[column.property]?.trim() &&
              (row.rank < 3 ||
                ($index === 0 && props.fixedTopData && !userMsg.leafNode))
            "
            class="flex items-center justify-center"
          >
            <img class="w-28px h-28px mr-2" :src="getSrc(row.rank, $index)" />
            <span class="w-60px inline-block">
              {{ row[column.property] }}
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table-column>
    <el-table-column
      v-else
      v-for="item in tableHeader"
      :key="item.field"
      :label="item.desc"
      :prop="item.field"
    >
      <template #default="{ $index, row, column }">
        <div
          v-if="
            row[props.revenueField] &&
            column.property === props.tableHeader?.[0]?.field &&
            row[column.property]?.trim() &&
            (row.rank < 3 ||
              ($index === 0 && props.fixedTopData && !userMsg.leafNode))
          "
          class="flex items-center justify-center"
        >
          <img class="w-28px h-28px mr-2" :src="getSrc(row.rank, $index)" />
          <span class="w-60px inline-block">
            {{ row[column.property] }}
          </span>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<style lang="scss" scoped>
.rank-table-container {
  width: 100%;
  /* 确保表格容器不会阻止sticky定位 */
  overflow: visible !important;
}

/* 确保表格体允许滚动和sticky定位 */
.rank-table-container :deep(.el-table__body-wrapper) {
  overflow: auto !important;
}

/* 固定第一行 */
.rank-table-container
  :deep(.el-table__body-wrapper .el-table__row:first-child) {
  position: sticky !important;
  top: 0 !important;
  z-index: 20 !important; /* 提高z-index确保在其他元素之上 */
  /* 新增悬浮样式 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #02a7f0;
  font-weight: bold;
}

:deep(tbody) {
  .topOne-v2 {
    color: #ffffff;
    background-color: #d9001b !important;
    &:hover {
      color: #000000;
      background-color: #afe4fd !important;
    }
  }

  .topTwo-v2 {
    color: #ffffff;
    background-color: #f59a23 !important;
    &:hover {
      color: #000000;
      background-color: #afe4fd !important;
    }
  }

  .topThree-v2 {
    color: #ffffff;
    background-color: #02a7f0 !important;
    &:hover {
      color: #000000;
      background-color: #afe4fd !important;
    }
  }

  .topTen-v2 {
    color: #63a103;
  }
  .topTwenty-v2 {
    color: #0000ff;
  }
}
</style>
