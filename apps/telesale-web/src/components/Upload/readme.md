# 上传组件

## 介绍
此组件用于图片上传，支持点击上传、粘贴上传，同时具备图片数量限制和大小限制功能，还可预览已上传的图片。

## 引入
```js
import Upload from '/@/components/Upload/index';
```

## 基础用法
```tsx
<template>
  <Upload v-model:path="uploadedPaths" :max="3" :size="2" />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import Upload from '/@/components/Upload/index';

const uploadedPaths = ref<string[]>([]);
</script>
```

## Api
继承element-plus的API，增加了以下属性：
<https://element-plus.org/zh-CN/component/date-picker.html#%E5%B1%9E%E6%80%A7>

### Props
| 参数 | 说明 | 类型 | 默认值 |
| ---- | ---- | ---- | ---- |
| path | 双向绑定的已上传图片路径数组 | string[] | - |
| max | 最多可上传的图片数量 | number | - |
| size | 单张图片允许的最大大小（单位：M） | number | 1 |

