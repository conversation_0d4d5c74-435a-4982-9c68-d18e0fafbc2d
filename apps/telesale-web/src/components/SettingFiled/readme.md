
# 设置列表字段组件

## 介绍
此组件用于设置列表字段，允许用户选择要显示的字段，并对已选字段进行排序。用户可以通过勾选可选字段来控制哪些字段显示在列表中，同时可以拖动已选字段进行排序。

## 引入
```js
import SettingFiled from '/@/components/SettingFiled/index.vue';
```

## 基础用法
```tsx
<template>
  <div>
    <SettingFiled
      :localKey="'collect-column'"
      v-model:visible="isModelItem"
      v-model:columns="columns"
      :listHeader="listHeader"
      :hideFiled="hideFiled"
      @success="handleSuccess"
    />
    <el-button @click="isVisible = true">打开设置</el-button>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import SettingFiled from '/@/components/SettingFiled/index.vue';

const isVisible = ref(false);
const tableColumns = ref([]);
const listHeader = ref([
  // 列头数据示例
  { field: 'id', desc: 'ID',  fixed: false },
  { field: 'name', desc: '姓名',  fixed: false }
]);
const hideFields = ref([]);

const handleVisibleChange = (value: boolean) => {
  isVisible.value = value;
};

const handleColumnsChange = (columns: any[]) => {
  tableColumns.value = columns;
};

const handleSuccess = () => {
  console.log('设置成功');
};
</script>
```

## Api

### Props
| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| localKey | 用于本地存储的键名，用于保存隐藏字段和显示字段的状态 | string | - |
| v-model:visible | 控制组件对话框的显示与隐藏 | boolean | - |
| v-model:columns | 表格列配置数组 | TableColumns[] | - |
| listHeader | 列表头部列配置数组 | TableColumns[] | - |
| hideFiled | 初始隐藏字段数组 | string[] | - |

### Emits
| 事件名 | 说明 | 参数 |
| --- | --- | --- |
| success | 设置操作成功时触发 | - |

### 注意事项
- `TableColumns` 类型需要根据项目中 `/@/components/ReTable/types` 里的实际定义来使用。
- 在使用时，要确保传入的 `listHeader` 数据格式符合组件的要求。 