<!--
 * @Date         : 2025-03-06 18:36:55
 * @Description  : 滑动开关组件，包含大->小和小->大两种排序选项
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->
<script lang="ts" setup>
const props = defineProps<{ value: string }>();

const emits = defineEmits(["update:value"]);

const activeOption = computed({
  get() {
    return props.value;
  },
  set(val: string) {
    emits("update:value", val);
  }
});

// 定义处理点击事件的函数
const handleOptionClick = (option: string) => {
  activeOption.value = option;
};
</script>

<template>
  <div class="sort-switch">
    <!-- 大->小选项 -->
    <div
      :class="{ active: activeOption === 'asc' }"
      @click="handleOptionClick('asc')"
    >
      小->大
    </div>
    <!-- 小->大选项 -->
    <div
      :class="{ active: activeOption === 'desc' }"
      @click="handleOptionClick('desc')"
    >
      大->小
    </div>
    <!-- 滑动指示器 -->
    <div
      class="slider"
      :style="{
        transform: activeOption === 'asc' ? 'translateX(0)' : 'translateX(100%)'
      }"
    >
      {{ activeOption === "asc" ? "小->大" : "大->小" }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sort-switch {
  display: flex;
  position: relative;
  width: 200px;
  height: 35px;
  border: 1px solid #ccc;
  border-radius: 10px;
  overflow: hidden;
}

.sort-switch > div {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 1;
}

.sort-switch .active {
  color: white;
}

.slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  background-color: var(--el-color-primary);
  color: #fff;
  transition: transform 0.3s ease;
  border-radius: 10px;
}
</style>
