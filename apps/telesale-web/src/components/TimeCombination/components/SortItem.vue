<!--
 * @Date         : 2025-02-19 16:18:24
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
const props = defineProps<{
  sortData: any[];
}>();

const optionsList = [
  {
    value: "asc",
    label: "升序"
  },
  {
    value: "desc",
    label: "降序"
  }
];
</script>

<template>
  <div class="flex" v-for="item in props.sortData" :key="item.key">
    <div class="w-100px">xxx</div>
    <el-select v-model="item.key" filterable>
      <el-option
        v-for="item in optionsList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <el-select v-model="item.key" filterable>
      <el-option
        v-for="item in optionsList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
  </div>
</template>

<style lang="scss" scoped></style>
