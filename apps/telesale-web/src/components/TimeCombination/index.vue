<!--
 * @Date         : 2025-02-19 16:12:45
 * @Description  : 时间组合排序组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import {
  CirclePlusFilled,
  Operation,
  Grid,
  CircleCloseFilled
} from "@element-plus/icons-vue";
import { TableColumns } from "../ReTable/types";
import SortSwitch from "./components/SortSwitch.vue";
import Sortable from "sortablejs";
import {
  generateRandomString,
  generateUniqueKey
} from "/@/views/system/cueModelDetail/utils";
import { cloneDeep, isEqual } from "lodash-es";
import { PopoverInstance } from "element-plus";

const props = defineProps<{
  value: any;
  data: TableColumns[];
}>();

const emits = defineEmits(["update:value", "onSearch"]);

const formRef = ref();
const popoverRef = ref<PopoverInstance>();
const sortRef = ref<HTMLDivElement>();
const cloneData = ref([]);

const sortList = computed(() => {
  const filterData = form.value.combSort?.map(item => item.field);
  const list = cloneDeep(props.data);

  return list.map(item => {
    item.field = item.field.replace(/([A-Z])/g, function (match) {
      return "_" + match.toLowerCase();
    });
    return {
      label: item.desc,
      value: item.field,
      disabled: filterData.includes(item.field)
    };
  });
});

const sortLen = computed(() => {
  return form.value.combSort?.filter(item => item.field).length || 0;
});

const form = ref({
  combSort: []
});

const add = () => {
  if (form.value.combSort.length >= 3) {
    ElMessage.warning("最多只能添加3个排序项");
    return;
  }

  form.value.combSort.push({
    field: "",
    sortType: "asc",
    key: generateUniqueKey(form.value.combSort)
  });
};

const initSort = () => {
  nextTick(() => {
    new Sortable(sortRef.value, {
      animation: 150,
      handle: ".move",
      onEnd: ({ oldIndex, newIndex }) => {
        const list = cloneDeep(form.value.combSort);
        console.log(oldIndex, newIndex);
        const temp = list[oldIndex];
        list.splice(oldIndex, 1);
        list.splice(newIndex, 0, temp);
        form.value.combSort = [];
        nextTick(() => {
          form.value.combSort = list;
        });
      }
    });
  });
};

const remove = (index: number) => {
  form.value.combSort.splice(index, 1);
};

const clear = () => {
  form.value.combSort = [];
};

const cancel = () => {
  popoverRef.value?.hide();
};

const showPopover = () => {
  if (!form.value.combSort?.length) {
    form.value.combSort = [];
  }
  cloneData.value = cloneDeep(form.value.combSort);
};

const hidePopover = () => {
  form.value.combSort = form.value.combSort.filter(item => item.field);
  console.log(form.value.combSort, cloneData.value);

  if (!isEqual(form.value.combSort, cloneData.value)) {
    emits("update:value", cloneDeep(form.value.combSort));
    emits("onSearch");
  }
};

watch(
  () => props.value,
  () => {
    form.value.combSort = cloneDeep(props.value);
  },
  {
    deep: true
  }
);

onMounted(() => {
  initSort();
});
</script>

<template>
  <div>
    <el-popover
      ref="popoverRef"
      placement="bottom"
      title="设置组合排序"
      :width="600"
      trigger="click"
      @show="showPopover"
      @hide="hidePopover"
    >
      <template #reference>
        <el-button
          class="m-2"
          :type="!!sortLen ? 'primary' : ''"
          :plain="!!sortLen"
        >
          <el-icon><Operation /></el-icon>
          组合排序
          <span v-if="sortLen">*{{ sortLen }}</span>
        </el-button>
      </template>
      <el-form :model="form" ref="formRef">
        <div class="sort-all" ref="sortRef">
          <div
            v-for="(item, index) in form.combSort"
            :key="item.key"
            class="flex gap-10px items-center h-50px"
          >
            <el-form-item>
              <el-icon color="#999" size="20" class="move"><Grid /></el-icon>
            </el-form-item>
            <el-form-item class="w-60%" :prop="'combSort.' + index + '.field'">
              <el-select
                v-model="item.field"
                placeholder="请选择类型"
                filterable
                style="width: 100%"
                :teleported="false"
              >
                <el-option
                  v-for="item in sortList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <div>
                <SortSwitch v-model:value="item.sortType" />
              </div>
            </el-form-item>
            <el-form-item>
              <el-icon size="18" @click="remove(index)">
                <CircleCloseFilled />
              </el-icon>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div class="flex justify-between">
        <div @click.stop="add" class="add">
          <el-icon>
            <CirclePlusFilled />
          </el-icon>
          添加排序条件
        </div>
        <div class="cursor-pointer" @click="clear">清空全部条件</div>
      </div>
      <div class="flex justify-end mt-20px">
        <!-- <el-button type="primary" plain round @click="cancel">取消</el-button> -->
        <el-button type="primary" round @click="cancel">排序</el-button>
      </div>
    </el-popover>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-form-item) {
  .el-input {
    width: 100%;
  }
}

.add {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #3f9eff;
  cursor: pointer;
}
</style>
