<script lang="ts" setup>
import { ref } from "vue";
import { ArrowDown, ArrowUp } from "@element-plus/icons-vue";
import { DropdownInstance } from "element-plus";
import { isEqual } from "lodash-es";

interface FilterOptionsProps {
  columns: any; // 筛选数据
  defaultFilterValue?: any; // 默认值
  label?: string; // 筛选数据的label值，默认label
  value?: string; // 筛选数据的value值，默认value
}

const props = withDefaults(defineProps<FilterOptionsProps>(), {
  defaultFilterValue: undefined,
  label: "label",
  value: "value"
});

const emits = defineEmits(["filter"]);

const headDropdownRef = ref<DropdownInstance>();
// 下拉状态
const visible = ref<boolean>(false);

// 缓存旧值，用于优化。默认值根据是否是多选决定
const oldValue = ref(props.defaultFilterValue || []);
// 多选值列表，默认值可通过props传递
const checkList = ref(props.defaultFilterValue || []);

// 控制下拉菜单显示隐藏
const showClick = () => {
  if (!visible.value) {
    headDropdownRef.value?.handleOpen();
  } else {
    headDropdownRef.value?.handleClose();
  }
};

// 下拉菜单显示隐藏时，该方法用于多选点击其他元素时，关闭下拉菜单触发筛选
const visibleChange = (val: boolean) => {
  visible.value = val;
  // 针对于多选
  // val为false时，关闭下拉状态,触发筛选。
  // 对比新老值，如果不相等，则触发筛选

  if (!val && !isEqual(checkList.value, oldValue.value)) {
    oldValue.value = checkList.value;
    emits("filter", checkList.value);
  }
};

// 多选保存，然后触发visibleChange事件
const save = () => {
  headDropdownRef.value?.handleClose();
};

// 多选重置
const reset = () => {
  checkList.value = props.defaultFilterValue || [];
};
// 多选清空
const clear = () => {
  checkList.value = [];
};

// 重置数据，用于表单重置时使用
const resetData = () => {
  oldValue.value = props.defaultFilterValue || [];
  reset();
};

const clearData = () => {
  oldValue.value = [];
  checkList.value = [];
};

watch(
  () => props.defaultFilterValue,
  n => {
    checkList.value = n || [];
    oldValue.value = n || [];
    emits("filter", n, false);
  },
  { immediate: true, deep: true }
);

defineExpose({
  resetData,
  clearData
});
</script>

<template>
  <div class="flex">
    <el-dropdown
      ref="headDropdownRef"
      trigger="contextmenu"
      :hide-on-click="false"
      @visibleChange="visibleChange"
    >
      <span class="el-dropdown-link" @click="showClick">
        <el-icon class="el-icon--right">
          <ArrowDown v-if="!visible" />
          <ArrowUp v-else />
        </el-icon>
      </span>
      <template #dropdown>
        <el-cascader-panel
          v-model="checkList"
          style="width: fit-content"
          :options="props.columns"
          :props="{
            multiple: true,
            label: props.label,
            value: props.value,
            emitPath: false
          }"
        />
        <div class="flex justify-between p-10px">
          <el-button @click="clear">清空</el-button>
          <el-button type="primary" @click="save">确定</el-button>
        </div>
      </template>
    </el-dropdown>
  </div>
</template>

<style lang="scss" scoped></style>
