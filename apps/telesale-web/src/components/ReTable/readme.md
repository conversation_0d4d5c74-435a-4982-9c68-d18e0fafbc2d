# 表格组件

## 介绍
用于展示数据列表，支持多种功能，如多选、排序、筛选、合计等。

## 引入

```js
import ReTable from '/@/components/ReTable/index';
```

## 基础用法

```tsx
<template>
  <ReTable
    :dataList="dataList"
    :listHeader="listHeader"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ReTable from '/@/components/ReTable/index';

const dataList = ref([
  // 示例数据
  { id: 1, name: '<PERSON>', age: 25 },
  { id: 2, name: '<PERSON>', age: 30 }
]);

const listHeader = ref([
  // 示例表头
  { field: 'id', desc: 'ID' },
  { field: 'name', desc: '姓名' },
  { field: 'age', desc: '年龄' }
]);
</script>
```

## 筛选排序等复杂用法

```tsx
<template>
  <ReTable
    v-if="device !== 'mobile'"
    ref="tableRefs"
    :dataList="dataList"
    :listHeader="columns"
    :sort-change="sortChange"
    @filterHeadData="filterHeadData"
    :widthOperation="140"
    @parantMath="parantMath"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ReTable from '/@/components/ReTable/index';

const dataList = ref([
  // 示例数据
  { id: 1, name: 'John', age: 25 },
  { id: 2, name: 'Jane', age: 30 }
]);

const searchForm = ref({});

const listHeader = ref([
  // 示例表头
  { field: 'id', desc: 'ID' },
  { field: 'name', desc: '姓名' },
  { field: 'age', desc: '年龄' }
]);

const sortChange = column => {
  // 排序回调函数
  console.log('排序回调函数', column);
}

const filterHeadData = (filterData: Record<string, any>) => {
  for (const key in filterData) {
    searchForm[key] = filterData[key];
  }
};

const parantMath = ({ key, params }) => {
  // 操作按钮点击事件
  console.log('操作按钮点击事件', key, params);
  // 示例：根据 key 执行不同的操作
  switch (key) {
    case 'add':
      // 执行添加操作
      break;
    case 'edit':
      // 执行编辑操作
      break;
    case 'delete':
      // 执行删除操作
      break;
    default:
      // 处理其他操作
      break;
  }
}

</script>
```

## Api

### Props

| 参数            | 说明                                                               | 类型      | 默认值  |
| --------------- | ------------------------------------------------------------------ | --------- | ------- |
| rowKey          | 表格行的唯一标识字段                                               | string    | undefined |
| dataList        | 显示的数据列表                                                     | any[]     | [] |
| listHeader      | 表头信息，包含 `Table-column` 属性                                 | TableObj[] | [] |
| showSummary     | 是否在表尾显示合计行，为了绕过 ele 的 bug，此值为 `true` 时，表格 border 为 `false` | boolean | false |
| selection       | 是否支持多选                                                       | boolean | false |
| indexCode       | 是否显示序号                                                       | boolean | false |
| summaryMethod   | 当 `showSummary` 为 `true` 时，自定义的合计计算方法                 | any | null |
| filterChange    | 表头筛选变化时的回调函数                                           | any | null |
| operation       | 表格操作按钮列表                                                   | OperationObj[] | [] |
| widthOperation  | 操作栏宽度                                                         | number | 100 |
| maxHeight       | 表格最大高度                                                       | number | undefined |
| sortChange      | 当表格的排序条件发生变化时触发的事件                               | any | null |
| description     | 表格无数据时显示的描述信息                                         | string | "暂无数据" |
| defaultExpandAll| 是否默认展开树形结构                                               | boolean | true |
| defaultSort     | 默认排序规则，包含 `prop`（排序字段）和 `order`（排序顺序）         | { prop: string; order: "ascending" \| "descending" } | undefined |

### 方法

| 方法名            | 说明                                                               | 参数 | 返回值 |
| ----------------- | ------------------------------------------------------------------ | ---- | ------ |
| setCurrent        | 设置当前选中行（仅支持单选表格高亮回显）                           | index: number | void |
| getClickRow       | 获取当前点击的行数据                                               | 无 | any |
| resetFilter       | 重置表格筛选条件                                                   | 无 | void |
| handleSelectionChange | 获取当前选中的行数据列表                                           | 无 | any[] |
| clearSort         | 清除表格排序条件                                                   | 无 | void |
| sort              | 手动触发表格排序                                                   | data: { prop: string; order: "ascending" \| "descending" } | void |
| clearFilterData   | 清除筛选数据                                                       | 无 | void |

### 事件

| 事件名            | 说明                                                               | 参数 |
| ----------------- | ------------------------------------------------------------------ | ---- |
| parantMath        | 操作按钮点击事件，传递操作类型和参数                               | { key: string; params: any } |
| filterHeadData    | 表头筛选数据变化时触发的事件                                       | val: Record<string, any> |




#### TableObj 属性

| 属性            | 说明                                                               | 类型      | 默认值  |
| --------------- | ------------------------------------------------------------------ | --------- | ------- |
| desc            | 对应表格显示的标题 label                                          | string    | -       |
| field           | 对应表格显示的字段名称 props                                       | string    | -       |
| fixed           | 是否固定左侧                                                       | boolean \| unknown | -       |
| sortable        | 对应列是否可以排序                                                 | boolean \| unknown | -       |
| minWidth        | 对应列的最小宽度                                                   | number \| unknown | -       |
| filtersList     | 数据过滤的选-推荐使用filterOptions属项                                                     | any[] \| object \| unknown | -       |
| showTip         | 当内容过长被隐藏时显示 tooltip                                     | boolean \| unknown | -       |
| filteredValue   | 选中的数据过滤项-推荐使用filterOptions属性                                                   | number \| unknown | -       |
| headerTip       | 是否显示 tooltip                                                   | boolean   | -       |
| event           | column 事件名称                                                    | string    | -       |
| htmlChange      | 是否直接挂载 dom                                                   | boolean \| unknown | -       |
| addType         | 新增元素的位置，可选值为 "up" 或 "down"                            | "up" \| "down" | -       |
| typeChange      | 值转换                                                             | any[] \| object \| unknown | -       |
| eleSet          | 涉及到样式变化，包含 referProp、val、sty 属性                      | object    | -       |
| filters         | column 展示数据的自定义函数                                        | Function \| unknown | -       |
| idTransfer      | 坐席 Id 转换成 name                                                | string \| unknown | -       |
| idName          | 坐席默认值，如果全局接口 workerId 和 workid 统一，可去掉           | string \| unknown | -       |
| timeChange      | 时间格式转换                                                       | number \| unknown | -       |
| customRender    | 自定义渲染函数                                                     | (record: RecordProps<T>) => VNodeChild \| VNodeChild | -       |
| slot            | 插槽配置，包含 name 属性                                           | object    | -       |
| isShow          | 是否显示该列，可为布尔值或函数                                     | boolean \| Function | -       |
| isCopy          | 是否可以复制                                                       | boolean   | -       |
| filterOptions   | 表头筛选配置项，包含 rowKey、columns、isMultiple 等属性            | FilterOptions[]    | -       |
| isFamily        | 是否是家庭 ID 弹窗                                                 | boolean   | -       |

#### FilterOptions 属性
| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| rowKey | 表格行的唯一标识字段，用于筛选时确定具体的行数据。 | string | - |
| columns | 表头筛选的列配置信息，可指定筛选的列字段、筛选类型等。 | Array<{ field: string; type: string; options?: any[] }> | [] |
| isMultiple | 是否支持多选筛选。若为 `true`，则用户可以同时选择多个筛选条件；若为 `false`，则只能选择一个筛选条件。 | boolean | false |
| defaultFilterValue | 默认选中的值 | string / number / Array | undefinde |
| label | 筛选数据的label值，默认label | string | 'label' |
| value | 筛选数据的value值，默认value | string | 'value' |
```

