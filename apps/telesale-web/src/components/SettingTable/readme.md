# 设置列表字段组件

## 介绍
此组件用于设置列表字段，可让用户选择要显示的字段并对其进行排序。

## 引入

```js
import SettingTable from '/@/components/SettingTable/index';
```

## 基础用法

```vue
<template>
  <SettingTable
    :visible="visible"
    :columns="columns"
    :listHeader="listHeader"
    :hideFiled="hideFiled"
    :showFiled="showFiled"
    @update:visible="handleVisibleUpdate"
    @update:columns="handleColumnsUpdate"
    @success="handleSuccess"
  />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import SettingTable from '/@/components/SettingTable/index';

const visible = ref(false);
const columns = ref([]);
const listHeader = ref([]);
const hideFiled = ref([]);
const showFiled = ref([]);

const handleVisibleUpdate = (newVisible: boolean) => {
  visible.value = newVisible;
};

const handleColumnsUpdate = (newColumns: any[]) => {
  columns.value = newColumns;
};

const handleSuccess = (data: any) => {
  console.log('Success data:', data);
};
</script>
```

## Api

### Props

| 参数       | 说明                                                         | 类型            | 默认值 |
| ---------- | ------------------------------------------------------------ | --------------- | ------ |
|v-model:visible    | 控制组件的显示与隐藏                                       | boolean         | false  |
| v-model:columns    | 表格列的配置项数组                                         | TableColumns[]  | []     |
| listHeader | 列表头部的列配置项数组                                       | TableColumns[]  | []     |
| hideFiled  | 隐藏字段的数组，存储字段名                                 | string[]        | []     |
| showFiled  | 显示字段的数组，存储字段名                                 | string[]        | []     |

### Events

| 事件名           | 说明                                                         | 参数                                       |
| ---------------- | ------------------------------------------------------------ | ------------------------------------------ |
| success          | 当用户保存设置时触发                                       | 包含隐藏字段列表和显示字段列表的对象       |

### 注意事项
- `TableColumns` 类型需要根据项目中 `/@/components/ReTable/types` 里的实际定义来使用。
- 在使用时，要确保传入的 `listHeader` 数据格式符合组件的要求。 