<!--
 * @Date         : 2024-04-15 15:34:07
 * @Description  : 设置列表字段组件
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
-->

<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import { TableColumns } from "/@/components/ReTable/types";
import { Grid, Top, Close } from "@element-plus/icons-vue";
import Sortable from "sortablejs";
import { nextTick } from "vue";
import { ElMessage } from "element-plus";
import { watch } from "vue";

const props = defineProps<{
  visible: boolean;
  columns: TableColumns[];
  listHeader: TableColumns[];
  hideFiled: string[];
  showFiled: string[];
}>();

const emits = defineEmits(["update:visible", "update:columns", "success"]);

const listHeader = computed<TableColumns[]>(() => {
  return props.listHeader.filter(item => {
    if (typeof item.isShow === "function") {
      return item.isShow();
    } else {
      return true;
    }
  });
});

const hideList = ref([]);
const allFiled = computed(() => listHeader.value.map(item => item.field));
const selectedFields = ref([]);
const initData = () => {
  hideList.value = props.hideFiled;
  const showFiled = allFiled.value.filter(
    item => !hideList.value.includes(item)
  );
  selectedFields.value =
    props.showFiled.length > 0 ? props.showFiled : showFiled;

  init();
};

addField();

const isModal = computed({
  get() {
    return props.visible;
  },
  set(value: boolean) {
    emits("update:visible", value);
  }
});

const selecteds = ref<string[]>([]);
const myColumns = ref<TableColumns[]>([]);

const checkAll = computed(
  () => selecteds.value.length === listHeader.value.length
);
const isIndeterminate = computed(
  () =>
    selecteds.value.length > 0 &&
    selecteds.value.length < listHeader.value.length
);

function addField() {
  if (
    selectedFields.value.length + hideList.value.length <
    allFiled.value.length
  ) {
    const all = [...selectedFields.value, ...hideList.value];
    let fixedLen = 0;
    const newFile = listHeader.value.filter(item => {
      if (item.fixed) fixedLen++;
      return !all.includes(item.field);
    });
    newFile.forEach(item => {
      if (item.addType === "up") {
        selectedFields.value.splice(fixedLen, 0, item.field);
      } else {
        selectedFields.value.push(item.field);
      }
    });
  }
}

const handleCheckAllChange = (val: boolean) => {
  selecteds.value = val
    ? listHeader.value.map(item => item.field)
    : listHeader.value.filter(item => item.fixed).map(item => item.field);
  myColumns.value = val
    ? listHeader.value
    : listHeader.value.filter(item => item.fixed);
};

const handleClose = () => {
  isModal.value = false;
  selecteds.value = selectedFields.value;
  const list = sortFiled();
  myColumns.value = list;
};

const changeFiled = (e: boolean, val: string) => {
  let list = [...myColumns.value];
  if (e) {
    list.push(listHeader.value.find(item => item.field === val));
  } else {
    list = myColumns.value.filter(item => item.field !== val);
  }
  myColumns.value = list;
};

const top = (index: number) => {
  const list = [...myColumns.value];
  const temp = list[index];
  const len = listHeader.value.filter(item => item.fixed)?.length || 0;
  list.splice(index, 1);
  list.splice(len, 0, temp);
  myColumns.value = [];
  nextTick(() => {
    myColumns.value = list;
  });
};

const del = (val: string) => {
  selecteds.value = selecteds.value.filter(item => item !== val);
  myColumns.value = myColumns.value.filter(item => item.field !== val);
};

const handleSave = () => {
  selectedFields.value = myColumns.value.map(item => item.field);
  const hideList = allFiled.value.filter(
    item => !selectedFields.value.includes(item)
  );
  emits("update:columns", myColumns.value);
  isModal.value = false;
  emits("success", {
    hideList: hideList,
    showList: selectedFields.value
  });
};

const open = () => {
  nextTick(() => {
    const el = document.querySelector(".sort-all");
    new Sortable(el, {
      group: "name",
      sort: true,
      animation: 150,
      handle: ".move",
      filter: ".disabled-move",
      dragClass: "sortable-drag",
      ghostClass: "sortable-drag",
      chosenClass: "sortable-drag",
      onEnd: ({ oldIndex, newIndex }) => {
        const list = [...myColumns.value];
        const temp = list[oldIndex];
        list.splice(oldIndex, 1);
        list.splice(newIndex, 0, temp);
        myColumns.value = [];
        nextTick(() => {
          myColumns.value = list;
        });
      },
      onMove(e) {
        return e.related.className.indexOf("disabled-move-row") === -1;
      }
    });
  });
};

const sortFiled = () => {
  const list = [];
  selecteds.value.forEach(item => {
    listHeader.value.forEach(col => {
      if (item === col.field) {
        list.push(col);
      }
    });
  });
  return list;
};

const init = () => {
  selecteds.value = selectedFields.value;
  const list = sortFiled();
  myColumns.value = list;
  emits("update:columns", list);
};

initData();

watch(
  () => listHeader.value,
  (n, old) => {
    if (n.length !== old.length) {
      const list = sortFiled();
      console.log("myColumns", list);
      myColumns.value = list;
      emits("update:columns", myColumns.value);
    }
  },
  {
    deep: true
  }
);

defineExpose({
  initData
});
</script>

<template>
  <div>
    <el-dialog
      title="列表字段设置"
      v-model="isModal"
      width="60%"
      :before-close="handleClose"
      @open="open"
    >
      <div class="mb-20px">
        左侧【可选字段】中被勾选的字段将会显示在列表，可以在右侧【已选字段】区域点击每个字段前面的图标拖动进行排序
      </div>
      <div class="flex">
        <div class="checked-box">
          <div class="text-18px c-black bg-#f8f8f8 px-10px py-10px">
            可选字段
          </div>
          <div class="p-10px">
            <el-checkbox
              v-model="checkAll"
              :indeterminate="isIndeterminate"
              @change="handleCheckAllChange"
            >
              全选
            </el-checkbox>
            <el-checkbox-group v-model="selecteds">
              <el-checkbox
                v-for="(item, index) in listHeader"
                :key="index"
                :label="item.field"
                :disabled="item.fixed"
                @change="changeFiled($event, item.field)"
              >
                {{ item.desc }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="sort-box">
          <div class="text-18px c-black bg-#f8f8f8 px-10px py-10px">
            已选字段
          </div>
          <div class="p-10px sort-all">
            <div
              class="flex justify-between items-center"
              v-for="(item, index) in myColumns"
              :key="item.field"
              :class="[item.fixed ? 'disabled-move-row' : '']"
            >
              <div class="flex">
                <el-icon
                  :class="[
                    item.fixed ? '' : 'cursor-move',
                    item.fixed ? 'disabled-move' : 'move'
                  ]"
                  size="18"
                >
                  <Grid v-if="!item.fixed" />
                </el-icon>
                <div class="ml-8px">{{ item.desc }}</div>
              </div>
              <div v-if="!item.fixed">
                <el-icon
                  size="18"
                  color="#409efc"
                  class="mr-5px"
                  @click="top(index)"
                >
                  <Top />
                </el-icon>
                <el-icon size="18" color="red" @click="del(item.field)">
                  <Close />
                </el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.checked-box {
  flex: 1;
  border: 1px solid #ccc;
  margin-right: 20px;
}
.sort-box {
  min-width: 240px;
  border: 1px solid #ccc;
}
.sortable-drag {
  background-color: #409efc;
  color: #fff;
}
</style>
