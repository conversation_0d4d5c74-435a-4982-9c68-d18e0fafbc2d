/*
 * @Date         : 2025-01-23 16:47:16
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import dayjs from "dayjs";

const getAfterTimeRange = (days: number) => {
  const start = dayjs().subtract(days, "day").startOf("day");
  const end = dayjs().subtract(1, "day").endOf("day");
  return [start, end];
};

const getBeforeTimeRange = (days: number) => {
  const start = dayjs().add(1, "day").startOf("day");
  const end = dayjs().add(days, "day").endOf("day");
  return [start, end];
};

const getTodayTimeRange = () => {
  const start = dayjs().startOf("day");
  const end = dayjs().endOf("day");
  return [start, end];
};

export const getShortcuts = (rang: Ref<string>, callback: Fn) => {
  return [
    {
      text: "过去30天",
      type: "after",
      name: "after30",
      onClick(picker) {
        picker.emit("pick", getAfterTimeRange(30));
        setTimeout(() => {
          setTimeout(() => {
            rang.value = "after30";
            callback();
          }, 0);
        }, 0);
      },
      getValue: () => getAfterTimeRange(30)
    },
    {
      text: "过去14天",
      type: "after",
      name: "after14",
      onClick: picker => {
        picker.emit("pick", getAfterTimeRange(14));
        setTimeout(() => {
          rang.value = "after14";
          callback();
        }, 0);
      },
      getValue: () => getAfterTimeRange(14)
    },
    {
      text: "过去7天",
      type: "after",
      name: "after7",
      onClick: picker => {
        picker.emit("pick", getAfterTimeRange(7));
        setTimeout(() => {
          rang.value = "after7";
          callback();
        }, 0);
      },
      getValue: () => getAfterTimeRange(7)
    },
    {
      text: "过去3天",
      type: "after",
      name: "after3",
      onClick: picker => {
        picker.emit("pick", getAfterTimeRange(3));
        setTimeout(() => {
          rang.value = "after3";
          callback();
        }, 0);
      },
      getValue: () => getAfterTimeRange(3)
    },
    {
      text: "今天",
      type: "today",
      name: "today",
      onClick: picker => {
        picker.emit("pick", getTodayTimeRange());
        setTimeout(() => {
          rang.value = "today";
          callback();
        }, 0);
      },
      getValue: () => getTodayTimeRange()
    },
    {
      text: "未来3天",
      type: "before",
      name: "before3",
      onClick: picker => {
        picker.emit("pick", getBeforeTimeRange(3));
        setTimeout(() => {
          rang.value = "before3";
          callback();
        }, 0);
      },
      getValue: () => getBeforeTimeRange(3)
    },
    {
      text: "未来7天",
      type: "before",
      name: "before7",
      onClick: picker => {
        picker.emit("pick", getBeforeTimeRange(7));
        setTimeout(() => {
          rang.value = "before7";
          callback();
        }, 0);
      },
      getValue: () => getBeforeTimeRange(7)
    },
    {
      text: "未来14天",
      type: "before",
      name: "before14",
      onClick: picker => {
        picker.emit("pick", getBeforeTimeRange(14));
        setTimeout(() => {
          rang.value = "before14";
          callback();
        }, 0);
      },
      getValue: () => getBeforeTimeRange(14)
    },
    {
      text: "未来30天",
      type: "before",
      name: "before30",
      onClick: picker => {
        picker.emit("pick", getBeforeTimeRange(30));
        setTimeout(() => {
          rang.value = "before30";
          callback();
        }, 0);
      },
      getValue: () => getBeforeTimeRange(30)
    }
  ];
};
