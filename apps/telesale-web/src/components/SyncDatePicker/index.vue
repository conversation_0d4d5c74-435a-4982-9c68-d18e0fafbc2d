<script lang="ts" setup>
import { computed, watch } from "vue";
import { useVModel } from "@vueuse/core";
import { getShortcuts } from "./data/index";
import dayjs from "dayjs";

const props = defineProps<{
  value: any;
  rang?: string;
  dateRange?: "after" | "before";
}>();

const emit = defineEmits<{
  (e: "update:value", value: any): void;
  (e: "update:rang", value: any): void;
}>();

const valueDate = useVModel(props, "value", emit);
const valueRang = ref(props.rang);
const shortcutsList = computed(() => {
  const shortcuts = getShortcuts(valueRang, updateRang);
  if (!props.dateRange) return shortcuts;
  return shortcuts.filter(
    item => item.type === props.dateRange || item.type === "today"
  );
});

const disabledDate = (time: Date) => {
  if (!props.dateRange) return false;
  const timeUnix =
    props.dateRange === "after"
      ? dayjs(time.getTime()).valueOf()
      : dayjs(time.getTime()).add(1, "day").valueOf();
  const current = new Date().getTime();

  if (props.dateRange === "after") {
    return timeUnix > current;
  } else if (props.dateRange === "before") {
    return timeUnix < current;
  }
};

const updateRang = () => {
  emit("update:rang", valueRang.value);
};

const changePanel = () => {
  valueRang.value = undefined;
  updateRang();
};

watch(
  [() => props.rang, () => props.value],
  (newValues, oldValues) => {
    const [newRang, newValue] = newValues;
    if (newRang) {
      const [start, end] = shortcutsList.value
        .find(item => item.name === newRang)
        .getValue();
      if (newValue) {
        const [startTime, endTime] = newValue;
        if (
          dayjs(startTime).unix() !== dayjs(start).unix() &&
          dayjs(endTime).unix() !== dayjs(end).unix()
        ) {
          valueDate.value = [start, end];
        }
      } else {
        valueDate.value = [start, end];
      }

      // valueDate.value = shortcutsList.value
      //   .find(item => item.name === newRang)
      //   ?.getValue();
    }
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <el-date-picker
    v-model="valueDate"
    range-separator="至"
    :disabled-date="disabledDate"
    :shortcuts="shortcutsList"
    v-bind="$attrs"
    @change="changePanel"
  />
</template>

<style scoped lang="scss"></style>
