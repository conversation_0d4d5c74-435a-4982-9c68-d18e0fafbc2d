# 日期选择器组件

## 介绍
`SyncDatePicker` 是一个基于 Element UI 的日期选择器组件，支持快捷日期范围选择，可根据传入的 `dateRange` 属性过滤快捷选项。

## 引入

```js
import SyncDatePicker from '/@/components/SyncDatePicker/index';
```

## 基础用法

```tsx
<template>
  <SyncDatePicker v-model="selectedDateRange" :rang="selectedRange" :dateRange="'after'" />
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import SyncDatePicker from '/@/components/SyncDatePicker/index';

const selectedDateRange = ref([]);
const selectedRange = ref('');
</script>
```

## API

继承element-plus的API，增加了以下属性：
<https://element-plus.org/zh-CN/component/date-picker.html#%E5%B1%9E%E6%80%A7>
### Props
| 参数       | 说明                                                         | 类型                          | 默认值 |
| ---------- | ------------------------------------------------------------ | ----------------------------- | ------ |
| v-model:value      | 双向绑定的日期范围值，格式为 `[startDate, endDate]`          | `any`                         | `undefined` |
| v-model:rang / rang       | 当前选择的快捷日期范围名称，如 `'after30'`、`'today'` 等     | `string`                      | `undefined` |
| dateRange  | 日期范围类型，可选值为 `'after'` 或 `'before'`，用于过滤快捷选项和禁用过去或者未来的时间选择 | `'after' / 'before'`          | `undefined` |


### 快捷选项

`SyncDatePicker` 组件提供了一系列快捷日期范围选项，这些选项定义在 `data/index.ts` 文件中。以下是可用的快捷选项列表：

| 文本        | 类型      | 名称       | 描述                                                         |
| ----------- | --------- | ---------- | ------------------------------------------------------------ |
| 过去 30 天  | `after`   | `after30`  | 选择过去 30 天的日期范围                                     |
| 过去 14 天  | `after`   | `after14`  | 选择过去 14 天的日期范围                                     |
| 过去 7 天   | `after`   | `after7`   | 选择过去 7 天的日期范围                                      |
| 过去 3 天   | `after`   | `after3`   | 选择过去 3 天的日期范围                                      |
| 今天        | `today`   | `today`    | 选择今天的日期范围                                           |
| 未来 3 天   | `before`  | `before3`  | 选择未来 3 天的日期范围                                      |
| 未来 7 天   | `before`  | `before7`  | 选择未来 7 天的日期范围                                      |
| 未来 14 天  | `before`  | `before14` | 选择未来 14 天的日期范围                                     |
| 未来 30 天  | `before`  | `before30` | 选择未来 30 天的日期范围                                     |

当传入 `dateRange` 属性时，组件会根据该属性过滤快捷选项，只显示符合条件的选项。例如，当 `dateRange` 为 `'after'` 时，只会显示类型为 `'after'` 和 `'today'` 的选项。