/*
 * @Date         : 2024-12-17 17:35:34
 * @Description  : 拖拽指令
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

import type { Directive } from "vue";

export const draggable: Directive = {
  mounted(el: HTMLElement) {
    // let startX = 0;
    let startY = 0;

    // let offsetXX = 0;
    let offsetYY = 0;

    let endX = 0;
    let endY = 0;

    el.style.cursor = "move";

    const transformValue = window
      .getComputedStyle(el)
      .transform.split(",")
      .map(item => Number.parseInt(item))
      .slice(4, 6);

    if (transformValue.length > 1) {
      endX = transformValue[0];
      endY = transformValue[1];
    }

    const handler = (e: MouseEvent) => {
      const { clientX, clientY } = e;
      // const offsetX = clientX - startX + endX;
      let offsetY = clientY - startY + endY;
      const viewHeight = window.innerHeight;
      // const viewWidth = window.innerWidth;
      const minY = el.offsetHeight;
      // const minX = el.offsetWidth;

      // if (offsetX > 0) {
      //   offsetX = 0;
      // } else if (offsetX < -viewWidth + minX) {
      //   offsetX = -viewWidth + minX;
      // }

      const currentTop = parseInt(el.style.top) || el.offsetTop || 0;
      console.log("currentTop", currentTop);

      if (offsetY < -currentTop) {
        offsetY = -currentTop;
      } else if (clientY > viewHeight - minY) {
        offsetY = viewHeight - minY;
      }

      // offsetXX = offsetX;
      offsetYY = offsetY;
      el.style.transform = `translate(${0}px, ${offsetY}px)`;
    };

    el.addEventListener("mousedown", (e: MouseEvent) => {
      const { clientX, clientY } = e;
      // startX = clientX;
      startY = clientY;

      window.addEventListener("mousemove", handler);
    });
    el.addEventListener("mouseup", () => {
      // endX = offsetXX;
      endY = offsetYY;
      window.removeEventListener("mousemove", handler);
    });

    el.addEventListener("mousedown", () => {
      endY = offsetYY;
      window.removeEventListener("mousemove", handler);
    });

    el.onselectstart = () => {
      return false;
    };
  }
};
