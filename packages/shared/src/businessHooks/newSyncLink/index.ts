/*
 * @Date         : 2025-02-20 16:34:23
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors: xiaozhen <EMAIL>
 */

import { onMounted, ref } from "vue";
import { getInstallmentPayType } from "../payPush/installmentPay";

interface Options {
  userId?: string;
  immediate?: boolean;
  getSyncDataApi: (params: { userId?: string }) => Promise<any>;
  getGoodInfoApi: (data: {
    userId?: string;
    id: string[];
    addContent: string[];
  }) => Promise<any>;
  getVenueLinkGoodsListApi: () => Promise<any>;
}

export const useNewSyncLink = (options: Options) => {
  const {
    immediate = true,
    getSyncDataApi,
    getGoodInfoApi,
    getVenueLinkGoodsListApi
  } = options;
  const loading = ref(false);
  const syncData = ref<any[]>([]);
  const goodList = ref([]);
  const goodInfo = ref();
  const form = ref({
    schoolYear: undefined,
    isInstallment: 2,
    goods: undefined,
    isPackGood: false,
    installmentPayType: ["alipayFq"],
    addPad: undefined
  });

  const filterDuplicateSkuGoods = async (dataArray: any[]) => {
    const { data } = await getVenueLinkGoodsListApi();
    const list = data.list;
    console.log("list", list);

    dataArray.forEach(data => {
      data.data = data.data.filter(item => {
        const config = list.find(conf => conf.goodsId === item.skuGoodId);
        if (config) {
          if (config.showType === 2) {
            return config.inRoot || config.inGroups;
          } else {
            return false;
          }
        }
        return true;
      });
    });
    return dataArray;
  };

  const getData = async () => {
    try {
      loading.value = true;
      const res = await getSyncDataApi({ userId: options.userId });
      syncData.value = await filterDuplicateSkuGoods(res.data.data);
      form.value.schoolYear = res.data.data[0].schoolYear;
      changeSchoolYear(form.value.schoolYear);
    } finally {
      loading.value = false;
    }
  };

  const getGoodInfo = (id: string, pad: string) => {
    goodInfo.value = undefined;
    if (!id) return;
    loading.value = true;
    getGoodInfoApi({
      id: [id],
      userId: options.userId,
      addContent: !pad ? [] : [pad]
    })
      .then(res => {
        goodInfo.value = res.data.data[0];
      })
      .finally(() => {
        loading.value = false;
      });
  };

  const changeSchoolYear = (e: string) => {
    form.value.goods = undefined;
    goodInfo.value = undefined;
    form.value.isPackGood = false;
    goodList.value =
      syncData.value.find(item => item.schoolYear === e)?.data || [];
  };

  const changeGood = () => {
    form.value.isPackGood = false;
    form.value.addPad = form.value.goods.addContent?.[0]?.label;
    getGoodInfo(form.value.goods.id, form.value.addPad);
  };

  const changeUpgrade = () => {
    if (form.value.isPackGood) {
      getGoodInfo(form.value.goods.packGood[0].id, form.value.addPad);
      return;
    }
    getGoodInfo(form.value.goods.id, form.value.addPad);
  };

  const handelData = () => {
    const params = {
      url: form.value.goods.payPage,
      installmentPayType: "",
      courseName: form.value.goods.strategyName,
      dynamic: true
    };

    if (form.value.isPackGood) {
      params.url = form.value.goods.packGood[0].payPage;
      params.courseName = form.value.goods.packGood[0].strategyName;
    }

    if (form.value.addPad) {
      const pad = form.value.goods.addContent?.find(
        item => item.label === form.value.addPad
      );
      params.url = pad.payPage;
      params.courseName =
        form.value.goods.strategyName +
        `${pad.label === "notAdd" ? "+" + pad.name : "+加购" + pad.name}`;
    }

    params.installmentPayType = getInstallmentPayType(
      form.value.isInstallment,
      form.value.installmentPayType
    );
    return params;
  };

  onMounted(() => {
    if (immediate) {
      getData();
    }
  });

  return {
    form,
    syncData,
    goodInfo,
    goodList,
    loading,
    getGoodInfo,
    getData,
    changeUpgrade,
    changeSchoolYear,
    changeGood,
    handelData
  };
};
