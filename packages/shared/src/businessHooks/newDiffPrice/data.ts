/*
 * @Date         : 2025-01-21 16:14:16
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

export const diffPriceColunms = [
  {
    field: "skuGoodName",
    desc: "目标商品",
    htmlChange: true,
    minWidth: 170
  },
  {
    field: "originalAmount",
    desc: "商品原价（划线价）",
    minWidth: 80,
    customRender: ({ text }) => "¥ " + text.toFixed(2)
  },

  {
    field: "reducePrice",
    desc: "商品直降",
    minWidth: 50,
    customRender: ({ text }) => "¥ " + text.toFixed(2)
  },

  {
    field: "directAmount",
    desc: "补差限时直降",
    minWidth: 60,
    customRender: ({ text, row }) => {
      return row.showSum ? "¥ " + text.toFixed(2) : "";
    }
  },

  {
    field: "otherAmount",
    desc: "补差价抵扣",
    minWidth: 60,
    slot: {
      name: "otherAmount"
    }
  },
  {
    field: "diffPriceName",
    desc: "实付",
    customRender: ({ row }) =>
      "¥ " + (row.amount - row.deductAmount).toFixed(2),
    minWidth: 50
  }
];
