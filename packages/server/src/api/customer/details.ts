/*
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-12 18:43:11
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-21 10:43:14
 * @FilePath: /telesale-web_v2/packages/server/src/api/customer/details.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import getHttp from "../../utils/http/ceateAixos";
const http = getHttp();

export interface TrailOrder {
  orderId: string;
  userId: string;
  deviceLock: boolean;
  trialStartTime: string;
  trialEndTime: string;
  status: string;
}

/**
 * @description: 获取体验机信息
 * @param {userId} string
 * @returns {trialOrderInfos}
 */
export const getTrialOrderListApi = (params: { userId: string }) => {
  return http.request<{
    trialOrderInfos: TrailOrder[];
  }>(
    "get",
    `/sync-order/order/get_user_trial_order_info`,
    {
      params
    },
    {
      isData: true
    }
  );
};

export interface CourseListReq {
  /**
   * 用户id
   */
  userId?: string;
}

export interface CourseList {
  list: {
    id?: string;
    courseId?: string;
    name?: string;
    type?: string;
  }[];
}

/**
 * @description 仅可在app购买的课程列表
 * https://yapi.yc345.tv/project/2519/interface/api/126081
 * <AUTHOR>
 * @date 2025-05-16
 * @export
 * @param {CourseListReq} params
 * @returns {Promise<CourseList>}
 */
export const getCourseAppListApi = (params: CourseListReq) => {
  return http.request<CourseList>(
    `get`,
    `/sync-order/course/ListCourseForApp`,
    {
      params
    },
    {
      isData: true
    }
  );
};

export interface QualityUserReq {
  /**
   * 用户ID
   */
  userId: string;
}

export interface QualityUserRes {
  /**
   * 值为true表示用户具有资格
   */
  ok: boolean;
}

/**
 * @description 判断用户是否是特殊的老未用户(用于推送特殊商品)
 * https://yapi.yc345.tv/project/2352/interface/api/126102
 * <AUTHOR>
 * @date 2025-05-21
 * @export
 * @param {QualityUserReq} params
 * @returns {Promise<QualityUserRes>}
 */
export const getQualityUserApi = (params: QualityUserReq) => {
  return http.request<QualityUserRes>(
    `get`,
    `/wuhan-datapool/workerAllocateInfo/isLowQualityUser`,
    {
      params
    }
  );
};
