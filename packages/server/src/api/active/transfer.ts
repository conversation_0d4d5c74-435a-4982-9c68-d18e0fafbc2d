/*
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-19 11:44:43
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-20 15:29:22
 * @FilePath: /telesale-web_v2/packages/server/src/api/active/transfer.ts
 * @Description: 转介绍活动相关api
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import getHttp from "../../utils/http/ceateAixos";
const http = getHttp();

export interface PlatformInfo {
  id?: number;
  name?: string;
  createdAt?: string;
  operatorId?: number;
}

export interface PlatformForm {
  id?: number;
  name?: string;
}

/**
 * @description: 获取平台列表
 * @returns {PlatformInfo}
 */
export const getPlatformListApi = () => {
  return http.request<{
    list: PlatformInfo[];
  }>(
    "post",
    `/wuhan-marketing/promotion/listReferralPlatform`,
    {},
    { isData: true }
  );
};

/**
 * @description: 平台详情
 * @param {number} id
 */
export const getPlatformApi = (data: { id: number }) => {
  return http.request<{
    item: PlatformForm;
  }>("post", `/wuhan-marketing/promotion/getReferralPlatform`, { data });
};

/**
 * @description: 添加平台
 * @param {PlatformForm} data
 */
export const addPlatformApi = (data: PlatformForm) => {
  return http.request(
    "post",
    `/wuhan-marketing/promotion/createReferralPlatform`,
    { data }
  );
};

/**
 * @description: 编辑平台
 * @param {PlatformForm} data
 */
export const updatePlatformApi = (data: PlatformForm) => {
  return http.request(
    "put",
    `/wuhan-marketing/promotion/updateReferralPlatform`,
    { data }
  );
};
