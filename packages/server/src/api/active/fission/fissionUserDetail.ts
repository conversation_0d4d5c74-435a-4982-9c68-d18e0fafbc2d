/*
 * @Date         : 2025-06-05 16:34:59
 * @Description  :
 * @Autor        : xiaozhen
 * @LastEditors  : xiaozhen
 */

/*
 * @Author: xiaozhen <EMAIL>
 * @Date: 2025-06-05 11:07:28
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-06-05 12:16:56
 * @FilePath: /telesale-web_v2/packages/server/src/api/active/fission/fissionUserDetail.ts
 * @Description: 裂变用户详情相关接口
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import getHttp from "../../../utils/http/ceateAixos";
const http = getHttp();

// 获取奖品记录列表接口类型定义

// 获取邀请记录列表接口类型定义
export interface ListInviteRecordReqBody {
  onionId?: string;
  inviteeOnionId?: string;
  inviteePhone?: string;
  bindTimeStart?: number;
  bindTimeEnd?: number;
  hasPurchased?: number;
  pageIndex?: number;
  pageSize?: number;
}

export interface InviteRecord {
  id?: number;
  onionId?: string;
  inviteeOnionId?: string;
  inviteePhone?: string;
  bindTime?: number;
  hasPurchased?: boolean;
  orderDetail?: any;
}

export interface ListInviteRecordResBody {
  list: InviteRecord[];
  total: number;
}

/**
 * @description 获取邀请记录列表
 * <AUTHOR>
 * @date 2025-06-05
 * @export
 * @param {ListInviteRecordReqBody} data
 * @returns {Promise<ListInviteRecordResBody>}
 */
export const listInviteRecordApi = (data: ListInviteRecordReqBody) => {
  return http.request<ListInviteRecordResBody>(
    `post`,
    `/wuhan-miniprogram/wechat_fission/listInviteRecord`,
    {
      data
    }
  );
};

/**
 * @description 导出邀请记录
 * <AUTHOR>
 * @date 2025-06-05
 * @export
 * @param {ListInviteRecordReqBody} data
 * @returns {Promise<any>}
 */
export const exportInviteRecordApi = (data: ListInviteRecordReqBody) => {
  return http.request<any>(
    `post`,
    `/wuhan-miniprogram/wechat_fission/exportInviteRecord`,
    {
      data
    },
    {
      responseType: "blob"
    }
  );
};
