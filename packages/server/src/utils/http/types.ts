/*
 * @Author: xia<PERSON>hen <EMAIL>
 * @Date: 2025-05-15 16:34:58
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-16 11:56:19
 * @FilePath: /telesale-web_v2/packages/server/src/utils/http/types.ts
 * @Description:
 *
 * Copyright (c) 2025 by ${git_name_email}, All Rights Reserved.
 */
import Axios, {
  Method,
  AxiosError,
  AxiosResponse,
  AxiosRequestConfig
} from "axios";

export type RequestMethods = Extract<
  Method,
  "get" | "post" | "put" | "delete" | "patch" | "option" | "head"
>;

export interface PureHttpError extends AxiosError {
  isCancelRequest?: boolean;
  config: PureHttpRequestConfig;
}

export interface PureHttpResponse extends AxiosResponse {
  config: PureHttpRequestConfig;
}

export interface PureHttpRequestConfig extends AxiosRequestConfig {
  beforeRequestCallback?: (request: PureHttpRequestConfig) => void;
  beforeResponseCallback?: (response: PureHttpResponse) => void;
  hideError?: boolean;
  isData?: boolean;
}

export interface ReturnValue<T = any> {
  data: T;
  headers: Object;
  status: number;
}

export interface PureHttp {
  request: <T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ) => Promise<ReturnValue<T>>;
}
