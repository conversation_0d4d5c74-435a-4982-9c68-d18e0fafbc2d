/*
 * @Author: xia<PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-12 18:42:12
 * @LastEditors: xiaozhen <EMAIL>
 * @LastEditTime: 2025-05-22 15:42:25
 * @FilePath: /telesale-web_v2/packages/server/src/utils/http/ceateAixos.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { PureHttp } from "./types";

let http: PureHttp | null = null;
// 创建API工厂
export const createAxios = (instance: PureHttp) => {
  http = instance;
};

const getHttp = () => {
  return http as PureHttp;
};

export default getHttp;
