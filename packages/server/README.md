# @telesale/server

这是一个用于telesale-web_v2 monorepo项目的共享API服务包，提供了可复用的API工厂和请求方法，简化了跨项目的API调用。

## 特性

- 基于Axios的API工厂
- 支持TypeScript类型定义
- 支持请求重试机制
- 支持请求和响应拦截器
- 支持ESM和CommonJS两种模块格式

## 安装

由于这是一个monorepo内部包，你可以在项目中直接引用：

```json
{
  "dependencies": {
    "@telesale/server": "workspace:*"
  }
}
```

## 使用方法

### 基本用法

```typescript
import { createApiFactory } from '@telesale/server';

// 创建API工厂
const apiFactory = createApiFactory();

// 创建API实例
const api = apiFactory.createApi({
  baseURL: 'https://api.example.com',
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 使用API实例发起请求
async function fetchData() {
  try {
    const data = await api.get('/users', { params: { page: 1 } });
    console.log(data);
  } catch (error) {
    console.error(error);
  }
}
```

### 配置拦截器

```typescript
import { createApiFactory } from '@telesale/server';

const apiFactory = createApiFactory();

const api = apiFactory.createApi({
  baseURL: 'https://api.example.com',
  interceptors: {
    request: {
      onFulfilled: (config) => {
        // 添加token
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${localStorage.getItem('token')}`;
        return config;
      },
      onRejected: (error) => {
        return Promise.reject(error);
      }
    },
    response: {
      onFulfilled: (response) => {
        // 处理响应数据
        return response.data;
      },
      onRejected: (error) => {
        // 处理错误
        if (error.response && error.response.status === 401) {
          // 处理未授权错误
        }
        return Promise.reject(error);
      }
    }
  }
});
```

### 配置请求重试

```typescript
import { createApiFactory } from '@telesale/server';

const apiFactory = createApiFactory();

const api = apiFactory.createApi({
  baseURL: 'https://api.example.com',
  retry: {
    count: 3, // 重试3次
    delay: 1000, // 每次重试间隔1秒
    condition: (error) => {
      // 自定义重试条件
      return error.response && error.response.status >= 500;
    }
  }
});
```

## 开发

```bash
# 开发模式
npm run dev

# 构建
npm run build
```

## 类型定义

包含完整的TypeScript类型定义，支持IDE智能提示。

```typescript
import type { ApiConfig, ApiInstance } from '@telesale/server';

// 使用类型
const config: ApiConfig = {
  baseURL: 'https://api.example.com'
};
```