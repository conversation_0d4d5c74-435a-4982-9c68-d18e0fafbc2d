---
description:
globs:
alwaysApply: true
---
# 项目文档管理规则

## 📅 时间获取规则

### 规则概述
当涉及到时间获取需求时，必须使用多种方式进行交叉验证，确保时间的准确性和可靠性。

### 获取方式
1. **本地系统时间**: 使用 `date` 和 `date -u` 命令
2. **网络时间验证**: 通过权威时间网站验证（timeanddate.com、time.gov等）
3. **JavaScript运行时验证**: 使用Node.js获取精确时间戳和格式化时间

### 验证要求
- 必须包含中国本地时间（CST UTC+8）
- 必须包含UTC标准时间
- 必须包含时间戳
- 必须包含多源验证结果

### 输出格式
```markdown
## 📅 当前准确时间

### 🇨🇳 中国时间（本地时间）
- **YYYY年MM月DD日 星期X HH:mm分**
- **时区：CST (UTC+8)**

### 🌍 世界标准时间
- **UTC时间：YYYY-MM-DD HH:mm:ss**
- **时间戳：[timestamp]**

### 📊 时间来源验证
[列出所有验证源及其结果]

### 🔍 时间精度说明
[说明各时间源的一致性和精度]
```

## 📁 文档结构管理规则

### 文档分类
- **AskLog**: 提问日志，按日期组织
- **DevLog**: 开发日志，按日期组织
- **Feature**: 功能规则文档，按功能组织

### 文件夹结构
```
@Docs/
├── AskLog/
│   └── YYYY-MM-DD/
│       ├── README.md          # 当日提问日志索引
│       └── topic-name.md      # 具体提问记录
├── DevLog/
│   └── YYYY-MM-DD/
│       ├── README.md          # 当日开发日志索引
│       └── feature-name.md    # 具体开发记录
├── Feature/
│   └── feature-name_rules.md  # 功能规则文档
└── README.md                   # 总文档索引
```

### 命名规范
- **AskLog**: `@Docs/AskLog/YYYY-MM-DD/topic-name.md`
- **DevLog**: `@Docs/DevLog/YYYY-MM-DD/feature-or-issue-name.md`
- **Feature**: `@Docs/Feature/feature-name_rules.md`

### 索引文件要求
- 每个日期文件夹必须包含README.md索引文件
- 索引文件必须包含当日活动概览
- 索引文件必须包含文档列表和链接
- 索引文件必须包含相关文件引用

## 📝 文档内容规范

### 必须包含的信息
1. **准确的时间信息** - 使用多源验证的时间
2. **清晰的标题层次** - 使用标准的Markdown格式
3. **完整的功能描述** - 详细说明功能和实现
4. **相关文件引用** - 提供完整的文档链接

### 文档更新规则
每次更新根据不同的内容，完成以下文档编写：
- @Docs/AskLog/YYYY-MM-DD/topic.md - 提问日志，用于按日期记录与AI的交互
- @Docs/DevLog/YYYY-MM-DD/progress.md - 开发日志，用于按日期记录进度、问题和解决方案
- @Docs/Feature/[feature-name]_rules.md - 功能规则文档，包含设计思路和实现具体细节，确保方便后期重构或迭代开发时快速熟悉功能细节
- @Docs/DevEnvConfig.md - 开发环境配置文档，在项目开始前，创建该文档用于记录如何从零开始搭建开发环境，确保环境一致性
- @Docs/ChangeLog.md - 版本变更日志，按语义化版本组织
- @Docs/FeatureMap.md - 功能地图，包含功能间依赖关系和链接，可视化展示功能之间的关系和依赖
- @Docs/FAQ.md - 常见问题解答，按主题分类
- @Docs/TechDebt.md - 技术债务跟踪，记录待优化点

## 🔧 维护指南

### 文档归档规则
- 所有文档必须按照实际创建日期进行归档
- 如果文档创建日期与内容日期不符，优先使用实际创建日期
- 同一天创建的文档统一归档到同一个日期文件夹

### 文档移动规则
- 当发现文档归档错误时，应立即移动到正确的日期文件夹
- 移动文档后必须更新所有相关的索引文件
- 移动文档后必须更新所有相关的文件引用链接

### 质量保证
- 定期检查文档的分类是否合理
- 定期检查文档链接的有效性
- 定期更新文档的时间信息
- 定期清理过时或重复的内容
