---
description: 功能文档生成规范和模板 - 完整的文档创建、组织和索引维护流程
globs:
alwaysApply: false
---
---
name: feature-docs-rules
description: 功能文档生成规范 - 包含文档创建、放置、索引维护的完整流程
---

# 功能文档生成规范

## 📖 概述

为了保持项目功能的可理解性和文档的一致性，每个重要功能都应该有相应的说明文档。

**文档生成依据**: 所有文档内容都基于Git提交分析，通过分析分支的新增提交来理解功能变更的范围、目的和实现方式。

**文档重点**: 关注功能的最终实现和使用方法，而不是详细的开发过程。通过Git提交获取变更信息，但文档重点描述最终的功能价值。

## 🎯 核心原则

1. **Git驱动**: 所有文档生成都基于Git提交分析，确保文档内容准确反映实际变更
2. **功能导向**: 重点描述最终功能而非开发历史
3. **用户友好**: 提供清晰的使用方法和代码示例
4. **结构统一**: 使用标准模板和格式
5. **索引维护**: 所有文档都要在项目根目录README中建立索引
6. **避免冗余**: 不创建不必要的文件夹结构

## 📁 文档放置规则

### 1. 组件功能文档
- **位置**: 在组件目录下创建 `README.md`
- **示例**: `apps/TreeHole/src/components/AudioPlayer/README.md`
- **适用场景**: 组件功能说明、使用方法、API文档
- **内容重点**: 组件概述、主要功能、使用方法、技术实现、业务场景

### 2. 子组件功能文档
- **位置**: 在子组件同级目录创建 `[组件名].md`
- **示例**: `apps/TreeHole/src/components/AudioPlayer/components/ControllableGif.md`
- **适用场景**: 独立子组件的详细说明
- **命名规则**: 不创建新文件夹，使用组件名作为文档名
- **内容重点**: 组件概述、核心特性、快速开始、技术实现、使用场景

### 3. 应用级功能记录
- **位置**: 在应用根目录的 `README.md` 中添加迭代记录
- **示例**: `apps/TreeHole/README.md` 的迭代记录部分
- **适用场景**: 功能发布记录、版本历史
- **格式**: `* [功能描述 日期](./文档链接) - 简要说明`

### 4. 项目级变更
- **位置**: 项目根目录的 `README.md` 或专门的文档
- **适用场景**: 架构调整、依赖升级、构建配置变更

### 5. 文档索引维护 ⭐
- **位置**: 项目根目录 `README.md` 的最下方
- **格式**: 按类型分组的文档链接列表
- **monorepo**: 在 `apps/[项目名]/README.md` 中维护
- **作用**: 提供项目所有功能文档的快速访问入口
- **重要性**: 每次新增文档都必须更新索引

## 文档模板

### 组件功能文档模板

```markdown
# [组件名] 组件

## 📖 组件概述
简要描述组件的用途、核心功能和设计目标。

## ✨ 核心特性
- 🎮 **特性1**: 功能描述
- ⚡ **特性2**: 功能描述
- 🖼️ **特性3**: 功能描述

## 🚀 快速开始

### 基本用法
```vue
<template>
  <ComponentName
    :prop1="value1"
    :prop2="value2"
  />
</template>

<script setup>
import ComponentName from './ComponentName.vue'
</script>
```

### Props
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| prop1 | string | ✅ | - | 参数说明 |
| prop2 | boolean | ❌ | false | 参数说明 |

## 🔧 技术实现

### 核心原理
1. **原理1**: 实现方式说明
2. **原理2**: 技术方案说明

### 关键方法
```typescript
// 核心方法签名
function methodName(): ReturnType
```

## 🎯 使用场景
- 场景1描述
- 场景2描述

## 📋 更新日志
### v1.0.0 ([日期])
- ✨ 功能点1
- ⚡ 功能点2
- 🔧 功能点3
```

## 📚 文档索引维护规范

### 索引位置和结构
- **位置**: 项目根目录 `README.md` 的最下方
- **monorepo**: 在 `apps/[项目名]/README.md` 中维护
- **标题**: 使用 `## 📚 文档索引`
- **分类**: 按文档类型分为三个子类别

### 索引分类说明
1. **组件文档**: 具体组件的功能说明文档
2. **功能文档**: 功能概览、规范等综合性文档
3. **开发文档**: 开发规范、工具文档等

### 索引格式规范
```markdown
## 📚 文档索引

### 组件文档
* [组件名 组件描述](./相对路径/组件名.md) - 简要功能说明

### 功能文档
* [功能名称](./相对路径/README.md) - 功能描述

### 开发文档
* [规范名称](./相对路径/文档名.md) - 规范说明
```

### 索引维护要求
- **新增文档**: 必须在索引中添加对应条目
- **路径格式**: 使用相对路径，以 `./` 开头
- **描述要求**: 简要说明文档的核心功能或用途
- **排序规则**: 同类别内按重要性或创建时间排序
- **链接验证**: 确保所有链接都能正确访问

### monorepo架构下的索引管理
- 每个应用独立维护自己的文档索引
- 不同应用间可以交叉引用（使用相对路径）
- 项目级文档可以在根目录README中索引

## 文档内容要求

### 必须包含的信息
1. **功能概述**: 最终实现的功能和价值
2. **主要特性**: 核心功能点和技术特性
3. **使用方式**: 集成方法和接口定义
4. **变更总结**: 分支变更的整体总结

### 推荐包含的信息
1. **性能提升**: 具体的性能改进点
2. **用户体验**: 用户感知的改进
3. **技术实现**: 关键技术方案
4. **代码示例**: 使用示例和接口定义

### 避免的内容
1. **详细提交历史**: 不记录每个commit的详细信息
2. **过程性描述**: 重点关注最终结果而非开发过程
3. **冗余测试说明**: 简化测试相关的描述

## 🔄 完整文档创建流程

### 第零步：Git提交分析 ⭐
- **拉取分支变更**: 获取当前分支相对于主分支的所有新增提交
- **分析提交内容**: 理解每个提交的变更范围和目的
- **识别核心功能**: 从提交历史中提取主要功能变更
- **收集变更文件**: 统计新增、修改、删除的文件

#### 常用Git命令
```bash
# 查看当前分支相对于主分支的提交
git log master..HEAD --oneline

# 查看具体提交的文件变更
git show --stat <commit-hash>

# 查看分支间的文件差异统计
git diff master..HEAD --stat

# 查看具体文件的变更内容
git show <commit-hash> -- <file-path>
```

### 第一步：功能分析
- 基于Git提交分析功能的核心价值和用途
- 确定文档类型和放置位置
- 收集使用示例和API信息
- 识别目标用户（开发者/业务人员）

### 第二步：创建功能文档
- 使用对应模板创建文档
- 重点描述功能特性和使用方法
- 添加完整的代码示例和技术说明
- 包含业务场景说明（如适用）

### 第三步：更新相关文档
- 更新应用README的迭代记录部分
- 更新组件概览文档（如适用）
- 添加文档间的交叉引用链接

### 第四步：维护文档索引 ⭐
- **必须执行**: 在项目根目录README的文档索引中添加新文档
- 按文档类型正确分类
- 添加简要的功能描述
- 确保链接路径正确

### 第五步：文档审查
- 检查文档完整性和可读性
- 验证代码示例的正确性
- 确保格式规范统一
- 验证所有链接可访问

## 工具和命令

### 常用Git命令
```bash
# 查看分支提交历史
git log master..feature/branch-name --oneline

# 查看文件变更统计
git show --stat commit-hash

# 查看分支间的差异
git diff master..feature/branch-name --stat
```

### 文档生成辅助
- 使用Mermaid图表展示架构变更
- 使用代码块展示关键实现
- 使用表格整理变更统计

## 📋 实际操作示例

### 场景：为AudioPlayer新增ControllableGif组件创建文档

#### 0. Git提交分析
```bash
# 查看分支提交历史
git log master..feature/0529_GIF --oneline
# 输出：
# 0ed90c2 fix: 优化GIF组件加载逻辑和预加载功能
# ff4cc9b fix: 更新GIF资源地址以使用新的CDN链接
# 9147035 fix: 添加可控GIF组件并更新GIF资源地址

# 查看文件变更统计
git diff master..feature/0529_GIF --stat
# 输出：
# apps/TreeHole/src/components/AudioPlayer/components/ControllableGif.vue | 209 +++++++++++++++++++
# apps/TreeHole/src/components/AudioPlayer/index.vue                   |  10 +-
```

#### 1. 分析和规划
```
功能：可控制的GIF组件（基于Git提交分析）
类型：子组件功能文档
位置：apps/TreeHole/src/components/AudioPlayer/components/ControllableGif.md
目标用户：开发者
核心变更：新增209行组件文件，修改主组件集成
```

#### 2. 创建组件文档
- 文件：`ControllableGif.md`
- 内容：组件概述、核心特性、快速开始、技术实现、使用场景、更新日志

#### 3. 更新相关文档
- 更新 `apps/TreeHole/README.md` 迭代记录
- 更新 `apps/TreeHole/src/components/README.md` 组件概览
- 在 `apps/TreeHole/src/components/AudioPlayer/README.md` 中添加引用

#### 4. 维护文档索引
在 `apps/TreeHole/README.md` 最下方的文档索引中添加：
```markdown
### 组件文档
* [ControllableGif 可控GIF组件](./src/components/AudioPlayer/components/ControllableGif.md) - 可控制播放状态的GIF组件，与音频状态同步
```

#### 5. 验证和审查
- 检查所有链接可访问
- 验证代码示例正确性
- 确保文档结构完整

## 🎯 成功案例参考

### 完整的文档体系示例
- [AudioPlayer 组件文档](../apps/TreeHole/src/components/AudioPlayer/README.md)
  - 完整的功能说明和组件架构
  - 详细的使用方法和状态管理
  - 清晰的技术实现和业务场景
- [ControllableGif 组件文档](../apps/TreeHole/src/components/AudioPlayer/components/ControllableGif.md)
  - 专注功能特性和核心价值
  - 丰富的代码示例和API文档
  - 实用的使用场景说明

### 文档索引示例
- [TreeHole 项目文档索引](../apps/TreeHole/README.md#-文档索引)
  - 按类型清晰分类
  - 完整的链接和描述
  - 便于快速访问

## ⚠️ 重要注意事项

### 文档创建原则
1. **Git分析优先**: 必须先分析Git提交，所有文档内容都基于实际代码变更
2. **功能导向**: 重点描述功能价值而非开发过程
3. **用户友好**: 提供清晰的使用方法和完整示例
4. **技术准确**: 确保API文档和代码示例的正确性
5. **结构清晰**: 使用统一的文档结构和格式
6. **持续更新**: 功能变更时及时更新文档内容

### 文档组织要求
6. **避免冗余**: 不为单个文档创建新文件夹
7. **命名规范**: 子组件文档使用 `[组件名].md` 格式
8. **路径管理**: 使用相对路径，便于项目迁移
9. **分类明确**: 按文档类型正确分类和组织

### 索引维护要求 ⭐
10. **必须执行**: 每次新增文档都必须更新项目根目录README的文档索引
11. **格式统一**: 使用标准的索引格式和分类
12. **描述完整**: 为每个文档提供简要但准确的功能描述
13. **链接验证**: 确保所有索引链接都能正确访问

### monorepo特殊要求
14. **独立维护**: 每个应用独立维护自己的文档索引
15. **交叉引用**: 支持应用间的文档交叉引用
16. **路径规范**: 使用相对路径避免跨项目依赖

### 质量保证
17. **审查机制**: 文档创建后必须进行完整性审查
18. **示例验证**: 所有代码示例都要经过验证
19. **链接检查**: 定期检查文档索引中的链接有效性
20. **用户反馈**: 根据使用反馈持续优化文档质量

## ✅ 文档创建检查清单

### Git分析阶段 ⭐
- [ ] 拉取当前分支相对于主分支的所有提交
- [ ] 分析每个提交的变更内容和目的
- [ ] 统计文件变更情况（新增/修改/删除）
- [ ] 识别核心功能和技术实现要点
- [ ] 理解变更的业务价值和用户影响

### 创建阶段
- [ ] 基于Git分析确定文档类型和放置位置
- [ ] 选择合适的文档模板
- [ ] 收集完整的功能信息和示例
- [ ] 编写文档内容（概述、特性、使用方法、技术实现）

### 组织阶段
- [ ] 更新应用README的迭代记录
- [ ] 更新相关的概览文档
- [ ] 添加文档间的交叉引用

### 索引维护阶段 ⭐
- [ ] 在项目根目录README最下方添加文档索引条目
- [ ] 按正确类型分类（组件文档/功能文档/开发文档）
- [ ] 添加简要功能描述
- [ ] 使用正确的相对路径

### 验证阶段
- [ ] 检查所有链接可访问
- [ ] 验证代码示例正确性
- [ ] 确认文档格式规范
- [ ] 测试文档的可读性和完整性

### 发布阶段
- [ ] 提交所有相关文档变更
- [ ] 通知团队成员新文档的位置
- [ ] 记录文档创建的经验和改进点
