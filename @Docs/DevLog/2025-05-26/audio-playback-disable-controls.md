# 音频播放时禁用录音和播放按钮功能实现

**日期**: 2025年5月26日 星期一 10:37  
**功能**: 在音频播放状态时禁用录音按钮和对话中的播放按钮  
**状态**: 已完成

## 📅 当前准确时间

### 🇨🇳 中国时间（本地时间）
- **2025年05月26日 星期一 10:37分**
- **时区：CST (UTC+8)**

### 🌍 世界标准时间
- **UTC时间：2025-05-26 02:37:03**
- **时间戳：1716693423000**

## 功能需求

当音频处于播放状态时，包括TTS流式返回时的音频播放状态下，需要禁用：
1. **录音按钮** - 防止录音与音频播放冲突
2. **对话中的播放按钮** - 防止多个音频同时播放

## 技术实现

### 1. ChatArea组件播放按钮禁用

#### 修改播放按钮禁用逻辑
```vue
<!-- 音频播放控制 -->
<div class="flex items-center gap-8px">
  <button
    @click="toggleAudio(message)"
    class="flex items-center gap-5px px-10px py-5px bg-blue-50 hover:bg-blue-100 rounded-6px text-blue-600 text-12px transition-colors disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-blue-50"
    :disabled="!message.content.trim() || (isPlaying() && playingMessage?.index !== message.index)"
    :title="getPlayButtonTooltip(message)"
  >
    <el-icon size="14">
      <VideoPlay v-if="playingMessage?.index !== message.index" />
      <VideoPause v-else />
    </el-icon>
    <span>
      {{ playingMessage?.index === message.index ? "暂停" : "播放" }}
    </span>
  </button>
</div>
```

#### 添加播放按钮提示方法
```typescript
/**
 * 获取播放按钮的提示文字
 * @param message 消息对象
 * @returns 提示文字
 */
function getPlayButtonTooltip(message: {
  content: string;
  isBot: boolean;
  index: string;
}): string {
  if (!message.content.trim()) {
    return "消息内容为空，无法播放";
  }
  
  if (playingMessage.value?.index === message.index) {
    return "点击暂停播放";
  }
  
  if (isPlaying()) {
    return "其他音频正在播放中，请等待播放完成";
  }
  
  return "点击播放语音";
}
```

### 2. 录音控制组件状态管理

#### RecordingControl组件已有的禁用逻辑
```typescript
/**
 * 计算按钮是否禁用
 */
const isButtonDisabled = computed(() => {
  return props.isAudioPlaying || props.isAiResponding;
});

/**
 * 获取按钮提示文字
 */
function getButtonTooltip() {
  if (props.isRecording) return "点击停止录音";
  if (props.isAudioPlaying) return "请等待语音播放完成";
  if (props.isAiResponding) return "AI正在思考中，请稍候";
  return "点击开始录音";
}
```

### 3. 音频播放状态管理

#### 全局音频播放器状态检查
使用 `audioPlayer.ts` 中的 `isPlaying()` 方法检查全局播放状态：

```typescript
/**
 * 检查是否正在播放
 */
export function isPlaying(): boolean {
  return isGlobalPlayerActive;
}
```

#### 流式音频播放状态同步
在 `ChatArea.vue` 中正确触发音频播放状态事件：

```typescript
/**
 * 开始流式音频播放
 */
function startStreamAudio(messageId: string) {
  // 设置播放状态
  const message = props.messages.find(msg => msg.index === messageId);
  if (message) {
    playingMessage.value = message;
    emit("audioPlayStateChange", true); // 通知父组件音频开始播放
  }
}

/**
 * 处理流式音频播放完成
 */
function handleStreamAudioComplete() {
  currentStreamMessageId.value = null;
  isStreamPlaying.value = false;
  audioQueue.value = [];
  playingMessage.value = null;
  emit("audioPlayStateChange", false); // 通知父组件音频播放结束
}
```

### 4. 录音权限检查

#### useExercise Hook中的录音检查
```typescript
/**
 * 检查是否可以开始录音
 */
function canStartRecording(
  isRecording: boolean,
  isAiResponding: boolean
): boolean {
  if (!isPracticing.value) {
    return false;
  }

  if (isRecording) {
    return false;
  }

  if (isAudioPlaying.value) {
    ElMessage.warning("请等待语音播放完成后再录音");
    return false;
  }

  if (isAiResponding) {
    ElMessage.warning("AI正在思考中，请稍候");
    return false;
  }

  return true;
}
```

## 状态流转图

```
空闲状态
    ↓
音频播放开始
    ↓
禁用录音按钮 + 禁用其他播放按钮
    ↓
音频播放结束
    ↓
恢复所有按钮可用状态
```

## 用户体验优化

### 1. 视觉反馈
- **禁用状态**: 按钮透明度降低，鼠标悬停无效果
- **提示信息**: 鼠标悬停显示禁用原因
- **状态指示**: 播放中显示动画效果

### 2. 交互反馈
- **录音阻止**: 显示"请等待语音播放完成后再录音"提示
- **播放阻止**: 显示"其他音频正在播放中，请等待播放完成"提示

## 技术要点

### 1. 状态同步
- 使用事件机制 `emit("audioPlayStateChange", isPlaying)` 同步播放状态
- 全局音频播放器状态通过 `isPlaying()` 方法统一管理

### 2. 防冲突机制
- 播放新音频前自动停止其他音频
- 录音前检查音频播放状态
- 流式TTS播放时正确管理播放状态

### 3. 错误处理
- 音频播放失败时正确重置状态
- 流式播放超时自动停止
- 异常情况下的资源清理

## 测试验证

### 测试场景
1. **普通音频播放时录音** - 应被阻止并显示提示
2. **流式TTS播放时录音** - 应被阻止并显示提示
3. **音频播放时点击其他播放按钮** - 应被禁用
4. **音频播放结束后** - 所有按钮应恢复可用

### 验证结果
- ✅ 录音按钮在音频播放时正确禁用
- ✅ 播放按钮在其他音频播放时正确禁用
- ✅ 流式TTS播放时状态同步正确
- ✅ 用户提示信息清晰明确

## 相关文件

### 修改的文件
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/components/ChatArea.vue`
  - 添加播放按钮禁用逻辑
  - 添加 `getPlayButtonTooltip` 方法

### 已有的相关文件
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/components/RecordingControl.vue`
  - 录音按钮禁用逻辑（已存在）
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useExercise.ts`
  - 录音权限检查（已存在）
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/utils/audioPlayer.ts`
  - 全局音频播放状态管理（已存在）

## 总结

本次功能实现通过以下方式确保音频播放时正确禁用相关控件：

1. **播放按钮禁用**: 使用全局播放状态检查，防止多音频同时播放
2. **录音按钮禁用**: 通过状态传递和计算属性实现自动禁用
3. **状态同步**: 使用事件机制确保播放状态在组件间正确传递
4. **用户反馈**: 提供清晰的提示信息说明禁用原因

功能实现完整，用户体验良好，技术架构清晰。 