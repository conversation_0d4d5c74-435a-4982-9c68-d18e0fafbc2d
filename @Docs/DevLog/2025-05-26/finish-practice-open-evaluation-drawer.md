# 完成练习后自动打开评估抽屉功能

**日期**: 2025年5月26日 星期一 17:33  
**功能**: 点击完成练习时，接口调用返回后关闭练习抽屉并自动打开课程评估抽屉  
**状态**: 已完成

## 📅 当前准确时间

### 🇨🇳 中国时间（本地时间）
- **2025年05月26日 星期一 17:33分**
- **时区：CST (UTC+8)**

### 🌍 世界标准时间
- **UTC时间：2025-05-26 09:33:27**
- **时间戳：1748232807000**

## 功能需求

用户希望在点击"完成练习"按钮时，接口调用成功后：
1. 关闭当前的练习抽屉
2. 自动打开课程评估抽屉
3. 使用刚才练习的课程参数ID进行评估数据获取

## 技术实现

### 1. RecordingControl组件修改

#### 修改完成练习方法
在`handleFinishPractice`方法中，接口调用成功后传递更多信息给父组件：

```typescript
/**
 * 完成练习 - 根据新的接口要求构建请求参数
 * @description 移除aiAppraise参数，使用秒级时间戳，传递实际对话消息和持续时长
 */
async function handleFinishPractice() {
  try {
    finishLoading.value = true;

    // ... 接口调用逻辑 ...

    // 调用完成练习接口
    await finishWorkerTrainingTaskCourse(requestData);

    ElMessage.success("练习完成成功！");

    // 通知父组件练习完成，传递课程信息以便打开评估抽屉
    emit("finish", {
      requestData,
      courseInfo: props.courseInfo,
      shouldOpenEvaluation: true // 标识需要打开评估抽屉
    });
  } catch (error) {
    console.error("完成练习失败:", error);
    ElMessage.error("完成练习失败，请重试");
  } finally {
    finishLoading.value = false;
  }
}
```

#### 传递的数据结构
```typescript
{
  requestData: WorkerTrainingTaskCourseUpdateRequest, // 接口请求数据
  courseInfo: WorkerTrainingTaskCourseInstance,       // 课程信息
  shouldOpenEvaluation: true                          // 是否需要打开评估抽屉
}
```

### 2. 练习抽屉组件修改

#### exercise/index.vue 修改
在`handleFinishPractice`方法中，直接传递完整数据给父组件：

```typescript
/**
 * 处理结束练习 - 从RecordingControl组件触发
 */
async function handleFinishPractice(data?: any) {
  console.log("练习完成，接收到的数据:", data);

  // 停止计时器
  if (timerState) {
    timerState.stopTimer();
  }

  // 清理资源
  audioRecorderState.cleanup();

  if (chatAreaRef.value) {
    chatAreaRef.value.cleanupAudioPlayer();
  }

  // 关闭抽屉并通知父组件
  drawerVisible.value = false;
  
  // 传递完整的数据给父组件，包括课程信息和评估抽屉标识
  emit("finish", data);
}
```

### 3. 任务课程详情弹窗修改

#### TaskCourseDetailDialog.vue 修改
在`handleExerciseFinish`方法中，检查是否需要打开评估抽屉：

```typescript
// 处理练习完成
async function handleExerciseFinish(data: any) {
  console.log("练习完成，接收到的数据:", data);

  // 关闭练习抽屉
  exerciseDrawerVisible.value = false;

  // 刷新任务课程数据
  await fetchTaskCourses();

  // 通知父组件刷新数据
  emit("refresh");

  ElMessage.success("课程练习已完成！");

  // 检查是否需要打开评估抽屉
  if (data?.shouldOpenEvaluation && data?.courseInfo) {
    console.log("自动打开评估抽屉，课程信息:", data.courseInfo);
    
    // 设置当前课程信息
    currentCourse.value = data.courseInfo;
    
    // 延迟一下再打开评估抽屉，确保练习抽屉已经完全关闭
    setTimeout(() => {
      evaluationDrawerVisible.value = true;
    }, 300);
  }
}
```

## 功能流程

### 1. 用户操作流程
1. 用户在练习抽屉中点击"完成练习"按钮
2. 系统调用`finishWorkerTrainingTaskCourse`接口
3. 接口调用成功后显示成功提示
4. 自动关闭练习抽屉
5. 延迟300ms后自动打开评估抽屉
6. 评估抽屉使用相同的课程ID获取评估数据

### 2. 数据传递流程
```
RecordingControl.vue (完成练习)
    ↓ emit("finish", data)
exercise/index.vue (练习抽屉)
    ↓ emit("finish", data)
TaskCourseDetailDialog.vue (任务详情弹窗)
    ↓ 检查 shouldOpenEvaluation
EvaluationDrawer.vue (评估抽屉)
```

### 3. 关键参数传递
- `courseInfo.workerTrainingTaskId` - 工作人员训练任务ID
- `courseInfo.trainingTaskCourseId` - 训练任务课程ID
- `shouldOpenEvaluation: true` - 标识需要打开评估抽屉

## 技术细节

### 1. 时序控制
使用`setTimeout`延迟300ms打开评估抽屉，确保练习抽屉完全关闭，避免UI冲突。

### 2. 数据一致性
评估抽屉使用相同的课程信息（`courseInfo`），确保获取到最新的评估数据。

### 3. 错误处理
如果接口调用失败，不会触发抽屉切换，用户可以重试。

### 4. 用户体验
- 显示成功提示信息
- 平滑的抽屉切换动画
- 自动刷新任务课程列表

## 测试验证

### 测试场景
1. **正常完成练习**
   - ✅ 接口调用成功
   - ✅ 练习抽屉关闭
   - ✅ 评估抽屉自动打开
   - ✅ 评估数据正确加载

2. **接口调用失败**
   - ✅ 显示错误提示
   - ✅ 练习抽屉保持打开
   - ✅ 不会打开评估抽屉

3. **数据传递验证**
   - ✅ 课程ID正确传递
   - ✅ 评估抽屉获取到正确数据

## 修改的文件

### 组件文件
- `RecordingControl.vue` - 修改完成练习方法，传递课程信息
- `exercise/index.vue` - 传递完整数据给父组件
- `TaskCourseDetailDialog.vue` - 处理自动打开评估抽屉逻辑

### 数据流变更
- 增加`shouldOpenEvaluation`标识
- 传递完整的`courseInfo`对象
- 保持`requestData`用于调试和日志

## 总结

本次功能实现成功解决了用户需求：
1. **无缝衔接** - 练习完成后自动打开评估抽屉
2. **数据一致** - 使用相同的课程ID获取评估数据
3. **用户友好** - 减少用户手动操作步骤
4. **稳定可靠** - 完善的错误处理和时序控制

这提升了整体的用户体验，让练习到评估的流程更加顺畅。 