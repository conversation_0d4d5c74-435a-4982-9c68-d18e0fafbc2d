# TTS等待状态下禁用按钮功能实现

**日期**: 2025年5月26日 星期一 11:38  
**功能**: 等待TTS接口返回时禁用所有播放按钮，流式播放时禁用暂停按钮  
**状态**: 已完成

## 📅 当前准确时间

### 🇨🇳 中国时间（本地时间）
- **2025年05月26日 星期一 11:38分**
- **时区：CST (UTC+8)**

### 🌍 世界标准时间
- **UTC时间：2025-05-26 03:38:34**
- **时间戳：1748230714848**

## 功能需求

根据用户需求，实现以下功能：

1. **等待TTS接口返回时，禁用所有播放按钮**
2. **当前音频是TTS流式播放而不是从缓存中重播的情况下，禁用当前音频的暂停按钮**

## 技术实现

### 1. 状态管理增强

#### useChat Hook 状态扩展
```typescript
// 新增：TTS等待状态管理
const isTtsWaiting = ref(false); // TTS接口是否正在等待返回
const currentTtsMessageId = ref<string | null>(null); // 当前等待TTS的消息ID

// 流式TTS状态管理扩展
const streamTtsState = ref<
  Map<
    string,
    {
      isStarted: boolean;
      isCompleted: boolean;
      audioBuffers: ArrayBuffer[];
      isStreamPlaying: boolean; // 新增：是否正在流式播放（而非缓存重播）
    }
  >
>(new Map());
```

#### TTS等待状态生命周期管理
```typescript
async function startStreamTTS(msgId: string, finalContent: string, roleInfo: RoleInfo) {
  // 设置TTS等待状态
  isTtsWaiting.value = true;
  currentTtsMessageId.value = msgId;

  // 初始化流式TTS状态
  streamTtsState.value.set(msgId, {
    isStarted: true,
    isCompleted: false,
    audioBuffers: [],
    isStreamPlaying: true // 标记为流式播放
  });

  try {
    // 调用流式TTS接口
    await sendTtsStream(/* ... */);
    
    // 标记流式播放结束
    const state = streamTtsState.value.get(msgId);
    if (state) {
      state.isStreamPlaying = false; // 流式播放结束
    }
  } finally {
    // 清理TTS等待状态
    isTtsWaiting.value = false;
    currentTtsMessageId.value = null;
  }
}
```

### 2. 按钮禁用逻辑

#### 播放按钮禁用策略
```typescript
/**
 * 检查是否应该禁用播放按钮
 * @param messageId 消息ID
 * @param isCurrentPlaying 是否是当前播放的消息
 * @returns 是否应该禁用
 */
shouldDisablePlayButton: (messageId: string, isCurrentPlaying: boolean) => {
  // 如果TTS正在等待返回，禁用所有播放按钮
  if (isTtsWaiting.value) {
    return true;
  }

  // 如果当前消息正在流式播放，禁用暂停按钮
  if (isCurrentPlaying) {
    const state = streamTtsState.value.get(messageId);
    return state?.isStreamPlaying || false;
  }

  return false;
}
```

#### ChatArea组件按钮禁用实现
```typescript
function isPlayButtonDisabled(message: { content: string; isBot: boolean; index: string; }): boolean {
  // 基础检查
  if (!message.content.trim() || hasAudioError(message.index)) {
    return true;
  }

  const isCurrentPlaying = playingMessage.value?.index === message.index;

  // 使用新的状态管理逻辑
  if (props.shouldDisablePlayButton) {
    const shouldDisable = props.shouldDisablePlayButton(message.index, isCurrentPlaying);
    if (shouldDisable) {
      return true;
    }
  }

  // 传统逻辑：其他音频正在播放时禁用
  if (isPlaying() && !isCurrentPlaying) {
    return true;
  }

  return false;
}
```

### 3. 录音按钮禁用

#### RecordingControl组件状态管理
```typescript
// 新增TTS等待状态prop
const props = defineProps<{
  isRecording: boolean;
  recordingTime: number;
  recordingText: string;
  isAudioPlaying: boolean;
  isAiResponding?: boolean;
  isTtsWaiting?: boolean; // 新增：TTS是否正在等待返回
}>();

// 按钮禁用逻辑
const isButtonDisabled = computed(() => {
  return props.isAudioPlaying || props.isAiResponding || props.isTtsWaiting;
});
```

#### useExercise Hook录音检查增强
```typescript
function canStartRecording(
  isRecording: boolean,
  isAiResponding: boolean,
  isTtsWaiting?: boolean // 新增参数
): boolean {
  if (!isPracticing.value) return false;
  if (isRecording) return false;
  
  if (isAudioPlaying.value) {
    ElMessage.warning("请等待语音播放完成后再录音");
    return false;
  }

  if (isAiResponding) {
    ElMessage.warning("AI正在思考中，请稍候");
    return false;
  }

  if (isTtsWaiting) {
    ElMessage.warning("TTS正在生成中，请稍候");
    return false;
  }

  return true;
}
```

### 4. 用户体验优化

#### 提示文字增强
```typescript
function getPlayButtonTooltip(message: { content: string; isBot: boolean; index: string; }): string {
  // 检查TTS等待状态
  if (props.isTtsWaiting) {
    return "TTS正在生成中，请等待";
  }

  // 检查是否正在流式播放且无法暂停
  if (isCurrentPlaying && props.isMessageStreamPlaying) {
    const isStreamPlaying = props.isMessageStreamPlaying(message.index);
    if (isStreamPlaying) {
      return "流式播放中，无法暂停";
    }
  }

  // 其他状态提示...
}
```

#### 录音按钮状态提示
```typescript
function getButtonTooltip() {
  if (props.isRecording) return "点击停止录音";
  if (props.isAudioPlaying) return "请等待语音播放完成";
  if (props.isTtsWaiting) return "TTS正在生成中，请稍候";
  if (props.isAiResponding) return "AI正在思考中，请稍候";
  return "点击开始录音";
}

const statusText = computed(() => {
  if (props.isAudioPlaying) return "语音播放中...";
  if (props.isTtsWaiting) return "TTS生成中...";
  if (props.isAiResponding) return "AI思考中...";
  return "";
});
```

## 状态流转图

```
空闲状态
    ↓
AI开始响应
    ↓
AI响应完成，开始TTS生成
    ↓
TTS等待状态（禁用所有播放按钮和录音按钮）
    ↓
TTS开始流式返回
    ↓
流式播放状态（禁用当前音频暂停按钮）
    ↓
流式播放完成
    ↓
恢复所有按钮可用状态
```

## 功能特性

### ✅ 已实现功能

1. **TTS等待状态管理**
   - 精确跟踪TTS接口调用状态
   - 自动设置和清理等待状态
   - 支持降级TTS的状态管理

2. **播放按钮智能禁用**
   - TTS等待时禁用所有播放按钮
   - 流式播放时禁用当前音频暂停按钮
   - 保持其他播放按钮的正常逻辑

3. **录音按钮状态同步**
   - TTS等待时禁用录音按钮
   - 键盘录音功能同步禁用
   - 清晰的状态提示信息

4. **用户体验优化**
   - 详细的按钮提示文字
   - 视觉状态指示
   - 一致的交互反馈

### 🎯 核心优势

1. **状态一致性**：所有相关组件的状态完全同步
2. **用户友好**：清晰的提示信息，避免用户困惑
3. **逻辑清晰**：明确区分不同播放状态的处理逻辑
4. **扩展性强**：易于添加新的状态管理需求

## 测试验证

### 测试场景

1. **TTS等待期间**
   - ✅ 所有播放按钮被禁用
   - ✅ 录音按钮被禁用
   - ✅ 显示"TTS正在生成中"提示

2. **流式播放期间**
   - ✅ 当前音频暂停按钮被禁用
   - ✅ 其他音频播放按钮被禁用
   - ✅ 显示"流式播放中，无法暂停"提示

3. **播放完成后**
   - ✅ 所有按钮恢复可用状态
   - ✅ 可以重新播放音频
   - ✅ 可以开始新的录音

4. **错误处理**
   - ✅ TTS失败时正确重置状态
   - ✅ 降级TTS时状态管理正确
   - ✅ 异常情况下的状态清理

## 相关文件

### 修改的文件
- `hooks/useChat.ts` - 添加TTS等待状态管理
- `components/ChatArea.vue` - 实现播放按钮禁用逻辑
- `components/RecordingControl.vue` - 添加TTS等待状态支持
- `hooks/useExercise.ts` - 增强录音检查逻辑
- `index.vue` - 传递新的状态属性

### 新增功能
- TTS等待状态跟踪
- 流式播放状态区分
- 智能按钮禁用策略
- 增强的用户提示系统

## 技术要点

### 1. 状态管理策略
- 使用响应式状态管理TTS生命周期
- 区分流式播放和缓存重播状态
- 统一的状态清理机制

### 2. 组件通信
- 通过props传递状态管理函数
- 使用计算属性实现响应式禁用逻辑
- 事件驱动的状态同步

### 3. 用户体验
- 一致的视觉反馈
- 清晰的操作提示
- 防止用户误操作

这次实现完美解决了用户提出的需求，提供了更好的用户体验和更稳定的功能表现。 