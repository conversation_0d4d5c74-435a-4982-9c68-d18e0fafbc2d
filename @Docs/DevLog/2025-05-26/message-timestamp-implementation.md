# 消息时间戳功能实现

## 📅 当前时间

**2025年05月26日 星期一 18:30分** (CST UTC+8)

## 🎯 需求描述

为finishWorkerTrainingTaskCourse接口中的conversationMessages添加正确的时间戳：
- 如果role是user，start为此条信息开始录音的时间
- 如果role是bot，start为此条信息开始调用TTS的时间

## 🔧 实现方案

### 1. 消息接口扩展

**文件**: `hooks/useChat.ts`

#### 扩展Message接口
```typescript
interface Message {
  content: string;
  isBot: boolean;
  index: string;
  /** 消息开始时间（秒级时间戳） - 用户消息为录音开始时间，机器人消息为TTS开始时间 */
  startTime?: number;
}
```

### 2. 录音开始时间记录

**文件**: `hooks/useAudioRecorder.ts`

#### 添加录音开始时间状态
```typescript
const recordingStartTime = ref<number>(0); // 录音开始时间（秒级时间戳）
```

#### 在开始录音时记录时间
```typescript
async function startRecording() {
  // ...
  recordingStartTime.value = Math.floor(Date.now() / 1000); // 记录录音开始时间
  // ...
}
```

#### 修改回调函数传递时间
```typescript
// 修改回调函数签名
const onTextRecognized = ref<(text: string, startTime: number) => void>();

// 在停止录音时传递开始时间
onTextRecognized.value(recognizedText, recordingStartTime.value);
```

### 3. 用户消息时间戳设置

**文件**: `hooks/useChat.ts`

#### 修改sendMessage函数
```typescript
async function sendMessage(
  content: string,
  workerTrainingTaskId: number,
  trainingTaskCourseId: number,
  roleInfo: RoleInfo,
  userMessageStartTime?: number // 新增参数
) {
  // 添加用户消息时设置开始时间
  messages.value.push({
    content: content,
    isBot: false,
    index: msgId,
    startTime: userMessageStartTime || Math.floor(Date.now() / 1000)
  });
}
```

### 4. 机器人消息TTS时间戳设置

**文件**: `hooks/useChat.ts`

#### 在流式TTS开始时设置时间
```typescript
async function startStreamTTS(msgId: string, finalContent: string, roleInfo: RoleInfo) {
  // 设置机器人消息的TTS开始时间
  const ttsStartTime = Math.floor(Date.now() / 1000);
  const messageIndex = messages.value.findIndex(msg => msg.index === msgId);
  if (messageIndex !== -1) {
    messages.value[messageIndex].startTime = ttsStartTime;
  }
  // ...
}
```

#### 在传统TTS中也设置时间
```typescript
async function fallbackToTraditionalTTS(msgId: string, content: string, roleInfo: RoleInfo) {
  // 设置机器人消息的TTS开始时间（如果还没有设置）
  const messageIndex = messages.value.findIndex(msg => msg.index === msgId);
  if (messageIndex !== -1 && !messages.value[messageIndex].startTime) {
    const ttsStartTime = Math.floor(Date.now() / 1000);
    messages.value[messageIndex].startTime = ttsStartTime;
  }
  // ...
}
```

### 5. 主组件调用修改

**文件**: `exercise/index.vue`

#### 修改sendMessage函数
```typescript
async function sendMessage(content: string, startTime?: number) {
  await chatState.sendMessage(
    content,
    exerciseState.getWorkerTrainingTaskId(),
    exerciseState.getTrainingTaskCourseId(),
    exerciseState.roleInfo.value,
    startTime // 传递录音开始时间
  );
}
```

#### 修改录音回调
```typescript
audioRecorderState.setCallbacks({
  onTextRecognized: (text: string, startTime: number) => {
    sendMessage(text, startTime);
  }
});
```

### 6. 接口调用时使用正确时间戳

**文件**: `components/RecordingControl.vue`

#### 更新消息类型定义
```typescript
messages?: Array<{
  content: string;
  isBot: boolean;
  index: string;
  startTime?: number;
}>;
```

#### 使用正确的时间戳
```typescript
const conversationMessages = (props.messages || []).map(msg => ({
  role: msg.isBot ? "bot" : "user",
  content: msg.content,
  start: (msg.startTime || practiceStartTime).toString(), // 使用消息的实际开始时间
  key: msg.index
}));
```

## 🔍 技术细节

### 时间戳记录时机
1. **用户消息**: 在`startRecording()`时记录，确保是录音开始的准确时间
2. **机器人消息**: 在`startStreamTTS()`或`fallbackToTraditionalTTS()`时记录，确保是TTS开始的准确时间

### 时间戳格式
- 统一使用秒级时间戳：`Math.floor(Date.now() / 1000)`
- 确保与接口要求的格式一致

### 兼容性处理
- 使用可选属性`startTime?`，确保向后兼容
- 在没有时间戳时使用练习开始时间作为fallback

## 📋 数据流程

### 用户消息流程
1. 用户开始录音 → 记录`recordingStartTime`
2. 录音结束 → 调用`onTextRecognized(text, startTime)`
3. 发送消息 → 设置`message.startTime = startTime`
4. 完成练习 → 使用`msg.startTime`作为`start`字段

### 机器人消息流程
1. AI回复完成 → 调用`startStreamTTS()`
2. TTS开始 → 记录并设置`message.startTime`
3. 完成练习 → 使用`msg.startTime`作为`start`字段

## 🎯 实现效果

1. **准确性**: 每条消息都有精确的开始时间戳
2. **一致性**: 用户消息和机器人消息都使用相同的时间戳格式
3. **可追溯性**: 可以准确追踪每个操作的时间点
4. **接口兼容**: 满足后端接口的时间戳要求

## 📝 相关文件

- `hooks/useChat.ts` - 聊天状态管理和消息时间戳
- `hooks/useAudioRecorder.ts` - 录音时间记录
- `exercise/index.vue` - 主练习组件
- `components/RecordingControl.vue` - 录音控制和接口调用

## 🔄 后续优化

1. 可考虑添加消息结束时间记录
2. 可添加时间戳验证和错误处理
3. 可优化时间戳的精度和格式 