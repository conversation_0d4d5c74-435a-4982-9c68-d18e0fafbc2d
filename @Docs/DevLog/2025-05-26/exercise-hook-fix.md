# 修复练习Hook中getWorkerTrainingTaskId函数报错

## 📅 当前准确时间

### 🇨🇳 中国时间（本地时间）
- **2025年05月26日 星期一 16:46分**
- **时区：CST (UTC+8)**

### 🌍 世界标准时间
- **UTC时间：2025-05-26T08:45:54.985Z**
- **时间戳：1748249154985**

### 📊 时间来源验证
- 本地系统时间：2025年 5月26日 星期一 16时46分00秒 CST
- JavaScript时间：2025年05月26日星期一 16:45:54
- 一致性：一致

## 🐛 问题描述

在练习页面中，控制台报错：`'Uncaught (in promise) TypeError: Cannot read properties of null (reading 'workerTrainingTaskId')'`

### 问题原因
1. `useExercise` hook中的 `getWorkerTrainingTaskId()` 函数直接访问 `courseInfo.workerTrainingTaskId`，但该字段可能为 `undefined` 或 `null`
2. `roleInfo` 计算属性使用的是假数据，需要从 `courseInfo` 中获取真实的角色信息

## 🔧 解决方案

### 1. 修复TaskCourseDetailDialog中的组件渲染逻辑

**文件位置：** `apps/telesale-web/src/views/aiQualityInspection/myTask/components/TaskCourseDetailDialog.vue`

**问题根因：** `currentCourse` 初始值为 `null`，但 `ExerciseDrawer` 组件在初始化时就会立即调用 `useExercise(props.courseInfo)`，导致传入 `null` 值。

**修改前：**
```vue
<!-- 练习抽屉 -->
<ExerciseDrawer
  v-model:visible="exerciseDrawerVisible"
  :course-info="currentCourse"
  @finish="handleExerciseFinish"
/>
```

**修改后：**
```vue
<!-- 练习抽屉 -->
<ExerciseDrawer
  v-if="currentCourse"
  v-model:visible="exerciseDrawerVisible"
  :course-info="currentCourse"
  @finish="handleExerciseFinish"
/>
```

**说明：** 添加 `v-if="currentCourse"` 确保只有在课程信息不为空时才渲染组件。

### 2. 增强handlePracticeCourse函数的数据验证

**修改前：**
```typescript
function handlePracticeCourse(course: WorkerTrainingTaskCourseInstance) {
  currentCourse.value = course;
  exerciseDrawerVisible.value = true;
}
```

**修改后：**
```typescript
function handlePracticeCourse(course: WorkerTrainingTaskCourseInstance) {
  if (!course) {
    ElMessage.error("课程信息不能为空");
    return;
  }
  
  if (!course.workerTrainingTaskId || !course.trainingTaskCourseId) {
    ElMessage.error("课程信息不完整，缺少必要的ID信息");
    console.error("课程信息不完整:", course);
    return;
  }
  
  console.log("开始练习课程:", course);
  currentCourse.value = course;
  exerciseDrawerVisible.value = true;
}
```

### 3. 修复getWorkerTrainingTaskId和getTrainingTaskCourseId函数

**文件位置：** `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useExercise.ts`

**修改前：**
```typescript
function getWorkerTrainingTaskId(): number {
  return Number(courseInfo.workerTrainingTaskId);
}

function getTrainingTaskCourseId(): number {
  return Number(courseInfo.trainingTaskCourseId);
}
```

**修改后：**
```typescript
function getWorkerTrainingTaskId(): number {
  const taskId = courseInfo?.workerTrainingTaskId;
  if (!taskId) {
    console.error('workerTrainingTaskId 为空，courseInfo:', courseInfo);
    throw new Error('workerTrainingTaskId 不能为空');
  }
  return Number(taskId);
}

function getTrainingTaskCourseId(): number {
  const courseId = courseInfo?.trainingTaskCourseId;
  if (!courseId) {
    console.error('trainingTaskCourseId 为空，courseInfo:', courseId);
    throw new Error('trainingTaskCourseId 不能为空');
  }
  return Number(courseId);
}
```

### 4. 修复roleInfo计算属性使用真实数据

**修改前：**
```typescript
const roleInfo = computed(() => {
  return {
    role: "father", // 默认值，实际应从API获取
    roleName: "张先生", // 默认值，实际应从API获取
    roleIntroduction:
      courseInfo?.course?.course?.target ||
      "35岁，IT行业工程师，关注孩子教育质量",
    personality: "关注教育质量、理性决策、注重性价比"
  };
});
```

**修改后：**
```typescript
const roleInfo = computed(() => {
  const course = courseInfo?.course?.course;
  return {
    role: course?.botRole || "customer", // 从课程信息中获取机器人角色
    roleName: course?.botName || "客户", // 从课程信息中获取机器人名称
    roleIntroduction:
      course?.target || course?.backgroundDesc || "练习对话角色",
    personality: course?.conversationReq || "按照课程要求进行对话"
  };
});
```

## 📋 数据结构说明

### WorkerTrainingTaskCourseInstance接口
```typescript
export interface WorkerTrainingTaskCourseInstance {
  workerTrainingTaskId?: string;  // 员工训练任务ID
  trainingTaskCourseId?: string;  // 任务具体的课程ID
  finished?: boolean;             // 是否完成
  finishedAt?: string;           // 完成时间
  score?: string;                // 评级
  workerTrainingConversationId?: string; // 训练会话ID
  course?: TrainingTaskCourseInstance;   // 课程信息
}
```

### TrainingCourseInstance接口（角色信息来源）
```typescript
export interface TrainingCourseInstance {
  id?: number;              // 课程ID
  name?: string;            // 课程名称
  maxDuration?: number;     // 最大持续时间
  botName?: string;         // 机器人名称 ✅ 用作roleName
  botRole?: string;         // 机器人角色 ✅ 用作role
  backgroundDesc?: string;  // 背景描述 ✅ 用作roleIntroduction备选
  conversationReq?: string; // 对话要求 ✅ 用作personality
  target?: string;          // 目标 ✅ 用作roleIntroduction主选
  // ...其他字段
}
```

## ✅ 修复效果

1. **组件渲染安全**：通过 `v-if` 条件渲染确保组件只在有数据时才初始化，避免传入 `null` 值
2. **数据验证增强**：在练习开始前验证课程信息的完整性，提供清晰的错误提示
3. **空值安全检查**：添加了对 `workerTrainingTaskId` 和 `trainingTaskCourseId` 的空值检查，避免运行时错误
4. **错误提示优化**：当数据为空时，提供清晰的错误信息和调试信息
5. **真实数据使用**：角色信息现在从课程配置中获取真实数据，而不是硬编码的假数据
6. **数据映射合理**：将课程配置中的字段合理映射到角色信息结构中

## 🔗 相关文件

- `apps/telesale-web/src/views/aiQualityInspection/myTask/components/TaskCourseDetailDialog.vue` - 主要修复文件（组件渲染逻辑）
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useExercise.ts` - Hook函数修复文件
- `apps/telesale-web/src/api/AIQualityInspection/taskManagement.ts` - 接口定义文件
- `apps/telesale-web/src/api/AIQualityInspection/courseManage.ts` - 课程管理接口定义

### 5. 修复ExerciseDrawer组件的Props类型检查警告

**文件位置：** `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/index.vue`

**问题：** Vue的prop类型检查在组件定义时触发警告：`'Invalid prop: type check failed for prop "courseInfo". Expected Object, got Null'`

**修改前：**
```typescript
const props = defineProps<{
  courseInfo: WorkerTrainingTaskCourseInstance;
}>();
```

**修改后：**
```typescript
const props = withDefaults(
  defineProps<{
    courseInfo?: WorkerTrainingTaskCourseInstance;
  }>(),
  {
    courseInfo: undefined
  }
);
```

**相关修改：**
- 添加了条件初始化hooks：`const exerciseState = props.courseInfo ? useExercise(props.courseInfo) : null;`
- 在所有使用hooks的地方添加了空值检查
- 在模板中使用可选链操作符：`exerciseState?.loading?.value`
- 在组件挂载和卸载时添加了条件检查

## 📝 注意事项

1. **组件渲染安全**：通过 `v-if="currentCourse"` 确保组件只在有数据时才渲染
2. **数据完整性验证**：确保传入的 `courseInfo` 数据完整，特别是 `workerTrainingTaskId` 和 `trainingTaskCourseId` 字段
3. **空值安全处理**：所有hooks和状态访问都添加了空值检查，避免运行时错误
4. **错误信息详细**：错误会在控制台输出详细的调试信息，便于排查问题
5. **类型安全**：Props定义支持可选类型，避免Vue的类型检查警告 