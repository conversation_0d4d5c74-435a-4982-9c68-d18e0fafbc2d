# finishWorkerTrainingTaskCourse 接口重构

## 📅 时间信息
- **重构日期**: 2025年05月26日
- **重构时间**: 14:16 (CST UTC+8)

## 🎯 重构目标
根据新的接口要求，重构 `finishWorkerTrainingTaskCourse` 接口调用，主要包括：
1. 移除 `aiAppraise` 参数
2. 使用秒级时间戳格式
3. 传递实际的对话消息内容
4. 移除消息中的 `createdAt` 参数
5. 传递实际的练习持续时长

## 🔧 修改内容

### 1. RecordingControl.vue 组件修改

#### 新增 Props
```typescript
/** 对话消息列表 */
messages?: Array<{ content: string; isBot: boolean; index: string }>;
/** 练习开始时间（秒级时间戳） */
practiceStartTime?: number;
/** 练习结束时间（秒级时间戳） */
practiceEndTime?: number;
/** 练习持续时长（秒） */
practiceDuration?: number;
```

#### 重构 handleFinishPractice 函数
- **移除**: `aiAppraise` 参数
- **修改**: 时间格式从 ISO 字符串改为秒级时间戳
- **新增**: 实际对话消息转换逻辑
- **新增**: 练习时长计算逻辑

#### 消息格式转换
```typescript
const conversationMessages = (props.messages || []).map(msg => ({
  role: msg.isBot ? "bot" : "user", // 角色：机器人或用户
  content: msg.content, // 消息内容
  start: practiceStartTime.toString(), // 消息开始时间（使用练习开始时间）
  key: msg.index // 消息唯一标识
}));
```

#### 请求参数结构
```typescript
const requestData: WorkerTrainingTaskCourseUpdateRequest = {
  workerTrainingTaskId: props.courseInfo.workerTrainingTaskId || "0",
  trainingTaskCourseId: props.courseInfo.trainingTaskCourseId || "0",
  conversation: {
    workerId: userMsg.id || 0, // 工作人员ID
    trainingTaskCourseId: props.courseInfo.trainingTaskCourseId || "0", // 训练任务课程ID
    duration: practiceDuration, // 实际练习时长（秒）
    messages: conversationMessages, // 实际对话消息（不包含createdAt）
    begin: practiceStartTime.toString(), // 开始时间（秒级时间戳）
    end: practiceEndTime.toString() // 结束时间（秒级时间戳）
    // 移除 aiAppraise 参数
  }
};
```

### 2. index.vue 父组件修改

#### 新增状态变量
```typescript
// 练习开始时间（秒级时间戳）
const practiceStartTime = ref<number>(0);
```

#### 传递新参数给 RecordingControl
```vue
<RecordingControl
  :messages="chatState.messages.value"
  :practice-start-time="practiceStartTime"
  :practice-duration="timerState.timer.value"
  <!-- 其他现有参数 -->
/>
```

#### 初始化练习开始时间
```typescript
// 设置练习开始时间（秒级时间戳）
practiceStartTime.value = Math.floor(Date.now() / 1000);
```

## 📋 接口变更对比

### 变更前
```typescript
conversation: {
  workerId: userMsg.id || 0,
  trainingTaskCourseId: props.courseInfo.trainingTaskCourseId || "0",
  duration: 0, // 模拟数据
  messages: [], // 空数组
  begin: new Date().toISOString(), // ISO 格式
  end: new Date().toISOString(), // ISO 格式
  aiAppraise: { // 需要移除的参数
    score: props.evaluationLevel || "B",
    result: "练习完成",
    createdAt: new Date().toISOString(),
    reason: `用户${userMsg.name || "系统"}完成了课程练习`
  }
}
```

### 变更后
```typescript
conversation: {
  workerId: userMsg.id || 0, // 工作人员ID
  trainingTaskCourseId: props.courseInfo.trainingTaskCourseId || "0", // 训练任务课程ID
  duration: practiceDuration, // 实际练习时长（秒）
  messages: conversationMessages, // 实际对话消息（不包含createdAt）
  begin: practiceStartTime.toString(), // 开始时间（秒级时间戳）
  end: practiceEndTime.toString() // 结束时间（秒级时间戳）
  // 移除 aiAppraise 参数
}
```

## ✅ 验证要点

1. **时间格式**: 确保 `begin` 和 `end` 使用秒级时间戳字符串
2. **消息内容**: 确保传递实际的对话消息，不包含 `createdAt` 字段
3. **持续时长**: 确保 `duration` 为实际练习时长（秒）
4. **参数移除**: 确保不再传递 `aiAppraise` 参数

## 🔍 测试建议

1. 测试完成练习功能是否正常工作
2. 验证传递给接口的参数格式是否正确
3. 检查控制台日志中的请求参数结构
4. 确认接口调用成功后的响应处理

## 📝 注意事项

1. 练习开始时间在组件挂载时设置，确保时间准确性
2. 如果没有传递开始时间，会根据当前时间和持续时长进行计算
3. 消息格式转换时，机器人消息角色为 "bot"，用户消息角色为 "user"
4. 所有时间相关的计算都使用秒级精度

## 🔗 相关文件

- `src/views/aiQualityInspection/myTask/exercise/components/RecordingControl.vue`
- `src/views/aiQualityInspection/myTask/exercise/index.vue`
- `src/api/AIQualityInspection/taskManagement.ts` 