# 评估抽屉消息时间戳显示功能实现

## 📅 当前时间

**2025年05月26日 星期一 18:40分** (CST UTC+8)

## 🎯 需求描述

在AI质量检查系统的评估抽屉中，为通话记录的每条对话添加时间戳显示功能，让用户可以清楚地看到每条消息的开始时间。

## 🔧 实现方案

### 1. 模板结构调整

**文件**: `views/aiQualityInspection/myTask/evaluation/index.vue`

#### 修改消息列表结构
```vue
<!-- 原始结构 -->
<div
  v-for="(message, index) in conversationData?.messages"
  :key="index"
  class="flex"
  :class="message.role === 'bot' ? 'justify-start' : 'justify-end'"
>

<!-- 修改后结构 -->
<div
  v-for="(message, index) in conversationData?.messages"
  :key="index"
  class="flex flex-col gap-5px"
>
  <!-- 时间戳显示 -->
  <div
    v-if="message.start"
    class="text-center text-gray-400 text-12px"
  >
    {{ formatTimestamp(message.start) }}
  </div>

  <!-- 消息内容 -->
  <div
    class="flex"
    :class="message.role === 'bot' ? 'justify-start' : 'justify-end'"
  >
    <!-- 原有的消息内容结构 -->
  </div>
</div>
```

### 2. 时间格式化函数

#### 添加formatTimestamp函数
```typescript
/**
 * 格式化时间戳为可读时间
 * @param timestamp 秒级时间戳字符串
 * @returns 格式化的时间字符串
 */
function formatTimestamp(timestamp?: string): string {
  if (!timestamp) return "";

  const date = new Date(Number(timestamp) * 1000);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const messageDate = new Date(
    date.getFullYear(),
    date.getMonth(),
    date.getDate()
  );

  // 判断是否是今天
  if (messageDate.getTime() === today.getTime()) {
    // 今天只显示时间
    return date.toLocaleTimeString("zh-CN", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    });
  } else {
    // 其他日期显示完整时间
    return date.toLocaleString("zh-CN", {
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit"
    });
  }
}
```

## 🎨 用户体验设计

### 1. 时间显示规则
- **今天的消息**: 只显示时间（如：14:30:25）
- **其他日期**: 显示日期和时间（如：05-25 14:30:25）
- **无时间戳**: 不显示时间信息

### 2. 样式设计
- **位置**: 消息上方居中显示
- **颜色**: 浅灰色（text-gray-400）
- **字体**: 12px小字体
- **间距**: 与消息内容保持5px间距

### 3. 条件显示
- 只有当消息包含`start`字段时才显示
- 确保向后兼容，不影响没有时间戳的消息

## 🔍 技术细节

### 数据源
- 使用`WorkerTrainingTaskMessage`接口中的`start`字段
- `start`字段为字符串类型的秒级时间戳
- 通过`getWorkerTrainingTaskCourseInfo`接口获取

### 时间戳处理
1. **输入格式**: 字符串类型的秒级时间戳
2. **转换**: 使用`Number(timestamp) * 1000`转换为毫秒级
3. **本地化**: 使用中文格式显示时间

### 布局调整
- 消息容器改为垂直布局（flex-col）
- 时间戳居中显示在消息上方
- 保持原有的消息对齐方式（左对齐/右对齐）

## 📋 显示效果

### 机器人消息时间戳
- 显示TTS开始生成的时间
- 便于分析AI响应的时间间隔

### 用户消息时间戳
- 显示录音开始的准确时间
- 帮助用户了解对话的时间节点

### 时间格式示例
```
14:30:25        (今天的消息)
05-25 14:30:25  (其他日期的消息)
```

## 🎯 实现效果

1. **清晰性**: 每条消息都有明确的时间标识
2. **一致性**: 与练习模块的时间戳显示保持一致
3. **可读性**: 智能的日期显示规则，减少冗余信息
4. **兼容性**: 不影响现有的消息显示和音频播放功能

## 📝 相关文件

- `views/aiQualityInspection/myTask/evaluation/index.vue` - 评估抽屉组件，添加时间戳显示

## 🔗 关联功能

- [消息时间戳记录功能](./message-timestamp-implementation.md) - 时间戳数据来源
- [练习模块时间戳显示](./message-timestamp-display.md) - 相同的显示逻辑

## 🔄 后续优化

1. 可考虑添加相对时间显示（如：2分钟前）
2. 可添加时间戳的点击交互功能
3. 可优化时间显示的国际化支持
4. 可添加时间戳的显示/隐藏切换功能 