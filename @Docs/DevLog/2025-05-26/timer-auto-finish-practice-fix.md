# 修复计时器到达最大持续时间时未调用完成练习逻辑

## 📅 当前准确时间

### 🇨🇳 中国时间（本地时间）
- **2025年05月26日 星期一 19:01分**
- **时区：CST (UTC+8)**

### 🌍 世界标准时间
- **UTC时间：2025-05-26T11:01:24.177Z**
- **时间戳：1748257284178**

### 📊 时间来源验证
- 本地系统时间：2025年 5月26日 星期一 19时01分24秒 CST
- JavaScript时间：2025年05月26日星期一 19:01:24
- 一致性：一致

## 🐛 问题描述

用户反馈：当计时到达最大持续时间时，并未调用成功完成练习的逻辑。

### 问题分析

通过代码分析发现，在 `handleAutoFinishPractice` 函数中存在逻辑不一致的问题：

1. **手动完成练习**（通过 `RecordingControl` 组件）：
   - ✅ 调用 `finishWorkerTrainingTaskCourse` API
   - ✅ 然后传递数据给父组件

2. **自动完成练习**（时间到达上限）：
   - ❌ **只是**传递数据给父组件
   - ❌ **没有**调用 `finishWorkerTrainingTaskCourse` API

这导致自动完成练习时，虽然前端显示完成了，但后端没有记录练习结果。

## 🔧 解决方案

### 1. 添加API调用函数

在 `index.vue` 中添加 `callFinishPracticeAPI` 函数，与 `RecordingControl` 组件中的逻辑保持一致：

```typescript
/**
 * 调用完成练习接口
 * @description 与RecordingControl组件中的逻辑保持一致
 */
async function callFinishPracticeAPI() {
  if (!exerciseState || !timerState || !props.courseInfo) {
    throw new Error("练习状态或课程信息不完整");
  }

  // 获取用户信息
  const userStore = useUserStoreHook();
  const userMsg = userStore.userMsg;

  // 获取当前时间戳（秒级）
  const currentTimestamp = Math.floor(Date.now() / 1000);

  // 计算练习开始和结束时间
  const practiceStartTimeValue = practiceStartTime.value || currentTimestamp - timerState.timer.value;
  const practiceEndTime = currentTimestamp;
  const practiceDuration = timerState.timer.value;

  // 转换消息格式，使用正确的时间戳
  const conversationMessages = chatState.messages.value.map(msg => ({
    role: msg.isBot ? "bot" : "user", // 角色：机器人或用户
    content: msg.content, // 消息内容
    start: (msg.startTime || practiceStartTimeValue).toString(), // 消息开始时间
    key: msg.index // 消息唯一标识
  }));

  // 构建请求参数 - 根据新的接口结构
  const requestData: WorkerTrainingTaskCourseUpdateRequest = {
    workerTrainingTaskId: props.courseInfo.workerTrainingTaskId || "0",
    trainingTaskCourseId: props.courseInfo.trainingTaskCourseId || "0",
    conversation: {
      workerId: userMsg.id || 0, // 工作人员ID
      trainingTaskCourseId: props.courseInfo.trainingTaskCourseId || "0", // 训练任务课程ID
      duration: practiceDuration, // 实际练习时长（秒）
      messages: conversationMessages, // 实际对话消息
      begin: practiceStartTimeValue.toString(), // 开始时间（秒级时间戳）
      end: practiceEndTime.toString() // 结束时间（秒级时间戳）
    }
  };

  console.log("自动完成练习请求参数:", requestData);

  // 调用完成练习接口
  await finishWorkerTrainingTaskCourse(requestData);
}
```

### 2. 修改自动完成练习逻辑

在 `handleAutoFinishPractice` 函数中，当有对话时，先调用API再处理后续逻辑：

```typescript
// 有对话，正常完成练习
console.log("练习时间到达上限但有对话，自动完成练习");

ElMessage({
  message: `练习时间已达到上限（${exerciseState.getMaxDuration()}分钟），系统自动完成练习`,
  type: "info",
  duration: 3000
});

try {
  // 调用完成练习接口 - 与手动完成练习保持一致
  await callFinishPracticeAPI();

  // 停止计时器
  timerState.stopTimer();

  // 清理资源
  audioRecorderState.cleanup();

  if (chatAreaRef.value) {
    chatAreaRef.value.cleanupAudioPlayer();
  }

  // 构造完成数据，包含评估抽屉标识
  const finishData = {
    shouldOpenEvaluation: true, // 自动打开评估抽屉
    courseInfo: props.courseInfo, // 传递课程信息
    messages: chatState.messages.value,
    duration: timerState.timer.value,
    practiceStartTime: practiceStartTime.value,
    isAutoFinish: true, // 标识为自动结束
    roleInfo: exerciseState.roleInfo.value
  };

  // 关闭抽屉并通知父组件
  drawerVisible.value = false;

  // 传递完整的数据给父组件，包括课程信息和评估抽屉标识
  emit("finish", finishData);

  console.log("自动完成练习成功，数据已传递给父组件", finishData);
} catch (error) {
  console.error("自动完成练习失败:", error);
  ElMessage.error("自动完成练习失败，请重试");
  
  // 即使接口调用失败，也要停止计时器和清理资源
  timerState.stopTimer();
  audioRecorderState.cleanup();
  
  if (chatAreaRef.value) {
    chatAreaRef.value.cleanupAudioPlayer();
  }
}
```

### 3. 添加必要的导入

```typescript
import { 
  WorkerTrainingTaskCourseInstance,
  WorkerTrainingTaskCourseUpdateRequest,
  finishWorkerTrainingTaskCourse
} from "/@/api/AIQualityInspection/taskManagement";
import { useUserStoreHook } from "/@/store/modules/user";
```

## ✅ 修复效果

### 修复前
- ❌ 自动完成练习时不调用API
- ❌ 后端没有练习记录
- ❌ 数据不一致

### 修复后
- ✅ 自动完成练习时调用API
- ✅ 后端正确记录练习结果
- ✅ 手动和自动完成逻辑一致
- ✅ 包含完整的错误处理

## 🔍 验证要点

1. **API调用一致性**：自动完成和手动完成都调用相同的API
2. **数据格式一致性**：传递给API的数据格式完全一致
3. **错误处理**：即使API调用失败，也要正确清理资源
4. **用户体验**：显示适当的成功/失败消息

## 📝 相关文件

- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/index.vue` - 主要修改文件
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/components/RecordingControl.vue` - 参考逻辑
- `apps/telesale-web/src/api/AIQualityInspection/taskManagement.ts` - API定义

## 🔮 后续优化建议

1. **代码复用**：可以考虑将完成练习的逻辑抽取为一个共用的hook
2. **状态管理**：可以在练习状态中统一管理完成练习的逻辑
3. **测试覆盖**：添加自动完成练习的单元测试
4. **日志记录**：增强日志记录，便于问题排查 