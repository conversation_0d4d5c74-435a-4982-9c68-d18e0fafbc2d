# 修复课程练习计时器最大持续时间问题

## 📅 当前准确时间

**2025年05月28日 星期三 15:03分** (CST UTC+8)
**时间戳：1748415800519**

### 验证结果
- 本地系统时间：2025年 5月28日 星期三 15时03分15秒 CST
- JavaScript时间：2025年05月28日星期三 15:03:20
- 一致性：一致

## 🐛 问题描述

课程练习的计时器有时候会出现超过最大持续时间的情况，经过分析发现是在更换任务后没有及时更新计时器的最大持续时间参数。

### 问题根因

1. **计时器初始化时机问题**：`useTimer` 在组件初始化时就被调用，此时传入的 `maxDurationMinutes` 是固定的，即使后续 `courseInfo` 发生变化，计时器的最大持续时间参数也不会更新。

2. **courseInfo变化时没有重新初始化计时器**：在 `watch` 监听器中，当 `courseInfo` 发生变化时，只是清理了状态，但没有重新初始化 `exerciseState` 和 `timerState`，导致计时器仍然使用旧的最大持续时间参数。

## 🔧 解决方案

### 1. 重构状态管理

**文件**: `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/index.vue`

#### 修改前的问题代码
```typescript
// 使用hooks - 传递响应式的 courseInfo 引用
const exerciseState = courseInfoRef.value ? useExercise(courseInfoRef) : null;
const timerState = exerciseState
  ? useTimer(exerciseState.getMaxDuration())
  : null;
```

#### 修改后的解决方案
```typescript
// 使用响应式状态管理hooks
let exerciseState: any = null;
let timerState: any = null;

// 创建响应式引用以便模板访问
const exerciseStateRef = ref<any>(null);
const timerStateRef = ref<any>(null);

/**
 * 初始化或重新初始化练习相关状态
 * @param courseInfo 课程信息
 */
function initializeExerciseStates(courseInfo: WorkerTrainingTaskCourseInstance | undefined) {
  console.log("初始化练习状态", {
    hasCourseInfo: !!courseInfo,
    courseId: courseInfo?.trainingTaskCourseId,
    maxDuration: courseInfo?.course?.course?.maxDuration
  });

  // 清理旧的状态
  if (timerState) {
    timerState.stopTimer();
  }

  // 重置状态
  exerciseState = null;
  timerState = null;
  exerciseStateRef.value = null;
  timerStateRef.value = null;

  // 如果有课程信息，创建新的状态
  if (courseInfo) {
    try {
      exerciseState = useExercise(courseInfoRef);
      const maxDuration = exerciseState.getMaxDuration();
      timerState = useTimer(maxDuration);
      
      // 设置计时器回调 - 确保计时器到达上限时执行自动完成逻辑
      timerState.setTimeUpCallback(handleAutoFinishPractice);
      
      // 更新响应式引用
      exerciseStateRef.value = exerciseState;
      timerStateRef.value = timerState;
      
      console.log("练习状态初始化完成", {
        maxDuration,
        hasExerciseState: !!exerciseState,
        hasTimerState: !!timerState,
        hasTimeUpCallback: true
      });
    } catch (error) {
      console.error("初始化练习状态失败:", error);
      exerciseState = null;
      timerState = null;
      exerciseStateRef.value = null;
      timerStateRef.value = null;
    }
  }
}

// 初始化状态
initializeExerciseStates(courseInfoRef.value);
```

### 2. 修复计时器回调设置问题

#### 问题描述
在重新初始化状态时，虽然创建了新的 `timerState`，但没有重新设置计时器的时间到达回调函数，导致计时器到达最大时间时不会执行自动完成练习的逻辑。

#### 解决方案
在 `initializeExerciseStates` 函数中，创建新的计时器状态后立即设置回调：

```typescript
// 设置计时器回调 - 确保计时器到达上限时执行自动完成逻辑
timerState.setTimeUpCallback(handleAutoFinishPractice);

// 验证回调是否设置成功
console.log("计时器回调设置验证", {
  hasCallback: typeof timerState.setTimeUpCallback === 'function',
  maxDurationSeconds: timerState.maxDurationSeconds,
  callbackFunction: handleAutoFinishPractice.name
});
```

### 3. 增强courseInfo变化监听

#### 修改前
```typescript
// 监听 courseInfo 变化，重新初始化相关状态
watch(
  courseInfoRef,
  (newCourseInfo, oldCourseInfo) => {
    // 如果课程ID发生变化，清理当前状态
    if (
      oldCourseInfo?.trainingTaskCourseId !==
      newCourseInfo?.trainingTaskCourseId
    ) {
      console.log("课程ID发生变化，清理状态");
      // 只清理状态，没有重新初始化
    }
  },
  { deep: true }
);
```

#### 修改后
```typescript
// 监听 courseInfo 变化，重新初始化相关状态
watch(
  courseInfoRef,
  (newCourseInfo, oldCourseInfo) => {
    console.log("courseInfo 发生变化:", {
      oldCourseId: oldCourseInfo?.trainingTaskCourseId,
      newCourseId: newCourseInfo?.trainingTaskCourseId,
      oldMaxDuration: oldCourseInfo?.course?.course?.maxDuration,
      newMaxDuration: newCourseInfo?.course?.course?.maxDuration,
      hasChange:
        oldCourseInfo?.trainingTaskCourseId !==
        newCourseInfo?.trainingTaskCourseId
    });

    // 如果课程ID发生变化，清理当前状态并重新初始化
    if (
      oldCourseInfo?.trainingTaskCourseId !==
      newCourseInfo?.trainingTaskCourseId
    ) {
      console.log("课程ID发生变化，清理状态并重新初始化");

      // 清理聊天数据（包括TTS状态）
      chatState.clearChat();

      // 清理录音状态
      audioRecorderState.cleanup();

      // 清理音频播放资源
      if (chatAreaRef.value) {
        chatAreaRef.value.cleanupAudioPlayer();
      }

      // 重置练习开始时间
      practiceStartTime.value = 0;

      // 重新初始化练习状态（包括计时器）
      initializeExerciseStates(newCourseInfo);

      console.log("课程切换状态清理和重新初始化完成");
    }
  },
  { deep: true }
);
```

### 4. 更新模板引用

将模板中的状态引用从直接访问改为使用响应式引用：

```vue
<!-- 修改前 -->
<span>{{ timerState?.formattedTime() || "00:00:00" }}</span>
:role-name="exerciseState?.roleInfo.value?.roleName || '客户'"
:practice-duration="timerState?.timer.value || 0"

<!-- 修改后 -->
<span>{{ timerStateRef?.formattedTime() || "00:00:00" }}</span>
:role-name="exerciseStateRef?.roleInfo.value?.roleName || '客户'"
:practice-duration="timerStateRef?.timer.value || 0"
```

## 🎯 修复效果

1. **正确的最大持续时间**：每次切换课程时，计时器都会使用新课程的最大持续时间参数
2. **状态同步**：练习状态和计时器状态在课程切换时会完全重新初始化
3. **资源清理**：旧的计时器会被正确停止和清理
4. **计时器回调正常工作**：计时器到达最大时间时会正确执行自动完成练习逻辑
   - 如果存在对话：自动完成练习并调用完成接口
   - 如果不存在对话：自动放弃练习
5. **日志追踪**：增加了详细的日志输出，便于调试和监控

## 🔍 验证方法

1. **切换不同最大持续时间的课程**：验证计时器是否使用正确的最大时间
2. **观察控制台日志**：确认状态初始化和清理过程，以及计时器回调设置
3. **测试自动结束功能**：
   - 验证计时器到达正确的最大时间后自动结束
   - 验证有对话时自动完成练习
   - 验证无对话时自动放弃练习
4. **检查回调设置日志**：确认计时器回调函数正确设置

## 📝 相关文件

- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/index.vue` - 主练习组件
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useTimer.ts` - 计时器Hook
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useExercise.ts` - 练习状态Hook

## 🔄 后续优化

1. 可以考虑将状态管理进一步封装到单独的Hook中
2. 可以添加更多的错误处理和边界情况处理
3. 可以优化类型定义，避免使用any类型 