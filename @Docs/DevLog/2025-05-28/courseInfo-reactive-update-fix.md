# courseInfo 响应式更新问题修复

## 📅 时间信息
- **创建时间**: 2025年5月28日 星期三 14:13:42 CST
- **问题发现**: 在任务详情中放弃练习后重新开始另一个练习时，trainingTaskCourseId 参数未更新

## 🐛 问题描述

### 问题现象
在任务详情 `TaskCourseDetailDialog.vue` 中：
1. 开始练习课程A
2. 在 `index.vue` 练习组件中放弃练习
3. 重新在任务详情中开始练习课程B
4. 发现 `useChat.ts` 中的 `trainingTaskCourseId` 参数仍然是课程A的ID，没有更新为课程B的ID

### 根本原因
`useExercise` hook 在组件初始化时接收的是静态的 `props.courseInfo`，当 props 发生变化时（比如切换到另一个课程），hook 内部的 `courseInfo` 并没有响应式更新。

## 🔧 解决方案

### 1. 修改 useExercise Hook

**文件**: `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useExercise.ts`

#### 主要变更
- 导入 `toRef` 和 `Ref` 类型
- 修改函数签名支持响应式参数
- 将 `courseInfo` 转换为响应式引用
- 更新所有使用 `courseInfo` 的地方使用 `courseInfoRef.value`

```typescript
// 修改前
export function useExercise(courseInfo: WorkerTrainingTaskCourseInstance) {
  // 直接使用静态的 courseInfo
  const roleInfo = computed(() => {
    const course = courseInfo?.course?.course;
    // ...
  });
}

// 修改后
export function useExercise(courseInfo: WorkerTrainingTaskCourseInstance | Ref<WorkerTrainingTaskCourseInstance>) {
  // 将 courseInfo 转换为响应式引用
  const courseInfoRef = typeof courseInfo === 'object' && 'value' in courseInfo 
    ? courseInfo 
    : toRef(() => courseInfo);

  // 角色信息 - 从课程信息中获取（响应式）
  const roleInfo = computed(() => {
    const course = courseInfoRef.value?.course?.course;
    // ...
  });
}
```

### 2. 修改练习组件

**文件**: `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/index.vue`

#### 主要变更
- 导入 `toRef` 和 `watch`
- 创建响应式的 `courseInfoRef`
- 传递响应式引用给 `useExercise`
- 添加 `watch` 监听课程变化
- 更新所有对 `props.courseInfo` 的引用

```typescript
// 创建响应式引用
const courseInfoRef = toRef(props, 'courseInfo');

// 传递给 useExercise
const exerciseState = courseInfoRef.value ? useExercise(courseInfoRef) : null;

// 监听课程变化
watch(courseInfoRef, (newCourseInfo, oldCourseInfo) => {
  // 如果课程ID发生变化，清理当前状态
  if (oldCourseInfo?.trainingTaskCourseId !== newCourseInfo?.trainingTaskCourseId) {
    console.log("课程ID发生变化，清理状态");
    // 清理聊天数据、录音状态、音频播放资源等
  }
}, { deep: true });
```

#### 模板更新
```vue
<!-- 修改前 -->
<InfoPanel :course-info="props.courseInfo" />
<RecordingControl :course-info="props.courseInfo" />

<!-- 修改后 -->
<InfoPanel :course-info="courseInfoRef" />
<RecordingControl :course-info="courseInfoRef" />
```

## 🧪 测试验证

### 测试步骤
1. 打开任务详情弹窗
2. 开始练习课程A（记录 trainingTaskCourseId）
3. 在练习中放弃练习
4. 重新开始练习课程B
5. 验证 `useChat.ts` 中的 `trainingTaskCourseId` 是否正确更新为课程B的ID

### 预期结果
- 课程切换时，`trainingTaskCourseId` 参数应该正确更新
- 聊天状态、录音状态、音频播放状态应该被正确清理
- 新的练习应该使用正确的课程ID进行API调用

## 📝 技术要点

### Vue 3 响应式系统
- 使用 `toRef` 创建对 props 属性的响应式引用
- 使用 `watch` 监听响应式数据的变化
- 在 `computed` 中使用响应式引用确保计算属性能够正确更新

### Hook 设计模式
- Hook 应该支持响应式参数以适应动态数据变化
- 使用类型联合 `T | Ref<T>` 提供灵活的参数接受方式
- 内部统一转换为响应式引用进行处理

### 状态管理
- 当关键参数变化时，及时清理相关状态
- 避免状态污染和数据不一致问题
- 确保组件状态与 props 保持同步

## 🔍 相关文件

### 修改的文件
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useExercise.ts`
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/index.vue`

### 相关文件
- `apps/telesale-web/src/views/aiQualityInspection/myTask/components/TaskCourseDetailDialog.vue`
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useChat.ts`

## 💡 经验总结

1. **响应式设计**: 在设计 Hook 时，应该考虑参数可能的动态变化，提供响应式支持
2. **状态清理**: 当关键参数变化时，必须及时清理相关状态，避免数据污染
3. **类型安全**: 使用 TypeScript 的类型联合确保 API 的灵活性和类型安全
4. **调试友好**: 添加详细的 console.log 帮助调试和问题定位

## 🚀 后续优化

1. 考虑将状态清理逻辑抽象为独立的 Hook
2. 添加更完善的错误处理和边界情况处理
3. 考虑使用 `watchEffect` 简化响应式逻辑
4. 添加单元测试验证响应式更新的正确性 