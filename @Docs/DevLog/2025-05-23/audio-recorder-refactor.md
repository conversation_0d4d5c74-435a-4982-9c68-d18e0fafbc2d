# 音频录音Hook重构实现

## 📅 当前准确时间

### 🇨🇳 中国时间（本地时间）
- **2025年05月23日 星期五 17:00分**
- **时区：CST (UTC+8)**

## 🎯 重构背景

原有的 `useAudioRecorder.ts` 使用浏览器原生的语音识别API（SpeechRecognition），存在以下问题：
1. **兼容性问题**：不同浏览器支持程度不一致
2. **准确性限制**：浏览器原生识别准确性有限
3. **功能局限**：无法与后端语音识别服务集成
4. **架构不一致**：与项目其他部分使用WebSocket的架构不统一

## 🔧 重构方案

### 核心思路
参考提供的代码示例，将浏览器原生语音识别替换为WebSocket实时语音识别，与后端服务深度集成。

### 主要改进

#### 1. 技术架构变更
```typescript
// 原来：浏览器原生API
const speechRecognition = ref<any>(null);
const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

// 现在：WebSocket实时识别
const wsRef = ref<WebSocket | null>(null);
const streamRef = ref<MediaStream | null>(null);
const audioContextRef = ref<AudioContext | null>(null);
```

#### 2. 音频处理优化
```typescript
// 音频数据实时处理和传输
scriptProcessorNode.onaudioprocess = event => {
  const float32Data = event.inputBuffer.getChannelData(0);
  const int16Data = new Int16Array(float32Data.length);
  for (let i = 0; i < float32Data.length; i++) {
    int16Data[i] = Math.min(1, float32Data[i]) * 0x7fff;
  }
  if (wsRef.value && wsRef.value.readyState === WebSocket.OPEN) {
    wsRef.value.send(int16Data);
  }
};
```

#### 3. 实时识别结果处理
```typescript
wsRef.value.onmessage = event => {
  const dataRaw = event.data.trim();
  try {
    const data = JSON.parse(dataRaw);
    let content = "";
    if (Array.isArray(data)) {
      data.forEach(item => {
        if (item.content) content += item.content;
      });
    } else if (data.content) {
      content = data.content;
    }
    
    if (content.length > 0) {
      recordingText.value = content;
      notifyStateChange();
    }
  } catch (err) {
    console.error("解析WebSocket数据失败:", err);
  }
};
```

### 关键特性

#### 1. 实时性能
- WebSocket连接确保低延迟通信
- 音频数据实时传输和处理
- 识别结果即时更新显示

#### 2. 稳定性保障
- 完整的WebSocket连接管理
- 音频流和上下文的正确清理
- 详细的错误处理和用户提示

#### 3. 用户体验
- 智能的录音时长检测
- 友好的错误提示信息
- 状态变化的实时通知

## 📁 修改文件

### 主要文件
- `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/hooks/useAudioRecorder.ts`

### 修改内容
1. ✅ 移除浏览器原生语音识别API相关代码
2. ✅ 新增WebSocket连接管理
3. ✅ 新增音频上下文和流处理
4. ✅ 重构录音状态管理
5. ✅ 优化错误处理和用户提示
6. ✅ 简化API接口，保持向后兼容

## 🔍 技术细节

### WebSocket连接管理
```typescript
// 创建WebSocket连接
const wsUrl = baseURL.robot.replace("https://", "wss://");
const websocketUrl = `${wsUrl}/admin/audio/asr?msgid=${currentMsgId}`;
wsRef.value = new WebSocket(websocketUrl);

// 连接状态处理
wsRef.value.onopen = () => console.log("WebSocket连接成功");
wsRef.value.onclose = () => console.log("WebSocket连接已关闭");
wsRef.value.onerror = error => {
  console.error("WebSocket连接错误:", error);
  ElMessageBox.alert("语音识别服务连接失败，请检查网络或重试", "错误");
  stopRecording();
};
```

### 音频处理流程
1. **获取麦克风权限**：配置音频参数（16kHz采样率，单声道）
2. **创建音频上下文**：使用Web Audio API处理音频数据
3. **实时数据转换**：Float32转Int16格式
4. **WebSocket传输**：实时发送音频数据到后端

### 状态管理优化
```typescript
function notifyStateChange() {
  const state: RecordingState = {
    isRecording: isRecording.value,
    recordingTime: recordingTime.value,
    recordingText: recordingText.value
  };
  onRecordingStateChange.value(state);
}
```

### 智能结果处理
- 录音时间检测：少于1秒提示时间过短
- 内容验证：过滤无效识别结果
- 延迟处理：确保WebSocket数据完整传输

## 🎯 实际效果

### 性能提升
1. **识别准确性**：使用后端专业语音识别服务，准确性显著提升
2. **实时性**：WebSocket连接确保低延迟实时识别
3. **稳定性**：统一的错误处理和资源管理

### 用户体验改进
1. **即时反馈**：识别结果实时显示
2. **智能提示**：根据录音时长和识别结果提供相应提示
3. **流畅操作**：录音开始/停止响应迅速

### 架构统一
1. **技术栈一致**：与项目其他WebSocket服务保持一致
2. **维护便利**：统一的连接管理和错误处理模式
3. **扩展性强**：便于后续功能扩展和优化

## 🔄 向后兼容

### API接口保持不变
```typescript
return {
  // 状态（保持原有接口）
  isRecording,
  recordingTime,
  recordingText,

  // 方法（保持原有接口）
  startRecording,
  stopRecording,
  cleanup,
  setCallbacks,
  checkRecordingPermission,
  resetRecordingState
};
```

### 移除的功能
- `isRecognitionSupported`：不再需要检查浏览器支持
- 浏览器原生语音识别相关的所有代码

## 📝 注意事项

### 网络依赖
- 需要稳定的网络连接
- WebSocket连接失败时有完整的错误处理

### 权限管理
- 仍需要麦克风权限
- 权限检查功能保持不变

### 资源清理
- 音频流、上下文、WebSocket连接的完整清理
- 组件卸载时自动清理所有资源

## 🚀 后续优化

1. **连接重试机制**：WebSocket断线自动重连
2. **音频质量优化**：支持更多音频参数配置
3. **缓存机制**：识别结果本地缓存
4. **性能监控**：添加识别准确性和延迟监控

## 📊 对比总结

| 特性 | 原实现（浏览器API） | 新实现（WebSocket） |
|------|-------------------|-------------------|
| 兼容性 | 浏览器依赖 | 统一支持 |
| 准确性 | 有限 | 专业服务 |
| 实时性 | 一般 | 优秀 |
| 集成度 | 独立 | 深度集成 |
| 维护性 | 复杂 | 统一 |
| 扩展性 | 受限 | 灵活 |

通过这次重构，音频录音功能不仅在技术架构上更加统一，在用户体验和功能稳定性方面也有了显著提升。 