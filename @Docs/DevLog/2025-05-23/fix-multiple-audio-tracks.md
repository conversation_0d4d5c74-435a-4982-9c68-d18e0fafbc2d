# 修复多个音轨同时播放问题开发日志

**日期**: 2025-01-27  
**问题**: 修复TTS流式播放中多个音轨同时播放的问题  
**状态**: 已完成

## 问题描述

在TTS流式播放功能中，存在多个音轨同时播放的问题，导致音频重叠和用户体验不佳。主要表现为：

1. **音频重叠**: 新的音频开始播放时，之前的音频没有正确停止
2. **状态混乱**: 播放状态管理不一致，导致多个播放器同时激活
3. **资源泄漏**: 音频资源没有正确释放，造成内存占用

## 问题分析

### 原因分析

1. **缺乏全局播放器管理**: 每个组件都有自己的音频播放逻辑，没有统一管理
2. **状态检查不完整**: 在开始新播放前没有完全停止所有现有播放
3. **异步操作竞态**: 音频解码等异步操作可能导致状态不一致

### 参考实现

参考 `chat.tsx` 中的实现，发现其使用了全局播放器管理机制：

```typescript
// 全局播放器状态
let isGlobalPlayerActive = false;
let currentPlayingSource: AudioBufferSourceNode | null = null;

// 统一的播放器函数
function player(audioContext, playBuffer, index) {
  // 确保同一时间只有一个音频在播放
}
```

## 解决方案

### 1. 创建全局音频播放器管理器

新建 `audioPlayer.ts` 文件，实现全局音频播放管理：

```typescript
// 全局状态管理
let globalAudioContext: AudioContext | null = null;
let currentPlayingSource: AudioBufferSourceNode | null = null;
let isGlobalPlayerActive = false;

// 核心函数
export function playerStop(): void
export function isPlaying(): boolean
export function replay(audioBuffer: ArrayBuffer, onEnded?: () => void): void
export function player(audioContext: AudioContext, playBuffer: AudioBuffer[], index: number): void
```

### 2. 重构ChatArea组件

#### 移除本地音频管理
- 删除 `audioContext`、`currentAudioSource` 等本地状态
- 删除 `playNextAudioFromQueue` 等本地播放逻辑
- 简化状态管理，只保留必要的UI状态

#### 使用全局播放器
```typescript
// 播放音频
function playAudio(message) {
  playerStop(); // 先停止所有播放
  replay(audioBuffer, onEndedCallback); // 使用全局播放器
}

// 流式播放
function startStreamAudio(messageId) {
  playerStop(); // 先停止所有播放
  startStreamPlayer(onEndedCallback); // 启动流式播放器
}
```

### 3. 优化播放流程

#### 播放前检查
- 每次开始新播放前，强制停止所有现有播放
- 使用 `isPlaying()` 检查全局播放状态
- 确保状态一致性

#### 异步操作保护
```typescript
// 在异步操作后再次检查状态
const audioBuffer = await audioContext.decodeAudioData(chunk);
if (!isPlaying()) {
  console.log("播放被取消，丢弃音频块");
  return;
}
```

## 技术实现

### 1. 全局播放器核心逻辑

```typescript
export function playerStop(): void {
  if (currentPlayingSource) {
    try {
      currentPlayingSource.stop();
      currentPlayingSource.disconnect();
    } catch (error) {
      console.warn("停止音频源时出错:", error);
    }
    currentPlayingSource = null;
  }
  isGlobalPlayerActive = false;
}

export function replay(audioBuffer: ArrayBuffer, onEnded?: () => void): void {
  playerStop(); // 先停止当前播放
  
  const audioContext = initGlobalAudioContext();
  audioContext.decodeAudioData(audioBuffer.slice(0))
    .then((decodedBuffer) => {
      if (isGlobalPlayerActive) return; // 防止竞态条件
      
      isGlobalPlayerActive = true;
      const source = audioContext.createBufferSource();
      source.buffer = decodedBuffer;
      source.connect(audioContext.destination);
      
      source.onended = () => {
        isGlobalPlayerActive = false;
        currentPlayingSource = null;
        if (onEnded) onEnded();
      };
      
      currentPlayingSource = source;
      source.start();
    });
}
```

### 2. 流式播放优化

```typescript
export function player(
  audioContext: AudioContext, 
  playBuffer: AudioBuffer[], 
  index: number
): void {
  if (index >= playBuffer.length || !isGlobalPlayerActive) {
    // 播放完成或被停止
    return;
  }
  
  const source = audioContext.createBufferSource();
  source.buffer = playBuffer[index];
  source.connect(audioContext.destination);
  
  source.onended = () => {
    // 播放下一个音频块
    setTimeout(() => {
      player(audioContext, playBuffer, index + 1);
    }, 50);
  };
  
  currentPlayingSource = source;
  source.start();
}
```

### 3. 组件集成

```typescript
// ChatArea.vue 中的修改
import { 
  playerStop, 
  isPlaying, 
  replay, 
  player,
  startStreamPlayer,
  initGlobalAudioContext,
  cleanupGlobalAudio
} from "../utils/audioPlayer";

function toggleAudio(message) {
  if (playingMessage.value?.index === message.index) {
    playerStop();
    playingMessage.value = null;
    emit("audioPlayStateChange", false);
  } else {
    if (isPlaying()) playerStop();
    playAudio(message);
  }
}
```

## 测试验证

### 1. 功能测试
- ✅ 单个音频播放正常
- ✅ 切换音频时正确停止前一个
- ✅ 流式播放连续性保持
- ✅ 播放状态同步正确

### 2. 边界测试
- ✅ 快速连续点击播放按钮
- ✅ 在音频解码过程中切换播放
- ✅ 网络异常情况下的播放控制
- ✅ 组件卸载时的资源清理

### 3. 性能测试
- ✅ 内存使用稳定，无泄漏
- ✅ CPU占用正常
- ✅ 音频质量保持

## 优化效果

### 1. 问题解决
- **音频重叠**: 完全消除，确保同一时间只有一个音频播放
- **状态一致**: 全局状态管理确保播放状态的一致性
- **资源管理**: 正确的资源清理，避免内存泄漏

### 2. 用户体验提升
- **响应性**: 播放控制更加及时和准确
- **稳定性**: 避免音频冲突和异常
- **流畅性**: 保持流式播放的连续性

### 3. 代码质量
- **可维护性**: 统一的播放器管理，代码更清晰
- **可扩展性**: 全局播放器易于扩展新功能
- **可测试性**: 独立的播放器模块便于单元测试

## 文件变更记录

### 新增文件
1. `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/utils/audioPlayer.ts`
   - 全局音频播放器管理器
   - 统一的播放控制逻辑
   - 流式播放支持

### 修改文件
1. `apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/components/ChatArea.vue`
   - 移除本地音频管理逻辑
   - 集成全局播放器
   - 简化状态管理
   - 优化播放控制流程

## 后续优化建议

### 1. 功能增强
- 添加音量控制
- 支持播放进度显示
- 实现播放速度调节

### 2. 性能优化
- 音频预加载策略
- 智能缓存管理
- 网络状况自适应

### 3. 用户体验
- 播放动画效果
- 键盘快捷键支持
- 无障碍访问优化

## 总结

本次修复成功解决了多个音轨同时播放的问题，通过引入全局音频播放器管理机制，确保了：

1. **唯一性**: 同一时间只有一个音频在播放
2. **一致性**: 播放状态在全局范围内保持一致
3. **可靠性**: 完善的错误处理和资源管理
4. **兼容性**: 与现有流式播放功能完全兼容

该修复参考了成熟的实现方案，提升了系统的稳定性和用户体验，为后续的音频功能扩展奠定了坚实基础。 