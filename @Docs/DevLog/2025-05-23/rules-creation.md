# 规则创建开发日志

## 📅 当前准确时间

### 🇨🇳 中国时间（本地时间）
- **2025年05月23日 星期五 16:33分**
- **时区：CST (UTC+8)**

### 🌍 世界标准时间
- **UTC时间：2025-05-23T08:33:31.733Z**
- **时间戳：1747989211735**

### 📊 时间来源验证
- 本地系统时间：2025年 5月23日 星期五 16时33分20秒 CST
- JavaScript时间：2025年05月23日星期五 16:33:31
- 一致性：一致

### 🔍 时间精度说明
本地系统时间与JavaScript时间保持一致，时间精度可靠。

## 📝 今日开发内容

### 1. 创建Vue项目拆分重构规则

#### 文件位置
- `.cursor/rules/vue-refactor.mdc`

#### 规则内容
- **拆分原则**：文件行数超过500行需要拆分，主文件控制在400行以内
- **文件结构规范**：定义了标准的组件文件夹结构
- **Hook拆分规范**：详细说明了Hook的命名、职责划分和通信方式
- **代码规范**：TypeScript类型定义、JSDoc注释、生命周期管理
- **重构流程**：分析、设计、实施、优化四个阶段
- **检查清单**：Hook质量、组件质量、文档质量检查项
- **最佳实践**：渐进式重构、团队协作、持续改进

#### 规则特点
- 基于实际的Vue项目重构经验
- 遵循Vue 3 Composition API最佳实践
- 提供完整的重构指导流程
- 包含详细的代码示例和检查清单

### 2. 修改Git提交规范

#### 文件位置
- `.cursor/rules/git.mdc`

#### 主要修改
- **强调AI生成commit的规范要求**：添加重要提醒，确保AI生成commit时严格遵守规范
- **完善Type类型表格**：增加使用场景示例列，更清晰地说明每种类型的适用场景
- **增加Scope示例**：提供常用的scope范围示例
- **新增AI生成Commit规范章节**：
  - 严格格式要求
  - 内容要求
  - 示例格式（正确和错误对比）
  - 质量检查清单
- **添加Commit模板**：提供标准的commit信息模板

#### 规范要点
- 必须使用规定的Type类型，不得自创
- 必须使用中文描述subject
- 必须确保header长度不超过108个字符
- 提供详细的正确和错误示例对比

## 🎯 规则应用场景

### Vue拆分重构规则
- 当组件文件超过500行时自动提醒拆分
- 在进行代码重构时提供标准流程指导
- 确保Hook的设计和实现符合最佳实践
- 提供完整的重构检查清单

### Git提交规范
- AI生成commit信息时严格遵守格式
- 确保提交历史的一致性和可读性
- 提供标准的commit模板和示例
- 支持团队协作的版本控制规范

## 📊 规则优势

### 1. 实践导向
- 基于真实项目的重构经验
- 提供可操作的具体指导
- 包含详细的代码示例

### 2. 标准化
- 统一的文件结构规范
- 一致的命名约定
- 标准化的流程指导

### 3. 可维护性
- 清晰的职责划分
- 完整的文档要求
- 持续改进机制

### 4. 团队协作
- 统一的代码风格
- 标准的提交规范
- 完善的审查机制

## 🔮 后续计划

### 1. 规则完善
- 根据实际使用情况优化规则内容
- 收集团队反馈并持续改进
- 增加更多实际案例和示例

### 2. 工具集成
- 考虑集成到开发工具中
- 提供自动化检查工具
- 建立规则遵循度监控

### 3. 培训推广
- 组织团队培训
- 分享最佳实践
- 建立知识库

## 📋 相关文件

- [Vue拆分重构规则](.cursor/rules/vue-refactor.mdc)
- [Git提交规范](.cursor/rules/git.mdc)
- [项目重构案例](../../../apps/telesale-web/src/views/aiQualityInspection/myTask/exercise/README.md) 