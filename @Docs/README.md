# 项目文档索引

## 📅 当前准确时间

### 🇨🇳 中国时间（本地时间）
- **2025年05月28日 星期三 15:03分**
- **时区：CST (UTC+8)**

### 🌍 世界标准时间
- **UTC时间：2025-05-28T07:03:20.518Z**
- **时间戳：1748415800519**

### 验证结果
- 本地系统时间：2025年 5月28日 星期三 15时03分15秒 CST
- JavaScript时间：2025年05月28日星期三 15:03:20
- 一致性：一致

## 📁 文档结构说明

本项目采用按日期和功能分类的文档组织方式，确保文档的可查找性和可维护性。

### 文件夹结构

```
@Docs/
├── AskLog/                    # 提问日志（按日期组织）
│   └── 2025-05-23/
│       ├── README.md          # 当日提问日志索引
│       └── time-acquisition-rules.md
├── DevLog/                    # 开发日志（按日期组织）
│   ├── 2025-05-23/
│   │   ├── README.md          # 当日开发日志索引
│   │   ├── docs-structure-optimization.md
│   │   ├── spacebar-recording-feature.md
│   │   ├── tts-troubleshooting.md
│   │   ├── exercise-optimization.md
│   │   ├── tts-stream-optimization.md
│   │   └── fix-multiple-audio-tracks.md
│   └── 2025-05-28/
│       ├── README.md          # 当日开发日志索引
│       └── timer-max-duration-fix.md
├── Feature/                   # 功能规则文档（按功能组织）
│   ├── time-acquisition_rules.md
│   └── exercise-component_rules.md
└── README.md                  # 本文档
```

## 📋 文档分类说明

### 1. AskLog - 提问日志
记录与AI的交互过程，包括需求分析、问题解决方案等。

**命名规范**: `@Docs/AskLog/YYYY-MM-DD/topic-name.md`

**当前内容**:
- **2025-05-23**: 时间获取规则创建相关的提问记录

### 2. DevLog - 开发日志
记录开发过程中的技术实现、问题解决、功能优化等。

**命名规范**: `@Docs/DevLog/YYYY-MM-DD/feature-or-issue-name.md`

**当前内容**:
- **2025-05-23**: 文档结构优化、空格键录音功能、练习组件优化、TTS故障排除、TTS流式播放优化、多音轨问题修复
- **2025-05-28**: 课程练习计时器最大持续时间问题修复、移除录音时间限制

### 3. Feature - 功能规则文档
详细的功能设计文档，包含设计思路、实现细节、使用规范等。

**命名规范**: `@Docs/Feature/feature-name_rules.md`

**当前内容**:
- **time-acquisition_rules.md**: 时间获取功能规则
- **exercise-component_rules.md**: 练习组件功能规则

## 🔍 快速导航

### 按日期查找
- [2025-05-23 提问日志](./AskLog/2025-05-23/README.md) - 时间获取规则
- [2025-05-23 开发日志](./DevLog/2025-05-23/README.md) - 文档结构优化
- [2025-05-28 开发日志](./DevLog/2025-05-28/README.md) - 计时器问题修复

### 按功能查找
- [时间获取功能](./Feature/time-acquisition_rules.md) - 多源时间验证规则
- [练习组件功能](./Feature/exercise-component_rules.md) - AI对话练习组件

## 📝 文档编写规范

### 1. 时间标准
- 所有文档必须包含准确的时间信息
- 使用多源验证确保时间准确性
- 统一的时间显示格式

### 2. 文件命名
- 使用小写字母和连字符
- 避免使用空格和特殊字符
- 包含清晰的功能或主题描述

### 3. 内容结构
- 明确的标题层次
- 完整的功能描述
- 详细的技术实现
- 相关文件引用

## 🔧 维护指南

### 1. 新增文档
1. 确定文档类型（AskLog/DevLog/Feature）
2. 创建对应的日期文件夹（如需要）
3. 按照命名规范创建文档
4. 更新相应的README.md索引

### 2. 文档更新
1. 保持时间信息的准确性
2. 更新相关文件的引用链接
3. 维护索引文件的完整性

### 3. 定期整理
1. 检查文档的分类是否合理
2. 清理过时或重复的内容
3. 优化文档结构和导航

## 📊 统计信息

### 文档数量统计
- **提问日志**: 1个日期文件夹，1个文档
- **开发日志**: 2个日期文件夹，7个文档
- **功能规则**: 2个功能文档
- **总计**: 9个主要文档

### 最近更新
- **最后更新时间**: 2025-05-28 15:21
- **主要变更**: 修复课程练习计时器最大持续时间问题，移除录音60秒时间限制
- **技术改进**: 重构状态管理，增强courseInfo变化监听，优化录音功能用户体验

---

> 本文档结构于2025-05-23建立，旨在提供清晰的文档组织和查找机制。 