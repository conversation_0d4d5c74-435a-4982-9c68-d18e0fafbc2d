# 音频按钮禁用功能规则

## 📅 文档信息

**创建时间**: 2025年05月26日 星期一 11:40分 (CST UTC+8)  
**最后更新**: 2025年05月26日 星期一 11:40分 (CST UTC+8)  
**版本**: v1.0.0  
**状态**: ✅ 已实现

## 🎯 功能概述

### 核心目标
实现智能的音频按钮禁用策略，在特定状态下禁用相关按钮，防止用户进行无效操作，提升用户体验。

### 主要功能
1. **TTS等待时禁用所有播放按钮** - 当等待TTS接口返回时，禁用所有播放按钮和录音按钮
2. **流式播放时禁用暂停按钮** - 当前音频是TTS流式播放时，禁用当前音频的暂停按钮
3. **智能状态提示** - 为禁用的按钮提供清晰的状态说明

## 🏗️ 架构设计

### 状态管理架构
```
useChat.ts (核心状态管理)
├── isTtsWaiting: boolean          // TTS接口等待状态
├── currentTtsMessageId: string    // 当前等待TTS的消息ID
└── streamTtsState: {              // 流式播放状态
    isPlaying: boolean
    messageId: string | null
    isStreamPlaying: boolean       // 区分流式播放和缓存重播
}
```

### 组件交互架构
```
index.vue (主页面)
├── ChatArea.vue (对话区域)
│   ├── 播放按钮禁用逻辑
│   └── 状态提示显示
├── RecordingControl.vue (录音控制)
│   ├── 录音按钮禁用逻辑
│   └── TTS等待状态检查
└── hooks/
    ├── useChat.ts (状态管理)
    ├── useExercise.ts (练习状态)
    └── audioPlayer.ts (全局播放器)
```

## 📋 功能规则

### 1. TTS等待状态规则

#### 触发条件
- TTS请求发起时设置 `isTtsWaiting = true`
- 记录当前等待的消息ID到 `currentTtsMessageId`

#### 禁用范围
- ✅ 所有音频播放按钮
- ✅ 录音开始按钮
- ✅ 其他音频相关操作按钮

#### 状态清理
- TTS请求完成后设置 `isTtsWaiting = false`
- 清空 `currentTtsMessageId`
- 在 `finally` 块中确保状态清理

#### 用户提示
- 按钮提示文字：`"TTS生成中，请稍候..."`
- 状态指示：显示"TTS生成中..."文字

### 2. 流式播放状态规则

#### 触发条件
- 开始流式TTS播放时设置 `isStreamPlaying = true`
- 仅对当前播放的消息生效

#### 禁用范围
- ✅ 当前音频的暂停按钮
- ❌ 其他音频的播放按钮（保持可用）
- ❌ 录音按钮（如果TTS未等待）

#### 状态区分
- **流式播放** (`isStreamPlaying = true`): 禁用暂停按钮
- **缓存重播** (`isStreamPlaying = false`): 允许暂停操作

#### 用户提示
- 按钮提示文字：`"正在流式播放，无法暂停"`
- 状态指示：播放按钮显示为禁用状态

### 3. 状态优先级规则

#### 优先级顺序
1. **TTS等待状态** (最高优先级)
   - 禁用所有相关按钮
   - 显示TTS等待提示

2. **流式播放状态** (中等优先级)
   - 仅禁用当前音频暂停按钮
   - 其他按钮保持正常状态

3. **正常状态** (默认优先级)
   - 所有按钮正常可用
   - 根据其他业务逻辑判断

## 🔧 技术实现

### 核心状态管理

#### useChat.ts 状态定义
```typescript
/**
 * TTS接口等待状态
 */
const isTtsWaiting = ref(false)

/**
 * 当前等待TTS的消息ID
 */
const currentTtsMessageId = ref<string | null>(null)

/**
 * 流式播放状态
 */
const streamTtsState = ref<StreamTtsState>({
  isPlaying: false,
  messageId: null,
  isStreamPlaying: false
})

interface StreamTtsState {
  isPlaying: boolean
  messageId: string | null
  isStreamPlaying: boolean // 区分流式播放和缓存重播
}
```

#### 状态查询方法
```typescript
/**
 * 检查指定消息是否正在流式播放
 * @param messageId - 消息ID
 * @returns 是否正在流式播放
 */
function isMessageStreamPlaying(messageId: string): boolean {
  return streamTtsState.value.isPlaying && 
         streamTtsState.value.messageId === messageId &&
         streamTtsState.value.isStreamPlaying
}

/**
 * 检查是否应该禁用播放按钮
 * @param messageId - 消息ID
 * @returns 是否应该禁用
 */
function shouldDisablePlayButton(messageId: string): boolean {
  return isTtsWaiting.value || isMessageStreamPlaying(messageId)
}
```

### TTS生命周期管理

#### startStreamTTS 方法
```typescript
/**
 * 开始流式TTS播放
 * @param messageId - 消息ID
 * @param text - 要播放的文本
 */
async function startStreamTTS(messageId: string, text: string) {
  try {
    // 1. 设置等待状态
    isTtsWaiting.value = true
    currentTtsMessageId.value = messageId
    
    // 2. 设置流式播放状态
    streamTtsState.value = {
      isPlaying: true,
      messageId,
      isStreamPlaying: true
    }
    
    // 3. 执行TTS流式播放逻辑
    await performStreamTTS(text)
    
  } catch (error) {
    console.error('TTS播放失败:', error)
    // 错误处理逻辑
  } finally {
    // 4. 清理状态（确保执行）
    isTtsWaiting.value = false
    currentTtsMessageId.value = null
    
    if (streamTtsState.value.messageId === messageId) {
      streamTtsState.value.isStreamPlaying = false
    }
  }
}
```

### 组件集成

#### ChatArea.vue 集成
```vue
<template>
  <div class="chat-area">
    <button 
      :disabled="isPlayButtonDisabled(message.id)"
      :title="getPlayButtonTitle(message.id)"
      @click="handlePlayAudio(message.id)"
      class="play-button"
    >
      <i :class="getPlayButtonIcon(message.id)"></i>
      {{ getPlayButtonText(message.id) }}
    </button>
  </div>
</template>

<script setup lang="ts">
/**
 * 检查播放按钮是否应该禁用
 */
function isPlayButtonDisabled(messageId: string): boolean {
  return chatStore.shouldDisablePlayButton(messageId)
}

/**
 * 获取播放按钮提示文字
 */
function getPlayButtonTitle(messageId: string): string {
  if (chatStore.isTtsWaiting) {
    return 'TTS生成中，请稍候...'
  }
  if (chatStore.isMessageStreamPlaying(messageId)) {
    return '正在流式播放，无法暂停'
  }
  return '播放音频'
}
</script>
```

#### RecordingControl.vue 集成
```vue
<template>
  <div class="recording-control">
    <button 
      :disabled="!canStartRecording"
      :title="recordingButtonTitle"
      @click="handleStartRecording"
      class="recording-button"
    >
      <i class="recording-icon"></i>
      {{ recordingButtonText }}
    </button>
  </div>
</template>

<script setup lang="ts">
interface Props {
  isTtsWaiting?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isTtsWaiting: false
})

/**
 * 计算是否可以开始录音
 */
const canStartRecording = computed(() => {
  return exerciseStore.canStartRecording() && !props.isTtsWaiting
})

/**
 * 录音按钮提示文字
 */
const recordingButtonTitle = computed(() => {
  if (props.isTtsWaiting) {
    return 'TTS生成中，请稍候...'
  }
  return exerciseStore.getRecordingButtonTitle()
})
</script>
```

## 🧪 测试规范

### 测试场景

#### 1. TTS等待状态测试
```typescript
describe('TTS等待状态测试', () => {
  test('TTS请求发起时，所有播放按钮被禁用', async () => {
    // 发起TTS请求
    await chatStore.startStreamTTS('msg-1', 'test text')
    
    // 验证状态
    expect(chatStore.isTtsWaiting).toBe(true)
    expect(chatStore.shouldDisablePlayButton('msg-1')).toBe(true)
    expect(chatStore.shouldDisablePlayButton('msg-2')).toBe(true)
  })
  
  test('录音按钮被禁用', async () => {
    chatStore.isTtsWaiting = true
    
    expect(exerciseStore.canStartRecording()).toBe(false)
  })
})
```

#### 2. 流式播放状态测试
```typescript
describe('流式播放状态测试', () => {
  test('流式播放时，当前音频暂停按钮被禁用', () => {
    chatStore.streamTtsState = {
      isPlaying: true,
      messageId: 'msg-1',
      isStreamPlaying: true
    }
    
    expect(chatStore.isMessageStreamPlaying('msg-1')).toBe(true)
    expect(chatStore.shouldDisablePlayButton('msg-1')).toBe(true)
    expect(chatStore.shouldDisablePlayButton('msg-2')).toBe(false)
  })
})
```

#### 3. 缓存重播测试
```typescript
describe('缓存重播测试', () => {
  test('缓存重播时，暂停按钮正常可用', () => {
    chatStore.streamTtsState = {
      isPlaying: true,
      messageId: 'msg-1',
      isStreamPlaying: false // 缓存重播
    }
    
    expect(chatStore.isMessageStreamPlaying('msg-1')).toBe(false)
    expect(chatStore.shouldDisablePlayButton('msg-1')).toBe(false)
  })
})
```

#### 4. 状态清理测试
```typescript
describe('状态清理测试', () => {
  test('TTS完成后，等待状态正确清理', async () => {
    await chatStore.startStreamTTS('msg-1', 'test')
    
    expect(chatStore.isTtsWaiting).toBe(false)
    expect(chatStore.currentTtsMessageId).toBe(null)
  })
  
  test('异常情况下，状态能够正确清理', async () => {
    // 模拟TTS失败
    jest.spyOn(chatStore, 'performStreamTTS').mockRejectedValue(new Error('TTS失败'))
    
    await chatStore.startStreamTTS('msg-1', 'test')
    
    expect(chatStore.isTtsWaiting).toBe(false)
    expect(chatStore.currentTtsMessageId).toBe(null)
  })
})
```

## 📊 性能指标

### 内存使用
- **新增状态变量**: 约 16 bytes
  - `isTtsWaiting`: 1 byte
  - `currentTtsMessageId`: 8 bytes (字符串引用)
  - `streamTtsState.isStreamPlaying`: 1 byte
  - 其他开销: 6 bytes

- **状态管理函数**: 约 2KB
- **总体影响**: 可忽略不计

### 响应性能
- **状态检查函数**: O(1) 时间复杂度
- **按钮状态更新**: 响应式自动更新，无额外开销
- **用户体验**: 无感知延迟

### 网络影响
- **减少无效请求**: 防止TTS等待时的重复请求
- **避免资源冲突**: 防止同时播放多个音频
- **提升稳定性**: 减少并发操作导致的错误

## 🔍 故障排查

### 常见问题

#### 1. 按钮一直禁用
**症状**: 按钮在TTS完成后仍然禁用  
**原因**: 状态清理逻辑未正确执行  
**解决**: 检查 `finally` 块中的状态重置逻辑

```typescript
// 确保状态清理
finally {
  isTtsWaiting.value = false
  currentTtsMessageId.value = null
  if (streamTtsState.value.messageId === messageId) {
    streamTtsState.value.isStreamPlaying = false
  }
}
```

#### 2. 流式播放状态错误
**症状**: 缓存重播时暂停按钮被错误禁用  
**原因**: `isStreamPlaying` 状态未正确区分  
**解决**: 确保缓存播放时设置 `isStreamPlaying = false`

```typescript
// 缓存播放时
streamTtsState.value = {
  isPlaying: true,
  messageId,
  isStreamPlaying: false // 明确设置为缓存播放
}
```

#### 3. 状态不同步
**症状**: 组件状态与store状态不一致  
**原因**: 响应式依赖未正确建立  
**解决**: 使用 `computed` 确保响应式更新

```typescript
// 使用computed确保响应式
const canStartRecording = computed(() => {
  return exerciseStore.canStartRecording() && !props.isTtsWaiting
})
```

### 调试工具

#### 状态监控
```typescript
// 开发环境下的状态监控
if (process.env.NODE_ENV === 'development') {
  watch([isTtsWaiting, streamTtsState], ([waiting, stream]) => {
    console.log('音频状态变化:', {
      isTtsWaiting: waiting,
      streamTtsState: stream,
      timestamp: new Date().toISOString()
    })
  }, { deep: true })
}
```

#### 按钮状态日志
```typescript
function shouldDisablePlayButton(messageId: string): boolean {
  const result = isTtsWaiting.value || isMessageStreamPlaying(messageId)
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`按钮禁用检查 [${messageId}]:`, {
      isTtsWaiting: isTtsWaiting.value,
      isStreamPlaying: isMessageStreamPlaying(messageId),
      result
    })
  }
  
  return result
}
```

## 🚀 扩展规划

### 短期优化 (v1.1.0)
1. **加载进度指示**
   - 显示TTS生成进度百分比
   - 添加进度条UI组件
   - 实时更新生成状态

2. **错误重试机制**
   - 自动重试失败的TTS请求
   - 指数退避重试策略
   - 用户手动重试选项

3. **视觉反馈增强**
   - 添加加载动画效果
   - 按钮状态过渡动画
   - 状态指示图标

### 中期规划 (v1.2.0)
1. **音频预加载**
   - 智能预加载下一条音频
   - 缓存策略优化
   - 网络状态感知

2. **快捷键支持**
   - 键盘快捷键操作
   - 快捷键状态同步
   - 无障碍访问支持

3. **批量操作**
   - 批量播放控制
   - 播放列表管理
   - 队列状态管理

### 长期规划 (v2.0.0)
1. **智能状态预测**
   - 基于用户行为预测状态
   - 自动优化按钮可用性
   - 机器学习状态管理

2. **多设备同步**
   - 跨设备状态同步
   - 云端状态存储
   - 实时状态广播

## 📚 相关文档

### 功能文档
- [音频播放架构设计](@Docs/Feature/audio-player_rules.md)
- [TTS流式播放实现](@Docs/Feature/tts-streaming_rules.md)
- [状态管理规范](@Docs/Feature/state-management_rules.md)

### 开发日志
- [音频按钮禁用功能开发日志](@Docs/DevLog/2025-05-26/audio-button-disable-feature.md)

### API文档
- [useChat Hook API](@Docs/API/useChat.md)
- [useExercise Hook API](@Docs/API/useExercise.md)
- [audioPlayer API](@Docs/API/audioPlayer.md)

## 🏷️ 标签

`#功能规则` `#音频播放` `#TTS` `#状态管理` `#用户体验` `#Vue3` `#TypeScript` `#按钮禁用` `#流式播放` 