# Exercise 组件功能规则文档

## 概述

Exercise 组件是课程练习功能的核心组件，提供AI角色扮演对话练习功能。用户可以通过语音与AI进行实时对话，系统会记录对话内容并提供语音播放功能。

## 组件架构

### 主组件 (`index.vue`)
- **职责**: 整体状态管理、API调用、组件协调
- **核心状态**: 录音状态、AI响应状态、音频播放状态、对话消息
- **生命周期**: 管理计时器、资源清理、欢迎消息

### 子组件结构

#### 1. InfoPanel (`components/InfoPanel.vue`)
- **职责**: 显示角色信息、课程信息、练习要求
- **特性**: 可折叠面板、信息分类展示、响应式设计

#### 2. ChatArea (`components/ChatArea.vue`)
- **职责**: 对话消息展示、音频播放控制
- **特性**: 自动滚动、播放状态管理、加载动画

#### 3. RecordingControl (`components/RecordingControl.vue`)
- **职责**: 录音控制界面、状态指示
- **特性**: 动态按钮状态、视觉反馈、操作提示

#### 4. AudioRecorder (`components/AudioRecorder.vue`)
- **职责**: 录音功能实现、语音识别
- **特性**: WebSocket连接、音频处理、错误重试

## 核心功能

### 1. 语音录音与识别

#### 技术实现
```typescript
// WebSocket连接语音识别服务
const wsUrl = `${baseURL}/admin/audio/asr?msgid=${messageId}`;
const ws = new WebSocket(wsUrl);

// 音频采集配置
const audioConfig = {
  sampleRate: 16000,
  channelCount: 1,
  echoCancellation: true,
  noiseSuppression: true,
  autoGainControl: true
};
```

#### 关键特性
- **实时语音识别**: 通过WebSocket实时传输音频数据
- **音频优化**: 启用回声消除、噪声抑制等功能
- **连接重试**: 网络异常时自动重连机制
- **时长限制**: 最大录音时长5分钟

### 2. AI对话系统

#### 对话流程
1. 用户语音输入 → 语音识别 → 文本消息
2. 发送到AI服务 → 获取AI回复
3. 调用TTS服务 → 生成语音
4. 自动播放AI语音回复

#### API调用
```typescript
// AI对话接口
const response = await workerTrainingTaskCourseChat({
  workerTrainingTaskId: Number(courseInfo.workerTrainingTaskId),
  trainingTaskCourseId: Number(courseInfo.trainingTaskCourseId),
  question: userMessage,
  chatContext: conversationHistory
});

// TTS语音合成
await sendTts({
  text: aiResponse,
  role: roleInfo.role,
  msgid: messageId
});
```

### 3. 音频播放管理

#### 播放控制
- **本地缓存优先**: 优先使用本地音频缓存
- **远程URL备用**: 缓存失效时使用远程音频URL
- **内存管理**: 自动清理Blob URL防止内存泄漏

#### 状态同步
```typescript
// 播放状态事件
emit("audioPlayStateChange", isPlaying);

// 父组件状态更新
function handleAudioPlayStateChange(isPlaying: boolean) {
  isAudioPlaying.value = isPlaying;
}
```

### 4. 状态管理机制

#### 核心状态
```typescript
interface ExerciseState {
  isRecording: boolean;        // 录音状态
  isAiResponding: boolean;     // AI响应状态
  isAudioPlaying: boolean;     // 音频播放状态
  recordingTime: number;       // 录音时长
  recordingText: string;       // 识别文本
  timer: number;               // 练习计时
  messages: Message[];         // 对话消息
}
```

#### 状态流转
1. **空闲状态**: 可以开始录音
2. **录音状态**: 禁用其他操作，显示录音界面
3. **AI响应状态**: 禁用录音，显示思考动画
4. **音频播放状态**: 禁用录音，显示播放控制

## 用户体验设计

### 1. 视觉反馈

#### 录音状态
- **录音按钮**: 红色脉冲动画
- **录音气泡**: 蓝色气泡显示识别文本
- **波形动画**: 模拟音频波形效果

#### AI思考状态
- **按钮变化**: 显示加载图标和旋转动画
- **思考气泡**: 绿色气泡显示思考动画
- **状态提示**: "AI思考中..."文字提示

### 2. 交互设计

#### 操作流程
1. **进入练习**: 自动播放欢迎消息
2. **开始对话**: 点击录音按钮开始录音
3. **语音输入**: 实时显示识别文本
4. **停止录音**: 再次点击按钮停止录音
5. **AI回复**: 自动播放AI语音回复
6. **继续对话**: 循环进行对话练习

#### 错误处理
- **权限错误**: 引导用户开启麦克风权限
- **网络错误**: 显示重试提示和自动重连
- **识别失败**: 提示重新录音

### 3. 响应式设计

#### 布局适配
- **桌面端**: 左右分栏布局，信息面板可折叠
- **移动端**: 上下堆叠布局，优化触摸操作
- **小屏幕**: 自动隐藏次要信息，突出核心功能

## 性能优化

### 1. 内存管理
- **音频缓存**: 使用Map存储音频数据，避免重复下载
- **Blob清理**: 及时清理Blob URL，防止内存泄漏
- **组件卸载**: 完整清理所有资源和事件监听器

### 2. 网络优化
- **连接复用**: WebSocket连接复用，减少连接开销
- **重试机制**: 智能重试策略，避免无效请求
- **超时控制**: 设置合理的连接和请求超时时间

### 3. 渲染优化
- **虚拟滚动**: 大量消息时使用虚拟滚动
- **懒加载**: 非关键组件延迟加载
- **状态优化**: 减少不必要的状态更新和重渲染

## 错误处理策略

### 1. 分类处理
- **权限错误**: 用户友好的权限引导
- **网络错误**: 自动重试和手动重试选项
- **服务错误**: 降级处理和错误上报

### 2. 用户提示
- **Toast消息**: 简短的操作反馈
- **确认对话框**: 重要操作的二次确认
- **状态指示**: 实时的操作状态显示

### 3. 恢复机制
- **自动重连**: WebSocket连接异常时自动重连
- **状态恢复**: 页面刷新后恢复练习状态
- **数据备份**: 重要数据的本地备份

## 扩展性设计

### 1. 插件化架构
- **录音插件**: 支持不同的录音实现
- **AI插件**: 支持不同的AI服务提供商
- **TTS插件**: 支持多种语音合成服务

### 2. 配置化
- **角色配置**: 支持自定义角色设置
- **界面配置**: 支持主题和布局自定义
- **功能配置**: 支持功能模块的开启关闭

### 3. 国际化
- **多语言支持**: 界面文本的多语言切换
- **语音识别**: 支持多种语言的语音识别
- **TTS语音**: 支持多种语言的语音合成

## 测试策略

### 1. 单元测试
- **组件测试**: 各子组件的独立功能测试
- **工具函数**: 纯函数的输入输出测试
- **状态管理**: 状态变更逻辑的测试

### 2. 集成测试
- **组件协作**: 父子组件间的交互测试
- **API集成**: 与后端服务的集成测试
- **端到端**: 完整用户流程的测试

### 3. 性能测试
- **内存泄漏**: 长时间使用的内存监控
- **网络性能**: 不同网络条件下的性能测试
- **设备兼容**: 不同设备和浏览器的兼容性测试

## 安全考虑

### 1. 数据安全
- **音频数据**: 录音数据的加密传输
- **对话内容**: 敏感信息的脱敏处理
- **用户隐私**: 符合隐私保护法规

### 2. 接口安全
- **身份验证**: API调用的身份验证
- **权限控制**: 基于角色的权限控制
- **防护机制**: 防止恶意攻击和滥用

## 维护指南

### 1. 代码维护
- **代码规范**: 遵循项目代码规范
- **注释文档**: 完善的代码注释和文档
- **版本控制**: 规范的Git提交和分支管理

### 2. 监控告警
- **错误监控**: 实时错误监控和告警
- **性能监控**: 关键性能指标的监控
- **用户反馈**: 用户问题的收集和处理

### 3. 更新升级
- **向后兼容**: 保持API的向后兼容性
- **渐进升级**: 支持功能的渐进式升级
- **回滚机制**: 问题发生时的快速回滚 